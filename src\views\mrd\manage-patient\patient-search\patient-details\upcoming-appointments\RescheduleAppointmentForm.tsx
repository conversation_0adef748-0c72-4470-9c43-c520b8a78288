import { FC, memo, useCallback } from 'react';

import { useForm, FormProvider } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import { Grid2 as Grid, Typography } from '@mui/material';
import dayjs from 'dayjs';
import * as yup from 'yup';

import { useBookConsultationStore } from '@/store/mrd/manage-patient/book-consultation';
import { useMrdPatientSearch } from '@/store/mrd/manage-patient/patient-search';

import ControlledDatePicker from '@/components/controlled-inputs/ControlledDatePicker';
import ControlledTimePicker from '@/components/controlled-inputs/ControlledTimePicker';

import AppButton from '@/core/components/app-button';
import { Consultation } from '@/types/mrd/manage-patient/consultation';

type RescheduleFormValues = {
  date: string;
  time: string;
};

type Props = {
  appointment: Consultation;
  onSave: (date: string, time: string) => void;
  onCancel: () => void;
  isLoading: boolean;
};

const rescheduleSchema = yup.object().shape({
  date: yup.string().required('Date is required'),
  time: yup.string().required('Time is required'),
});

const RescheduleAppointmentForm: FC<Props> = ({
  appointment,
  onSave,
  onCancel,
  isLoading,
}) => {
  const { doctors } = useBookConsultationStore();
  const { patient } = useMrdPatientSearch();

  const doctor = doctors.find((doc) => doc.id === appointment.doctorId);

  const methods = useForm<RescheduleFormValues>({
    resolver: yupResolver(rescheduleSchema),
    defaultValues: {
      date: appointment.date,
      time: appointment.time,
    },
  });

  const { handleSubmit, control } = methods;

  const onSubmit = useCallback(
    (data: RescheduleFormValues) => {
      onSave(data.date, data.time);
    },
    [onSave]
  );

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={2} rowSpacing={4} className="p-base">
          <Grid size={6}>
            <Typography variant="body2" color="textSecondary">
              Patient Name
            </Typography>
            <Typography variant="body1" className="font-semibold">
              {patient?.name || 'N/A'}
            </Typography>
          </Grid>
          <Grid size={6}>
            <Typography variant="body2" color="textSecondary">
              Doctor
            </Typography>
            <Typography variant="body1" className="font-semibold">
              {doctor?.name || 'N/A'}
            </Typography>
          </Grid>

          <Grid size={6}>
            <ControlledDatePicker
              label="Select Date"
              name="date"
              control={control}
              required
              minDate={dayjs()}
            />
          </Grid>
          <Grid size={6}>
            <ControlledTimePicker
              label="Select Time"
              name="time"
              control={control}
              required
            />
          </Grid>
        </Grid>
        <div className="flex justify-end gap-base p-base">
          <AppButton
            variant="outlined"
            onClick={onCancel}
            disabled={isLoading}
            fullWidth
          >
            Cancel
          </AppButton>
          <AppButton type="submit" loading={isLoading} fullWidth>
            Save
          </AppButton>
        </div>
      </form>
    </FormProvider>
  );
};

export default memo(RescheduleAppointmentForm);
