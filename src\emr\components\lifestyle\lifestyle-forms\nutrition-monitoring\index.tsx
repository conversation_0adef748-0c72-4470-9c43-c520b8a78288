import React, { useState, useRef, useEffect, useCallback } from 'react';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useLifestyleStore } from '@/store/lifestyle';
import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';
import { useUserStore } from '@/store/userStore';

import { getDoctorName } from '@/utils/constants/lifeStyle';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { lifestyleFormMode } from '@/constants/lifestyle';

import { LifestyleQuestion, lifestyleSource } from '@/emr/types/lifestyle';

import { DateRange } from '@/core/components/date-range-picker/types';

import Accordion from '../shared/Accordion';
import FinalizeModal from '../shared/FinalizeModal';
import TimeLine from '../shared/TimeLine';
import TimelineTitle from '../shared/TimelineTitle';

import NutritionMonitoringItem from './NutritionMonitoringItem';

type Props = {
  isShowTitle?: boolean;
};

const { DATE_DD_MM_YYYY_SLASH, STANDARD_DATE } = DateFormats;
const { NUTRITION_MONITORING_SHEET_SOURCE } = lifestyleSource;

const NutritionMonitoringTimeline = ({ isShowTitle = true }: Props) => {
  const isMobile = useIsMobile();
  const {
    loadingTimeLine,
    patientLifestyle,
    finaliseRecord,
    updating,
    getPatientLifestyle,
  } = useLifestyleStore();
  const { onExpand } = useLifestyleUtilStore();
  const { patient } = useCurrentPatientStore();
  const doctor = useDoctorStore((state) => state.doctorProfile);

  const { name } = useUserStore();
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);
  const [finaliseData, setFinaliseData] = useState<LifestyleQuestion | null>(
    null
  );
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });

  const fetchByDate = useCallback(
    async (range: DateRange) => {
      if (patient?.id) {
        await getPatientLifestyle(
          patient.id,
          NUTRITION_MONITORING_SHEET_SOURCE,
          range.from ? formatDate(range.from as string, STANDARD_DATE) : '',
          range.to ? formatDate(range.to as string, STANDARD_DATE) : ''
        );
      }
    },
    [patient?.id, getPatientLifestyle]
  );

  useEffect(() => {
    const hasBothDates = dateRange?.from && dateRange?.to;
    const isCleared = !dateRange?.from && !dateRange?.to;

    const shouldFetch = (hasBothDates || isCleared) && !isCalendarOpen;

    if (shouldFetch) {
      fetchByDate(dateRange);
    }
  }, [dateRange, isCalendarOpen, fetchByDate]);

  const handleDateChange = (range: DateRange) => {
    setDateRange(range);
  };

  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  const toggleAccordion = (index: number) => {
    setExpandedIndex((prev) => (prev === index ? null : index));
  };

  const onFinaliseRecord = useCallback(async () => {
    await finaliseRecord(finaliseData);
    setFinaliseData(null);
    if (patient?.id) {
      getPatientLifestyle(patient?.id, NUTRITION_MONITORING_SHEET_SOURCE);
    }
  }, [finaliseData, finaliseRecord, getPatientLifestyle, patient?.id]);

  useEffect(() => {
    if (expandedIndex !== null && scrollContainerRef.current) {
      const scrollContainer = scrollContainerRef.current;
      const targetAccordion = scrollContainer.querySelector(
        `.accordion-item-${expandedIndex}`
      );

      const expandedAccordionOffset = expandedIndex * 50;

      if (targetAccordion instanceof HTMLElement) {
        scrollContainer.scrollTo({
          top: expandedAccordionOffset,
          behavior: 'smooth',
        });
      }
    }
  }, [expandedIndex]);

  return (
    <div className="mx-0 mt-3 h-full p-0 pb-20 md:pb-2 md:p-2 md:pr-1 w-full overflow-y-hidden overflow-x-hidden">
      {isShowTitle && (
        <TimelineTitle
          title="Nutrition Monitoring Sheet"
          dateRange={dateRange}
          onDateChange={handleDateChange}
          setIsCalendarOpen={setIsCalendarOpen}
          isCalendarOpen={isCalendarOpen}
        />
      )}
      <div
        ref={scrollContainerRef}
        className={`overflow-y-auto h-[calc(100%-2.5rem)] w-full pr-1 pb-2 ${
          isMobile ? 'h-[calc(100dvh-17.5rem)]' : 'rounded-thin-scrollbar'
        }`}
      >
        <TimeLine
          loading={loadingTimeLine}
          items={patientLifestyle?.map((assessment, index) => ({
            content: (
              <div key={assessment.id} className={`accordion-item-${index}`}>
                <Accordion
                  open={expandedIndex === index}
                  onToggle={() => toggleAccordion(index)}
                  date={formatDate(
                    assessment.updated_on,
                    DATE_DD_MM_YYYY_SLASH
                  )}
                  onFinalise={() => setFinaliseData(assessment)}
                  designation={doctor?.general?.department}
                  doctorName={getDoctorName(doctor?.general?.fullName, name)}
                  showFinaliseButton={
                    assessment?.status === lifestyleFormMode.EDITABLE
                  }
                  onExpand={() => onExpand(assessment)}
                  width="100%"
                >
                  <NutritionMonitoringItem data={assessment} />
                </Accordion>
              </div>
            ),
          }))}
        />
      </div>
      <FinalizeModal
        open={!!finaliseData}
        onClose={() => setFinaliseData(null)}
        onFinalize={onFinaliseRecord}
        loading={updating}
      />
    </div>
  );
};

export default NutritionMonitoringTimeline;
