import { Patient } from '@/query/patient';

import { calculateAge } from '@/helpers/dates';

import { PatientI } from '@/types';

interface FormattedOrganization {
  name: string;
  address: string;
  phone: string;
  email: string;
  contactPerson: string;
}

// Types
interface PatientData {
  id?: string;
  name?: string;
  age?: string | number;
  mobile?: string;
  gender?: string;
}

export const calculateTotalAmount = (medicines: any[] = []): number => {
  return medicines.reduce((sum, med) => {
    const cost = parseFloat(med.cost || '0') * parseFloat(med.quantity || '0');
    return sum + (isNaN(cost) ? 0 : cost);
  }, 0);
};

export const formatOrganization = (
  organization: any
): FormattedOrganization => {
  if (!organization) {
    return { name: '', address: '', phone: '', email: '', contactPerson: '' };
  }

  const address = organization.address
    ? [
        organization.address.street,
        organization.address.city,
        organization.address.state,
        organization.address.postalCode,
        organization.address.country,
      ]
        .filter(Boolean)
        .join(', ')
    : '';

  return {
    name: organization.name || '',
    address,
    phone: organization.contactPhone || '',
    email: organization.contactEmail || '',
    contactPerson: organization.contactPersonName || '',
  };
};

export const formatPatientData = (
  patient: Patient | PatientI | null | undefined,
  selectedHistory: any = {}
): PatientData => {
  const defaultValues = {
    name: '',
    age: '',
    mobile: '',
    gender: '',
    id: '',
  };

  if (!patient && !selectedHistory) {
    return defaultValues;
  }

  return {
    name:
      (patient as Patient)?.name ||
      (patient as PatientI)?.name ||
      selectedHistory?.medicines?.[0]?.patientName ||
      '',
    age: (patient as Patient)?.dob
      ? calculateAge((patient as Patient).dob as string)
      : '',
    mobile:
      (patient as Patient)?.contact?.phone ||
      (patient as PatientI)?.contact?.phone ||
      selectedHistory?.medicines?.[0]?.patientMobile ||
      '',
    gender: (patient as Patient)?.sex || (patient as PatientI)?.sex || '',
    id:
      (patient as Patient)?.id ||
      (patient as PatientI)?.id ||
      selectedHistory?.id?.split('-')[0] ||
      '',
  };
};
