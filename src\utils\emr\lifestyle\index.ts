import dayjs from 'dayjs';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { LifestyleFilter } from '@/constants/emr/lifestyle/filter';

const { ONE_MONTH, SEVEN_DAYS, FIFTEEN_DAYS } = LifestyleFilter;
const { STANDARD_DATE } = DateFormats;

export const getFromDateAndDate = (
  filter: LifestyleFilter
): [string, string] => {
  const today = dayjs();

  let fromDate = '';
  let toDate = formatDate(today, STANDARD_DATE);

  switch (filter) {
    case ONE_MONTH: {
      fromDate = formatDate(today.subtract(1, 'month'), STANDARD_DATE);
      break;
    }
    case SEVEN_DAYS: {
      fromDate = formatDate(today.subtract(6, 'day'), STANDARD_DATE);
      break;
    }
    case FIFTEEN_DAYS: {
      fromDate = formatDate(today.subtract(14, 'day'), STANDARD_DATE);
      break;
    }
    default: {
      fromDate = formatDate(today.startOf('month'), STANDARD_DATE);
      break;
    }
  }
  return [fromDate, toDate];
};

export const isValidURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};
