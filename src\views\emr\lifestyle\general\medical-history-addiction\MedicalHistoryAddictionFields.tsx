import React, { memo, useCallback, useState } from 'react';

import { FieldArrayWithId, useFormContext } from 'react-hook-form';

import {
  generateYearOptions,
  statusOption,
  substanceFrequencyOption,
  substanceHistoryOption,
} from '@/constants/emr/lifestyle/medical-history-addiction';

import ControlledRadio from '@/components/controlled-inputs/ControlledRadio';

import AppButton from '@/core/components/app-button';
import AppIcon from '@/core/components/app-icon';
import AppIconButton from '@/core/components/app-icon-button';
import {
  MedicalHistoryAddiction,
  MedicalHistoryAddictionForm,
  NicotineDependenceTestResult,
} from '@/types/emr/lifestyle/medical-history-addiction';

import NicotineDependenceTest from './NicotineDependenceTest';

import StyledSelectField from './components/StyledSelectField';
import StyledTextField from './components/StyledTextField';

// Re-export types for backward compatibility
export type { StyledTextFieldProps } from './components/StyledTextField';
export type { StyledSelectFieldProps } from './components/StyledSelectField';

type Props = {
  fields: FieldArrayWithId<MedicalHistoryAddictionForm, 'diagnosis', 'id'>[];
  onAdd: () => void;
  onRemove: (index: number) => void;
  medicalHistory: MedicalHistoryAddiction | null;
};

const yearOptions = generateYearOptions();

const MedicalHistoryAddictionFields: React.FC<Props> = ({
  fields,
  onAdd,
  onRemove,
  medicalHistory,
}) => {
  const { control, watch, setValue } =
    useFormContext<MedicalHistoryAddictionForm>();

  const [showNicotineTest, setShowNicotineTest] = useState(false);

  const smokingHistory = watch('smoking.history');
  const smokingCount = watch('smoking.count');

  const handleNicotineTestSave = useCallback(
    (testResult: NicotineDependenceTestResult) => {
      setValue('nicotineDependenceTest', testResult);
    },
    [setValue]
  );

  return (
    <div className="flex-1 p-base space-y-6 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
      {/* Diagnosis Section */}
      <div className="space-y-4">
        {fields?.map((field, index) => (
          <div key={field.id} className="border-b space-y-4 p-base">
            <div className="grid grid-cols-12 gap-4 items-end">
              <div className="col-span-4">
                <div className="text-xs">
                  <StyledTextField
                    name={`diagnosis.${index}.diseaseName`}
                    control={control}
                    label="Diagnosis"
                    placeholder="Name of Disease"
                    fullWidth
                    initiallyReadonly
                  />
                </div>
              </div>

              <div className="col-span-2">
                <div className="text-xs">
                  <StyledSelectField
                    name={`diagnosis.${index}.yearOfDiagnosis`}
                    control={control}
                    label="Year of Diagnosis"
                    options={yearOptions}
                    placeholder="YYYY"
                  />
                </div>
              </div>

              <div className="col-span-2">
                <div className="text-xs">
                  <StyledTextField
                    name={`diagnosis.${index}.diagnosisDuration`}
                    control={control}
                    label="Diagnosis Duration"
                    placeholder="5 months"
                    initiallyReadonly
                  />
                </div>
              </div>

              <div className="col-span-4">
                <ControlledRadio
                  name={`diagnosis.${index}.status`}
                  control={control}
                  label="Status"
                  options={statusOption}
                  row
                />
              </div>
            </div>

            <div className="mt-4 flex gap-base items-end">
              <StyledTextField
                name={`diagnosis.${index}.treatmentHistory`}
                control={control}
                label="Treatment History"
                placeholder="Text"
                fullWidth
                initiallyReadonly
              />

              <div className="flex gap-base items-center pb-2 pl-7">
                {fields.length > 1 && (
                  <AppIconButton
                    variant="outlined"
                    onClick={() => onRemove(index)}
                    color="error"
                    sx={{ borderRadius: 1 }}
                  >
                    <AppIcon icon="material-symbols:delete-outline-rounded" />
                  </AppIconButton>
                )}
                <div className="flex whitespace-nowrap gap-1 items-center text-sm">
                  Add another record
                  <AppIconButton
                    variant="outlined"
                    color="info"
                    onClick={onAdd}
                    sx={{ borderRadius: 1 }}
                  >
                    <AppIcon icon="ic:round-add" />
                  </AppIconButton>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="space-y-2">
        {/* Smoking History Section */}
        <div className="space-y-2">
          <ControlledRadio
            label="Smoking History"
            name="smoking.history"
            control={control}
            options={substanceHistoryOption}
            row
          />
          {(smokingHistory === 'current' || smokingHistory === 'former') && (
            <div className="grid grid-cols-12 gap-4 items-end">
              <div className="col-span-2">
                <div className="text-xs">
                  <StyledTextField
                    name="smoking.count"
                    control={control}
                    label="Smoking Pack Year"
                    type="number"
                    placeholder="0"
                    initiallyReadonly
                    fullWidth
                  />
                </div>
              </div>
              <div className="col-span-2">
                <div className="text-xs">
                  <StyledSelectField
                    name="smoking.frequency"
                    control={control}
                    label="Frequency"
                    options={substanceFrequencyOption}
                    placeholder="Select"
                  />
                </div>
              </div>
              <div className="col-span-3">
                <AppButton
                  type="button"
                  variant="text"
                  onClick={() => setShowNicotineTest(true)}
                  sx={{ textDecoration: 'underline' }}
                >
                  Nicotine Dependence Test
                </AppButton>
              </div>
            </div>
          )}
        </div>

        {/* Alcohol Consumption Section */}
        <div className="space-y-2">
          <ControlledRadio
            label="Alcohol Consumption"
            name="alcohol.history"
            control={control}
            options={substanceHistoryOption}
            row
          />
        </div>

        {/* Oral Tobacco Section */}
        <div className="space-y-2">
          <ControlledRadio
            label="Oral Tobacco"
            name="tobacco.history"
            control={control}
            options={substanceHistoryOption}
            row
          />
        </div>

        {/* Drugs Section */}
        <div className="space-y-2">
          <ControlledRadio
            label="Drugs"
            name="drugs.history"
            control={control}
            options={substanceHistoryOption}
            row
          />
        </div>
      </div>
      {/* Nicotine Dependence Test Modal */}
      <NicotineDependenceTest
        isOpen={showNicotineTest}
        onClose={() => setShowNicotineTest(false)}
        control={control}
        onSave={handleNicotineTestSave}
        isEditMode={!!medicalHistory?.id}
      />
    </div>
  );
};

export default memo(MedicalHistoryAddictionFields);
