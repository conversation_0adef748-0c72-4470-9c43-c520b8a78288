import { useEffect, useState } from 'react';

import { format, isToday } from 'date-fns';
import { BiChevronDown } from 'react-icons/bi';

import { cn } from '@/lib/utils';

import { archivo } from '@/utils/fonts';

import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

export type DatePickerProps = {
  value?: Date | string;
  onChange?: (date?: Date) => void;
  className?: string;
};

const Label = ({ date }: { date?: Date }) => {
  if (!date) {
    return <span>Select a date</span>;
  }

  if (isToday(date)) {
    return <span>Today</span>;
  }

  return format(date, 'PPP');
};

/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the app date picker component instead.
 */
export function DatePicker({
  value,
  onChange = () => {},
  className,
}: DatePickerProps) {
  const [date, setDate] = useState<Date>();
  const [isOpen, setIsOpen] = useState(false);

  const handleSelectDate = (selectedDate?: Date) => {
    setDate(selectedDate);
    onChange(selectedDate);
    setIsOpen(false);
  };

  useEffect(() => {
    if (typeof value === 'string') {
      setDate(new Date(value));
    } else {
      setDate(value);
    }
  }, [value]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <button
          className={cn(
            'flex gap-5 items-center justify-between border border-black px-2 py-1 rounded-full',
            archivo.className,
            className,
            'text-xs'
          )}
        >
          <Label date={date} />
          <BiChevronDown />
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar mode="single" selected={date} onSelect={handleSelectDate} />
      </PopoverContent>
    </Popover>
  );
}
