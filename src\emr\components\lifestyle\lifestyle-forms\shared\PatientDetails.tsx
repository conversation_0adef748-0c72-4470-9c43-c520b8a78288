import { useCurrentPatientStore } from '@/store/currentPatientStore';

import colors from '@/utils/colors';

import MaterialSymbolsPatientList from '@/assets/svg/MaterialSymbolsPatientList';

import { calculateAge } from '@/helpers/dates';

import TitleWithIcon from './TitleWithIcon';

type Props = {
  cardBgColor?: string;
};

const PatientDetails: React.FC<Props> = ({
  cardBgColor = colors.common.lightBlue,
}) => {
  const { patient } = useCurrentPatientStore();
  return (
    <div className="rounded-lg w-full max-w-full">
      <TitleWithIcon
        icon={<MaterialSymbolsPatientList />}
        title="Patient Details"
      />
      <div
        style={{ backgroundColor: cardBgColor }}
        className="p-3 mt-2 rounded-md flex flex-wrap gap-x-4 gap-y-2"
      >
        <Detail label="Name" value={patient?.name} />
        <Detail
          label="Patient ID"
          value={patient?.id}
          className="border-r-2 md:border-r-0"
        />
        <Detail
          label="Age"
          value={patient?.dob ? `${calculateAge(patient?.dob)} yrs` : undefined}
          className="ml-0 pl-0 md:pl-2 border-l-0 md:border-l-2 "
        />
      </div>
    </div>
  );
};

type DetailProps = {
  label: string;
  value?: string;
  className?: string;
};

const Detail: React.FC<DetailProps> = ({ label, value }) => (
  <span className="flex text-xs md:text-base items-center border-r-2 border-black pr-3 last:border-r-0 first:pl-0">
    {label}:{' '}
    <span className="font-medium ml-1 text-xs md:text-base ">
      {value || '--'}
    </span>
  </span>
);

export default PatientDetails;
