import { createTheme, ThemeOptions } from '@mui/material/styles';

import components from './components';
import { darkPalette, lightPalette } from './palette';
import { shadows } from './shadows';
import { shape } from './shape';
import './types';
import { Settings } from './types';
import typography from './typography';

const baseThemeOptions: ThemeOptions = {
  typography,
  shape,
  spacing: (factor: number) => `${factor * 0.25}rem`,
  shadows,
};

export const lightTheme = (settings: Settings) =>
  createTheme({
    ...baseThemeOptions,
    palette: lightPalette,
    components: components({ ...settings, palette: lightPalette, typography }),
  });

export const darkTheme = (settings: Settings) =>
  createTheme({
    ...baseThemeOptions,
    palette: darkPalette,
    components: components({ ...settings, palette: darkPalette, typography }),
  });
