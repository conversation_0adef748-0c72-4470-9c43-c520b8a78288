import { useState, useEffect, useCallback, useRef, useMemo } from 'react';

import { components } from 'react-select';
import type { SingleValue } from 'react-select';

import { Box, Divider, Typography } from '@mui/material';

import { useTestStore } from '@/store/emr/lab/reports-store';

import { createDebouncedSearch } from '@/utils/search';

import AsyncSearch from '@/core/components/search';
import { LabTestItem } from '@/types/emr/lab';

import CommonSelectDropdown from '../packages/DepartmentDropdown';

interface TestOption {
  value: string;
  label: string;
  test?: LabTestItem;
}

interface TestSearchProps {
  placeholder?: string;
  onChange?: (test: LabTestItem | null) => void;
  pageKey: string;
}

const TestSearch: React.FC<TestSearchProps> = ({
  placeholder = 'Search by Test',
  onChange,
  pageKey,
}) => {
  const { searchTests, setSelectedSearchTest, labDepartments } = useTestStore();

  const [inputValue, setInputValue] = useState('');
  const [selectedOption, setSelectedOption] = useState<TestOption | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('ALL');

  // Enhanced refs for better race condition handling
  const abortControllerRef = useRef<AbortController | null>(null);
  const currentSearchRef = useRef<string>('');
  const searchRequestIdRef = useRef<number>(0); // Add request ID counter
  const pendingCallbacksRef = useRef<Set<number>>(new Set()); // Track pending callbacks

  // Create debounced search with enhanced error handling
  const { search: debouncedSearch, cancel: cancelSearch } = useMemo(
    () =>
      createDebouncedSearch<LabTestItem>(
        async (searchTerm: string, signal?: AbortSignal) => {
          try {
            // Check if request was aborted before making API call
            if (signal?.aborted) {
              throw new Error('Request aborted');
            }

            const results = await searchTests(
              searchTerm,
              selectedDepartment || ''
            );

            // Check again after API call
            if (signal?.aborted) {
              throw new Error('Request aborted');
            }

            return results;
          } catch (error) {
            if (
              error instanceof Error &&
              error.name !== 'AbortError' &&
              error.message !== 'Request aborted'
            ) {
              console.error('Error searching tests:', error);
            }
            throw error; // Re-throw to be handled by debounced function
          }
        },
        500,
        {
          minLength: 1,
          maxWait: 1000,
          onStart: () => {
            // Cancel any pending requests
            if (abortControllerRef.current) {
              abortControllerRef.current.abort();
            }
            abortControllerRef.current = new AbortController();
          },
          onComplete: () => {
            abortControllerRef.current = null;
          },
          onError: (error) => {
            // Only log non-abort errors
            if (
              error instanceof Error &&
              error.name !== 'AbortError' &&
              error.message !== 'Request aborted'
            ) {
              console.error('Search error:', error);
            }
          },
        }
      ),
    [searchTests, selectedDepartment]
  );

  // Transform results to options
  const transformResults = useCallback(
    (results: LabTestItem[]): TestOption[] => {
      return results
        .filter(
          (test): test is LabTestItem & { id: string; name: string } =>
            !!test.id && !!test.name
        )
        .map((test) => ({
          value: test.id,
          label: test.name,
          test,
        }));
    },
    []
  );

  // Enhanced load options function with better race condition handling
  const loadOptions = useCallback(
    (inputVal: string, callback: (options: readonly TestOption[]) => void) => {
      const trimmedInput = inputVal.trim();

      // If input is empty, return empty options immediately
      if (trimmedInput.length === 0) {
        // Clear any pending requests
        cancelSearch();
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        // Clear pending callbacks
        pendingCallbacksRef.current.clear();
        callback([]);
        return;
      }

      // Generate unique request ID
      const requestId = ++searchRequestIdRef.current;
      pendingCallbacksRef.current.add(requestId);

      // Update the current search ref
      currentSearchRef.current = trimmedInput;

      // Enhanced callback wrapper with request ID validation
      const wrappedCallback = (results: LabTestItem[]) => {
        // Check if this request is still valid
        if (!pendingCallbacksRef.current.has(requestId)) {
          return; // This request has been superseded
        }

        // Check if the search term is still current
        if (currentSearchRef.current !== trimmedInput) {
          pendingCallbacksRef.current.delete(requestId);
          return; // Search term has changed
        }

        try {
          const options = transformResults(results);

          // Final validation before calling callback
          if (
            currentSearchRef.current === trimmedInput &&
            pendingCallbacksRef.current.has(requestId)
          ) {
            callback(options);
          }
        } catch (error) {
          console.error('Error transforming results:', error);
          callback([]);
        } finally {
          pendingCallbacksRef.current.delete(requestId);
        }
      };

      // Error callback wrapper
      const errorCallback = () => {
        if (pendingCallbacksRef.current.has(requestId)) {
          pendingCallbacksRef.current.delete(requestId);
          // Only show empty results if this is still the current search
          if (currentSearchRef.current === trimmedInput) {
            callback([]);
          }
        }
      };

      // Use debounced search with enhanced error handling
      try {
        debouncedSearch(
          trimmedInput,
          wrappedCallback,
          abortControllerRef.current?.signal
        ).catch(errorCallback);
      } catch (error) {
        errorCallback();
      }
    },
    [debouncedSearch, transformResults, cancelSearch]
  );

  // Handle input change with cleanup
  const handleInputChange = useCallback(
    (newValue: string, { action }: { action: string }) => {
      if (action === 'input-change') {
        setInputValue(newValue);

        // If input is cleared, immediately clear everything
        if (!newValue.trim()) {
          // Cancel all pending operations
          cancelSearch();
          if (abortControllerRef.current) {
            abortControllerRef.current.abort();
          }
          pendingCallbacksRef.current.clear();
          currentSearchRef.current = '';
        }
      }
    },
    [cancelSearch]
  );

  // Handle department change with cleanup
  const handleDepartmentChange = useCallback(
    (value: string) => {
      setSelectedDepartment(value);

      // Cancel all pending operations
      cancelSearch();
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      pendingCallbacksRef.current.clear();
      currentSearchRef.current = '';
    },
    [cancelSearch]
  );

  // Enhanced cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      cancelSearch();
      pendingCallbacksRef.current.clear();
    };
  }, [cancelSearch]);

  const handleChange = (newValue: SingleValue<TestOption>) => {
    const test = newValue ? newValue.test : null;
    if (test) {
      setSelectedSearchTest(test, pageKey);
    }
    onChange?.(test as LabTestItem);
    setSelectedOption(newValue);
    setInputValue('');

    // Clear selection after a short delay
    setTimeout(() => setSelectedOption(null), 800);
  };

  const CustomOption = (props: any) => {
    const { test } = props.data;
    return (
      <>
        <components.Option {...props}>
          <Box display="flex" alignItems="center" px={1} py={0.5}>
            <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
              {test.name}
            </Typography>
          </Box>
        </components.Option>
        <Divider sx={{ my: 0 }} />
      </>
    );
  };

  const CustomSingleValue = (props: any) => {
    const { test } = props.data;
    return (
      <Box display="flex" alignItems="center" px={1} py={0.5}>
        <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
          {test.name}
        </Typography>
      </Box>
    );
  };

  return (
    <>
      <div className="flex-[2]">
        <AsyncSearch<TestOption>
          loadOptions={loadOptions}
          onChange={handleChange}
          placeholder={placeholder}
          value={selectedOption}
          defaultOptions={[]}
          cacheOptions={false}
          components={{
            Option: CustomOption,
            SingleValue: CustomSingleValue,
            DropdownIndicator: () => null,
            IndicatorSeparator: () => null,
          }}
          onInputChange={handleInputChange}
          inputValue={inputValue}
          noOptionsMessage={({ inputValue }) =>
            inputValue.trim().length === 0
              ? 'Start typing to search tests'
              : 'No tests found'
          }
        />
      </div>
      <div className="flex-[1.2]">
        <CommonSelectDropdown
          name="department"
          value={selectedDepartment}
          placeholder="Select Your Department"
          options={labDepartments}
          onChange={handleDepartmentChange}
          className="w-full"
          formControlProps={{ sx: { maxWidth: '100%' } }}
        />
      </div>
    </>
  );
};

export default TestSearch;
