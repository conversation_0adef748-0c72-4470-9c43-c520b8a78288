import { ReactNode, useCallback, useState } from 'react';

import { Collapse } from '@mui/material';

import { cn } from '@/lib/utils';

import colors from '@/utils/colors';

export type LifestyleTabItem = {
  label: string;
  icon: ReactNode;
  content: ReactNode;
  disabled?: boolean;
};

type LifestyleTabsProps = {
  items: LifestyleTabItem[];
  className?: string;
};

const getButtonStyle = (isActive: boolean) => {
  return {
    backgroundColor: isActive ? colors.common.navyBlue : 'transparent',
    borderBottomColor: isActive
      ? colors.common.navyBlue
      : colors.common.paleBlue,
  };
};

const LifestyleTabs = ({ items, className }: LifestyleTabsProps) => {
  const [activeTab, setActiveTab] = useState(0);

  const isActive = useCallback(
    (index: number) => activeTab === index,
    [activeTab]
  );

  return (
    <div className={cn('flex flex-col w-full', className)}>
      <div className="flex items-center w-full">
        <div className="flex flex-1 w-full max-w-full overflow-x-auto scrollbar-hide shadow-custom-xs rounded select-none">
          <div className="flex border-gray-200 w-fit min-w-full h-full justify-between items-baseline rounded">
            {items.map((item, index) => (
              <button
                key={index}
                disabled={item?.disabled}
                onClick={() => setActiveTab(index)}
                style={getButtonStyle(isActive(index))}
                className={cn(
                  'flex flex-1 w-fit items-center h-full gap-2 px-2 py-2 text-sm font-medium rounded-t-lg box-border border-b-4',
                  isActive(index)
                    ? `text-white border-b-[${colors.common.navyBlue}]`
                    : 'text-gray-600'
                )}
              >
                <div className="w-5 h-5">{item.icon}</div>
                <Collapse
                  in={isActive(index)}
                  orientation="horizontal"
                  collapsedSize={0}
                  className="w-fit flex items-center"
                  timeout={{ appear: 100, enter: 600, exit: 0 }}
                >
                  <span className="whitespace-nowrap">{item?.label}</span>
                </Collapse>
              </button>
            ))}
          </div>
        </div>
      </div>
      <div className="w-full h-full pt-2">
        {items[activeTab] && <>{items[activeTab].content}</>}
      </div>
    </div>
  );
};

export default LifestyleTabs;
