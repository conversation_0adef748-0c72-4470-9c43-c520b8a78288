import React, { FC, memo } from 'react';

import {
  Modal,
  Box,
  Typography,
  IconButton,
  Divider,
  Fade,
  Zoom,
} from '@mui/material';
import { XIcon } from 'lucide-react';

type Props = {
  imagePreview: string | null;
  onClose: () => void;
};

const PreviewModal: FC<Props> = ({ imagePreview, onClose }) => {
  return (
    <Modal
      open={!!imagePreview}
      onClose={onClose}
      closeAfterTransition
      slotProps={{ backdrop: { timeout: 300 } }}
    >
      <Fade in={!!imagePreview} timeout={300}>
        <Box
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-75 p-4"
          onClick={onClose}
        >
          <Zoom in={!!imagePreview} timeout={300}>
            <Box
              className="bg-white rounded-md w-full max-w-4xl min-h-[50vh] h-auto max-h-[70vh] flex flex-col gap-1 shadow-lg"
              onClick={(e) => e.stopPropagation()}
            >
              <Box className="flex justify-between items-center p-2 pl-3 w-full h-12">
                <Typography variant="h6">Document Preview</Typography>
                <IconButton onClick={onClose} color="error">
                  <XIcon />
                </IconButton>
              </Box>
              <Divider />
              <Box className="flex-1 overflow-hidden flex justify-center items-center p-2 w-full h-full">
                <img
                  src={imagePreview!}
                  alt="Preview"
                  className="w-auto h-auto max-w-full max-h-[60vh] object-contain rounded-lg"
                  draggable={false}
                />
              </Box>
            </Box>
          </Zoom>
        </Box>
      </Fade>
    </Modal>
  );
};

export default memo(PreviewModal);
