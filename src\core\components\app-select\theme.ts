'use client';

import { GroupBase, StylesConfig, Theme } from 'react-select';

import { FormControlProps } from '@mui/material';
import { Theme as MuiTheme } from '@mui/material/styles';

import { BaseOption } from '@/types';

const mixWithWhite = (hex: string, percentage: number): string => {
  percentage = Math.min(100, Math.max(0, percentage));

  hex = hex.replace(/^#/, '');

  if (hex.length === 3) {
    hex = hex
      .split('')
      .map((char) => char + char)
      .join('');
  }

  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);

  const mix = (channel: number) =>
    Math.round(channel + (255 - channel) * (percentage / 100));

  const toHex = (value: number) => value.toString(16).padStart(2, '0');

  const mixedR = toHex(mix(r));
  const mixedG = toHex(mix(g));
  const mixedB = toHex(mix(b));

  return `#${mixedR}${mixedG}${mixedB}`;
};

const hexToRGBA = (hex: string, alpha = 1): string => {
  const [r, g, b] = hex
    .replace(/^#/, '')
    .match(/.{2}/g)!
    .map((x) => parseInt(x, 16));
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

const getColorPaletteColor = (color: any, theme: MuiTheme) => {
  let palette = theme.palette.primary;

  if (color in theme.palette) {
    palette = (theme.palette as any)[color];
  }

  return palette;
};

type ColorOption = BaseOption & { color?: string };

export const getBaseStyles = <T>(
  theme: MuiTheme,
  error?: boolean,
  color?: FormControlProps['color']
): StylesConfig<T, boolean, GroupBase<T>> => {
  const palette = getColorPaletteColor(color, theme);

  return {
    option: (base, state) => ({
      ...base,
      fontSize: 14,
      ...(state.isSelected && {
        backgroundColor: palette.main,
        ':active': { backgroundColor: palette.light },
      }),
      ':hover': {
        backgroundColor: hexToRGBA(
          theme.palette.common.black,
          +theme.palette.action.hover
        ),
      },
    }),
    placeholder: (base) => ({ ...base, color: theme.palette.text.disabled }),
    menu: (base) => ({
      ...base,
      color: theme.palette.common.black,
      backgroundColor: theme.palette.common.white,
      zIndex: 9999,
    }),
    menuList: (base) => ({ ...base }),
    multiValue: (base, { data }) => {
      const colorOption = data as ColorOption;
      return {
        ...base,
        backgroundColor: colorOption.color || palette.main,
        borderRadius: theme.shape.borderRadius,
        color: palette.contrastText,
        paddingLeft: '4px',
      };
    },
    control: (base, state) => ({
      ...base,
      width: '100%',
      boxShadow: 'none',
      ':hover': {
        border: `1px solid ${theme.palette.common.black}`,
      },
      ':focus-within': {
        border: `2px solid ${palette.main}`,
        ':hover': {
          border: `2px solid ${palette.main}`,
        },
      },
      ...(error && {
        border: `1px solid ${theme.palette.error.main}`,
        ':focus-within': {
          border: `2px solid ${theme.palette.error.main}`,
          ':hover': {
            border: `2px solid ${theme.palette.error.main}`,
          },
        },
        ':hover': {
          border: `1px solid ${theme.palette.error.main}`,
        },
      }),
      backgroundColor: mixWithWhite(theme.palette.background.paper, 5),
      ...(state.isDisabled && {
        backgroundColor: theme.palette.grey[300],
      }),
    }),

    multiValueLabel: (base) => ({
      ...base,
      backgroundColor: palette.main,
      color: palette.contrastText,
      padding: '3px 6px',
    }),
    multiValueRemove: (base) => ({
      ...base,
      cursor: 'pointer',
      ':hover': {
        backgroundColor: theme.palette.error.main,
      },
    }),
    menuPortal: (base) => ({ ...base, zIndex: 3 }),
    valueContainer: (base, state) => ({
      ...base,
      ...(state.isMulti && {
        padding: theme.spacing(0.75, 0.25, 0.25, 0.25),
      }),
    }),
    singleValue: (base) => ({
      ...base,
      color: theme.palette.common.black,
    }),
  };
};

export function getBaseTheme(currentTheme: Theme, theme: MuiTheme): Theme {
  return {
    ...currentTheme,
    borderRadius: theme.shape.borderRadius as number,
    spacing: { ...currentTheme.spacing, controlHeight: 40 },
    colors: {
      ...currentTheme.colors,
      primary: theme.palette.primary.main,
      primary25: theme.palette.action.hover,
      danger: theme.palette.error.main,
    },
  };
}
