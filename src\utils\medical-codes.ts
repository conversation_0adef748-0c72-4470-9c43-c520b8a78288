import { ICDApiResponse, SnomedApiResponse } from '@/types/medical-codes';

export const formatSnomedCT = (data: SnomedApiResponse): string[] => {
  const options = data?.items?.map((item) => item?.term);

  return options ?? [];
};

export const formatICD = (data: ICDApiResponse): string[] => {
  const options = data?.destinationEntities?.map((entity) => {
    return `${entity?.theCode ?? ''} - ${entity?.title ?? ''}`;
  });
  return options ?? [];
};
