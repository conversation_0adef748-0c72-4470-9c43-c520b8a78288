import { useCallback, useEffect } from 'react';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

const usePermission = () => {
  const { data } = useUserStore();
  const { fetchDoctorProfileByEmail, doctorProfile } = useDoctorStore();

  const hasPermission = useCallback((_permissionKeys?: string[]) => true, []);

  const hasDepartment = useCallback(
    (department?: string[]) => {
      if (!department) return true;
      if (!doctorProfile?.general?.department) return false;

      return department.some(
        (permissionKey) => doctorProfile.general?.department === permissionKey
      );
    },
    [doctorProfile]
  );

  useEffect(() => {
    if (data?.email && !doctorProfile?.id) {
      fetchDoctorProfileByEmail(data.email);
    }
  }, [data?.email, doctorProfile?.id, fetchDoctorProfileByEmail]);

  return {
    hasPermission,
    hasDepartment,
  };
};

export default usePermission;
