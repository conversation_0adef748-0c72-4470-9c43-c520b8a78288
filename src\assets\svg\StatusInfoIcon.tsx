import React from 'react';

const StatusInfoIcon = ({ color }: { color: string }) => (
  <svg
    width="9"
    height="9"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.00354 11C8.76504 11 11.0035 8.76152 11.0035 6.00002C11.0035 3.23852 8.76504 1.00002 6.00354 1.00002C3.24204 1.00002 1.00354 3.23852 1.00354 6.00002C1.00354 8.76152 3.24204 11 6.00354 11Z"
      fill={color}
      stroke={color}
      strokeWidth="2"
      strokeLinejoin="round"
    />
    <path d="M8.25342 6.75018L6.00342 4.50018L3.75342 6.75018" fill={color} />
    <path
      d="M8.25342 6.75018L6.00342 4.50018L3.75342 6.75018"
      stroke="#FCFCFC"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default StatusInfoIcon;
