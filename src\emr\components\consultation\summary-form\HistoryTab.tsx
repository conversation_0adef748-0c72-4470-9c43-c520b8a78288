import React from 'react';

import { UseFormReturn } from 'react-hook-form';

import EditableText from '@/lib/common/editable_text';

import { SummarizeConversationRes } from '@/query/speech';

import {
  historyFields,
  lifestyleHealthHistoryFields,
  HistoryField,
} from '@/utils/constants/consultation';

type HistoryTabProps = {
  editable: boolean;
  data: any;
  form: UseFormReturn<SummarizeConversationRes['summary']>;
  handleChangeHistoryField: (field: HistoryField, newValue?: string) => void;
  handleChangeLifestyleHistoryField: (
    field: HistoryField,
    newValue?: string
  ) => void;
};

const HistoryTab: React.FC<HistoryTabProps> = ({
  editable,
  data,
  form,
  handleChangeHistoryField,
  handleChangeLifestyleHistoryField,
}) => {
  const getDefaultValue = (key: any): string => {
    const value = data?.summary?.[key as keyof typeof data.summary];
    return typeof value === 'string' ? value : '';
  };

  // Common content class for all EditableText components
  const contentClass = "min-h-[36px] py-2 max-h-[96px] overflow-y-auto border border-[#DAE1E7] bg-white rounded-md px-3 shadow-sm";
  const sectionClass = "flex flex-col gap-2";
  const fieldClass = "flex flex-col gap-1";

  return (
    <div className="flex flex-col gap-2">
      <section>
        {[historyFields[0], historyFields[1]].map((field) => (
          <EditableText
            key={field.key}
            label={field.label}
            defaultValue={getDefaultValue(field.key)}
            editable={editable}
            bg={'white'}
            emptyPlaceholder={'Nil'}
            labelClassName="font-medium text-sm text-[#001926] mb-1"
            contentClassName="min-h-[36px] py-2 max-h-[96px] overflow-y-auto border border-[#DAE1E7] bg-white rounded-md px-3 shadow-sm mb-2"
            onChange={(newValue) =>
              handleChangeHistoryField(field.key as HistoryField, newValue)
            }
          />
        ))}
        <div className="grid grid-cols-2 gap-2">
          {historyFields.slice(2).map((field) => (
            <EditableText
              key={field.key}
              label={field.label}
              defaultValue={getDefaultValue(field.key)}
              editable={editable}
              bg={'white'}
              emptyPlaceholder={'Nil'}
              className={fieldClass}
              contentClassName={contentClass}
              onChange={(newValue) =>
                handleChangeHistoryField(field.key as HistoryField, newValue)
              }
            />
          ))}
        </div>
      </section>
      <section className={sectionClass}>
        <h3 className="font-medium text-base text-[#001926]">Lifestyle Health History</h3>
        <div className="grid grid-cols-2 gap-2">
          {lifestyleHealthHistoryFields.map((field) => (
            <EditableText
              key={field.key}
              label={field.label}
              labelSize="Small"
              defaultValue={getDefaultValue(field.key)}
              editable={editable}
              bg={'white'}
              emptyPlaceholder={'Nil'}
              className={fieldClass}
              contentClassName={contentClass}
              onChange={(newValue) =>
                handleChangeLifestyleHistoryField(
                  field.key as HistoryField,
                  newValue
                )
              }
            />
          ))}
        </div>
      </section>
      <section className={sectionClass}>
        <h3 className="font-medium text-base text-[#001926]">Current Medical History</h3>
        <EditableText
          label=""
          defaultValue={data?.summary?.currentMedicationHistory}
          editable={editable}
          bg={'white'}
          emptyPlaceholder={'Nil'}
          contentClassName="min-h-[36px] py-2 max-h-[96px] overflow-y-auto border border-[#DAE1E7] bg-white rounded-md px-3 shadow-sm mb-2"
          onChange={(newValue) =>
            handleChangeHistoryField('currentMedicationHistory', newValue)
          }
        />
      </section>
    </div>
  );
};

export default HistoryTab;
