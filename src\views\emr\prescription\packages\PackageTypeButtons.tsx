import React from 'react';

import {
  PrescriptionPackageType,
  prescriptionPackageTypes,
} from '@/types/emr/prescription';

type PackageTypeButtonsProps = {
  activePackageType?: PrescriptionPackageType;
  onSelectType?: (type: PrescriptionPackageType) => void;
  disabled?: boolean;
};

const BUTTON_CLASSES = {
  active: 'bg-black text-white',
  inactive: 'bg-white text-black',
};

export const PackageTypeButtons: React.FC<PackageTypeButtonsProps> = ({
  activePackageType,
  onSelectType,
  disabled,
}) => (
  <div className="flex gap-1 w-full justify-between">
    <button
      onClick={() => onSelectType?.(prescriptionPackageTypes.DEPARTMENT)}
      disabled={disabled}
      className={`py-1 w-1/2 min-w-50 rounded-md text-sm border transition-colors duration-200 disabled:opacity-50 ${
        activePackageType === prescriptionPackageTypes.DEPARTMENT
          ? BUTTON_CLASSES.active
          : BUTTON_CLASSES.inactive
      }`}
    >
      Dept. Packages
    </button>

    <button
      onClick={() => onSelectType?.(prescriptionPackageTypes.USER)}
      disabled={disabled}
      className={`py-1 w-1/2 min-w-50 rounded-md text-sm border transition-colors duration-200 disabled:opacity-50 ${
        activePackageType === prescriptionPackageTypes.USER
          ? BUTTON_CLASSES.active
          : BUTTON_CLASSES.inactive
      }`}
    >
      User Packages
    </button>
  </div>
);
