# Core Components Documentation - ARCA EMR

## Overview

This document covers the core reusable components in the ARCA EMR system. These components form the foundation of the application's UI and provide consistent styling and behavior across all modules.

## Architecture

The core components follow a structured approach:

- **Location**: `src/core/components/`
- **Styling**: Material-UI with custom styled components
- **TypeScript**: Full type safety with proper interfaces
- **Reusability**: Designed for use across EMR and MRD modules

## Core UI Components

### AppButton (`app-button/`)

**Purpose**: Standardized button component with loading states and consistent styling.

**Key Features**:

- Loading state with spinner overlay
- Disabled state handling
- Material-UI Button wrapper
- Consistent ripple effects

**Usage**:

```tsx
import AppButton from '@core/components/app-button';

<AppButton loading={isSubmitting} onClick={handleSubmit}>
  Save Patient
</AppButton>;
```

### AppTextField (`app-text-field/`)

**Purpose**: Enhanced text input with validation, formatting, and edit modes.

**Key Features**:

- Read-only mode with edit toggle
- Value formatting support
- Input validation
- Consistent label styling
- Multiline support

**Usage**:

```tsx
import AppTextField from '@core/components/app-text-field';

<AppTextField
  label="Patient Name"
  value={patientName}
  onChange={setPatientName}
  required
  initiallyReadonly={!isEditing}
/>;
```

### AppSelect (`app-select/`)

**Purpose**: Dropdown selection component with search and custom styling.

**Key Features**:

- Searchable options
- Custom option rendering
- Multi-select support
- Async data loading

### AppDatePicker (`app-date-picker/`)

**Purpose**: Date selection with medical-specific formatting and validation.

**Key Features**:

- Medical date formats
- Age calculation integration
- Range validation
- Accessibility compliance

### Table (`table/` and `table-v2/`)

**Purpose**: Data table component for patient records and medical data.

**Key Features**:

- Sortable columns
- Loading states
- Sticky headers
- Custom cell rendering
- Responsive design

**Usage**:

```tsx
import Table from '@core/components/table';

<Table
  headers={patientHeaders}
  rows={patientData}
  loading={isLoading}
  sortOptions={sortConfig}
/>;
```

## Form Components

### Controlled Inputs (`controlled-inputs/`)

**Purpose**: Form components integrated with react-hook-form for medical data entry.

**Key Features**:

- Automatic validation
- Error state handling
- Medical data formatting
- Accessibility support

### File Input (`file-input/`)

**Purpose**: File upload component for medical documents and images.

**Key Features**:

- Multiple file types support
- Preview functionality
- Progress indication
- Validation (file size, type)

## Modal Components

### AppModal (`app-modal/`)

**Purpose**: Standardized modal wrapper for dialogs and forms.

**Key Features**:

- Consistent styling
- Backdrop handling
- Keyboard navigation
- Focus management

### DeleteModal (`delete-modal/`)

**Purpose**: Confirmation dialog for destructive actions.

**Key Features**:

- Warning styling
- Action confirmation
- Keyboard shortcuts
- Accessibility compliance

### StatusModal (`status-modal/`)

**Purpose**: System-wide status notifications and alerts.

**Key Features**:

- Success/error/warning states
- Auto-dismiss functionality
- Icon integration
- Toast-like behavior

## Specialized Components

### AppAsyncSearch (`app-async-search/`)

**Purpose**: Search component with API integration for patient/medical code lookup.

**Key Features**:

- Debounced search
- Loading states
- Result highlighting
- Keyboard navigation

### MultiLevelDropdown (`multi-level-dropdown/`)

**Purpose**: Hierarchical selection for medical categories and departments.

**Key Features**:

- Nested option support
- Mobile-responsive
- Search within levels
- Breadcrumb navigation

### StageWise (`stage-wise/`)

**Purpose**: Workflow progress indicator for medical processes.

**Key Features**:

- Step visualization
- Progress tracking
- Conditional navigation
- Status indicators

## Component Guidelines

### Usage Patterns

1. **Import from core**: Always import from `@core/components/`
2. **Type safety**: Use provided TypeScript interfaces
3. **Consistent props**: Follow established prop naming conventions
4. **Accessibility**: All components include ARIA attributes

### Styling Approach

- **Material-UI base**: Built on MUI components
- **Custom styling**: Using styled-components for customization
- **Theme integration**: Respects global theme settings
- **Responsive design**: Mobile-first approach

### Best Practices

1. **Prop validation**: Use TypeScript interfaces for all props
2. **Default values**: Provide sensible defaults for optional props
3. **Error handling**: Include proper error states and messages
4. **Performance**: Use React.memo for expensive components
5. **Testing**: Include unit tests for complex logic

## Integration with EMR Modules

### Patient Management

- PatientCard component for patient display
- Form components for patient data entry
- Table components for patient lists

### Medical Records

- File upload for medical documents
- Date pickers for medical history
- Search components for medical codes

### Consultation

- Modal components for consultation notes
- Text fields for medical observations
- Status indicators for consultation progress

## Migration Notes

**Legacy Components**: The `emr/components/` directory contains legacy components. New development should use `core/components/` or `views/` as specified in the folder structure guide.

**Deprecation Path**: Legacy components will be gradually migrated to the core structure. Avoid using deprecated components in new features.

## Related Documentation

- [Folder Structure Guide](./FOLDER_STRUCTURE_GUIDE.md) - Component organization
- [MUI Theme Setup](./MUI_THEME_SETUP.md) - Styling configuration
- [Code Format Rules](./CODE_FORMAT_RULES.md) - Component coding standards
