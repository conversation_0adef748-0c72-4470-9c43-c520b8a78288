'use client';

import React, { FC, memo } from 'react';

import useIsMobile from '@/hooks/use-mobile-layout';

import DesktopLayout from './desktop';
import MobileLayout from './mobile';

type Props = {
  children: React.ReactNode;
};

const EmrLayout: FC<Props> = ({ children }) => {
  const isMobile = useIsMobile();

  if (isMobile) {
    return <MobileLayout>{children}</MobileLayout>;
  }

  return <DesktopLayout>{children}</DesktopLayout>;
};

export default memo(EmrLayout);
