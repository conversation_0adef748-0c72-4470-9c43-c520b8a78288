import { toast } from 'sonner';
import { create } from 'zustand';

import { GetPatientHistoryRes, updatePatientHistory } from '@/query/patient';

import { FINALIZED } from '@/utils/constants/consultation';
import { getErrorMessage } from '@/utils/error-message';

type ConsultationStore = {
  updatePatientHistory: (
    _id: string,
    _patientId: string,
    _history: GetPatientHistoryRes[0],
    _callback?: () => Promise<void>
  ) => Promise<GetPatientHistoryRes[0] | null>;
  finaliseRecord: (_history: GetPatientHistoryRes[0]) => Promise<void>;
  updating: boolean;
  history: GetPatientHistoryRes[0] | null;
};

export const useConsultationStore = create<ConsultationStore>()((set) => ({
  history: null,
  updating: false,

  updatePatientHistory: async (id, patientId, history, callback) => {
    try {
      set({ updating: true });
      const { data } = await updatePatientHistory(id, patientId, history);

      if (callback) await callback();

      set({ history: data, updating: false });
      toast.success('Consultation updated successfully');
      return data;
    } catch (err) {
      console.error('Error updating patient history:', err);
      set({ updating: false });
      toast.error(getErrorMessage(err, 'Error updating Consultation'));
      return null;
    }
  },

  finaliseRecord: async (history) => {
    try {
      set({ updating: true });

      if (!history?.id || !history?.patientId) {
        throw new Error('Missing id or patientId');
      }

      const updatedHistory = {
        ...history,
        status: FINALIZED,
      };

      const { data } = await updatePatientHistory(
        history.id,
        history.patientId,
        updatedHistory
      );

      set({ history: data, updating: false });
      toast.success('Consultation finalized successfully');
    } catch (err) {
      console.error('Finalization error:', err);
      set({ updating: false });
      toast.error('Failed to finalize consultation');
    }
  },
}));
