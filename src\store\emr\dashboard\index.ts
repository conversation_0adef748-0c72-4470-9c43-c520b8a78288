import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import {
  getEmrUpcomingAppointments,
  getEmrDashboardWidgetData,
  EmrUpcomingAppointment,
  EmrDashboardWidgetData,
} from '@/query/emr/dashboard';

export interface EmrDashboardFilters {
  searchQuery: string;
  page: number;
  pageSize: number;
}

export interface EmrDashboardState {
  // Data
  upcomingAppointments: EmrUpcomingAppointment[];
  widgetData: EmrDashboardWidgetData | null;

  // Loading states
  isLoadingAppointments: boolean;
  isLoadingWidgetData: boolean;

  // Filters
  filters: EmrDashboardFilters;

  // Pagination
  totalItems: number;
  totalPages: number;

  // Actions
  setFilters: (filters: Partial<EmrDashboardFilters>) => void;
  setSearchQuery: (query: string) => void;
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;

  // API Actions
  fetchUpcomingAppointments: () => Promise<void>;
  fetchWidgetData: () => Promise<void>;

  // Reset
  resetFilters: () => void;
}

const initialFilters: EmrDashboardFilters = {
  searchQuery: '',
  page: 1,
  pageSize: 10,
};

export const useEmrDashboardStore = create<EmrDashboardState>()(
  persist(
    (set, get) => ({
      // Initial state
      upcomingAppointments: [],
      widgetData: null,
      isLoadingAppointments: false,
      isLoadingWidgetData: false,
      filters: initialFilters,
      totalItems: 0,
      totalPages: 0,

      // Filter actions
      setFilters: (newFilters) => {
        set((state) => ({
          filters: { ...state.filters, ...newFilters },
        }));
        // Fetch appointments when filters change
        get().fetchUpcomingAppointments();
      },

      setSearchQuery: (query) => {
        set((state) => ({
          filters: { ...state.filters, searchQuery: query, page: 1 },
        }));
        // Debounce search - you might want to implement debouncing
        get().fetchUpcomingAppointments();
      },

      setPage: (page) => {
        set((state) => ({
          filters: { ...state.filters, page },
        }));
        get().fetchUpcomingAppointments();
      },

      setPageSize: (pageSize) => {
        set((state) => ({
          filters: { ...state.filters, pageSize, page: 1 },
        }));
        get().fetchUpcomingAppointments();
      },

      // API actions
      fetchUpcomingAppointments: async () => {
        const { filters } = get();
        set({ isLoadingAppointments: true });

        try {
          const response = await getEmrUpcomingAppointments({
            searchQuery: filters.searchQuery,
            page: filters.page,
            pageSize: filters.pageSize,
          });

          set({
            upcomingAppointments: response.data,
            totalItems: response.totalItems,
            totalPages: response.totalPages,
            isLoadingAppointments: false,
          });
        } catch (error) {
          console.error('Error fetching EMR upcoming appointments:', error);
          set({
            upcomingAppointments: [],
            totalItems: 0,
            totalPages: 0,
            isLoadingAppointments: false,
          });
        }
      },

      fetchWidgetData: async () => {
        set({ isLoadingWidgetData: true });

        try {
          const widgetData = await getEmrDashboardWidgetData();
          set({
            widgetData,
            isLoadingWidgetData: false,
          });
        } catch (error) {
          console.error('Error fetching EMR widget data:', error);
          set({
            widgetData: null,
            isLoadingWidgetData: false,
          });
        }
      },

      // Reset
      resetFilters: () => {
        set({ filters: initialFilters });
        get().fetchUpcomingAppointments();
      },
    }),
    {
      name: 'emr-dashboard-store',
      partialize: (state) => ({
        filters: state.filters,
      }),
    }
  )
);
