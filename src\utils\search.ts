import { debounce } from 'lodash';

type SearchFunction<T> = (
  searchTerm: string,
  signal?: AbortSignal
) => Promise<T[]>;

export function createDebouncedSearch<T>(
  searchFn: SearchFunction<T>,
  wait = 500,
  options?: {
    minLength?: number;
    onStart?: () => void;
    onComplete?: () => void;
    onError?: (error: unknown) => void;
    maxWait?: number;
  }
) {
  const {
    minLength = 1,
    onStart,
    onComplete,
    onError,
    maxWait = 1000,
  } = options || {};

  const debouncedSearch = debounce(
    async (
      searchTerm: string,
      callback: (results: T[]) => void,
      signal?: AbortSignal
    ) => {
      const trimmedSearch = searchTerm.trim();

      // Skip empty or too short searches
      if (trimmedSearch.length < minLength) {
        callback([]);
        return;
      }

      // Check if already aborted
      if (signal?.aborted) {
        return;
      }

      try {
        onStart?.();

        // Check again before making the API call
        if (signal?.aborted) {
          return;
        }

        const results = await searchFn(trimmedSearch, signal);

        // Final check before calling callback
        if (!signal?.aborted) {
          callback(results);
        }
      } catch (error) {
        // Only handle non-abort errors
        if (
          signal?.aborted ||
          (error instanceof Error &&
            (error.name === 'AbortError' ||
              error.message === 'Request aborted'))
        ) {
          return; // Silently ignore aborted requests
        }

        onError?.(error);
        console.error('Search error:', error);

        // Still call callback with empty results on error if not aborted
        if (!signal?.aborted) {
          callback([]);
        }
      } finally {
        if (!signal?.aborted) {
          onComplete?.();
        }
      }
    },
    wait,
    { maxWait, leading: false, trailing: true }
  );

  return {
    search: async (
      searchTerm: string,
      callback: (results: T[]) => void,
      signal?: AbortSignal
    ) => {
      // Cancel any pending searches before starting a new one
      debouncedSearch.cancel();

      // Return a promise that resolves when the debounced function completes
      return new Promise<void>((resolve) => {
        const wrappedCallback = (results: T[]) => {
          callback(results);
          resolve();
        };

        debouncedSearch(searchTerm, wrappedCallback, signal);
      });
    },
    cancel: () => {
      debouncedSearch.cancel();
    },
    flush: () => {
      return new Promise<void>((resolve) => {
        // Flush will execute any pending debounced function immediately
        const result = debouncedSearch.flush();
        if (result instanceof Promise) {
          result.finally(() => resolve());
        } else {
          resolve();
        }
      });
    },
  };
}
