'use client';

import React, { memo, useState, useEffect } from 'react';

// Store
import { useMrdDashboardStore } from '@/store/mrd/dashboard';
import { useDoctorStore } from '@/store/mrd/queue/doctor';

// Components
import colors from '@/utils/colors';
import {
  getTimeBasedGreeting,
  getLastUpdatedText,
} from '@/utils/mrd/dashboard/greeting';

import AvgWaitTimeIcon from '@/assets/svg/dashboard/AvgWaitTimeIcon';
import PatientQueueIcon from '@/assets/svg/dashboard/PatientQueueIcon';
import TodaysAppointmentsIcon from '@/assets/svg/dashboard/TodaysAppointmentsIcon';
import TotalPatientsIcon from '@/assets/svg/dashboard/TotalPatientsIcon';

import { AverageCardSkeleton } from '@/views/emr/lifestyle/shared/dashboard-components/DashboardSkeleton';
import ChartPlaceholder from '@/views/shared/dashboard/ChartPlaceholder';
import CurrentAppointments from '@/views/shared/dashboard/CurrentAppointments';
import DoctorSelect from '@/views/shared/dashboard/CurrentAppointments/DoctorSelect';
import DashboardCalendar from '@/views/shared/dashboard/DashboardCalendar';
import DashboardHeader from '@/views/shared/dashboard/DashboardHeader';
import DashboardWidgetCard from '@/views/shared/dashboard/DashboardWidgetCard';

// Reusable skeleton from EMR lifestyle

const Dashboard = () => {
  const {
    widgetData,
    isLoadingWidgetData,
    fetchWidgetData,
    upcomingAppointments,
    isLoadingAppointments,
    totalItems,
    totalPages,
    filters,
    setSearchQuery,
    setPage,
    setPageSize,
    fetchUpcomingAppointments,
  } = useMrdDashboardStore();
  console.log('widgetData', upcomingAppointments);
  const { date, setDate, selectedDoctor } = useDoctorStore();
  const [greeting, setGreeting] = useState<string>('');
  const [lastUpdated, setLastUpdated] = useState<string>('');
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);

  // Refetch appointments when doctor or date changes
  useEffect(() => {
    fetchUpcomingAppointments();
  }, [selectedDoctor, date, fetchUpcomingAppointments]);

  // Update greeting and last updated text every minute
  useEffect(() => {
    const updateGreeting = () => {
      setGreeting(getTimeBasedGreeting());
      setLastUpdated(getLastUpdatedText(lastRefreshTime || undefined));
    };

    updateGreeting();
    const interval = setInterval(updateGreeting, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [lastRefreshTime]);

  // Load widget data and appointments on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.all([fetchWidgetData(), fetchUpcomingAppointments()]);
      const now = new Date();
      setLastRefreshTime(now);
      setLastUpdated(getLastUpdatedText(now));
    };

    loadInitialData();
  }, [fetchWidgetData, fetchUpcomingAppointments]);

  const handleRefresh = async () => {
    await Promise.all([fetchWidgetData(), fetchUpcomingAppointments()]);
    const now = new Date();
    setLastRefreshTime(now);
    setLastUpdated(getLastUpdatedText(now));
  };

  return (
    <div className="h-full w-full flex flex-col p-6 bg-gray-50 overflow-y-auto">
      <DashboardHeader
        greeting={greeting}
        lastUpdated={lastUpdated}
        onRefresh={handleRefresh}
        isLoading={isLoadingWidgetData}
        lastUpdatedMargin="mb-6"
      />

      {/* Widget Cards */}
      <div className="grid grid-cols-4 gap-4 mb-4" style={{ gap: '16px' }}>
        {isLoadingWidgetData ? (
          <>
            <AverageCardSkeleton />
            <AverageCardSkeleton />
            <AverageCardSkeleton />
            <AverageCardSkeleton />
          </>
        ) : (
          <>
            <DashboardWidgetCard
              title="Total Patients"
              count={widgetData?.totalPatients ?? null}
              status="All time registered"
              icon={<TotalPatientsIcon />}
              bgColor={colors.dashboard.totalPatients.bgColor}
              borderColor={colors.dashboard.totalPatients.borderColor}
            />
            <DashboardWidgetCard
              title="Today's Appointments"
              count={widgetData?.todaysAppointments ?? null}
              status="---"
              icon={<TodaysAppointmentsIcon />}
              bgColor={colors.dashboard.appointments.bgColor}
              borderColor={colors.dashboard.appointments.borderColor}
            />
            <DashboardWidgetCard
              title="Patient Queue"
              count={widgetData?.patientQueue ?? null}
              status="Currently waiting"
              icon={<PatientQueueIcon />}
              bgColor={colors.dashboard.patientQueue.bgColor}
              borderColor={colors.dashboard.patientQueue.borderColor}
            />
            <DashboardWidgetCard
              title="Avg. Wait Time"
              count={
                widgetData?.averageWaitingTime
                  ? `${widgetData.averageWaitingTime} Mins`
                  : null
              }
              status="Today's average"
              icon={<AvgWaitTimeIcon />}
              bgColor={colors.dashboard.avgWaitTime.bgColor}
              borderColor={colors.dashboard.avgWaitTime.borderColor}
            />
          </>
        )}
      </div>

      {/* Charts and Calendar Row */}
      <div
        className="grid grid-cols-4 gap-4 mb-4 min-h-87"
        style={{ gap: '16px' }}
      >
        <ChartPlaceholder title="Peak Hours" />
        <ChartPlaceholder
          title="Avg. Consultation time for each Dept."
          colSpan="col-span-2"
        />
        <DashboardCalendar />
      </div>

      {/* Bottom Row - Additional Charts */}
      <div className="grid grid-cols-4 gap-4 min-h-87" style={{ gap: '16px' }}>
        <ChartPlaceholder title="Appointment No-show Rate" minHeight="250px" />
        {/* Upcoming Appointments Table - 2 columns width */}
        <div className="col-span-2">
          <CurrentAppointments
            title="Upcoming Appointments"
            appointments={upcomingAppointments}
            isLoading={isLoadingAppointments}
            totalItems={totalItems}
            totalPages={totalPages}
            currentPage={filters.page}
            searchQuery={filters.searchQuery}
            onSearchChange={setSearchQuery}
            onPageChange={setPage}
            onPageSizeChange={setPageSize}
            showDateColumn={false} // MRD shows date & time
            showDoctorSelect={true}
            showDatePicker={true}
            doctorSelectComponent={<DoctorSelect />}
            selectedDate={date}
            onDateChange={(newDate) => setDate(newDate || undefined)}
          />
        </div>

        {/* Empty space - 1 column width */}
        <div></div>
      </div>
    </div>
  );
};

export default memo(Dashboard);
