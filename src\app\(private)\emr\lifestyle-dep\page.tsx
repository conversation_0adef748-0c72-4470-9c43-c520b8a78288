'use client';

import React, { ReactNode, useCallback, useEffect, useMemo } from 'react';

import { BiSad } from 'react-icons/bi';

import { useRouter } from 'next/navigation';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useLifestyleStore } from '@/store/lifestyle';
import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import colors from '@/utils/colors';
import { pageIds } from '@/utils/constants/docAssist';

import {
  LifestyleFormsType,
  lifestyleModes,
  mobileViews,
  NutritionAndDietForm,
  MobileViewType,
} from '@/constants/lifestyle';

import Chat from '@/emr/components/chat';
import DocAssist from '@/emr/components/doc_assist';
import AwarenessAdaptationTimeline from '@/emr/components/lifestyle/lifestyle-forms/awareness-adaptation';
import AwarenessAdaptationModal from '@/emr/components/lifestyle/lifestyle-forms/awareness-adaptation/AwarenessAdaptationModal';
import DietaryAssessmentTimeline from '@/emr/components/lifestyle/lifestyle-forms/dietary-assessment';
import DietaryAssessmentModal from '@/emr/components/lifestyle/lifestyle-forms/dietary-assessment/DietaryAssessmentModal';
import NutritionMonitoringTimeline from '@/emr/components/lifestyle/lifestyle-forms/nutrition-monitoring';
import NutritionMonitoringModal from '@/emr/components/lifestyle/lifestyle-forms/nutrition-monitoring/NutritionMonitoringModal';
import FormModal from '@/emr/components/lifestyle/lifestyle-forms/shared/FormModal';
import AddRecordManuallyMob from '@/emr/components/lifestyle/mobile/AddRecordManuallyMob';
import AmbientListeningMob from '@/emr/components/lifestyle/mobile/AmbientListeningMob';
import NowConsultingMob from '@/emr/components/lifestyle/mobile/NowConsultingMob';
import TimelineMob from '@/emr/components/lifestyle/mobile/TimelineMob';
import NowConsulting from '@/emr/components/lifestyle/NowConsulting';
import NoPatientView from '@/emr/components/shared/NoPatientView';
import { lifestyleSource } from '@/emr/types/lifestyle';

import TopNavigation from '@/core/layout/emr/mobile/TopNavigation';

import './styles.scss';

const {
  DIETARY_ASSESSMENT,
  LIFESTYLE_AWARENESS_ADAPTATION,
  NUTRITION_MONITORING_SHEET,
} = NutritionAndDietForm;
const {
  NOW_CONSULTING,
  ADD_RECORD_MANUALLY,
  AMBIENT_LISTENING,
  RECORDING_CONSULTATION,
  TIMELINE,
} = mobileViews;

const { CREATE } = lifestyleModes;
const {
  DIETARY_ASSESSMENT_SOURCE,
  AWARENESS_ADAPTATION_SOURCE,
  NUTRITION_MONITORING_SHEET_SOURCE,
} = lifestyleSource;

const Lifestyle = () => {
  const { patient, isActivePatient } = useCurrentPatientStore();
  const isMobile = useIsMobile();
  const { back } = useRouter();
  const {
    mobilePage,
    currentTab,
    isModalOpen,
    setMobilePage,
    closeModal,
    openModal,
  } = useLifestyleUtilStore();
  const { getLifestyleQuestions, getPatientLifestyle } = useLifestyleStore();

  const fetchQuestionBySource = useCallback(async () => {
    switch (currentTab) {
      case DIETARY_ASSESSMENT:
        {
          getLifestyleQuestions(DIETARY_ASSESSMENT_SOURCE);
          if (patient?.id) {
            getPatientLifestyle(patient.id, DIETARY_ASSESSMENT_SOURCE);
          }
        }
        break;
      case LIFESTYLE_AWARENESS_ADAPTATION:
        {
          getLifestyleQuestions(AWARENESS_ADAPTATION_SOURCE);
          if (patient?.id) {
            getPatientLifestyle(patient.id, AWARENESS_ADAPTATION_SOURCE);
          }
        }
        break;
      case NUTRITION_MONITORING_SHEET:
        {
          getLifestyleQuestions(NUTRITION_MONITORING_SHEET_SOURCE);
          if (patient?.id) {
            getPatientLifestyle(patient.id, NUTRITION_MONITORING_SHEET_SOURCE);
          }
        }
        break;
      default:
        break;
    }
  }, [currentTab, getLifestyleQuestions, patient?.id, getPatientLifestyle]);

  const toggleMobilePage = useCallback(() => {
    if (mobilePage === NOW_CONSULTING) {
      back();
    } else {
      setMobilePage(NOW_CONSULTING);
    }
  }, [back, mobilePage, setMobilePage]);

  useEffect(() => {
    fetchQuestionBySource();
  }, [fetchQuestionBySource]);

  const lifestyleForms = useMemo<Record<LifestyleFormsType, ReactNode>>(
    () => ({
      [DIETARY_ASSESSMENT]: <DietaryAssessmentTimeline />,
      [LIFESTYLE_AWARENESS_ADAPTATION]: <AwarenessAdaptationTimeline />,
      [NUTRITION_MONITORING_SHEET]: <NutritionMonitoringTimeline />,
    }),
    []
  );

  const formModals = useMemo<Record<LifestyleFormsType, ReactNode>>(
    () => ({
      [DIETARY_ASSESSMENT]: <DietaryAssessmentModal />,
      [LIFESTYLE_AWARENESS_ADAPTATION]: <AwarenessAdaptationModal />,
      [NUTRITION_MONITORING_SHEET]: <NutritionMonitoringModal />,
    }),
    []
  );

  const mobileViewPages = useMemo<Record<MobileViewType, ReactNode>>(
    () => ({
      [NOW_CONSULTING]: <NowConsultingMob />,
      [ADD_RECORD_MANUALLY]: <AddRecordManuallyMob />,
      [AMBIENT_LISTENING]: <AmbientListeningMob />,
      [RECORDING_CONSULTATION]: <>Recording Consultation</>,
      [TIMELINE]: <TimelineMob />,
    }),
    []
  );

  const renderLifestyle = useCallback(() => {
    if (!patient || !isActivePatient) {
      return <NoPatientView />;
    } else {
      return (
        <>
          <div className="w-[28%] h-full max-h-full flex flex-col gap-1">
            <NowConsulting onAddRecordManually={() => openModal(CREATE)} />
          </div>
          <div
            className={`h-full max-h-full flex-grow-0 flex-shrink bg-white rounded-base shadow-base border overflow-hidden w-[60%] max-w-[60%] border-[${colors.common.ashGray}]`}
          >
            {lifestyleForms[currentTab]}
          </div>
          <div
            className={`h-full bg-white rounded-base shadow-base border overflow-x-hidden flex-grow flex-shrink-0 overflow-y-auto w-[22%] border-[${colors.common.ashGray}]`}
          >
            <DocAssist pageId={pageIds.LIFESTYLE} />
          </div>
        </>
      );
    }
  }, [currentTab, lifestyleForms, openModal, patient, isActivePatient]);

  const renderMobileView = useCallback(() => {
    if (!patient) {
      return (
        <>
          <TopNavigation onClickBack={toggleMobilePage} />
          <div className="bg-white flex-1 min-h-[calc(100vh-13rem)] flex items-center justify-center rounded-lg font-medium gap-2 text-black/40">
            <BiSad />
            No active patient present
          </div>
        </>
      );
    } else {
      return (
        <>
          <TopNavigation onClickBack={toggleMobilePage} />
          <div className="h-full w-full">{mobileViewPages[mobilePage]}</div>
        </>
      );
    }
  }, [mobilePage, patient, toggleMobilePage, mobileViewPages]);

  if (isMobile) {
    return (
      <div className="flex flex-col h-full w-full">{renderMobileView()}</div>
    );
  }

  return (
    <>
      <div className="flex-1 flex gap-base w-full h-full max-h-full">
        {renderLifestyle()}
      </div>
      {patient && <Chat />}
      <FormModal open={isModalOpen} onClose={closeModal}>
        {formModals[currentTab]}
      </FormModal>
    </>
  );
};

export default Lifestyle;
