import React, { useState, useCallback, memo } from 'react';

import { Control, Controller } from 'react-hook-form';

import { List, ListItem } from '@mui/material';
import { MdArrowDropDown } from 'react-icons/md';

import colors from '@/utils/colors';

import CustomModal from '@/core/components/modal';

import { Prescription } from '../NewPrescription';

import CustomTextField from './TableCustomTextField';

type OptionType = {
  code: string;
  label: string;
};

interface OptionsSelectorProps {
  name: `prescription.${number}.${string}`;
  control: Control<Prescription>;
  options: OptionType[];
  title: string;
  rules?: { [key: string]: any };
  isNotValid?: boolean;
}

const OptionItem = memo(
  ({
    option,
    isSelected,
    onClick,
    isLast,
  }: {
    option: OptionType;
    isSelected: boolean;
    onClick: (code: string) => void;
    isLast: boolean;
  }) => {
    const handleClick = useCallback(() => {
      onClick(option.code);
    }, [onClick, option.code]);

    return (
      <ListItem
        disableGutters
        className={`hover:bg-gray-100 cursor-pointer ${isSelected ? 'text-[1AA6F1]' : ''} ${
          !isLast ? 'border-b border-gray-200' : ''
        }`}
        onClick={handleClick}
      >
        <div
          className={`flex w-full text-sm ${isSelected ? 'text-[#1AA6F1] font-medium' : ''}`}
        >
          <div className="w-16">{option.code}:</div>
          <div>{option.label}</div>
        </div>
      </ListItem>
    );
  }
);

OptionItem.displayName = 'OptionItem';

const OptionsSelector = ({
  name,
  control,
  options,
  title,
  rules,
  isNotValid,
}: OptionsSelectorProps) => {
  const [open, setOpen] = useState(false);

  const handleOpen = useCallback(() => {
    setOpen(true);
  }, []);

  const handleClose = useCallback(() => {
    setOpen(false);
  }, []);

  const handleOptionSelect = useCallback(
    (onChange: (value: string) => void, code: string) => {
      onChange(code);
      setOpen(false);
    },
    []
  );

  return (
    <div>
      <Controller
        rules={rules}
        name={name}
        control={control}
        render={({ field }) => {
          const selectedOption = options.find(
            (opt) => opt.code === field.value
          );

          return (
            <>
              <CustomTextField
                value={selectedOption?.label || ''}
                onClick={handleOpen}
                hideEndAdornmentIcon={Boolean(selectedOption)}
                endAdornmentIcon={
                  <MdArrowDropDown
                    size={18}
                    color={isNotValid ? `${colors.common.redRose}` : 'black'}
                  />
                }
                placeholder="Select"
                color={
                  field.value
                    ? 'inherit'
                    : isNotValid
                      ? `${colors.common.redRose}`
                      : 'gray'
                }
                readOnly
              />
              <CustomModal
                open={open}
                onClose={handleClose}
                title={title}
                width={'300px'}
                minHeight="200px"
                maxHeight="72vh"
                titleBoxSx={{ mb: 0 }}
                titleTypographySx={{ fontSize: '18px', fontWeight: 600 }}
                showDivider={false}
                contentSx={{ mb: 0 }}
                content={
                  <List className="p-0">
                    {options.map((option, index) => (
                      <OptionItem
                        key={option.code}
                        option={option}
                        isSelected={field.value === option.code}
                        onClick={() =>
                          handleOptionSelect(field.onChange, option.code)
                        }
                        isLast={index === options.length - 1}
                      />
                    ))}
                  </List>
                }
              />
            </>
          );
        }}
      />
    </div>
  );
};

export default memo(OptionsSelector);

OptionsSelector.displayName = 'OptionsSelector';
