import Link from 'next/link';

import { AUTH_IMAGE } from '@/constants/image-url';

import AppTextField from '@/core/components/app-text-field';

const SignInPage = () => {
  return (
    <div className="h-screen overflow-hidden flex gap-10 px-5">
      <div className="flex-1 flex flex-col justify-center py-5">
        <div className="p-8">
          <svg
            width="109"
            height="32"
            viewBox="0 0 109 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20.1424 0.843087L16.9853 0L14.3248 9.89565L11.9228 0.961791L8.76555 1.80488L11.3608 11.4573L4.8967 5.01518L2.58549 7.31854L9.67576 14.3848L0.845959 12.0269L0 15.1733L9.64767 17.7496C9.53721 17.2748 9.47877 16.7801 9.47877 16.2717C9.47877 12.6737 12.4055 9.75685 16.0159 9.75685C19.6262 9.75685 22.5529 12.6737 22.5529 16.2717C22.5529 16.7768 22.4952 17.2685 22.3861 17.7405L31.1541 20.0818L32 16.9354L22.314 14.3489L31.1444 11.9908L30.2984 8.84437L20.6128 11.4308L27.0768 4.98873L24.7656 2.68538L17.7737 9.65357L20.1424 0.843087Z"
              fill="#E4626F"
            />
            <path
              d="M22.3777 17.7771C22.107 18.9176 21.5356 19.9421 20.7515 20.763L27.1034 27.0935L29.4147 24.7901L22.3777 17.7771Z"
              fill="#E4626F"
            />
            <path
              d="M20.6872 20.8293C19.8936 21.6371 18.8907 22.2399 17.7661 22.5505L20.0775 31.1473L23.2346 30.3042L20.6872 20.8293Z"
              fill="#E4626F"
            />
            <path
              d="M17.6483 22.5818C17.1265 22.7155 16.5796 22.7866 16.016 22.7866C15.4122 22.7866 14.8275 22.705 14.2724 22.5522L11.959 31.1569L15.1161 31.9999L17.6483 22.5818Z"
              fill="#E4626F"
            />
            <path
              d="M14.1608 22.5206C13.0533 22.1945 12.0683 21.584 11.291 20.7739L4.92334 27.1199L7.23454 29.4233L14.1608 22.5206Z"
              fill="#E4626F"
            />
            <path
              d="M11.238 20.7178C10.474 19.9026 9.91743 18.8917 9.65253 17.7688L0.855957 20.1178L1.70191 23.2642L11.238 20.7178Z"
              fill="#E4626F"
            />
            <path
              d="M48.3987 25H45.0237L51.1686 7.54544H55.072L61.2254 25H57.8504L53.1885 11.125H53.0521L48.3987 25ZM48.5095 18.1562H57.714V20.696H48.5095V18.1562ZM62.5644 25V7.54544H69.1099C70.4508 7.54544 71.5758 7.77839 72.4849 8.2443C73.3996 8.71021 74.09 9.36362 74.5559 10.2045C75.0275 11.0398 75.2633 12.0142 75.2633 13.1278C75.2633 14.2471 75.0246 15.2187 74.5474 16.0426C74.0758 16.8608 73.3797 17.4943 72.4593 17.9432C71.5388 18.3863 70.4082 18.6079 69.0672 18.6079H64.4053V15.9829H68.6411C69.4252 15.9829 70.0672 15.875 70.5672 15.6591C71.0672 15.4375 71.4366 15.1165 71.6752 14.696C71.9195 14.2699 72.0417 13.7471 72.0417 13.1278C72.0417 12.5085 71.9195 11.9801 71.6752 11.5426C71.4309 11.0994 71.0587 10.7642 70.5587 10.5369C70.0587 10.304 69.4138 10.1875 68.6241 10.1875H65.7263V25H62.5644ZM71.5814 17.0909L75.9025 25H72.3741L68.1297 17.0909H71.5814ZM92.1541 13.4346H88.9666C88.8757 12.9119 88.7081 12.4488 88.4638 12.0454C88.2195 11.6363 87.9155 11.2898 87.5518 11.0057C87.1882 10.7216 86.7734 10.5085 86.3075 10.3665C85.8473 10.2187 85.3501 10.1449 84.816 10.1449C83.8672 10.1449 83.0263 10.3835 82.2933 10.8608C81.5604 11.3324 80.9865 12.0256 80.5717 12.9403C80.157 13.8494 79.9496 14.9602 79.9496 16.2727C79.9496 17.6079 80.157 18.7329 80.5717 19.6477C80.9922 20.5568 81.566 21.2443 82.2933 21.7102C83.0263 22.1704 83.8643 22.4006 84.8075 22.4006C85.3303 22.4006 85.8189 22.3324 86.2734 22.196C86.7337 22.054 87.1456 21.8466 87.5092 21.5738C87.8785 21.3011 88.1882 20.9659 88.4382 20.5682C88.6939 20.1704 88.87 19.7159 88.9666 19.2045L92.1541 19.2216C92.0348 20.0511 91.7763 20.8295 91.3785 21.5568C90.9865 22.2841 90.4723 22.9261 89.8359 23.4829C89.1996 24.0341 88.4553 24.4659 87.603 24.7784C86.7507 25.0852 85.8047 25.2386 84.7649 25.2386C83.2308 25.2386 81.8615 24.8835 80.657 24.1733C79.4524 23.4631 78.5035 22.4375 77.8104 21.0966C77.1172 19.7557 76.7706 18.1477 76.7706 16.2727C76.7706 14.392 77.12 12.7841 77.8189 11.4488C78.5178 10.1079 79.4695 9.08237 80.674 8.37214C81.8785 7.66192 83.2422 7.3068 84.7649 7.3068C85.7365 7.3068 86.6399 7.44317 87.4751 7.71589C88.3104 7.98862 89.0547 8.38919 89.7081 8.9176C90.3615 9.44033 90.8984 10.0824 91.3189 10.8437C91.745 11.5994 92.0234 12.4631 92.1541 13.4346ZM96.1203 25H92.7453L98.8901 7.54544H102.794L108.947 25H105.572L100.91 11.125H100.774L96.1203 25ZM96.231 18.1562H105.436V20.696H96.231V18.1562Z"
              fill="#232323"
            />
          </svg>
        </div>
        <div className="px-24 flex flex-col gap-8 flex-1 justify-center max-w-[640px]">
          <div className="flex flex-col gap-3">
            <span className="text-[40px] font-bold leading-[110%] -tracking-[1.6px] text-[#232323]">
              Sign in
            </span>
            <p className="text-[#969696] leading-[150%]">
              Please login to continue to your account.
            </p>
          </div>

          <div className="flex flex-col gap-5">
            <AppTextField label="Username" />
            <AppTextField label="Password" />
            <label className="flex items-center gap-2.5 accent-[#367AFF]">
              <input type="checkbox" className="h-4 w-4" />
              <span className="text-base font-medium">Keep me logged in</span>
            </label>

            <button className="py-2 px-2 rounded-lg bg-[#53BDF5] border border-[#367AFF] text-white font-semibold text-base">
              Sign In
            </button>

            <div className="flex items-center gap-2.5">
              <div className="w-full border-[0.5px] border-[#D9D9D9]"></div>
              <span className="text-[#6E6E6E] font-medium">or</span>
              <div className="w-full border-[0.5px] border-[#D9D9D9]"></div>
            </div>

            <button className="py-2 px-2 rounded-lg bg-white border border-[#E6E8E7] font-semibold text-base flex items-center justify-center gap-2 text-[#232323]">
              Sign in with Google{' '}
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M20.16 12.1932C20.16 11.5905 20.1059 11.0109 20.0055 10.4546H12V13.7425H16.5746C16.3775 14.805 15.7786 15.7052 14.8784 16.308V18.4407H17.6255C19.2327 16.9609 20.16 14.7818 20.16 12.1932Z"
                  fill="#4285F4"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M12.0002 20.5C14.2952 20.5 16.2193 19.7389 17.6257 18.4407L14.8786 16.3079C14.1175 16.8179 13.1439 17.1193 12.0002 17.1193C9.78635 17.1193 7.91248 15.6241 7.24407 13.615H4.4043V15.8173C5.80294 18.5952 8.67749 20.5 12.0002 20.5Z"
                  fill="#34A853"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M7.24387 13.615C7.07387 13.105 6.97728 12.5602 6.97728 12C6.97728 11.4398 7.07387 10.895 7.24387 10.385V8.18274H4.40409C3.82841 9.33024 3.5 10.6284 3.5 12C3.5 13.3716 3.82841 14.6698 4.40409 15.8173L7.24387 13.615Z"
                  fill="#FBBC05"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M12.0002 6.88069C13.2482 6.88069 14.3686 7.30955 15.2495 8.15183L17.6875 5.71387C16.2155 4.34227 14.2914 3.5 12.0002 3.5C8.67748 3.5 5.80294 5.40478 4.4043 8.18273L7.24407 10.385C7.91248 8.37592 9.78635 6.88069 12.0002 6.88069Z"
                  fill="#EA4335"
                />
              </svg>
            </button>
          </div>

          <span className="self-center text-base text-[#6C6C6C]">
            Need an account?{' '}
            <Link
              href="/signup"
              className="font-semibold text-[#367AFF] underline"
            >
              Create one
            </Link>
          </span>
        </div>
      </div>
      <div className="py-10 overflow-hidden min-h-full">
        <img src={AUTH_IMAGE} alt="" className="h-full" />
      </div>
    </div>
  );
};
export default SignInPage;
