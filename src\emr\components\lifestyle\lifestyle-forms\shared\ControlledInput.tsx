import React from 'react';

import { Controller, Path, Control, FieldValues } from 'react-hook-form';

import Tooltip from '@/core/components/tooltip';

import InputField, { InputFieldProps } from './InputField';

type CustomInputProps<T extends FieldValues> = InputFieldProps & {
  name: Path<T>;
  control: Control<T>;
  showTooltip?: boolean;
};

const ControlledInput = <T extends FieldValues>({
  label,
  name,
  control,
  disabled,
  showTooltip = true,
  ...props
}: CustomInputProps<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => (
        <Tooltip title={value} hidden={!showTooltip || !value || !disabled}>
          <InputField
            label={label}
            value={value}
            onChange={onChange}
            disabled={disabled}
            {...props}
          />
        </Tooltip>
      )}
    />
  );
};

export default ControlledInput;
