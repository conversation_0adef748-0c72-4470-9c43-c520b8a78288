import React, { FC, memo } from 'react';

import Loading from '@/lib/common/loading';
import { cn } from '@/lib/utils';

import useIsMobile from '@/hooks/use-mobile-layout';

type TimelineItem = {
  content: React.ReactNode;
};

type Props = {
  items: TimelineItem[];
  loading?: boolean;
};

const TimeLine: FC<Props> = ({ items, loading = false }) => {
  const isMobile = useIsMobile();

  if (loading) {
    return (
      <div className="w-full h-[96%] flex items-center justify-center">
        <Loading />
      </div>
    );
  }

  return (
    <div className="w-full h-[calc(100%-2rem)]">
      {items?.map((item, index) => (
        <div key={index} className="flex w-full">
          {index !== 0 && !isMobile && <div className="border-l-4" />}
          <div
            className={cn(
              !isMobile &&
                'border-l-4 border-t-4 mt-9 rounded-ss-xl w-5 min-w-4',
              {
                'ml-[-5px]': index !== 0 && !isMobile,
              }
            )}
          />

          <div className="w-full  pl-1 flex flex-col mt-2">{item.content}</div>
        </div>
      ))}
      {items?.length === 0 && (
        <div className="w-full h-full flex items-center justify-center text-secondary-foreground">
          No records found.
        </div>
      )}
    </div>
  );
};

export default memo(TimeLine);
