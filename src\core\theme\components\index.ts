import { ComponentSettings, OverrideComponent } from '@core/theme/types';

import MuiButton from './MuiButton';
import MuiCard from './MuiCard';
import MuiIconButton from './MuiIconButton';
import MuiModal from './MuiModal';
import MuiPaper from './MuiPaper';
import MuiRadio from './MuiRadio';
import MuiTextField from './MuiTextField';
import MuiTypography from './MuiTypography';

const components = (
  settings: ComponentSettings
): Partial<OverrideComponent> => ({
  MuiButton: MuiButton(settings),
  MuiPaper: MuiPaper(settings),
  MuiCard: MuiCard(settings),
  MuiTextField: MuiTextField(settings),
  MuiModal: MuiModal(settings),
  MuiTypography: MuiTypography(settings),
  MuiIconButton: MuiIconButton(settings),
  MuiRadio: <PERSON>iRadio(settings),
});

export default components;
