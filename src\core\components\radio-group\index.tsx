import React from 'react';

import { FieldError, UseFormRegisterReturn } from 'react-hook-form';

interface RadioOption {
  value: string;
  label: React.ReactNode;
}

/**
 * @deprecated This type is deprecated and will be removed in future versions.
 * Please use the radio group field component instead.
 */
interface RadioGroupProps {
  name: string;
  options: RadioOption[];
  register: UseFormRegisterReturn;
  defaultValue?: string;
  className?: string;
  errors?: FieldError;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
}

/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the radio group field component instead.
 */
const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  options,
  register,
  defaultValue,
  className = '',
  errors,
  required = false,
  ...rest
}) => {
  return (
    <div className={`flex flex-col gap-2.5 ${className}`}>
      <span>
        {name}
        {required && <span className="text-red-500 text-xl ml-0.5">*</span>}
      </span>
      <div className="flex items-center gap-5 w-full">
        {options.map((option) => (
          <label
            key={option.value}
            className="rounded-md cursor-pointer border border-black flex-1 overflow-hidden"
          >
            <input
              type="radio"
              value={option.value}
              className="hidden peer"
              defaultChecked={defaultValue === option.value}
              {...register}
              {...rest}
            />
            <span className="flex items-center justify-center py-1 gap-1 peer-checked:bg-black peer-checked:text-white w-full h-full">
              {option?.label}
            </span>
          </label>
        ))}
      </div>
      {errors?.message && (
        <span className=" text-red-500 text-xs">{errors.message}</span>
      )}
    </div>
  );
};

export default RadioGroup;
