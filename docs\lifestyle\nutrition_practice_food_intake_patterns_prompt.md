# Ambient Listening Prompt – Food Intake Patterns

## Use case:

Ambiently listen to a doctor–patient conversation and extract structured lifestyle data related to: **Food Intake Patterns** and **Beverage Intake Patterns**.

## Output Instruction:

Analyze and return structured information in JSON format for the following fields:  
`major_meals_per_day`, `major_meals_per_day_other`, `snacks_per_day`, `missed_meal`, `missed_meal_other`, `family_meals`, `tv_meals`, `taste_rating`, `satiety_rating`, `tea`, `tea_cups_per_day`, `coffee`, `coffee_cups_per_day`  
Each field's value must be in **string format**.

- If a value is not available, still include the key with an empty string.
- No markdown fences (` ```json `) should be included.

## Field Specifications:

### Food Intake Patterns

- `major_meals_per_day`: One of ["1", "2", "3", "4", "Other"]
- `major_meals_per_day_other`: If "Other" is selected above, specify the number
- `snacks_per_day`: One of ["1", "2", "3", "4", "5"]
- `missed_meal`: One of ["Breakfast", "Lunch", "Dinner", "Others"]
- `missed_meal_other`: If "Others" is selected above, specify the meal type
- `family_meals`: Number of meals with family (0-4)
- `tv_meals`: Number of meals while watching TV (0-4)
- `taste_rating`: Rating from 0-10 (0=Unpleasant to 10=Delicious)
- `satiety_rating`: Rating from 0-10 (0=Not filling to 10=Overly full)

### Beverage Intake Patterns

- `tea`: "Yes" or "No"
- `tea_cups_per_day`: Number (1-10) if tea is "Yes", empty if "No"
- `coffee`: "Yes" or "No"
- `coffee_cups_per_day`: Number (1-10) if coffee is "Yes", empty if "No"

## Additional Rules:

- Only output in **English** regardless of input language.
- Do not include any reasoning or explanations.
- Normalize spoken values to the exact options:
  - For meals/snacks per day: Convert words to numbers ("three" → "3")
  - For missed meals: Map variations ("morning meal" → "Breakfast", "evening meal" → "Dinner")
  - For yes/no questions: Convert affirmative/negative responses to "Yes"/"No"
- Default values if unclear:
  - major_meals_per_day: "3"
  - snacks_per_day: "2"
  - family_meals: "1"
  - tv_meals: "0"
  - taste_rating: "5"
  - satiety_rating: "5"
- For ratings, convert descriptive terms to numbers:
  - "very unpleasant", "terrible" → 0-2
  - "not good", "bland" → 3-4
  - "okay", "average" → 5-6
  - "good", "tasty" → 7-8
  - "excellent", "delicious" → 9-10

## Example Output:

```json
{
  "questions": [
    {
      "id": "food_intake",
      "title": "Food intake patterns",
      "icon": "material-symbols:dining-outline",
      "fields": [
        {
          "id": "major_meals_per_day",
          "label": "Number of major meals per day",
          "type": "radio",
          "options": ["1", "2", "3", "4", "Other"],
          "allowOtherSpecify": true,
          "modal": {
            "title": "Meals per day",
            "inputType": "number"
          },
          "value": "2"
        },
        {
          "id": "snacks_per_day",
          "label": "Number of snacks per day",
          "type": "radio",
          "options": ["1", "2", "3", "4", "5"],
          "value": "3"
        },
        {
          "id": "missed_meal",
          "label": "Which meal do you skip/miss most often in a day?",
          "type": "radio",
          "options": ["Breakfast", "Lunch", "Dinner", "Others"],
          "allowOtherSpecify": true,
          "modal": {
            "title": "Meal Type",
            "inputType": "text"
          },
          "value": "Breakfast"
        },
        {
          "id": "family_meals",
          "label": "How many meals do you eat with family in a day?",
          "type": "radio",
          "options": ["0", "1", "2", "3", "4"],
          "value": "2"
        },
        {
          "id": "tv_meals",
          "label": "How many meals do you eat while watching TV in a day?",
          "type": "radio",
          "options": ["0", "1", "2", "3", "4"],
          "value": "1"
        },
        {
          "id": "taste_rating",
          "label": "How do you rate the taste of your meals?",
          "type": "slider",
          "min": 0,
          "max": 10,
          "step": 1,
          "description": "(0=Unpleasant to 10=Delicious)",
          "value": 7
        },
        {
          "id": "satiety_rating",
          "label": "How do you rate the satiety of your meals?",
          "type": "slider",
          "min": 0,
          "max": 10,
          "step": 1,
          "description": "(0=Not filling to 10=Overly full)",
          "value": 3
        }
      ]
    },
    {
      "id": "beverage_intake",
      "title": "Beverage intake patterns",
      "icon": "material-symbols:coffee-outline-rounded",
      "fields": [
        {
          "id": "tea",
          "label": "Tea",
          "type": "conditional",
          "conditions": [
            {
              "label": "Yes",
              "subField": {
                "id": "tea_cups_per_day",
                "label": "Cups per Day",
                "type": "number",
                "min": 1,
                "max": 10
              }
            },
            {
              "label": "No"
            }
          ],
          "value": {
            "value": "Yes",
            "subField": "2"
          }
        },
        {
          "id": "coffee",
          "label": "Coffee",
          "type": "conditional",
          "conditions": [
            {
              "label": "Yes",
              "subField": {
                "id": "coffee_cups_per_day",
                "label": "Cups per Day",
                "type": "number",
                "min": 1,
                "max": 10
              }
            },
            {
              "label": "No"
            }
          ],
          "value": {
            "value": "No"
          }
        }
      ]
    }
  ]
}
```
