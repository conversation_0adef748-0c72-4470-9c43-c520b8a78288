export interface EmrUpcomingAppointment {
  id: string;
  patientId: string;
  patientName: string;
  appointmentTime: string;
  status:
    | 'scheduled'
    | 'confirmed'
    | 'cancelled'
    | 'completed'
    | 'arrived'
    | 'no-show';
  department: string;
}

export interface EmrUpcomingAppointmentsParams {
  searchQuery?: string;
  page?: number;
  pageSize?: number;
}

export interface EmrUpcomingAppointmentsResponse {
  data: EmrUpcomingAppointment[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
}

export const getEmrUpcomingAppointments = async (
  params: EmrUpcomingAppointmentsParams
): Promise<EmrUpcomingAppointmentsResponse> => {
  try {
    // TODO: Replace with actual API endpoint
    // const { data } = await api.get('/emr/dashboard/upcoming-appointments', {
    //   params: {
    //     searchQuery: params.searchQuery,
    //     page: params.page || 1,
    //     pageSize: params.pageSize || 10,
    //   },
    // });

    // Mock data for now - replace with actual API call
    const mockData: EmrUpcomingAppointment[] = [];

    // Apply search filter
    let filteredData = mockData;
    if (params.searchQuery) {
      const query = params.searchQuery.toLowerCase();
      filteredData = filteredData.filter(
        (appointment) =>
          appointment.patientName.toLowerCase().includes(query) ||
          appointment.patientId.toLowerCase().includes(query)
      );
    }

    // Pagination
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const totalItems = filteredData.length;
    const totalPages = Math.ceil(totalItems / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      totalItems,
      totalPages,
      currentPage: page,
    };
  } catch (error) {
    console.error('Error fetching EMR upcoming appointments:', error);
    throw error;
  }
};

// EMR Dashboard widget data types
export interface EmrDashboardWidgetData {
  todaysAppointments: number;
  patientQueue: number;
  averageWaitingTime: number;
  myPatients: number;
  averageWaitingTimeDetails: {
    averageWaitingTime: number;
    totalCompletedConsultations: number;
    validCalculations: number;
    unit: string;
    date: string;
  };
  organizationId: string;
  date: string;
}

// API function to get EMR dashboard widget data
export const getEmrDashboardWidgetData =
  async (): Promise<EmrDashboardWidgetData> => {
    try {
      // TODO: Replace with actual API endpoint
      // const { data } = await api.get('/emr/dashboard/v0.1/dashboard/user');

      // Mock data for now - replace with actual API call
      return {
        todaysAppointments: 0,
        patientQueue: 0,
        averageWaitingTime: 0,
        myPatients: 0,
        averageWaitingTimeDetails: {
          averageWaitingTime: 0,
          totalCompletedConsultations: 0,
          validCalculations: 0,
          unit: 'minutes',
          date: new Date().toISOString().split('T')[0],
        },
        organizationId: 'org-123',
        date: new Date().toISOString().split('T')[0],
      };
    } catch (error) {
      console.error('Error fetching EMR dashboard widget data:', error);
      throw error;
    }
  };
