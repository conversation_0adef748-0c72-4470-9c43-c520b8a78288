import React from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import AppTextField, {
  AppTextFieldProps,
} from '@/core/components/app-text-field';

type Props<T extends FieldValues> = AppTextFieldProps & {
  name: Path<T>;
  control: Control<T>;
  rules?: any;
};

const ControlledTextField = <T extends FieldValues>({
  name,
  control,
  rules,
  ...props
}: Props<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <AppTextField
          {...field}
          {...props}
          error={!!fieldState.error?.message}
          helperText={fieldState.error?.message}
        />
      )}
    />
  );
};

export default ControlledTextField;
