import React from 'react';

import {
  Accordion as MuiAccordion,
  AccordionSummary,
  AccordionDetails,
  styled,
} from '@mui/material';
import { CgExpand } from 'react-icons/cg';
import { FaAngleRight } from 'react-icons/fa6';

import { cn } from '@/lib/utils';

import useIsMobile from '@/hooks/use-mobile-layout';

import colors from '@/utils/colors';

import ExpandIcon from '@/assets/svg/ExpandIcon';

import OutLinedIconButton from './OutlinedIconButton';

const StyledAccordion = styled(MuiAccordion)<{ width?: string }>(
  ({ expanded, theme, width }) => ({
    width: width || 'fit-content',
    minWidth: '100%',
    border: expanded ? `1px solid #BDBDBD` : `2px solid #E0E0E0`,

    ['& .MuiAccordionSummary-root']: {
      height: 55,
      minHeight: 55,
      padding: 0,
      borderRadius: 5,
      boxShadow: expanded ? theme.shadows[2] : 'none',
      width: '100%',
    },

    ['& .MuiAccordionSummary-content']: {
      margin: 0,
      height: 55,
      minHeight: 55,
      display: 'flex',
      justifyContent: 'space-between',
      padding: 15,
      alignItems: 'center',
      borderRadius: 5,
      gap: 1,
      [theme.breakpoints.down('sm')]: {
        padding: 5,
        gap: 5,
      },
    },

    ['& .MuiAccordionDetails-root']: {
      backgroundColor: colors.common.lightBlue,
      padding: 2.5,
    },
  })
);

type AccordionProps = {
  children: React.ReactNode;
  designation?: string;
  doctorName?: string;
  date?: string;
  open: boolean;
  onToggle?: () => void;
  onFinalise?: () => void;
  onExpand?: () => void;
  showFinaliseButton?: boolean;
  isDownloadPDF?: boolean;
  pdfDocument?: JSX.Element;
  fileName?: string;
  expand?: boolean;
  width?: string;
  className?: string;
  hideFinaliseButton?: boolean;
};

const Accordion: React.FC<AccordionProps> = ({
  children,
  onToggle,
  open,
  date,
  doctorName,
  designation,
  onFinalise,
  onExpand,
  showFinaliseButton = true,
  expand,
  width,
  className = '',
  hideFinaliseButton = false,
}) => {
  const isMobile = useIsMobile();
  return (
    <StyledAccordion
      className={`border-2 h-full ${className}`}
      expanded={open}
      onChange={onToggle}
      width={width}
    >
      <AccordionSummary className="gap-1">
        <span className="flex gap-1 items-center">
          <OutLinedIconButton showBorder={!expand}>
            {!expand && (
              <FaAngleRight
                className={cn(
                  'rotate-90 transition-all text-black text-xs sm:text-xl ',
                  {
                    'rotate-0': !open,
                  }
                )}
              />
            )}
          </OutLinedIconButton>
          {isMobile ? (
            <div className="flex gap-2 items-center ">
              <span className="font-semibold md:text-base text-sm max-w-[44px] break-words line-clamp-2 overflow-hidden">
                {doctorName}
              </span>
              {designation && <span>|</span>}
              <span className="font-light text-xs max-w-[44px] break-words line-clamp-2 overflow-hidden">
                {designation}
              </span>
            </div>
          ) : (
            <div className="flex gap-2 items-center max-w-50 min-w-50">
              <span
                className="font-semibold md:text-base text-sm break-words line-clamp-2 overflow-hidden"
                title={doctorName}
              >
                {doctorName}
              </span>
              {designation && <span>|</span>}
              <span className="font-light text-xs">{designation}</span>
            </div>
          )}
        </span>
        <span className="font-light text-xs italic hidden md:flex ">
          {date}
        </span>

        <span className="flex flex-row justify-end gap-1 md:gap-2 items-center md:max-w-50 md:min-w-50 ">
          <div className="flex items-center">
            {!hideFinaliseButton && (
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  onFinalise?.();
                }}
                className={cn(
                  'px-3 text-xs font-light py-0.5 border relative border-black rounded-full min-h-5.5 md:min-h-7.5 bg-gray-100 text-black hover:bg-gray-200 transition order-2 sm:order-1'
                )}
                disabled={!showFinaliseButton}
              >
                <span className={showFinaliseButton ? '' : 'invisible'}>
                  Finalise Record
                </span>
                <span
                  className={
                    showFinaliseButton
                      ? 'hidden'
                      : 'absolute inset-0 bg-transparent flex justify-center items-center'
                  }
                >
                  Finalised
                </span>
              </button>
            )}
          </div>
          {/* TODO: add print button */}
          {/* <OutLinedIconButton
            className="order-1 sm:order-1"
            onClick={(e) => e.stopPropagation()}
          >
            {isDownloadPDF && pdfDocument ? (
              <PDFDownloadLink document={pdfDocument} fileName={fileName}>
                <FiPrinter className="text-black" />
              </PDFDownloadLink>
            ) : (
              <FiPrinter className="text-black" />
            )}
          </OutLinedIconButton> */}

          <OutLinedIconButton
            onClick={(e) => {
              e.stopPropagation();
              onExpand?.();
            }}
            className="order-3 sm:order-2"
          >
            {expand ? (
              <ExpandIcon />
            ) : (
              <CgExpand className="text-black text-xl" />
            )}
          </OutLinedIconButton>
        </span>
      </AccordionSummary>
      <AccordionDetails className="accordion-details ">
        {children}
      </AccordionDetails>
    </StyledAccordion>
  );
};

export default Accordion;
