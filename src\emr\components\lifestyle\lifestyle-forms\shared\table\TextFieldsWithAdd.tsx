import { useCallback } from 'react';

import { Path, useFormContext, useWatch } from 'react-hook-form';

import { HiMinusCircle, HiPlusCircle } from 'react-icons/hi';

import colors from '@/utils/colors';

import {
  LifestyleQuestion,
  Questions,
  SubQuestions,
} from '@/emr/types/lifestyle';

import ControlledInput from '../ControlledInput';

type FieldProps = {
  appendName: Path<LifestyleQuestion>;
  isReadOnly?: boolean;
  isMaximized?: boolean;
  values?: Questions[];
  name: Path<LifestyleQuestion>;
};

const TextFieldsWithAdd = ({
  appendName,
  isMaximized,
  isReadOnly,
  name,
}: FieldProps) => {
  const { control, setValue, getValues } = useFormContext<LifestyleQuestion>();

  const fields = useWatch({ control, name: appendName });

  const handleAddRow = useCallback(() => {
    const currentValues = getValues(name);

    const updatedValues: SubQuestions[] = (currentValues || []).map(
      (item: SubQuestions) => {
        if (item?.defaultValue || item?.defaultValue === '') {
          return {
            ...item,
            sub_questions: [
              ...(item?.sub_questions ?? []),
              {
                ...item?.sub_questions?.[0],
                value: item.defaultValue,
              },
            ],
          };
        }
        return item;
      }
    );
    setValue(name, updatedValues, {
      shouldTouch: true,
    });
  }, [getValues, name, setValue]);

  const handleRemoveRow = useCallback(
    (index: number) => {
      const currentValues = getValues(name);

      const updatedValues: SubQuestions[] = (currentValues || []).map(
        (item: SubQuestions) => {
          return {
            ...item,
            sub_questions: item?.sub_questions?.filter((_, i) => i !== index),
          };
        }
      );

      setValue(name, updatedValues, {
        shouldTouch: true,
      });
    },
    [getValues, name, setValue]
  );

  return (
    <div className="flex flex-col gap-2">
      {fields?.map((q: SubQuestions, index: number) => (
        <div key={index} className="flex items-center gap-2">
          <ControlledInput
            name={`${appendName}.${index}.value` as any}
            control={control}
            className="w-full"
            iconClassName="max-h-5 right-[-2px] flex gap-1"
            disabled={isReadOnly}
            variant={isMaximized ? 'transparent' : undefined}
            inputClassName={
              isMaximized
                ? `!w-full border border-[${colors.text.slateGray}] rounded-md`
                : '!w-full'
            }
            endDecoration={
              isReadOnly ? undefined : (
                <HiPlusCircle
                  onClick={handleAddRow}
                  style={{ fontSize: 18, color: 'black' }}
                />
              )
            }
            endIconSecondary={
              index !== 0 ||
              (isReadOnly && (
                <HiMinusCircle
                  onClick={() => handleRemoveRow(index)}
                  style={{ fontSize: 18, color: 'red' }}
                />
              ))
            }
          />
        </div>
      ))}
    </div>
  );
};

export default TextFieldsWithAdd;
