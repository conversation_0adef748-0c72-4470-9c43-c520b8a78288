import React, { memo, useCallback, useEffect } from 'react';

import {
  ArrayPath,
  Control,
  FieldArray,
  FieldValues,
  Path,
  useFieldArray,
} from 'react-hook-form';

import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import { lifestyleModes } from '@/constants/lifestyle';

import { SubQuestions } from '@/emr/types/lifestyle';

import AddButton from './AddButton';
import ControlledInput from './ControlledInput';
import RemoveButton from './RemoveButton';

type Props<T extends FieldValues> = {
  control: Control<T>;
  name: ArrayPath<T>;
  label?: string;
  isReadOnly?: boolean;
};

const { CREATE } = lifestyleModes;

const FieldArrayText = <T extends FieldValues>({
  control,
  name,
  label,
  isReadOnly,
}: Props<T>) => {
  const { formMode } = useLifestyleUtilStore();

  const { fields, remove, append } = useFieldArray({
    control,
    name: name,
  });

  const onAppend = useCallback(() => {
    append({ value: '' } as FieldArray<T, ArrayPath<T>>);
  }, [append]);

  useEffect(() => {
    if (formMode === CREATE && !isReadOnly) {
      onAppend();
    }
  }, [onAppend, formMode, isReadOnly]);

  return (
    <div className="w-full md:w-1/2 border-r flex flex-col gap-2 p-2">
      <span>{label}</span>
      {fields?.map((field, index) => {
        if ((field as SubQuestions['value'])?.value === '' && isReadOnly) {
          return null;
        }
        return (
          <div key={field.id} className="flex gap-2 items-center">
            <ControlledInput
              control={control}
              variant={isReadOnly ? 'transparent' : 'gray'}
              className="w-full"
              name={`${name}.${index}.value` as Path<T>}
              placeholder={isReadOnly ? '' : 'Enter'}
              disabled={isReadOnly}
            />
            {!isReadOnly && (
              <>
                {fields?.length - 1 > index ? (
                  <RemoveButton onRemove={() => remove(index)} />
                ) : (
                  <AddButton onClick={onAppend} />
                )}
              </>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default memo(FieldArrayText);
