'use client';

import React, { memo, useState, useEffect } from 'react';

// Store
import { useEmrDashboardStore } from '@/store/emr/dashboard';
import { useUserStore } from '@/store/userStore';

// Components
import colors from '@/utils/colors';
import {
  getTimeBasedGreeting,
  getLastUpdatedText,
} from '@/utils/mrd/dashboard/greeting';

import AvgWaitTimeIcon from '@/assets/svg/dashboard/AvgWaitTimeIcon';
import PatientQueueIcon from '@/assets/svg/dashboard/PatientQueueIcon';
import TodaysAppointmentsIcon from '@/assets/svg/dashboard/TodaysAppointmentsIcon';
import TotalPatientsIcon from '@/assets/svg/dashboard/TotalPatientsIcon';

import ChartPlaceholder from '@/views/shared/dashboard/ChartPlaceholder';
import CurrentAppointments from '@/views/shared/dashboard/CurrentAppointments';
import DashboardCalendar from '@/views/shared/dashboard/DashboardCalendar';
import DashboardHeader from '@/views/shared/dashboard/DashboardHeader';
import DashboardWidgetCard from '@/views/shared/dashboard/DashboardWidgetCard';
import SectionTitle from '@/views/shared/dashboard/SectionTitle';

const EmrDashboard = () => {
  const {
    widgetData,
    isLoadingWidgetData,
    fetchWidgetData,
    upcomingAppointments,
    isLoadingAppointments,
    totalItems,
    totalPages,
    filters,
    setSearchQuery,
    setPage,
    setPageSize,
    fetchUpcomingAppointments,
  } = useEmrDashboardStore();
  const { name } = useUserStore();
  const [_greeting, setGreeting] = useState<string>('');
  const [lastUpdated, setLastUpdated] = useState<string>('');
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);

  // Update greeting and last updated text every minute
  useEffect(() => {
    const updateGreeting = () => {
      setGreeting(getTimeBasedGreeting());
      setLastUpdated(getLastUpdatedText(lastRefreshTime || undefined));
    };

    updateGreeting();
    const interval = setInterval(updateGreeting, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [lastRefreshTime]);

  // Load widget data and appointments on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.all([fetchWidgetData(), fetchUpcomingAppointments()]);
      const now = new Date();
      setLastRefreshTime(now);
      setLastUpdated(getLastUpdatedText(now));
    };

    loadInitialData();
  }, [fetchWidgetData, fetchUpcomingAppointments]);

  const handleRefresh = async () => {
    await Promise.all([fetchWidgetData(), fetchUpcomingAppointments()]);
    const now = new Date();
    setLastRefreshTime(now);
    setLastUpdated(getLastUpdatedText(now));
  };

  const pageGreeting = `Welcome back ${name ? `Dr. ${name}` : 'Doctor'}`;

  return (
    <div className="h-full w-full flex flex-col p-6 bg-gray-50 overflow-y-auto">
      <DashboardHeader
        greeting={pageGreeting}
        lastUpdated={lastUpdated}
        onRefresh={handleRefresh}
        isLoading={isLoadingWidgetData}
      />

      {/* Section 1 Title */}
      <SectionTitle>Section 1</SectionTitle>

      {/* Widget Cards */}
      <div className="grid grid-cols-4 gap-4 mb-4" style={{ gap: '16px' }}>
        <DashboardWidgetCard
          title="Today's Appointments"
          count={widgetData?.todaysAppointments ?? null}
          status="---"
          icon={<TodaysAppointmentsIcon />}
          bgColor={colors.dashboard.appointments.bgColor}
          borderColor={colors.dashboard.appointments.borderColor}
        />
        <DashboardWidgetCard
          title="Patient Queue"
          count={widgetData?.patientQueue ?? null}
          status="Currently waiting"
          icon={<PatientQueueIcon />}
          bgColor={colors.dashboard.patientQueue.bgColor}
          borderColor={colors.dashboard.patientQueue.borderColor}
        />
        <DashboardWidgetCard
          title="Avg. Wait Time"
          count={
            widgetData?.averageWaitingTime
              ? `${widgetData.averageWaitingTime} Mins`
              : null
          }
          status="Today's average"
          icon={<AvgWaitTimeIcon />}
          bgColor={colors.dashboard.avgWaitTime.bgColor}
          borderColor={colors.dashboard.avgWaitTime.borderColor}
        />
        <DashboardWidgetCard
          title="My Patients"
          count={widgetData?.myPatients ?? null}
          status="All time registered"
          icon={<TotalPatientsIcon />}
          bgColor={colors.dashboard.totalPatients.bgColor}
          borderColor={colors.dashboard.totalPatients.borderColor}
        />
      </div>

      {/* Charts and Calendar Row */}
      <div
        className="grid grid-cols-4 gap-4 mb-4 min-h-87"
        style={{ gap: '16px' }}
      >
        <ChartPlaceholder title="Today's Appointments" />
        <ChartPlaceholder title="Avg. Consultation time" colSpan="col-span-1" />
        <ChartPlaceholder
          title="Appointment Attendance Rate"
          colSpan="col-span-1"
        />
        <DashboardCalendar />
      </div>

      {/* Section 2 Title */}
      <SectionTitle>Section 2</SectionTitle>

      {/* Bottom Row */}
      <div className="grid grid-cols-4 gap-4 min-h-87" style={{ gap: '16px' }}>
        {/* Current Appointments Table - 2 columns width */}
        <div className="col-span-2">
          <CurrentAppointments
            title="Upcoming Appointments"
            appointments={upcomingAppointments}
            isLoading={isLoadingAppointments}
            totalItems={totalItems}
            totalPages={totalPages}
            currentPage={filters.page}
            searchQuery={filters.searchQuery}
            onSearchChange={setSearchQuery}
            onPageChange={setPage}
            onPageSizeChange={setPageSize}
            showDatePicker={true} // MRD shows date picker
            showDateColumn={false} // EMR shows only time, not date & time
          />
        </div>
        {/* Empty space - 1 column width */}
        <div></div>
      </div>
    </div>
  );
};

export default memo(EmrDashboard);
