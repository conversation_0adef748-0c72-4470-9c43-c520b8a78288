import { FC, useCallback } from 'react';

import AppLayout from '@core/layout/shared/AppLayout';
import NavLink from '@core/layout/shared/side-bar/NavLink';

import { cn } from '@/lib/utils';

import ProfileIcon from '@/assets/svg/profile-icon.svg';

import { routes } from '@/constants/routes';

import emrNavigation from '@/core/configs/emr/navigation-desktop';

const { EMR_PROFILE_PERSONAL_INFO } = routes;

const DesktopLayout: FC<{ children: React.ReactNode }> = ({ children }) => {
  const renderBottom = useCallback(() => {
    return (
      <div
        className={cn(
          'flex flex-col mt-auto pt-1 border-t',
          'items-center',
          'w-full'
        )}
      >
        <NavLink
          icon={<ProfileIcon />}
          href={EMR_PROFILE_PERSONAL_INFO}
          text="My Profile"
        />
      </div>
    );
  }, []);

  return (
    <AppLayout navItem={emrNavigation} renderBottom={renderBottom}>
      {children}
    </AppLayout>
  );
};

export default DesktopLayout;
