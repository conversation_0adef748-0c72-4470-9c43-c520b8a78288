import { useMemo } from 'react';

import type { GroupBase, StylesConfig } from 'react-select';

import AsyncSelect, { AsyncProps } from 'react-select/async';

/**
 * @deprecated This type is deprecated and will be removed in future versions.
 * Please use the app async search component instead.
 */
interface AsyncPatientSearchProps<T>
  extends AsyncProps<T, false, GroupBase<T>> {
  defaultOptions?: T[];
}

/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the app async search component instead.
 */
const AsyncSearch = <T extends unknown>({
  defaultOptions = [],
  placeholder = 'Search...',
  components: customComponents,
  ...rest
}: AsyncPatientSearchProps<T>) => {
  const customStyles: StylesConfig<T, false> = useMemo(
    () => ({
      placeholder: (base) => ({
        ...base,
        color: '#aaa',
        opacity: 1,
        fontStyle: 'italic',
        fontSize: 13,
      }),
      control: (base) => ({
        ...base,
        minHeight: '32px',
        height: '32px',
        borderColor: '#ddd',
        boxShadow: 'none',
        '&:hover': {
          borderColor: '#bbb',
        },
      }),
      valueContainer: (base) => ({
        ...base,
        padding: '0 8px',
        height: '32px',
      }),
      indicatorsContainer: (base) => ({
        ...base,
        height: '32px',
      }),
      dropdownIndicator: (base) => ({
        ...base,
        padding: '4px',
      }),
      input: (base) => ({
        ...base,
        margin: '0',
        padding: '0',
      }),
      menuPortal: (base) => ({
        ...base,
        zIndex: 2000,
      }),
    }),
    []
  );

  return (
    <AsyncSelect<T>
      defaultOptions={defaultOptions}
      isClearable
      cacheOptions
      styles={customStyles}
      placeholder={placeholder}
      menuPortalTarget={
        typeof window !== 'undefined' ? document.body : undefined
      }
      components={{
        ...customComponents,
      }}
      {...rest}
    />
  );
};

export default AsyncSearch;
