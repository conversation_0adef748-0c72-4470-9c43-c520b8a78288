import React, { FC, memo } from 'react';

import { MenuItem, Divider, styled, Menu } from '@mui/material';

import { useUserStore } from '@/store/userStore';

import ExchangeIcon from '@/assets/svg/ExchangeIcon';
import LogoutIcon from '@/assets/svg/LogoutIcon';

import { PERMISSION_KEYS } from '@/constants/permission-keys';

const ProfileMenu = styled(Menu)(({ theme }) => ({
  ['& .menu-paper']: {
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.spacing(1),
    padding: theme.spacing(1),
    boxShadow: 'none',

    ['& .menu-list']: {
      padding: 0,

      ['& .menu-item']: {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(1),

        ['&:hover']: {
          backgroundColor: '#e8f4ff',
          borderRadius: theme.spacing(1),
        },
      },
    },
  },
}));

type Props = {
  open: boolean;
  anchorEl: HTMLElement | null;
  handleClose: () => void;
  handleSwitchModule: () => void;
  handleLogout: () => void;
  isEMR: boolean;
};

const MenuOption: FC<Props> = ({
  open,
  anchorEl,
  handleClose,
  handleSwitchModule,
  handleLogout,
  isEMR,
}) => {
  const { permissions = [] } = useUserStore();

  // Check if user has both EMR and MRD access
  const hasEMRAccess = permissions.includes(PERMISSION_KEYS.EMR_ACCESS);
  const hasMRDAccess = permissions.includes(PERMISSION_KEYS.MRD_ACCESS);

  // Only show switch button if user has access to both modules
  const showSwitchButton = hasEMRAccess && hasMRDAccess;

  return (
    <ProfileMenu
      id="demo-positioned-menu"
      aria-labelledby="demo-positioned-button"
      anchorEl={anchorEl}
      open={open}
      onClose={handleClose}
      classes={{
        paper: 'menu-paper border border-2 border-gray-400',
        list: 'menu-list',
      }}
    >
      {showSwitchButton && (
        <>
          <MenuItem onClick={handleSwitchModule} className="menu-item">
            <ExchangeIcon />
            Switch to {isEMR ? 'MRD' : 'EMR'}
          </MenuItem>
          <Divider />
        </>
      )}
      <MenuItem onClick={handleLogout} className="menu-item">
        <LogoutIcon />
        Log out
      </MenuItem>
    </ProfileMenu>
  );
};

export default memo(MenuOption);
