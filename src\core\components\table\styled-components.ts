'use client';

import { IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';

import colors from '@/utils/colors';

export const StyledTableHead = styled(TableHead)(({ theme }) => ({
  '& th': {
    fontSize: '12px',
    paddingTop: theme.spacing(1),
    paddingBottom: theme.spacing(1),
    color: theme.palette.common.white,
    backgroundColor: colors.common.navyBlue,
    zIndex: 1000,
  },
}));

export const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: Number(theme.shape.borderRadius) / 2,
  '& tbody td': {
    fontSize: 12,
    paddingTop: theme.spacing(1),
    paddingBottom: theme.spacing(1),
    border: `1px solid ${colors.common.navyBlue}`,
    borderTop: 'none',
    minHeight: 40,
  },
  '& tbody tr td:first-of-type': {
    borderLeft: `1.5px solid ${colors.common.navyBlue}`,
  },
  '& tbody tr td:last-of-type': {
    borderRight: `1.5px solid ${colors.common.navyBlue}`,
  },
}));

export const SortIconButton = styled(IconButton)(({ theme }) => ({
  padding: 0,
  width: 26,
  height: 26,
  position: 'relative',
  '& .sort-icon': {
    color: colors.common.charcoalGray,
    width: 15,
    height: 15,
    position: 'absolute',
    transform: 'translate(-50%, -50%)',

    '&.asc': {
      top: '40%',
      left: '40%',
    },

    '&.desc': {
      top: '60%',
      left: '60%',
    },

    '&.selected': {
      color: theme.palette.common.white,
    },
  },
}));
