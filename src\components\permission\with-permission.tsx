'use client';

import { useEffect } from 'react';

import { usePathname, useRouter } from 'next/navigation';

import { useHasPermission } from '@/utils/permissions';

import NoAccessMessage from './no-access-message';

type WithPermissionProps = {
  requiredPermissions: string[];
  children: React.ReactNode;
  showLayout?: boolean;
};

/**
 * Higher-order component that wraps a component with permission checks
 * @param WrappedComponent The component to wrap with permission checks
 * @param options Configuration options for the permission check
 * @returns A new component with permission checks
 */
export function withPermission<T>(
  WrappedComponent: React.ComponentType<T>,
  options: {
    requiredPermissions: string[];
    showLayout?: boolean;
  }
) {
  const { requiredPermissions, showLayout = true } = options;

  return function WithPermissionWrapper(props: T) {
    const hasPermission = useHasPermission(requiredPermissions);
    const router = useRouter();
    const pathname = usePathname();

    useEffect(() => {
      // If user doesn't have permission and is not on the home page, redirect to home
      if (!hasPermission && pathname !== '/') {
        router.replace('/');
      }
    }, [hasPermission, pathname, router]);

    if (!hasPermission) {
      return showLayout ? (
        <div className="flex justify-between w-full gap-base h-full">
          <div className="w-full h-full">
            <NoAccessMessage />
          </div>
        </div>
      ) : (
        <NoAccessMessage />
      );
    }

    return <WrappedComponent {...(props as any)} />;
  };
}

/**
 * Component that renders its children only if the user has the required permissions
 * @param requiredPermissions Array of permission keys required to access the content
 * @param children Content to render if user has permission
 * @param showLayout Whether to show the layout (header/sidebar) when access is denied
 * @returns The children if user has permission, otherwise a no access message
 */
export function PermissionGuard({
  requiredPermissions,
  children,
  showLayout = true,
}: WithPermissionProps) {
  const hasPermission = useHasPermission(requiredPermissions);

  if (!hasPermission) {
    return showLayout ? (
      <div className="flex bg-white justify-between w-full gap-base h-full rounded-lg">
        <div className="w-full h-full">
          <NoAccessMessage />
        </div>
      </div>
    ) : (
      <NoAccessMessage />
    );
  }

  return <>{children}</>;
}
