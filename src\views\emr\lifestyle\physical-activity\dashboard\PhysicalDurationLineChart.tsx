import React, { useMemo } from 'react';

import { useLifestyleFilterStore } from '@/store/emr/lifestyle/filter-store';

import PhysicalAreaChart from '../../shared/dashboard-components/PhysicalAreaChart';

interface DurationChartPoint {
  date: string;
  value: number;
}

interface PhysicalDurationLineChartProps {
  data: DurationChartPoint[];
}

const PhysicalDurationLineChart: React.FC<PhysicalDurationLineChartProps> = ({
  data,
}) => {
  const { fromDate, toDate } = useLifestyleFilterStore();

  const chartData = useMemo(() => {
    if (data.length > 0) {
      return data.map((d) => ({ ...d, value: d.value }));
    }

    let days = 7;
    if (fromDate && toDate) {
      const diffTime = Math.abs(
        new Date(toDate).getTime() - new Date(fromDate).getTime()
      );
      days = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    } else {
      const urlParams = new URLSearchParams(window.location.search);
      const filter = urlParams.get('filter');
      if (filter === '7d') days = 7;
      else if (filter === '15d') days = 15;
      else if (filter === '30d') days = 30;
    }

    const today = new Date();
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return Array.from({ length: days }, (_, i) => {
      const date = new Date(today);
      date.setDate(today.getDate() - (days - 1 - i));
      const formattedDate = `${monthNames[date.getMonth()]} ${date.getDate()}`;
      return {
        date: formattedDate,
        value: 0,
      };
    });
  }, [data, fromDate, toDate]);

  return (
    <PhysicalAreaChart
      data={chartData}
      dataKey="value"
      color="#00ACC1"
      title="Total Duration"
      unit="mins"
      height={320}
      showLegend={false}
      yAxisDomain={data.length === 0 ? [0, 40] : undefined}
    />
  );
};

export default PhysicalDurationLineChart;
