import React from 'react';

interface StyledKeyValuePairProps {
  label: string;
  value: React.ReactNode;
  className?: string;
}

const StyledKeyValuePair: React.FC<StyledKeyValuePairProps> = ({
  label,
  value,
  className = '',
}) => (
  <div className={`flex flex-col space-y-1 ${className}`}>
    <div
      className="text-sm font-bold text-gray-900"
      style={{ fontSize: '14px', fontWeight: 700 }}
    >
      {label}
    </div>
    <div
      className="text-sm font-normal"
      style={{ color: '#64707D', fontSize: '14px', fontWeight: 400 }}
    >
      {value || '-'}
    </div>
  </div>
);

export default StyledKeyValuePair;
