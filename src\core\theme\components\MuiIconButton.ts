import { ComponentSettings, OverrideComponent } from '@core/theme/types';

const MuiIconButton = ({
  palette,
}: ComponentSettings): OverrideComponent['MuiIconButton'] => ({
  styleOverrides: {
    root: {
      color: palette.primary.main,
      transition: 'background 0.2s',
      '&:hover': {
        backgroundColor: palette.action.hover,
        color: palette.primary.dark,
      },
    },
  },
  defaultProps: {
    size: 'medium',
    color: 'primary',
  },
});

export default MuiIconButton;
