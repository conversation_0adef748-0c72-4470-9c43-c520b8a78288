import React from 'react';

import { PrescriptionPackageData } from '@/types/emr/prescription/package';

interface EmptyViewProps {
  packages: PrescriptionPackageData[];
}

export const EmptyView: React.FC<EmptyViewProps> = ({ packages }) => {
  return (
    <div className="flex items-center justify-center h-full text-gray-500">
      <div className="text-center">
        <p className="text-lg mb-2">
          {packages.length === 0
            ? 'No packages available'
            : 'Select a package to view details'}
        </p>
        <p className="text-sm">
          {packages.length === 0
            ? 'Create your first package to get started'
            : 'Choose a package from the list to see its medicines'}
        </p>
      </div>
    </div>
  );
};
