import React, { FC, memo, useCallback } from 'react';

import { AiOutlineLoading } from 'react-icons/ai';

import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu';

import { DropdownMenuOption, DropDownProps } from './types';

const hoverHighlightClass =
  'hover:bg-[#A2DAF8] focus:bg-bg-[#A2DAF8] data-[highlighted]:bg-[#A2DAF8] data-[state=open]:bg-[#A2DAF8]';

const DropDown: FC<DropDownProps> = ({
  handleSelect,
  open,
  maxHeight,
  isLoading,
  options,
  isMultiLevel,
  loading,
}) => {
  const renderSubOptions = useCallback(
    (subOptions: (DropdownMenuOption | string)[]) => {
      return subOptions.map((subOption, idx) => {
        const hasNestedSubOptions =
          typeof subOption !== 'string' &&
          Array.isArray(subOption.subOptions) &&
          subOption.subOptions.length > 0;

        return (
          <React.Fragment key={idx}>
            {typeof subOption === 'string' || !hasNestedSubOptions ? (
              <DropdownMenuItem
                onClick={() =>
                  handleSelect(
                    typeof subOption === 'string' ? subOption : subOption.value,
                    (subOption as DropdownMenuOption).key as string,
                    true
                  )
                }
                className={`px-3 py-3 min-h-[50px] border-b border-gray-200 last:border-b-0 whitespace-normal break-words cursor-pointer ${hoverHighlightClass}`}
              >
                <span className="w-full text-left leading-relaxed block">
                  {typeof subOption === 'string' ? subOption : subOption.value}
                </span>
              </DropdownMenuItem>
            ) : (
              <DropdownMenuSub>
                <DropdownMenuSubTrigger
                  className={`px-3 py-3 min-h-[50px] border-b border-gray-200 last:border-b-0 whitespace-normal break-words cursor-pointer ${hoverHighlightClass}`}
                  onClick={() =>
                    handleSelect(subOption.value, subOption.key as string)
                  }
                >
                  <span className="w-full text-left leading-relaxed block">
                    {subOption.value}
                  </span>
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent
                  className={`ml-2 border-gray-300 overflow-y-auto rounded-thin-scrollbar ${maxHeight} w-[280px] max-w-[280px] shadow-lg`}
                >
                  {renderSubOptions(subOption.subOptions || [])}
                </DropdownMenuSubContent>
              </DropdownMenuSub>
            )}
          </React.Fragment>
        );
      });
    },
    [handleSelect, maxHeight]
  );

  if (!open) return null;

  return (
    <DropdownMenuContent
      className={`border border-gray-300 overflow-y-auto rounded-thin-scrollbar ${maxHeight} z-[9999] bg-white w-[280px] max-w-[280px] shadow-lg`}
      side="bottom"
      align="start"
      forceMount
    >
      {isLoading ? (
        <div className="p-2 flex justify-center text-center">
          <AiOutlineLoading className="animate-spin" />
        </div>
      ) : (
        <>
          {options.map((option, idx) => (
            <React.Fragment key={idx}>
              {(option.subOptions && option.subOptions.length > 0) ||
              isMultiLevel ? (
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger
                    className={`px-3 py-3 min-h-[50px] border-b border-gray-200 last:border-b-0 whitespace-normal break-words cursor-pointer ${hoverHighlightClass}`}
                    onClick={() => handleSelect(option.value)}
                    onMouseEnter={() =>
                      handleSelect(option.value, option.key as string)
                    }
                  >
                    <span className="w-full text-left leading-relaxed block">
                      {option.value}
                    </span>
                  </DropdownMenuSubTrigger>
                  <DropdownMenuSubContent
                    className={`ml-3.5 border-gray-300 overflow-y-auto rounded-thin-scrollbar ${maxHeight} w-[280px] max-w-[280px] shadow-lg`}
                  >
                    {loading ? (
                      <div className="p-2 flex justify-center text-center">
                        <AiOutlineLoading className="animate-spin" />
                      </div>
                    ) : (
                      renderSubOptions(option?.subOptions ?? [])
                    )}
                  </DropdownMenuSubContent>
                </DropdownMenuSub>
              ) : (
                <DropdownMenuItem
                  className={`px-3 py-3 min-h-[50px] border-b border-gray-200 last:border-b-0 whitespace-normal break-words cursor-pointer ${hoverHighlightClass}`}
                  onClick={() =>
                    handleSelect(option.value, option.key as string)
                  }
                >
                  <span className="w-full text-left leading-relaxed block">
                    {option.value}
                  </span>
                </DropdownMenuItem>
              )}
            </React.Fragment>
          ))}
        </>
      )}
    </DropdownMenuContent>
  );
};

export default memo(DropDown);
