import { memo, useState } from 'react';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import AppModal from '@/core/components/app-modal';
import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AccordionTitle from '../../../shared/AccordionTitle';
import FinalizeModal from '../../../shared/FinalizeModal';

import AttitudeModal from './AttitudeModal';

type Props = {
  open: boolean;
  onClose: () => void;
  formFields: QuestionnaireResponse | null;
  onFinalize: (data: QuestionnaireResponse | null) => void;
};

const AttitudeEditModal = memo<Props>(
  ({ open, onClose, formFields, onFinalize }) => {
    const [showFinalizeModal, setShowFinalizeModal] = useState(false);

    return (
      <AppModal
        open={open}
        onClose={onClose}
        title=""
        classes={{
          root: 'w-[60vw] h-[80vh] flex flex-col min-h-0',
          body: 'flex-1 h-full flex flex-col min-h-0 !p-0',
          header: '!hidden',
        }}
      >
        <>
          <div className="h-full flex flex-col">
            <div className="flex items-center w-full h-10 p-base bg-[#B4E5FE] rounded-base flex-shrink-0">
              <AccordionTitle
                expand={true}
                doctorName={formFields?.doctor?.name}
                finalised={
                  formFields?.status === LifestyleRecordStatus.FINALIZED
                }
                onExpand={onClose}
                onFinalise={() => {
                  setShowFinalizeModal(true);
                }}
                open={open}
                stepper={['Attitude']}
                designation={formFields?.doctor?.designation}
                department={formFields?.doctor?.department}
                date={formFields?.created_on}
                isAttitude={true}
              />
            </div>
            <div className="flex-1 w-full p-base overflow-hidden">
              <AttitudeModal
                patientData={formFields}
                mode={LifestyleMode.VIEW}
                onAfterSubmit={() => {
                  onClose();
                }}
              />
            </div>
          </div>

          <FinalizeModal
            open={showFinalizeModal}
            onClose={() => setShowFinalizeModal(false)}
            onFinalize={() => {
              onFinalize?.(formFields);
              setShowFinalizeModal(false);
            }}
          />
        </>
      </AppModal>
    );
  }
);

AttitudeEditModal.displayName = 'AttitudeEditModal';

export default AttitudeEditModal;
