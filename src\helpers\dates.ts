/**
 * @deprecated calculateAge is deprecated use calculate age from src\utils\mrd\manage-patient\calculate-age.ts
 */
export function calculateAge(dob: string) {
  const birthDate = new Date(dob);

  const today = new Date();

  let age = today.getFullYear() - birthDate.getFullYear();

  const monthDifference = today.getMonth() - birthDate.getMonth();
  const dayDifference = today.getDate() - birthDate.getDate();

  if (monthDifference < 0 || (monthDifference === 0 && dayDifference < 0)) {
    age--;
  }

  return age;
}
