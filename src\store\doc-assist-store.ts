import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface SearchState {
  searches: Record<string, { query: string; knowledge: string | null }>;
  setSearch: (
    _pageId: string,
    _query: string,
    _knowledge: string | null
  ) => void;
  getSearch: (_pageId: string) => { query: string; knowledge: string | null };
  knowledge: string | null;
  setKnowledge: (_knowledge: string) => void;
}

export const useDocAssistStore = create<SearchState>()(
  persist(
    (set, get) => ({
      searches: {},
      knowledge: null,

      setSearch: (pageId, query, knowledge) =>
        set((state) => ({
          searches: {
            ...state.searches,
            [pageId]: { query, knowledge },
          },
        })),

      getSearch: (pageId) => {
        const state = get();
        return state.searches[pageId] || { query: '', knowledge: null };
      },

      setKnowledge: (knowledge) => set({ knowledge }),
    }),
    {
      name: 'doc-assist-storage',
    }
  )
);
