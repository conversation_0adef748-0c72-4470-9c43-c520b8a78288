import React, { FC, memo, ReactNode } from 'react';

import { Typography } from '@mui/material';

type Align = 'left' | 'center' | 'right';

const alignClasses: Record<Align, string> = {
  left: 'justify-start',
  center: 'justify-center',
  right: 'justify-end items-end',
};

export type KeyValuePairProps = {
  label: string;
  value?: ReactNode;
  align?: Align;
  loading?: boolean;
  className?: string;
};

const KeyValuePair: FC<KeyValuePairProps> = ({
  label,
  value,
  align = 'left',
  loading,
  className = '',
}) => {
  return (
    <div
      className={`flex flex-col gap-1 flex-1 ${alignClasses[align]} ${className}`}
    >
      <Typography variant="subtitle2" className="truncate" color="primary">
        {label}
      </Typography>
      {loading ? (
        <span className="w-20 h-5 bg-gray-200 rounded animate-pulse" />
      ) : (
        <Typography variant="subtitle1" color="primary">
          {value}
        </Typography>
      )}
    </div>
  );
};

export default memo(KeyValuePair);
