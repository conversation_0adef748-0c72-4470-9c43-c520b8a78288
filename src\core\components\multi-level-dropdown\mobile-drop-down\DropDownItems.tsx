import React, { memo } from 'react';

import ButtonBase from '@mui/material/ButtonBase';
import { AiOutlineLoading } from 'react-icons/ai';
import { FaAngleRight } from 'react-icons/fa6';

import { cn } from '@/lib/utils';

import { DropDownItemsProps } from '../types';

const DropDownItems = ({
  value,
  selected,
  hasSubOption,

  onClick,
  isLoading,
}: DropDownItemsProps) => {
  return (
    <div className="w-full">
      <ButtonBase className="w-full !my-1" onClick={onClick}>
        <div
          className={cn(
            'px-3 py-3 flex justify-between w-full text-sm items-start min-h-[50px] border-b border-gray-200 last:border-b-0 text-left',
            { [`bg-[#A2DAF8]`]: selected }
          )}
        >
          <span className="w-full text-left leading-relaxed whitespace-normal break-words block">
            {value}
          </span>
          {hasSubOption && (
            <div className="ml-2 flex-shrink-0 self-start mt-1">
              {isLoading ? (
                <AiOutlineLoading className="animate-spin" />
              ) : (
                <FaAngleRight fontSize={15} />
              )}
            </div>
          )}
        </div>
      </ButtonBase>
    </div>
  );
};

export default memo(DropDownItems);
