import ConsultationIconMob from '@/assets/svg/ConsultationIconMob';
import LifestyleIconMob from '@/assets/svg/LifestyleIconMob';
import PatientInfoIconMob from '@/assets/svg/PatientInfoIconMob';

import { mapObjectToArray } from '@/helpers/utils';

import departments from '@/constants/departments';
import { routes } from '@/constants/routes';

export type NavItems = {
  label: string | React.ReactNode;
  path: string;
  icon: React.ReactNode;
  permissions?: string[];
  department?: string[];
};

const { lifestyle } = departments;

const emrNavItems: NavItems[] = [
  {
    label: 'Patient Info',
    path: routes.EMR_PATIENT_INFO,
    icon: <PatientInfoIconMob />,
  },
  {
    label: 'Consultation',
    path: routes.EMR_CONSULTATION,
    icon: <ConsultationIconMob />,
  },
  {
    label: 'Lifestyle',
    path: routes.EMR_LIFE_STYLE,
    icon: <LifestyleIconMob />,
    department: mapObjectToArray(lifestyle),
  },
  // TODO: Enable this when reports are ready
  // {
  //   label: 'Reports',
  //   path: routes.EMR_REPORTS,
  //   icon: <ReportIconMob />,
  // },
  // { label: 'Dashboard', path: '', icon: <DashboardIcon /> },
];

export default emrNavItems;
