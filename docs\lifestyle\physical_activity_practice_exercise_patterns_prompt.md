# Ambient Listening Prompt – Exercise Patterns

## Use case:

Ambiently listen to a doctor–patient conversation and extract structured lifestyle data related to: **Exercise Patterns**.

## Output Instruction:

Analyze and return structured information in JSON format for exercise activities.  
The output should be an array of exercise objects.  
Each field's value must be in **string format**.

- If no exercise activities are mentioned, return an empty array.
- No markdown fences (` ```json `) should be included.

## Expected JSON Structure:

Return an array of exercise objects with these fields:

- `activity_type`: One of ["Aerobics", "Strength", "Flexibility", "Balance"]
- `activity`: The specific activity as plain text
- `duration`: Duration in minutes (numeric value)
- `intensity`: One of ["Mild", "Moderate", "Intense"]
- `frequency`: One of ["Daily", "Three times a week", "Four times a week", "Five times a week", "Six times a week", "Weekly"]

## Activity Type Mapping:

### Aerobics

Running, jogging, walking, cycling, swimming, dancing, jumping rope, stairs climbing, elliptical, rowing

### Strength

Weight lifting, resistance training, body weight exercises, push-ups, pull-ups, squats, lunges, planks, gym workouts

### Flexibility

Stretching, yoga (non-balance focused), pilates, tai chi movements

### Balance

Balance-specific yoga poses, standing on one leg, balance board exercises, stability ball exercises

## Additional Rules:

- Only output in **English** regardless of input language.
- Do not include any reasoning or explanations.
- Map variations in spoken language:
  - Activity types: Categorize based on the activity nature
  - Duration: Convert to minutes ("1 hour" → "60", "half hour" → "30")
  - Intensity:
    - "light", "easy", "low" → "Mild"
    - "medium", "regular" → "Moderate"
    - "hard", "vigorous", "high" → "Intense"
  - Frequency:
    - "every day", "daily" → "Daily"
    - "3x week", "thrice weekly" → "Three times a week"
    - "4 times", "four days" → "Four times a week"
    - "5 times" → "Five times a week"
    - "6 times" → "Six times a week"
    - "once a week", "weekly" → "Weekly"
- Default values if unclear:
  - duration: 60
  - intensity: "Moderate"
  - frequency: "Three times a week"

## Example Output:

```json
{
  "questions": [
    {
      "id": "exercise_patterns",
      "title": "Exercise Patterns",
      "icon": "dumbbell",
      "fields": [
        {
          "id": "exercise_table",
          "label": "Exercise Activities",
          "type": "table",
          "headers": [
            {
              "id": "activity_type",
              "label": "Activity Type",
              "type": "select",
              "options": ["Aerobics", "Strength", "Flexibility", "Balance"]
            },
            {
              "id": "activity",
              "label": "Activity",
              "type": "conditional_select",
              "dependsOn": "activity_type",
              "options": {
                "Aerobics": [
                  "Walking",
                  "Jogging",
                  "Running",
                  "Cycling",
                  "Swimming",
                  "Dancing",
                  "Zumba",
                  "Skipping",
                  "Kickboxing",
                  "Rowing",
                  "Hiking",
                  "Stair Climbing",
                  "Sports-Cricket,Basketball,Football,Badminton,Lawn Tennis,Golf"
                ],
                "Strength": [
                  "Body Weight Exercises",
                  "Free Weight Exercises",
                  "Machine Based Exercise",
                  "Resistance Band Exercise",
                  "Functional Strenght Exercise"
                ],
                "Flexibility": [
                  "Static Stretching",
                  "Dynamic Stretching",
                  "Yoga",
                  "Pilates based flexibility",
                  "Foam Rolling"
                ],
                "Balance": [
                  "Basic Balance Exercise",
                  "Balance Enhancing Yoga Poses",
                  "Advanced Functional Balance Exercise",
                  "Sports Specific or dynamic Balance drills",
                  "Foam Rolling"
                ]
              }
            },
            {
              "id": "duration",
              "label": "Duration (mins)",
              "type": "number"
            },
            {
              "id": "intensity",
              "label": "Intensity",
              "type": "select",
              "options": ["Mild", "Moderate", "Intense"]
            },
            {
              "id": "frequency",
              "label": "Frequency",
              "type": "select",
              "options": [
                "Daily",
                "Three times a week",
                "Four times a week",
                "Five times a week",
                "Six times a week",
                "Weekly"
              ]
            }
          ],
          "defaultRows": [
            {
              "activity_type": "Aerobics",
              "activity": "Cycling",
              "duration": "60",
              "intensity": "Mild",
              "frequency": "Daily"
            },
            {
              "activity_type": "Strength",
              "activity": "Body Weight Exercises",
              "duration": "60",
              "intensity": "Moderate",
              "frequency": "Three times a week"
            },
            {
              "activity_type": "Flexibility",
              "activity": "Dynamic Stretching",
              "duration": "60",
              "intensity": "Intense",
              "frequency": "Four times a week"
            },
            {
              "activity_type": "Balance",
              "activity": "Balance Enhancing Yoga Poses",
              "duration": "60",
              "intensity": "Mild",
              "frequency": "Daily"
            }
          ],
          "value": [
            {
              "activity_type": "Flexibility",
              "activity": "Dynamic Stretching",
              "duration": "120",
              "intensity": "Mild",
              "frequency": "Three times a week"
            },
            {
              "activity_type": "Strength",
              "activity": "Resistance Band Exercise",
              "duration": "100",
              "intensity": "Moderate",
              "frequency": "Six times a week"
            },
            {
              "activity_type": "Aerobics",
              "activity": "Swimming",
              "duration": "40",
              "intensity": "Intense",
              "frequency": "Six times a week"
            },
            {
              "activity_type": "Aerobics",
              "activity": "Zumba",
              "duration": "20",
              "intensity": "Intense",
              "frequency": "Weekly"
            }
          ]
        }
      ]
    }
  ]
}
```
