import React, { useState, useRef, useEffect } from 'react';

import { cn } from '@/lib/utils';

type DiagnosisStatusSelectorPropsWithAll = {
  value?: 'provisional' | 'confirmed' | 'all';
  onChange?: (value: 'provisional' | 'confirmed' | 'all') => void;
  disabled?: boolean;
  placeholder?: string;
  showAllOption: true;
  className?: string;
  isSavedRecord?: boolean;
};

type DiagnosisStatusSelectorPropsWithoutAll = {
  value?: 'provisional' | 'confirmed';
  onChange?: (value: 'provisional' | 'confirmed') => void;
  disabled?: boolean;
  placeholder?: string;
  showAllOption?: false;
  className?: string;
  isSavedRecord?: boolean;
};

type DiagnosisStatusSelectorProps =
  | DiagnosisStatusSelectorPropsWithAll
  | DiagnosisStatusSelectorPropsWithoutAll;

export default function DiagnosisStatusSelector({
  value,
  onChange,
  disabled = false,
  placeholder = 'Select status',
  showAllOption = false,
  className,
  isSavedRecord = false,
}: DiagnosisStatusSelectorProps) {
  const getOptions = () => {
    if (showAllOption) {
      return [
        { label: 'All Status', value: 'all' as const },
        { label: 'Provisional', value: 'provisional' as const },
        { label: 'Confirmed', value: 'confirmed' as const },
      ];
    }

    if (value === 'provisional') {
      return [
        { label: 'Provisional', value: 'provisional' as const },
        { label: 'Confirmed', value: 'confirmed' as const },
      ];
    } else if (value === 'confirmed') {
      if (isSavedRecord) {
        return [{ label: 'Confirmed', value: 'confirmed' as const }];
      } else {
        return [
          { label: 'Confirmed', value: 'confirmed' as const },
          { label: 'Provisional', value: 'provisional' as const },
        ];
      }
    }

    return [
      { label: 'Provisional', value: 'provisional' as const },
      { label: 'Confirmed', value: 'confirmed' as const },
    ];
  };

  const options = getOptions();

  const handleChange = (newValue: string) => {
    if (onChange) {
      if (showAllOption) {
        (onChange as (value: 'provisional' | 'confirmed' | 'all') => void)(
          newValue as 'provisional' | 'confirmed' | 'all'
        );
      } else {
        (onChange as (value: 'provisional' | 'confirmed') => void)(
          newValue as 'provisional' | 'confirmed'
        );
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'provisional':
        return 'bg-[#DAE1E7] text-black';
      case 'confirmed':
        return 'bg-[#0496E1] text-white';
      case 'all':
        return 'bg-transparent border border-black text-black';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const handleToggle = () => {
    if (disabled) return;

    if (showAllOption) {
      return;
    }

    if (value === 'provisional') {
      handleChange('confirmed');
    } else if (value === 'confirmed') {
      if (!isSavedRecord) {
        handleChange('provisional');
      }
    }
  };

  const displayText = () => {
    if (value === 'provisional') return 'Provisional';
    if (value === 'confirmed') return 'Confirmed';
    if (value === 'all') return 'All Status';
    return placeholder;
  };

  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showAllOption]);

  const handleOptionClick = (optionValue: string) => {
    handleChange(optionValue);
    setIsOpen(false);
  };

  if (showAllOption) {
    return (
      <div className="relative" ref={dropdownRef}>
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className={cn(
            'h-6 border-0 rounded px-3 text-sm w-auto min-w-[70px] cursor-pointer',
            value && getStatusColor(value),
            disabled && 'cursor-not-allowed opacity-50',
            className
          )}
          style={{ fontSize: '14px', fontWeight: 'normal' }}
          disabled={disabled}
        >
          {displayText()}
        </button>

        {isOpen && options.length > 0 && (
          <div className="absolute top-full left-0 mt-1 rounded-lg min-w-[80px] bg-white shadow-lg border border-gray-200 z-[99999]">
            {options.map((option) => (
              <div
                key={option.value}
                onClick={() => handleOptionClick(option.value)}
                className="h-6 text-sm cursor-pointer hover:bg-gray-50 px-2 py-1 first:rounded-t-lg last:rounded-b-lg"
              >
                {option.label}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <button
      type="button"
      onClick={handleToggle}
      className={cn(
        'h-6 border-0 rounded px-3 text-sm w-auto min-w-[70px] cursor-pointer',
        value && getStatusColor(value),
        disabled && 'cursor-not-allowed opacity-50',
        value === 'confirmed' && isSavedRecord && 'cursor-not-allowed',
        className
      )}
      style={{ fontSize: '14px', fontWeight: 'normal' }}
      disabled={disabled || (value === 'confirmed' && isSavedRecord)}
    >
      {displayText()}
    </button>
  );
}
