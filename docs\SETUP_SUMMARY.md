# ARCA EMR - Documentation Summary

Overview of the ARCA EMR project documentation.

## 📁 Documentation Files

### Core Setup & Guidelines

1. **[AUTHENTICATION_SETUP.md](./AUTHENTICATION_SETUP.md)** - Quick Azure AD B2C setup guide
2. **[AUTHENTICATION_SYSTEM.md](./AUTHENTICATION_SYSTEM.md)** - Technical authentication details
3. **[CODE_FORMAT_RULES.md](./CODE_FORMAT_RULES.md)** - Essential coding standards
4. **[FOLDER_STRUCTURE_GUIDE.md](./FOLDER_STRUCTURE_GUIDE.md)** - 4-layer feature organization
5. **[PRE_COMMIT_HOOKS.md](./PRE_COMMIT_HOOKS.md)** - Automatic code quality checks
6. **[MUI_THEME_SETUP.md](./MUI_THEME_SETUP.md)** - MUI theme configuration

### Core Architecture & Components

7. **[CORE_ARCHITECTURE.md](./CORE_ARCHITECTURE.md)** - System architecture and data flow
8. **[CORE_COMPONENTS.md](./CORE_COMPONENTS.md)** - Reusable UI component library
9. **[HOOKS_AND_UTILITIES.md](./HOOKS_AND_UTILITIES.md)** - Custom hooks and utility functions
10. **[DEPRECATED_COMPONENTS.md](./DEPRECATED_COMPONENTS.md)** - Components to avoid in new development

### Module-Specific

- **[lifestyle/LIFESTYLE_BASE.md](./lifestyle/LIFESTYLE_BASE.md)** - Lifestyle module guidelines

## 🛠️ Development Commands

```bash
npm run dev           # Start development server
npm run build         # Build for production
npm run lint          # Check code quality
npm run lint:fix      # Auto-fix issues
npm run format:all    # Format all files
```

## 🔧 Key Conventions

- **Feature Organization**: 4-layer structure (routes, components, state, queries)
- **Naming**: kebab-case folders, PascalCase components, camelCase variables
- **Import Order**: React → Next.js → External → @core → @/ → Relative → CSS
- **Legacy Note**: `emr/` folder is deprecated - use `views/` or `core/components/` instead

## 📋 New Developer Checklist

1. ✅ Run `npm install` to set up hooks
2. ✅ Read CODE_FORMAT_RULES.md
3. ✅ Follow 4-layer structure for new features
4. ✅ Use views/ or core/components/ instead of emr/
5. ✅ Test pre-commit hooks work
