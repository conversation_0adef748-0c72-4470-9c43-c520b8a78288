import React from 'react';

interface CustomBarCursorProps {
  x: number;
  y: number;
  width: number;
  height: number;
  barWidth: number;
  fill?: string;
  opacity?: number;
}

export const CustomBarCursor: React.FC<CustomBarCursorProps> = ({
  x,
  y,
  width,
  height,
  barWidth,
  fill = 'rgba(0, 0, 0, 0.15)',
  //   opacity = 0.15,
}) => {
  // Add 10px to the bar width for the cursor
  const cursorWidth = barWidth + 10;
  const cursorX = x + (width - cursorWidth) / 2;

  return (
    <rect
      x={cursorX}
      y={y}
      width={cursorWidth}
      height={height}
      fill={fill}
      //   fillOpacity={opacity}
    />
  );
};

export default CustomBarCursor;
