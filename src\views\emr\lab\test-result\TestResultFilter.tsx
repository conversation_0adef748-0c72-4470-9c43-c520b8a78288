import { memo } from 'react';

import { IoMdSearch } from 'react-icons/io';

import TextInput from '@core/components/text-input';

import { useTestStore } from '@/store/emr/lab/reports-store';
import { useTestReportFilterStore } from '@/store/emr/lab/test-report-filter';

import CommonSelectDropdown from '../packages/DepartmentDropdown';

import FilterBySelect from './FilterBySelect';

const TestResultFilter = () => {
  const { labDepartments } = useTestStore();
  const {
    dateFilter,
    customEndDate,
    customStartDate,
    department,
    searchText,
    onDateFilterChange,
    onDepartmentChange,
    onSearchTextChange,
  } = useTestReportFilterStore();

  return (
    <div className="w-full flex items-center flex-now-wrap">
      <div className="flex w-[80%] pr-1">
        <TextInput
          placeholder="Search Test"
          onChange={(e) => onSearchTextChange(e.target.value)}
          value={searchText}
          fieldClassName="!py-[0.42rem] !pl-3 text-xs placeholder:italic rounded-r-none border-[#ccc] border-r-0"
          endDecoration={<IoMdSearch />}
          className="w-3/5"
        />
        <div className="w-2/5">
          <CommonSelectDropdown
            name="department"
            value={department ?? ''}
            placeholder="Department"
            options={labDepartments}
            onChange={(value) =>
              onDepartmentChange(value === 'ALL' ? null : value)
            }
            className="w-full"
            sx={{ borderStartStartRadius: 0, borderEndStartRadius: 0 }}
          />
        </div>
      </div>
      <div className="w-[20%]">
        <FilterBySelect
          onChange={onDateFilterChange}
          value={dateFilter}
          selectedDateRange={[customStartDate, customEndDate]}
        />
      </div>
    </div>
  );
};

export default memo(TestResultFilter);
