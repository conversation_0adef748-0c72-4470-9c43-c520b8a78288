import { AiOutlineClose } from 'react-icons/ai';

import { PackageData } from '@/store/emr/lab/package-store';

import { ModalMode, PackageType, packageTypes } from '@/types/emr/lab';

type ModalHeaderProps = {
  activePackageType: PackageType;
  modalMode: ModalMode;
  selectedPackage: PackageData | null;
  onClose: () => void;
};

export const PackageModalHeader: React.FC<ModalHeaderProps> = ({
  activePackageType,

  onClose,
}) => (
  <div className="flex justify-between items-center py-2 px-4 ">
    <h2 className="text-md font-semibold">
      {activePackageType === packageTypes.DEPARTMENT
        ? 'Department Packages'
        : 'User Packages'}
    </h2>
    <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
      <AiOutlineClose size={16} color="black" />
    </button>
  </div>
);
