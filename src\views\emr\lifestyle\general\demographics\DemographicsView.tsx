import { memo } from 'react';

import { useFormContext } from 'react-hook-form';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { DemographicsForm } from '@/types/emr/lifestyle/demographics';

import StyledKeyValuePair from '../medical-history-addiction/components/StyledKeyValuePair';

import ContactsTable from './ContactsTable';

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const DemographicsView = () => {
  const { getValues } = useFormContext<DemographicsForm>();

  const demographics = getValues();

  return (
    <div className="flex flex-col h-full p-6 space-y-6">
      {/* First Row - Patient Name and CMCH ID */}
      <div className="grid grid-cols-6 gap-6 w-full">
        <StyledKeyValuePair label="Patient Name" value={demographics?.name} />
        <StyledKeyValuePair label="CMCH ID" value={demographics?.cmchId} />
      </div>

      {/* Second Row - Other fields */}
      <div className="grid grid-cols-6 gap-6 w-full">
        <StyledKeyValuePair
          label="Date of Birth"
          value={formatDate(demographics?.dob, DATE_DD_MM_YYYY_SLASH)}
        />

        <StyledKeyValuePair label="Age" value={demographics?.age} />
        <StyledKeyValuePair
          label="Gender"
          value={
            demographics?.sex
              ? demographics.sex.charAt(0).toUpperCase() +
                demographics.sex.slice(1)
              : '-'
          }
        />
        <StyledKeyValuePair
          label="Marital Status"
          value={demographics?.maritalStatus?.label}
        />
      </div>

      <div className="space-y-2">
        <div className="text-[14px] font-bold text-[#012436]">
          Contact Information
        </div>
        <ContactsTable contacts={demographics?.contacts} isViewMode={true} />
      </div>
    </div>
  );
};

export default memo(DemographicsView);
