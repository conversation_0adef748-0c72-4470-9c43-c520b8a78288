// Format boolean value to 'Yes' or 'No'
export const formatBooleanValue = (value: boolean | undefined): string => {
  return value !== undefined ? (value ? 'Yes' : 'No') : 'No';
};

export interface VitalSign {
  label: string;
  value: string | number;
  unit: string;
}

export interface ExaminationField {
  label: string;
  value: string;
}

export interface SystemicExamination {
  key: string;
  label: string;
  value: string;
}

export type HistoryField = {
  label: string;
  content?: string;
};

export const getVitalsData = (summary: any): VitalSign[] => {
  const vitals = summary?.vitals || {};
  return [
    {
      label: 'Heart Rate',
      value: vitals?.heartRate || vitals?.pulse || '',
      unit: '(bpm)',
    },
    {
      label: 'Systolic',
      value: vitals?.systolicPressure || '',
      unit: '(mmHg)',
    },
    {
      label: 'Diastolic',
      value: vitals?.diastolicPressure || '',
      unit: '(mmHg)',
    },
    {
      label: 'Respiratory Rate',
      value: vitals?.respiratoryRate || '',
      unit: '(/min)',
    },
    { label: 'SpO2', value: vitals?.spO2 || vitals?.spo2 || '', unit: '(%)' },
    { label: 'Temperature', value: vitals?.temperature || '', unit: '(°F)' },
  ];
};

export const getAnthropometryData = (summary: any): VitalSign[] => {
  const anthropometry = summary?.anthropometry || {};
  return [
    { label: 'Height', value: anthropometry?.height || '', unit: '(cm)' },
    { label: 'Weight', value: anthropometry?.weight || '', unit: '(Kg)' },
    { label: 'Body Mass Index', value: anthropometry?.bmi || '', unit: '' },
    {
      label: 'Waist Circumference',
      value: anthropometry?.waistCircumference || '',
      unit: '(cm)',
    },
  ];
};

export const getGeneralPhysicalExamData = (
  summary: any
): ExaminationField[] => {
  const generalExam = summary?.generalPhysicalExamination || {};
  return [
    { label: 'Pallor', value: formatBooleanValue(generalExam?.pallor) },
    { label: 'Icterus', value: formatBooleanValue(generalExam?.icterus) },
    { label: 'Cyanosis', value: formatBooleanValue(generalExam?.cyanosis) },
    { label: 'Clubbing', value: formatBooleanValue(generalExam?.clubbing) },
  ];
};

export const getSystemicExamData = (summary: any): SystemicExamination[] => {
  const systemicExam = summary?.systemicExamination || {};
  return [
    {
      key: 'neurological',
      label: 'Neurological Examination',
      value: systemicExam?.neurologicalExamination || 'Nil',
    },
    {
      key: 'cardiovascular',
      label: 'Cardiovascular Examination',
      value: systemicExam?.cardiovascularExamination || 'Nil',
    },
    {
      key: 'respiratory',
      label: 'Respiratory Examination',
      value: systemicExam?.respiratoryExamination || 'Nil',
    },
    {
      key: 'gastrointestinal',
      label: 'Abdomen Examination',
      value: systemicExam?.abdomenExamination || 'Nil',
    },
    {
      key: 'genitourinary',
      label: 'Rheumatological Examination',
      value: systemicExam?.rheumatologicalExamination || 'Nil',
    },
  ];
};

export const getHistoryFields = (summary: any): HistoryField[] => [
  { label: 'Presenting Complaints', content: summary?.presentingComplaints },
  {
    label: 'History of Presenting Illness',
    content: summary?.historyOfPresenting,
  },
  { label: 'Past Medical History', content: summary?.pastMedicalHistory },
  { label: 'Past Surgical History', content: summary?.pastSurgicalHistory },
  { label: 'Family History', content: summary?.familyHistory },
  { label: 'Addiction History', content: summary?.addictionHistory },
  { label: 'Diet History', content: summary?.dietHistory },
  {
    label: 'Physical Activity History',
    content: summary?.physicalActivityHistory,
  },
  { label: 'Stress History', content: summary?.stressHistory },
  { label: 'Sleep History', content: summary?.sleepHistory },
  {
    label: 'Current Medication History',
    content: summary?.currentMedicationHistory,
  },
];
