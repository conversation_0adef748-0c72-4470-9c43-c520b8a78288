import { useEffect } from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useNutritionDashboardStore } from '@/store/emr/lifestyle/nutrition-dashboard-store';

import {
  getNutritionAverage,
  getNutritionChart,
  getNutritionPercentageChart,
  getNutritionSummary,
} from '@/query/emr/lifestyle/nutrition-dashboard';

export const useNutritionDashboard = () => {
  const { patient } = useCurrentPatientStore();
  const {
    activeFilter,
    setActiveFilter,
    setAverageData,
    setChartData,
    setPercentageData,
    setMacroSummary,
    setMicroSummary,
    setLoading,
    setLoadingAverage,
    setLoadingCharts,
    setLoadingPercentage,
    setLoadingMacroSummary,
    setLoadingMicroSummary,
    setError,
    setErrorAverage,
    setErrorCharts,
    setErrorPercentage,
    setErrorMacroSummary,
    setErrorMicroSummary,
  } = useNutritionDashboardStore();

  const fetchAllData = async () => {
    if (!patient?.id) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch average data
      setLoadingAverage(true);
      setErrorAverage(null);
      try {
        const avgData = await getNutritionAverage(patient.id, activeFilter);
        setAverageData(avgData);
      } catch (error) {
        console.error('Error fetching average data:', error);
        setErrorAverage('Failed to fetch average data');
      } finally {
        setLoadingAverage(false);
      }

      // Fetch chart data for all metrics
      setLoadingCharts(true);
      setErrorCharts(null);
      try {
        const [
          caloriesChart,
          carbsChart,
          proteinChart,
          fatChart,
          fiberChart,
          microChart,
          additionalChart,
        ] = await Promise.all([
          getNutritionChart(patient.id, activeFilter, 'calories'),
          getNutritionChart(patient.id, activeFilter, 'carbs'),
          getNutritionChart(patient.id, activeFilter, 'protein'),
          getNutritionChart(patient.id, activeFilter, 'fat'),
          getNutritionChart(patient.id, activeFilter, 'fiber'),
          getNutritionChart(patient.id, activeFilter, 'micro'),
          getNutritionChart(patient.id, activeFilter, 'additional'),
        ]);

        setChartData({
          calories: caloriesChart,
          carbs: carbsChart,
          protein: proteinChart,
          fat: fatChart,
          fiber: fiberChart,
          micro: microChart,
          additional: additionalChart,
        });
      } catch (error) {
        console.error('Error fetching chart data:', error);
        setErrorCharts('Failed to fetch chart data');
      } finally {
        setLoadingCharts(false);
      }

      // Fetch percentage chart data
      setLoadingPercentage(true);
      setErrorPercentage(null);
      try {
        const percentageChart = await getNutritionPercentageChart(
          patient.id,
          activeFilter
        );
        setPercentageData(percentageChart);
      } catch (error) {
        console.error('Error fetching percentage chart data:', error);
        setErrorPercentage('Failed to fetch percentage chart data');
      } finally {
        setLoadingPercentage(false);
      }

      // Fetch summary data
      setLoadingMacroSummary(true);
      setLoadingMicroSummary(true);
      setErrorMacroSummary(null);
      setErrorMicroSummary(null);
      try {
        const [macroData, microData] = await Promise.all([
          getNutritionSummary(patient.id, 'macro', activeFilter),
          getNutritionSummary(patient.id, 'micro', activeFilter),
        ]);

        setMacroSummary(macroData);
        setMicroSummary(microData);
      } catch (error) {
        console.error('Error fetching summary data:', error);
        setErrorMacroSummary('Failed to fetch macro summary data');
        setErrorMicroSummary('Failed to fetch micro summary data');
      } finally {
        setLoadingMacroSummary(false);
        setLoadingMicroSummary(false);
      }
    } catch (error) {
      console.error('Error fetching nutrition data:', error);
      setError('Failed to fetch nutrition data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, [patient?.id, activeFilter]);

  return {
    activeFilter,
    setActiveFilter,
    fetchAllData,
  };
};
