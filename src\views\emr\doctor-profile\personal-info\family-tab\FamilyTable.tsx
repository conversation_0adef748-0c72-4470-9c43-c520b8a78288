import React, { FC, memo, useMemo } from 'react';

import { Control, FieldArrayWithId, UseFormRegister } from 'react-hook-form';

import dayjs from 'dayjs';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import {
  aadharInput,
  aadharValidation,
  preventNonAlphabeticInput,
} from '@/utils/validation';

import {
  familyHeaders,
  familyStatusOptions,
} from '@/constants/emr/doctor-profile/personal-info';

import Table from '@/core/components/table';
import { Row } from '@/core/components/table/types';
import {
  actionButtonCellProps,
  getFileCellProps,
  getInputCellProps,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import ActionButton from '../shared/ActionButton';
import TableDatePicker from '../shared/TableDatePicker';
import TableFilePicker from '../shared/TableFilePicker';
import TableSelect from '../shared/TableSelect';
import TableTextarea from '../shared/TableTextarea';

import { FormData } from '.';

type Props = {
  fields: FieldArrayWithId<FormData, 'family', 'id'>[];
  control: Control<FormData>;
  handleItemEdit: (index: number) => () => void;
  handleOnDelete: (itemToDelete: ItemToDelete) => void;
  itemToEdit: number | null;
  register: UseFormRegister<FormData>;
};

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const FamilyTable: FC<Props> = ({
  fields,
  control,
  itemToEdit,
  handleItemEdit,
  handleOnDelete,
}) => {
  const handleAadharKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    aadharInput(e as unknown as React.KeyboardEvent<HTMLInputElement>);
  };

  const rows = useMemo<Row[]>(
    () =>
      fields?.map((field, index) => ({
        key: field.id,
        name: {
          value: (
            <TableTextarea
              name={`family.${index}.name`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={preventNonAlphabeticInput}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        relation: {
          value: (
            <TableTextarea
              name={`family.${index}.relation`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={preventNonAlphabeticInput}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        dependent: {
          value: (
            <TableTextarea
              name={`family.${index}.dependent`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={preventNonAlphabeticInput}
            />
          ),
          cellProps: {
            ...getInputCellProps(itemToEdit !== index),
            sx: {
              ...getInputCellProps(itemToEdit !== index).sx,
              maxWidth: 60,
            },
          },
        },
        dob: {
          value: (
            <TableDatePicker
              name={`family.${index}.dob`}
              control={control}
              disabled={itemToEdit !== index}
              format={DATE_DD_MM_YYYY_SLASH}
              maxDate={dayjs()}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        aadharNumber: {
          value: (
            <TableTextarea
              name={`family.${index}.aadharNumber`}
              control={control}
              rules={{
                validate: (value) => {
                  const val = value as string | undefined;
                  if (!val) return true;
                  return aadharValidation(val) || 'Invalid Aadhar number';
                },
              }}
              disabled={itemToEdit !== index}
              onKeyDown={handleAadharKeyDown}
              placeholder="123412341234"
            />
          ),
          cellProps: {
            ...getInputCellProps(itemToEdit !== index),
            sx: {
              ...getInputCellProps(itemToEdit !== index).sx,
              minWidth: 150,
            },
          },
        },
        occupation: {
          value: (
            <TableTextarea
              name={`family.${index}.occupation`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={preventNonAlphabeticInput}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        documents: {
          value: (
            <TableFilePicker
              name={`family.${index}.documents`}
              control={control}
              disabled={itemToEdit !== index}
              imageUrl={field?.documents}
            />
          ),
          cellProps: getFileCellProps(itemToEdit !== index),
        },
        status: {
          value: (
            <TableSelect
              name={`family.${index}.status`}
              control={control}
              disabled={itemToEdit !== index}
              options={familyStatusOptions}
            />
          ),
          cellProps: {
            ...getInputCellProps(itemToEdit !== index),
            sx: {
              ...getInputCellProps(itemToEdit !== index).sx,
              minWidth: 110,
            },
          },
        },
        edit: {
          value:
            itemToEdit !== index ? (
              <ActionButton actionFor="edit" onClick={handleItemEdit(index)} />
            ) : (
              <></>
            ),
          cellProps: actionButtonCellProps,
        },
        delete: {
          value: (
            <ActionButton
              actionFor="delete"
              onClick={() => handleOnDelete({ index, uuId: field.uuId })}
            />
          ),
          cellProps: actionButtonCellProps,
        },
      })),
    [fields, control, handleOnDelete, itemToEdit, handleItemEdit]
  );

  return (
    <Table
      headers={familyHeaders}
      rows={rows}
      tableContainerProps={{
        sx: {
          '& tbody td': {
            minHeight: 30,
          },
        },
      }}
    />
  );
};

export default memo(FamilyTable);
