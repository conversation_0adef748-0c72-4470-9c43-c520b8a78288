import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

interface Medicine {
  id?: string;
  genericName?: string;
  GenericName?: string;
  brandName?: string;
  BrandName?: string;
  strength?: string;
  Strength?: string;
  nosPerMlOrGm?: string;
  Measure?: string;
  frequency?: string;
  duration?: string;
  quantity?: string;
  route?: string;
  instructions?: string;
  cost?: string;
}

interface PatientData {
  id?: string;
  name?: string;
  age?: string | number;
  mobile?: string;
  gender?: string;
}

interface DoctorData {
  name: string;
  qualification?: string;
  registration?: string;
}

interface OrganizationData {
  name?: string;
  address?: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
}

interface PrescriptionData {
  id: string;
  doctor: string | DoctorData;
  created_on?: string;
  medicines?: Medicine[];
  patient?: PatientData;
  organization?: OrganizationData;
}

export const getPrescriptionHtml = (
  data: PrescriptionData,
  totalAmount: number,
  currentPage: number = 1,
  totalPages: number = 1,
  digitalSignature?: string,
  organizationLogo?: string,
  letterHeadDetails?: string
): string => {
  const medicines = data.medicines || [];

  return `
    <div class="print-container">
      <div class="header">
        <div class="doctor-info">
          <p class="doctor-name">${typeof data.doctor === 'string' ? data.doctor : (data.doctor as DoctorData)?.name}</p>
          ${
            letterHeadDetails
              ? letterHeadDetails
                  .split('\n')
                  .map(
                    (line) =>
                      `<p class=\"doctor-name\" style=\"margin: 0;\">${line.trim()}</p>`
                  )
                  .join('')
              : ''
          }
        </div>
        <div class="hospital-info">
          <div class="logo-section">
            ${organizationLogo ? `<img src=\"${organizationLogo}\" alt=\"Organization Logo\" style=\"max-width: 120px; max-height: 60px; border-radius: 4px;\" />` : '<div class="logo-placeholder"></div>'}
          </div>
          <div class="hospital-details" style="text-align: right;">
            <p style="margin: 0; font-size: 12px; line-height: 1.4; color: #001926;">${data.organization?.name || ''}</p>
            ${data.organization?.address ? `<p style=\"margin: 4px 0 0 0; font-size: 12px; line-height: 1.4; color: #001926;\">${data.organization.address}</p>` : ''}
            ${data.organization?.phone ? `<p style=\"margin: 4px 0 0 0; font-size: 12px; line-height: 1.4; color: #001926;\">${data.organization.phone}</p>` : ''}
            ${data.organization?.email ? `<p style=\"margin: 4px 0 0 0; font-size: 12px; line-height: 1.4; color: #001926;\">${data.organization.email}</p>` : ''}
          </div>
        </div>
      </div>
      
      <div class="prescription-title">Prescription</div>
      
      <div class="patient-info">
        <span><strong>Patient Name:</strong> ${data.patient?.name || 'N/A'}</span>
        <span><strong>Patient ID:</strong> ${data.patient?.id || data.id?.split('-')[0] || 'N/A'}</span>
        <span><strong>Age:</strong> ${data.patient?.age || 'N/A'}</span>
        <span><strong>Mobile:</strong> ${data.patient?.mobile || 'N/A'}</span>
        <span><strong>Date:</strong> ${formatDate(data.created_on || new Date().toISOString(), DateFormats.DATE_DD_MM_YYYY_SLASH)}</span>
        ${data.patient?.gender ? `<span><strong>Gender:</strong> ${data.patient.gender}</span>` : ''}
      </div>
      
      <table class="medicines-table">
        <thead>
          <tr>
            <th style="width: 4%;">No</th>
            <th style="width: 10%;">Drug Name</th>
            <th style="width: 12%;">Generic Name</th>
            <th style="width: 12%;">Brand Name</th>
            <th style="width: 8%;">Strength</th>
            <th style="width: 8%;">Nos/ml/gm</th>
            <th style="width: 8%;">Frequency</th>
            <th style="width: 8%;">Duration</th>
            <th style="width: 5%;">QTY</th>
            <th style="width: 7%;">Route</th>
            <th style="width: 12%;">Instructions</th>
            <th style="width: 8%;">Cost</th>
          </tr>
        </thead>
        <tbody>
          ${
            medicines.length > 0
              ? medicines
                  .map(
                    (med, index) => `
            <tr>
              <td>${String(index + 1).padStart(2, '0')}</td>
              <td>${med.genericName || med.GenericName || 'N/A'}</td>
              <td>${med.genericName || med.GenericName || 'N/A'}</td>
              <td>${med.brandName || med.BrandName || 'N/A'}</td>
              <td>${med.strength || med.Strength || 'N/A'}</td>
              <td>${med.nosPerMlOrGm || med.Measure || 'N/A'}</td>
              <td>${med.frequency || 'N/A'}</td>
              <td>${med.duration || 'N/A'}</td>
              <td>${med.quantity || 'N/A'}</td>
              <td>${med.route || 'N/A'}</td>
              <td>${med.instructions || 'N/A'}</td>
              <td>${(parseFloat(med.cost || '0') * parseFloat(med.quantity || '1')).toFixed(2)}</td>
            </tr>
          `
                  )
                  .join('')
              : `
            <tr>
              <td colspan="12" style="text-align: center; padding: 20px;">No medicines found</td>
            </tr>
          `
          }
        </tbody>
      </table>
      
      <div class="divider"></div>
      
      <div class="signature-row">
        <div class="signature-container">
          ${
            digitalSignature
              ? `<img src="${digitalSignature}" alt="Digital Signature" style="width: 100px; height: 41px; object-fit: contain;" />`
              : '<div class="signature-box"></div>'
          }
        </div>
        <div class="page-number">${String(currentPage).padStart(2, '0')} out of ${String(totalPages).padStart(2, '0')}</div>
      </div>
      
      <div class="divider"></div>
      
      <div class="total-amount">Total Amount : <span class="amount">${totalAmount.toFixed(2)}</span></div>
      
      <div class="divider"></div>
      
      <div class="return-policy">
        Medicines returned after 15 days won't be taken back.
      </div>
      
      <div class="powered-by">
        Powered By <img src="/images/auth-logo.png" alt="Arca Logo" class="arca-logo" onerror="this.style.display='none'; this.nextElementSibling?.classList.remove('hidden')" />
        <span class="arca-logo-text hidden">ARCA</span>
      </div>
    </div>
  `;
};

export const prescriptionStyles = `
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: Arial, sans-serif;
    font-size: 11px;
    line-height: 1.3;
    color: #000;
    background: white;
    padding: 15px;
  }
  
  .print-container {
    width: 100%;
    max-width: 210mm;
    margin: 0 auto;
    background: white;
    border: 1px solid #ccc;
    padding: 20px;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 15px;
  }
  
  .doctor-info {
    text-align: left;
    flex: 1;
  }
  
  .doctor-info .doctor-name {
    font-family: Archivo, Arial, sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 150%;
    letter-spacing: -2.2%;
    color: #001926;
    margin-bottom: 5px;
  }
  
  .doctor-info p {
    margin-bottom: 3px;
    font-family: Archivo, Arial, sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 150%;
    letter-spacing: -2.2%;
    color: #001926;
  }
  
  .hospital-info {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    flex: 1;
    justify-content: flex-end;
  }
  
  .logo-section .logo-placeholder {
    width: 80px;
    height: 40px;
    background-color: #D9D9D9;
    border-radius: 4px;
  }
  
  .hospital-details {
    text-align: right;
  }
  
  .hospital-name {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  
  .hospital-details p {
    margin-bottom: 2px;
    font-size: 10px;
  }
  
  .prescription-title {
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    color: #012436;
    margin: 20px 0;
  }
  
  .patient-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 10px 0;
    width: 100%;
  }
  
  .patient-info span {
    flex: 1;
    text-align: left;
    padding: 0 5px;
  }
  
  .patient-info span {
    font-size: 12px;
    color: #012436;
    white-space: nowrap;
    margin-right: 10px;
  }
  
  .patient-details {
    flex: 1;
  }
  
  .patient-row {
    display: flex;
    gap: 30px;
    margin-bottom: 8px;
  }
  
  .patient-row span {
    font-size: 12px;
    min-width: 150px;
  }
  
  .date-section {
    text-align: right;
  }
  
  .date-section span {
    font-size: 12px;
  }
  
  .medicines-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 10px;
    border: 1px solid #C4CCD3;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .medicines-table thead th {
    background-color: #64707D;
    color: white;
    font-weight: 500;
    text-align: center;
    padding: 8px 4px;
    border-right: 1px solid #e0e0e0;
    position: relative;
  }
  
  .medicines-table thead th:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 60%;
    background-color: #e0e0e0;
    transform: translateY(-50%);
  }
  
  .medicines-table thead th:last-child {
    border-right: none;
  }
  
  .medicines-table tbody tr:nth-child(odd) td {
    background-color: #DAE1E7;
  }
  
  .medicines-table tbody tr:nth-child(even) td {
    background-color: #ffffff;
  }
  
  .medicines-table td {
    padding: 8px 4px;
    text-align: center;
    vertical-align: middle;
    border-right: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    color: #000000;
    position: relative;
  }
  
  .medicines-table td:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 60%;
    background-color: #e0e0e0;
    transform: translateY(-50%);
  }
  
  .medicines-table td:last-child {
    border-right: none;
  }
  
  .medicines-table tbody tr:last-child td {
    border-bottom: none;
  }
  
  .footer-section {
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin: 15px 0;
  }
  
  .divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 10px 0;
  }
  
  .signature-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin: 15px 0;
    color: #C2CDD6
  }
  
  .signature-container {
    display: flex;
    flex-direction: column;
  }
  
  .signature-box {
    width: 150px;
    height: 40px;
    background-color: #C2CDD6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
  }
  
  .powered-by {
    text-align: center;
    margin: 30px auto 0;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    position: relative;
    width: 100%;
  }
  
  .page-number {
    font-size: 12px;
    color: #666;
    margin-top: 20px;
    margin-top: 10px;
  }
  
  .total-amount {
    text-align: right;
    font-weight: bold;
    margin: 10px 0;
    font-size: 14px;
  }
  
  .total-amount .amount {
    color: #64707D;
  }
  
  .return-policy {
    text-align: center;
    margin: 15px 0;
    font-size: 11px;
    color: #666;
  }
  
  .arca-logo {
    height: 16px;
    width: auto;
    margin-left: 5px;
  }
  
  .arca-logo-text {
    font-weight: bold;
    color: #000;
    font-size: 14px;
    margin-left: 5px;
    letter-spacing: 0.5px;
  }
  
  .hidden {
    display: none;
  }
  
  .page-number {
    font-size: 12px;
    color: #666;
    margin-top: 20px;
  @media print {
    body {
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
      padding: 0;
    }
    
    .medicines-table th::after,
    .medicines-table td::after {
      display: none !important;
    }
    
    .print-container {
      border: none;
      padding: 15px;
      margin: 0;
    }
    
    .medicines-table {
      border: 1px solid #ddd !important;
      border-radius: 4px !important;
      overflow: hidden !important;
    }
    
    .medicines-table thead th {
      background-color: #64707D !important;
      color: white !important;
      border-right: 1px solid white !important;
    }
    
    .medicines-table thead th:last-child {
      border-right: none !important;
    }
    
    .medicines-table tbody tr:nth-child(odd) td {
      background-color: #DAE1E7 !important;
    }
    
    .medicines-table tbody tr:nth-child(even) td {
      background-color: #ffffff !important;
    }
    
    .medicines-table td {
      border-right: 1px solid #e0e0e0 !important;
      border-bottom: 1px solid #e0e0e0 !important;
      color: #000000 !important;
    }
    
    .medicines-table td:last-child {
      border-right: none !important;
    }
    
    @page {
      size: A4;
      margin: 0.5in;
    }
  }
`;
