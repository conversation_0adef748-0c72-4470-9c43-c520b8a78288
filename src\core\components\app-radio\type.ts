import { FieldError } from 'react-hook-form';

import { FormControlProps, RadioGroupProps } from '@mui/material';

export interface RadioOption {
  value: string;
  label: React.ReactNode;
}

export type AppRadioProps = RadioGroupProps & {
  name: string;
  options: RadioOption[];
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  errors?: FieldError;
  required?: boolean;
  disabled?: boolean;
  label?: string;
  formControlProps?: FormControlProps;
};
