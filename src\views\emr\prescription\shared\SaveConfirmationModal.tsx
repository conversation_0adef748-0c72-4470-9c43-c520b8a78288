import { Box } from '@mui/material';

import CustomModal from '@/core/components/modal';
import PrimaryButton from '@/core/components/primary-button';

interface ConfirmationModalProps {
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
  title?: string | JSX.Element;
  confirmText?: string;
  cancelText?: string;
  width?: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  open,
  onConfirm,
  onCancel,
  isLoading = false,
  title = 'Confirm',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  width = '20vw',
}) => {
  return (
    <CustomModal
      open={open}
      onClose={onCancel}
      title={title}
      width={width}
      minHeight="100px"
      maxHeight="25vh"
      titleBoxSx={{ textAlign: 'center' }}
      titleTypographySx={{ fontSize: '18px', fontWeight: 500 }}
      showDivider={false}
      contentSx={{ mb: 0 }}
      content={
        <Box pt={2} pb={0} textAlign="center">
          <Box display="flex" flexDirection="column" gap={2}>
            <PrimaryButton
              type="button"
              onClick={onCancel}
              className="capitalize text-black bg-[#C2CDD6] text-md h-8 w-full"
            >
              {cancelText}
            </PrimaryButton>
            <PrimaryButton
              type="button"
              onClick={onConfirm}
              className="capitalize text-md h-8 w-full"
              isLoading={isLoading}
            >
              {confirmText}
            </PrimaryButton>
          </Box>
        </Box>
      }
    />
  );
};

export default ConfirmationModal;
