import { useState } from 'react';

import { FaEdit } from 'react-icons/fa';

export default function NotesPanel() {
  const [notes] = useState<{ content: string; date: string }[]>([
    { content: 'Next visit on 12/05/2024', date: '12/05/2024' },
  ]);

  return (
    <div className="p-3">
      <div className="flex items-center justify-between mb-3">
        <span className="font-medium text-sm text-gray-700">Notes</span>
        <button className="text-gray-400 hover:text-gray-600 transition-colors">
          <FaEdit className="w-4 h-4" />
        </button>
      </div>

      <div className="space-y-2">
        {notes.map((note, index) => (
          <div key={index} className="text-xs text-gray-600">
            • {note.content}
          </div>
        ))}
      </div>
    </div>
  );
}
