import React, { FC, memo } from 'react';

import { Box, Button, ButtonProps } from '@mui/material';

import Loading from '@/lib/common/loading';

import { LoadingButton } from './styled-component';

type Props = ButtonProps;

const AppButton: FC<Props> = ({
  loading = false,
  children,
  onClick = () => {},
  disabled,
  ...props
}) => {
  if (loading) {
    const { startIcon: _startIcon, endIcon: _endIcon, ...rest } = props;
    return (
      <LoadingButton disableRipple {...rest}>
        <Box className="invisible">{children}</Box>
        <Box className="loading-container">
          <Loading />
        </Box>
      </LoadingButton>
    );
  }

  if (disabled) {
    return (
      <Button disableRipple {...props}>
        {children}
      </Button>
    );
  }

  return (
    <Button onClick={onClick} {...props}>
      {children}
    </Button>
  );
};

export default memo(AppButton);
