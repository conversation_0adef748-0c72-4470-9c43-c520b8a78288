import { darken, lighten } from '@mui/material/styles';
import createPalette, {
  PaletteOptions,
} from '@mui/material/styles/createPalette';

import {
  accentColors,
  chartColors,
  darkColors,
  lightColors,
} from '@core/theme/colors';

const darkPaletteColor: PaletteOptions = {
  mode: 'dark',
  primary: {
    main: darkColors.primary,
    contrastText: darkColors.primaryForeground,
    light: lighten(darkColors.primary, 0.5),
    dark: darken(darkColors.primary, 0.5),
  },
  secondary: {
    main: darkColors.secondary,
    contrastText: darkColors.secondaryForeground,
    dark: darken(darkColors.secondary, 0.5),
    light: lighten(darkColors.secondary, 0.5),
  },
  error: {
    main: darkColors.destructive,
    contrastText: darkColors.destructiveForeground,
    light: lighten(darkColors.destructive, 0.5),
    dark: darken(darkColors.destructive, 0.5),
  },
  background: {
    default: darkColors.background,
    paper: darkColors.card,
  },
  text: {
    primary: darkColors.foreground,
    secondary: darkColors.mutedForeground,
    disabled: lightColors.disabledText,
  },
  divider: darkColors.border,
  // Custom colors
  accent: {
    pink: accentColors.pink,
    green: accentColors.green,
    main: darkColors.accent,
    contrastText: darkColors.accentForeground,
  },
  chart: chartColors.dark,
};

export const darkPalette = createPalette(darkPaletteColor);
