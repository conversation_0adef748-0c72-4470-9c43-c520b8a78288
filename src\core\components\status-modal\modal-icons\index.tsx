import React from 'react';

import { ModalType } from '@/store/status-modal-store';

// Success Icon (existing)
export const SuccessIcon = () => (
  <svg
    width="99"
    height="99"
    viewBox="0 0 99 99"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="49.3333" cy="49.3333" r="49.3333" fill="#06C6A7" />
    <path
      d="M26.0002 51.4074L40.1541 65.3333L72.0002 34"
      stroke="white"
      strokeWidth="8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Error Icon
export const ErrorIcon = () => (
  <svg
    width="99"
    height="99"
    viewBox="0 0 99 99"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="49.3333" cy="49.3333" r="49.3333" fill="#EF4444" />
    <path
      d="M35 35L63.6667 63.6667M63.6667 35L35 63.6667"
      stroke="white"
      strokeWidth="8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Warning Icon
export const WarningIcon = () => (
  <svg
    width="99"
    height="99"
    viewBox="0 0 99 99"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="49.3333" cy="49.3333" r="49.3333" fill="#F59E0B" />
    <path
      d="M49.3333 35V55.6667"
      stroke="white"
      strokeWidth="8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle cx="49.3333" cy="70" r="4" fill="white" />
  </svg>
);

// Info Icon
export const InfoIcon = () => (
  <svg
    width="99"
    height="99"
    viewBox="0 0 99 99"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="49.3333" cy="49.3333" r="49.3333" fill="#3B82F6" />
    <path
      d="M49.3333 63.6667V49.3333"
      stroke="white"
      strokeWidth="8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle cx="49.3333" cy="35" r="4" fill="white" />
  </svg>
);

// Confirmation Icon
export const ConfirmationIcon = () => (
  <svg
    width="99"
    height="99"
    viewBox="0 0 99 99"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="49.3333" cy="49.3333" r="49.3333" fill="#6B7280" />
    <path
      d="M49.3333 35V55.6667"
      stroke="white"
      strokeWidth="8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle cx="49.3333" cy="70" r="4" fill="white" />
  </svg>
);

// Icon mapper
const iconMap: Record<ModalType, React.ComponentType> = {
  success: SuccessIcon,
  error: ErrorIcon,
  warning: WarningIcon,
  info: InfoIcon,
  confirmation: ConfirmationIcon,
};

interface ModalIconProps {
  type: ModalType;
  className?: string;
}

export const ModalIcon: React.FC<ModalIconProps> = ({ type, className }) => {
  const IconComponent = iconMap[type];

  if (!IconComponent) {
    return null;
  }

  return (
    <div className={className}>
      <IconComponent />
    </div>
  );
};
