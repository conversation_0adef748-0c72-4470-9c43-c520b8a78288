import { Header } from '@/core/components/table/types';
import { testReportTabs } from '@/types/emr/lab';

const { NEW_TEST, TEST_RESULTS, ORDER_HISTORY } = testReportTabs;

export type TestTabKey =
  | typeof NEW_TEST
  | typeof TEST_RESULTS
  | typeof ORDER_HISTORY;

type TabItem = {
  key: TestTabKey;
  label: string;
};

export const labTestTabs: TabItem[] = [
  { key: TEST_RESULTS, label: 'Test Results' },
  { key: NEW_TEST, label: 'New Test' },
  { key: ORDER_HISTORY, label: 'Order History' },
];

export const reportHeaders: Header[] = [
  { key: 'no', header: 'No' },
  {
    key: 'testName',
    header: 'Test Name',
    cellProps: { sx: { minWidth: 140 } },
  },
  {
    key: 'quantity',
    header: 'Qty',
    cellProps: { sx: { minWidth: 60 } },
  },
  { key: 'instructions', header: 'Instructions' },
  { key: 'cost', header: 'Cost' },
  {
    key: 'toBeDoneBy',
    header: 'To be done by',
    cellProps: { sx: { minWidth: 120 } },
  },
  { key: 'close', header: '' },
];

export const editableKeys = [
  'instructions',
  'quantity',
  'toBeDoneBy',
  'cost',
  'close',
];

export const commonButtonProps = {
  type: 'button' as const,
  className:
    'flex gap-2 justify-center align-center capitalize px-3 py-1 text-xs leading-tight h-7 text-center whitespace-normal break-words min-w-[110px]',
};

export const tableContainerSx = {
  maxHeight: '100%',
  '& tbody tr': {
    height: 30,
  },
  '& tbody tr:nth-of-type(odd)': {
    backgroundColor: '#ffffff',
  },
  '& tbody tr:nth-of-type(even)': {
    backgroundColor: '#DAE1E7',
  },
  '& th': {
    whiteSpace: 'nowrap',
    fontSize: '0.875rem',
    padding: '8px 16px',
    borderLeft: '1px solid #64707D',
    textAlign: 'center',
  },
  '& td': {
    whiteSpace: 'nowrap',
    fontSize: '0.875rem',
    padding: '8px 16px',
    textAlign: 'center',
  },
};

export const addTestHeaders: Header[] = [
  {
    key: 'testName',
    header: 'Test Name',
    cellProps: { sx: { minWidth: 140, textAlign: 'center' } },
  },
  { key: 'close', header: '' },
];

export const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 900,
  bgcolor: 'background.paper',
  boxShadow: 24,
  borderRadius: 1,
  display: 'flex',
  flexDirection: 'column',
  height: '95vh',
  maxHeight: '95vh',
};

export const MAX_ITEMS_PER_COLUMN = 15;

export const CELL_TEXT_PROPS = {
  sx: { minWidth: 120, padding: '2px 8px !important', textAlign: 'center' },
};

export const ACTION_CELL_PROPS = {
  sx: { width: 60, textAlign: 'center', padding: '0px 4px !important' },
};

export const getPackageTableHeaders = (
  modalMode: string,
  testItemCount: number
) => {
  const useTwoColumns = testItemCount > MAX_ITEMS_PER_COLUMN;

  if (modalMode === 'detail') {
    return useTwoColumns
      ? [
          { key: 'checkbox1', header: '', cellProps: ACTION_CELL_PROPS },
          { key: 'testName1', header: 'Test Name', cellProps: CELL_TEXT_PROPS },
          { key: 'checkbox2', header: '', cellProps: ACTION_CELL_PROPS },
          { key: 'testName2', header: 'Test Name', cellProps: CELL_TEXT_PROPS },
        ]
      : [
          { key: 'checkbox1', header: '', cellProps: ACTION_CELL_PROPS },
          { key: 'testName1', header: 'Test Name', cellProps: CELL_TEXT_PROPS },
        ];
  } else {
    return useTwoColumns
      ? [
          { key: 'testName1', header: 'Test Name', cellProps: CELL_TEXT_PROPS },
          { key: 'close1', header: '', cellProps: ACTION_CELL_PROPS },
          { key: 'testName2', header: 'Test Name', cellProps: CELL_TEXT_PROPS },
          { key: 'close2', header: '', cellProps: ACTION_CELL_PROPS },
        ]
      : [
          { key: 'testName1', header: 'Test Name', cellProps: CELL_TEXT_PROPS },
          { key: 'close1', header: '', cellProps: ACTION_CELL_PROPS },
        ];
  }
};

export const textFieldSx = {
  '& .MuiOutlinedInput-root': {
    height: 30,
    '& fieldset': {
      borderColor: 'gray',
    },
    '&:hover fieldset': {
      borderColor: 'gray',
    },
    '&.Mui-focused fieldset': {
      borderColor: 'gray',
    },
  },
  '& .MuiOutlinedInput-input': {
    color: 'black',
    fontSize: '12px',
  },
};

export const testResultHeaders: Header[] = [
  { key: 'date', header: 'Date', sortFieldName: 'created_on' },
  {
    key: 'department',
    header: 'Department',
    cellProps: { sx: { minWidth: 150 } },
    sortFieldName: 'department',
  },
  { key: 'testName', header: 'Test Name', sortFieldName: 'testName' },
  { key: 'result', header: 'Result' },
  { key: 'reference', header: 'Reference' },
  { key: 'status', header: 'Status' },
  { key: 'expand', header: '' },
];

export const printTableHeader: Header[] = [
  {
    key: 'department',
    header: 'Department',
    cellProps: { sx: { minWidth: 150 } },
  },
  { key: 'testName', header: 'Test Name' },
  { key: 'result', header: 'Result' },
  { key: 'reference', header: 'Reference' },
];
