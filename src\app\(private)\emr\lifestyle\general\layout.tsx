'use client';

import { memo } from 'react';

import { usePathname, useRouter } from 'next/navigation';

import { useDemographicsStore } from '@/store/emr/lifestyle/general/demographics';

import { routes } from '@/constants/routes';

import PageTab, { TabItem } from '@/views/emr/lifestyle/shared/page-tab';
import PageTitle from '@/views/emr/lifestyle/shared/PageTitle';

const GeneralLayout = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { activeTab, setActiveTab } = useDemographicsStore();

  // Define tabs with proper typing
  const tabs: (TabItem & { onClick?: () => void })[] = [
    {
      label: 'Demographics',
      path: routes.EMR_LIFESTYLE_GENERAL,
      onClick: () => {
        router.push(routes.EMR_LIFESTYLE_GENERAL);
        // Reset view mode when switching to this tab
        // The actual view mode state is managed within each tab component
      },
    },
    {
      label: 'Medical History & Addiction',
      path: routes.EMR_LIFESTYLE_GENERAL_MEDICAL_HISTORY,
      onClick: () => {
        router.push(routes.EMR_LIFESTYLE_GENERAL_MEDICAL_HISTORY);
        // Reset view mode when switching to this tab
        // The actual view mode state is managed within each tab component
      },
    },
  ];

  // Handle tab click
  const handleTabClick = (tab: TabItem) => {
    // Update the active tab in the store
    setActiveTab(tab.label);

    // Call the tab's onClick handler if it exists
    const fullTab = tabs.find((t) => t.path === tab.path);
    if (fullTab?.onClick) {
      fullTab.onClick();
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Page Title */}
      <PageTitle title="Consultation Timeline" hideFilterBy />

      {/* Tab Navigation */}
      <PageTab
        tabs={tabs.map(({ onClick, ...tab }) => tab)}
        onTabClick={handleTabClick}
      />

      {/* Content */}
      <div className="flex-1 overflow-hidden">{children}</div>
    </div>
  );
};

export default memo(GeneralLayout);
