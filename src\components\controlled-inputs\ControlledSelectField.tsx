import React from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';
import { ActionMeta } from 'react-select';

import AppSelectField from '@core/components/app-select';
import { AppSelectFieldProps } from '@core/components/app-select/type';

type Props<T extends FieldValues, O> = Omit<
  AppSelectFieldProps<O>,
  'value' | 'onChange'
> & {
  name: Path<T>;
  control: Control<T>;
  onChange?: (value: O | O[], actionMeta?: ActionMeta<O>) => void;
};

const ControlledSelectField = <
  T extends FieldValues,
  O extends Record<string, any>,
>({
  name,
  control,
  onChange,
  ...props
}: Props<T, O>) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <AppSelectField<O>
          {...props}
          onChange={(value: O | O[], actionMeta?: ActionMeta<O>) => {
            field.onChange(value);
            onChange && onChange(value, actionMeta);
          }}
          value={field.value}
          error={!!fieldState.error?.message}
          helperText={fieldState.error?.message}
        />
      )}
    />
  );
};

export default ControlledSelectField;
