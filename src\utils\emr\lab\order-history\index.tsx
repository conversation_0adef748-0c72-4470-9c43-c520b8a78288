import React from 'react';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { Row } from '@/core/components/table/types';
import { OrderHistoryItem } from '@/types/emr/lab';

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const getTableBgColor = (i: number) => {
  return i % 2 === 0 ? '#ffffff' : '#DAE1E7';
};

const getDummyRow = (i: number) => ({
  value: undefined,
  returnNullForEmpty: true,
  cellProps: { sx: { bgcolor: getTableBgColor(i) } },
});

type GetOrderHistoryTableRowsProps = {
  orders: OrderHistoryItem[];
  selectedItems: string[];
  onSelectItem: (id: string) => void;
  onSelectGroup: (groupId: string, testIds: string[]) => void;
};

const groupOrdersByResultId = (orders: OrderHistoryItem[]) => {
  const groups: Record<string, OrderHistoryItem[]> = {};

  orders.forEach((order) => {
    const actualId = order.id.split('~')[0];
    if (!groups[actualId]) {
      groups[actualId] = [];
    }
    groups[actualId].push(order);
  });

  return groups;
};

export const getOrderHistoryTableRows = ({
  orders,
  selectedItems,
  onSelectItem,
  onSelectGroup,
}: GetOrderHistoryTableRowsProps): Row[] => {
  if (!orders?.length) return [];

  const rows: Row[] = [];
  const groupedOrders = groupOrdersByResultId(orders);

  let groupIndex = 0;

  Object.entries(groupedOrders).forEach(([actualId, groupOrders]) => {
    const totalTestsInGroup = groupOrders.length;
    const firstOrder = groupOrders[0];

    const hasUniformStatus = groupOrders.every(
      (order) => order.status === firstOrder.status
    );
    const isGroupSelected = groupOrders.every((order) =>
      selectedItems.includes(order.id)
    );

    groupOrders.forEach((order, testIndex) => {
      const isFirstInGroup = testIndex === 0;

      const row: Row = {
        checkbox: isFirstInGroup
          ? {
              value: (
                <input
                  type="checkbox"
                  className="custom-checkbox"
                  checked={isGroupSelected}
                  onChange={() =>
                    onSelectGroup(
                      actualId,
                      groupOrders.map((o) => o.id)
                    )
                  }
                />
              ),
              cellProps: {
                rowSpan: totalTestsInGroup,
                sx: { bgcolor: getTableBgColor(groupIndex) },
              },
            }
          : getDummyRow(groupIndex),

        date: isFirstInGroup
          ? {
              value: formatDate(firstOrder.date, DATE_DD_MM_YYYY_SLASH),
              cellProps: {
                rowSpan: totalTestsInGroup,
                sx: { bgcolor: getTableBgColor(groupIndex) },
              },
            }
          : getDummyRow(groupIndex),

        department: {
          value: order.department,
          cellProps: { sx: { bgcolor: getTableBgColor(groupIndex) } },
        },

        testName: {
          value: order.testName,
          cellProps: { sx: { bgcolor: getTableBgColor(groupIndex) } },
        },

        amount: {
          value: order.amount,
          cellProps: { sx: { bgcolor: getTableBgColor(groupIndex) } },
        },

        status: hasUniformStatus
          ? isFirstInGroup
            ? {
                value: firstOrder.status === 'Paid' ? 'Paid' : 'Unpaid',
                cellProps: {
                  rowSpan: totalTestsInGroup,
                  sx: { bgcolor: getTableBgColor(groupIndex) },
                },
              }
            : getDummyRow(groupIndex)
          : {
              value: order.status === 'Paid' ? 'Paid' : 'Unpaid',
              cellProps: { sx: { bgcolor: getTableBgColor(groupIndex) } },
            },
      };

      rows.push(row);
    });

    groupIndex++;
  });

  return rows;
};

export const getActualIdsFromSelection = (
  selectedItems: string[]
): string[] => {
  return Array.from(new Set(selectedItems.map((id) => id.split('~')[0])));
};

export const getSelectedOrdersWithActualIds = (
  orders: OrderHistoryItem[],
  selectedItems: string[]
) => {
  const selectedOrders = orders.filter((order) =>
    selectedItems.includes(order.id)
  );

  return selectedOrders.map((order) => ({
    ...order,
    actualId: order.id.split('~')[0],
  }));
};
