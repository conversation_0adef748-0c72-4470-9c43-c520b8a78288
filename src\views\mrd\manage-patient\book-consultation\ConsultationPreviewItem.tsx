import React, { memo } from 'react';

import { capitalize } from '@mui/material';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { ConsultationForm } from '@/types/mrd/manage-patient/consultation';

import KeyValuePair from '../shared/KeyValuePair';

const ConsultationPreviewItem = ({
  consultation,
}: {
  consultation: ConsultationForm;
}) => {
  return (
    <div className="flex gap-base py-base border-t">
      <KeyValuePair label="Doctor" value={consultation.doctorId?.name} />
      <KeyValuePair label="Consultation Id" value={consultation.id} />
      <KeyValuePair
        label="Date"
        value={formatDate(consultation.date, DateFormats.DATE_DD_MM_YYYY_SLASH)}
      />
      <KeyValuePair label="Time" value={consultation.time} />
      <KeyValuePair label="Type" value={capitalize(consultation?.type)} />
      <KeyValuePair
        label="Consultation Fee"
        value={
          consultation.paymentStatus === 'PAID' && consultation.consultationFee
            ? consultation.consultationFee
            : consultation.doctorId?.consultationFee || '--'
        }
      />
    </div>
  );
};

export default memo(ConsultationPreviewItem);
