import React, { memo, useCallback, useEffect, useState } from 'react';

import { Controller, Path, useForm } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import { Stack } from '@mui/material';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import * as yup from 'yup';

import FileInput from '@core/components/file-input';
import TextInput from '@core/components/text-input';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { getNestedValue } from '@/utils/emr/doctor-profile/personal-info';
import { isFileList } from '@/utils/fileTypeChecks';
import {
  aadharInput,
  aadharValidation,
  allowAlphanumericInput,
  enforceAlphabeticInputWithSpace,
  enforceNumericWithHyphen,
} from '@/utils/validation';

import PencilIcon from '@/assets/svg/PencilIcon';

import { profileTabs } from '@/constants/emr/doctor-profile/personal-info';

import { useFileUpload } from '@/emr/hooks/use-file-upload';

import DatePicker from '@/core/components/date-picker';
import type { Documents } from '@/types/emr/doctor-profile/personal-info';

import { TextAreaInput } from './Components';

import SaveButton from './shared/SaveButton';
import Title from './shared/Title';

dayjs.extend(isSameOrBefore);

const docDefaultValues = {
  number: '',
  name: '',
  description: '',
  issuedAt: '',
  issuedPlace: '',
  renewedAt: '',
  url: '',
};

const defaultValues: Documents = {
  aadhar: docDefaultValues,
  passport: docDefaultValues,
  panCard: docDefaultValues,
};

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const docValidationSchema = yup.object().shape({
  issuedAt: yup
    .string()
    .nullable()
    .optional()
    .test('is-not-future-date-issue', 'Enter a valid date', (value) => {
      if (!value) return true;
      return dayjs(value).isSameOrBefore(dayjs(), 'day');
    }),
});

const documentsValidationSchema = yup.object().shape({
  aadhar: docValidationSchema.optional(),
  passport: docValidationSchema.optional(),
  panCard: docValidationSchema.optional(),
});

const Documents = () => {
  const {
    doctorProfile,
    createDoctorProfile,
    updateDoctorProfile,
    setTabName,
    fetchDoctorProfileByEmail,
  } = useDoctorStore();

  const { data: userData } = useUserStore();
  const isMobile = useIsMobile();

  const userId = userData?.id;

  const { fileUpload } = useFileUpload({ userId, type: 'document' });

  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);

  const methods = useForm<Documents>({
    defaultValues,
    resolver: yupResolver(documentsValidationSchema),
  });
  const {
    handleSubmit,
    register,
    formState: { errors },
    getValues,
    reset,
    control,
  } = methods;

  const onSubmit = useCallback(
    async (data: Documents) => {
      let aadharDocumentUrl = data?.aadhar?.url;
      let passportDocumentUrl = data?.passport?.url;
      let panCardDocumentUrl = data?.panCard?.url;

      if (isFileList(aadharDocumentUrl)) {
        if (aadharDocumentUrl.length) {
          aadharDocumentUrl = await fileUpload(aadharDocumentUrl[0]);
        }
      }

      if (isFileList(passportDocumentUrl)) {
        if (passportDocumentUrl.length) {
          passportDocumentUrl = await fileUpload(passportDocumentUrl[0]);
        }
      }

      if (isFileList(panCardDocumentUrl)) {
        if (panCardDocumentUrl.length) {
          panCardDocumentUrl = await fileUpload(panCardDocumentUrl[0]);
        }
      }

      const finalData = {
        ...data,
        aadhar: {
          ...data.aadhar,
          url: aadharDocumentUrl,
        },
        passport: {
          ...data.passport,
          url: passportDocumentUrl,
        },
        panCard: { ...data.panCard, url: panCardDocumentUrl },
      };

      if (doctorProfile?.id) {
        updateDoctorProfile(doctorProfile.id, { documents: finalData });
      } else {
        createDoctorProfile({
          documents: finalData,
          username: userData?.email,
        });
      }
      setEditableField(new Set());
      setIsSubmitted(true);
    },
    [
      createDoctorProfile,
      doctorProfile?.id,
      fileUpload,
      updateDoctorProfile,
      userData?.email,
    ]
  );

  const isFieldDisabled = useCallback(
    (fieldName: Path<Documents>) => {
      if (editableField.has(fieldName)) {
        return false;
      }

      const formValues = getValues();
      const fieldValue = formValues[fieldName as keyof Documents];
      const doctorFieldValue = getNestedValue(
        doctorProfile?.documents,
        fieldName
      );

      return (
        !!doctorFieldValue ||
        (isSubmitted && !!fieldValue && fieldValue === doctorFieldValue)
      );
    },
    [editableField, getValues, doctorProfile?.documents, isSubmitted]
  );

  const handleEditClick = useCallback(
    (fieldName: Path<Documents>) => {
      setEditableField((prev) => new Set(prev.add(fieldName)));
    },
    [setEditableField]
  );

  const renderEditIcon = useCallback(
    (fieldName: Path<Documents>) => {
      return isFieldDisabled(fieldName) ? (
        <button type="button" onClick={() => handleEditClick(fieldName)}>
          <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
        </button>
      ) : null;
    },
    [isFieldDisabled, handleEditClick]
  );

  useEffect(() => {
    setTabName(profileTabs.DOCUMENT_DETAILS);
    if (userData?.email) {
      fetchDoctorProfileByEmail(userData.email);
    }
  }, [fetchDoctorProfileByEmail, setTabName, userData.email]);

  useEffect(() => {
    if (doctorProfile?.documents) {
      reset(doctorProfile?.documents);
    } else {
      reset(defaultValues);
    }
  }, [doctorProfile?.documents, reset]);

  const documentFields = {
    'aadhar.number': (
      <TextInput
        label="Aadhar Number"
        {...register('aadhar.number', {
          validate: (value) => {
            if (!value) return true;
            return aadharValidation(value);
          },
        })}
        endDecoration={renderEditIcon('aadhar.number')}
        disabled={isFieldDisabled('aadhar.number')}
        errors={errors.aadhar?.number}
        placeholder="123456789876543"
        onKeyDown={aadharInput}
        onInput={enforceNumericWithHyphen()}
        className="w-full md:w-5/12"
        inputMode="numeric"
        maxLength={14}
      />
    ),
    'aadhar.name': (
      <TextInput
        label="Name In Aadhar"
        {...register('aadhar.name')}
        placeholder="Dr. Shaji Kurian"
        endDecoration={renderEditIcon('aadhar.name')}
        disabled={isFieldDisabled('aadhar.name')}
        className="w-full md:w-5/12"
        onInput={enforceAlphabeticInputWithSpace}
      />
    ),
    'aadhar.issuedAt': (
      <DatePicker
        label="Issued Date"
        format={DATE_DD_MM_YYYY_SLASH}
        name="aadhar.issuedAt"
        control={control}
        disableInput={isFieldDisabled('aadhar.issuedAt')}
        renderEditIcon={() => renderEditIcon('aadhar.issuedAt')}
        maxDate={dayjs()}
        slotProps={{
          textField: {
            error: !!errors.aadhar?.issuedAt,
          },
        }}
      />
    ),
    'aadhar.url': (
      <Controller
        name="aadhar.url"
        control={control}
        render={({ field }) => (
          <FileInput
            label="Aadhar"
            value={field.value}
            onChange={(files: FileList | null) => field.onChange(files)}
            className="flex-1 flex-grow "
            isBoxStyle
            showPreview
            maxNameLength={isMobile ? 8 : 55}
            allowedFileTypes={['image/jpeg', 'image/png', 'application/pdf']}
            fileTypeErrorMessage="*Only JPG, PNG, and PDF files are allowed."
            maxFileSize={6}
          />
        )}
      />
    ),
    'aadhar.description': isMobile ? (
      <TextAreaInput
        label="Description"
        {...register('aadhar.description')}
        endDecoration={renderEditIcon('aadhar.description')}
        disabled={isFieldDisabled('aadhar.description')}
        className="w-full md:w-5/12"
        rows={3}
      />
    ) : (
      <TextInput
        label="Description"
        {...register('aadhar.description')}
        endDecoration={renderEditIcon('aadhar.description')}
        disabled={isFieldDisabled('aadhar.description')}
        className="w-full md:w-5/12"
      />
    ),
    'passport.number': (
      <TextInput
        label="Passport Number"
        {...register('passport.number')}
        endDecoration={renderEditIcon('passport.number')}
        disabled={isFieldDisabled('passport.number')}
        placeholder="123456789876543"
        className="w-full md:w-4/12"
        onKeyDown={allowAlphanumericInput}
      />
    ),
    'passport.name': (
      <TextInput
        label="Name In Passport"
        {...register('passport.name')}
        placeholder="Dr. Shaji Kurian"
        endDecoration={renderEditIcon('passport.name')}
        disabled={isFieldDisabled('passport.name')}
        className="w-full md:w-4/12"
      />
    ),
    'passport.issuedAt': (
      <DatePicker
        label="Issued Date"
        format={DATE_DD_MM_YYYY_SLASH}
        name="passport.issuedAt"
        control={control}
        disableInput={isFieldDisabled('passport.issuedAt')}
        renderEditIcon={() => renderEditIcon('passport.issuedAt')}
        maxDate={dayjs()}
        slotProps={{
          textField: {
            error: !!errors.passport?.issuedAt,
          },
        }}
      />
    ),
    'passport.renewedAt': (
      <DatePicker
        label="Renewal Date"
        format={DATE_DD_MM_YYYY_SLASH}
        name="passport.renewedAt"
        control={control}
        disableInput={isFieldDisabled('passport.renewedAt')}
        renderEditIcon={() => renderEditIcon('passport.renewedAt')}
      />
    ),
    'passport.issuedPlace': (
      <TextInput
        label="Issued Place"
        {...register('passport.issuedPlace')}
        endDecoration={renderEditIcon('passport.issuedPlace')}
        disabled={isFieldDisabled('passport.issuedPlace')}
        placeholder="Kochi"
        className="w-full md:w-2/12"
      />
    ),
    'passport.url': (
      <Controller
        name="passport.url"
        control={control}
        render={({ field }) => (
          <FileInput
            label="Passport"
            value={field.value}
            onChange={(files: FileList | null) => field.onChange(files)}
            className="flex-1 flex-grow "
            isBoxStyle
            showPreview
            maxNameLength={isMobile ? 8 : 25}
            allowedFileTypes={['image/jpeg', 'image/png', 'application/pdf']}
            fileTypeErrorMessage="*Only JPG, PNG, and PDF files are allowed."
            maxFileSize={6}
          />
        )}
      />
    ),
    'passport.description': isMobile ? (
      <TextAreaInput
        label="Description"
        {...register('passport.description')}
        endDecoration={renderEditIcon('passport.description')}
        disabled={isFieldDisabled('passport.description')}
        className="w-full md:w-6/12"
        rows={3}
      />
    ) : (
      <TextInput
        label="Description"
        {...register('passport.description')}
        endDecoration={renderEditIcon('passport.description')}
        disabled={isFieldDisabled('passport.description')}
        className="w-full md:w-6/12"
      />
    ),

    'panCard.number': (
      <TextInput
        label="PAN"
        placeholder="123456789876543"
        {...register('panCard.number')}
        endDecoration={renderEditIcon('panCard.number')}
        disabled={isFieldDisabled('panCard.number')}
        onKeyDown={allowAlphanumericInput}
        className="w-full md:w-5/12"
      />
    ),
    'panCard.name': (
      <TextInput
        label="Name In PAN"
        placeholder="Dr. Shaji Kurian"
        {...register('panCard.name')}
        endDecoration={renderEditIcon('panCard.name')}
        disabled={isFieldDisabled('panCard.name')}
        className="w-full md:w-4/12"
      />
    ),
    'panCard.issuedAt': (
      <DatePicker
        label="Issued Date"
        format={DATE_DD_MM_YYYY_SLASH}
        name="panCard.issuedAt"
        control={control}
        disableInput={isFieldDisabled('panCard.issuedAt')}
        maxDate={dayjs()}
        renderEditIcon={() => renderEditIcon('panCard.issuedAt')}
        slotProps={{
          textField: {
            error: !!errors.panCard?.issuedAt,
          },
        }}
      />
    ),
    'panCard.url': (
      <Controller
        name="panCard.url"
        control={control}
        render={({ field }) => (
          <FileInput
            label="Pancard"
            value={field.value}
            onChange={(files: FileList | null) => field.onChange(files)}
            className="flex-1 flex-grow"
            isBoxStyle
            showPreview
            maxNameLength={isMobile ? 8 : 55}
            allowedFileTypes={['image/jpeg', 'image/png', 'application/pdf']}
            fileTypeErrorMessage="*Only JPG, PNG, and PDF files are allowed."
            maxFileSize={6}
          />
        )}
      />
    ),
    'panCard.description': isMobile ? (
      <TextAreaInput
        label="Description"
        {...register('panCard.description')}
        endDecoration={renderEditIcon('panCard.description')}
        disabled={isFieldDisabled('panCard.description')}
        className="w-full md:w-5/12"
        rows={3}
      />
    ) : (
      <TextInput
        label="Description"
        {...register('panCard.description')}
        endDecoration={renderEditIcon('panCard.description')}
        disabled={isFieldDisabled('panCard.description')}
        className="w-full md:w-5/12"
      />
    ),
  };

  const renderDocumentFields = () => (
    <>
      <div className="w-full flex gap-2">
        {documentFields['aadhar.number']}
        {documentFields['aadhar.name']}
        <div className="w-3/12">{documentFields['aadhar.issuedAt']}</div>
      </div>
      <div className="w-full flex gap-2 mt-5 ">
        {documentFields['aadhar.url']}
        {documentFields['aadhar.description']}
      </div>
      <div className="w-full flex gap-2 pt-5">
        {documentFields['passport.number']}
        {documentFields['passport.name']}
        <div className="w-2/12">{documentFields['passport.issuedAt']}</div>
        <div className="w-2/12">{documentFields['passport.renewedAt']}</div>
      </div>
      <div className="w-full flex gap-2 mt-5 ">
        {documentFields['passport.issuedPlace']}
        {documentFields['passport.url']}
        {documentFields['passport.description']}
      </div>
      <div className="w-full flex gap-2 pt-5">
        {documentFields['panCard.number']}
        {documentFields['panCard.name']}
        <div className="w-2/12">{documentFields['panCard.issuedAt']}</div>
      </div>
      <div className="w-full flex gap-2 mt-5 ">
        {documentFields['panCard.url']}
        {documentFields['panCard.description']}
      </div>
    </>
  );

  const renderMobDocumentFields = () => (
    <div className="w-full flex flex-col gap-4 ">
      <div className="w-full grid grid-cols-2 gap-5">
        <div className="col-span-2">{documentFields['aadhar.number']}</div>
        <div className="col-span-2">{documentFields['aadhar.name']}</div>
        {documentFields['aadhar.url']}
        {documentFields['aadhar.issuedAt']}
        <div className="col-span-2">{documentFields['aadhar.description']}</div>
      </div>

      <div className="w-full grid grid-cols-2 gap-5">
        {documentFields['passport.number']}
        {documentFields['passport.name']}
        {documentFields['passport.issuedAt']}
        {documentFields['passport.renewedAt']}
        {documentFields['passport.issuedPlace']}
        {documentFields['passport.url']}
        <div className="col-span-2">
          {documentFields['passport.description']}
        </div>
      </div>

      <div className="w-full grid grid-cols-2 gap-5">
        {documentFields['panCard.number']}
        {documentFields['panCard.name']}
        {documentFields['panCard.issuedAt']}
        {documentFields['panCard.url']}
        <div className="col-span-2">
          {documentFields['panCard.description']}
        </div>
      </div>
    </div>
  );

  return (
    <Stack
      spacing={2}
      component="form"
      {...methods}
      onSubmit={handleSubmit(onSubmit)}
      className="overflow-y-auto pr-2"
    >
      <Title className="mt-1" title="Documents" />
      <div className={isMobile ? 'pb-5' : ''}>
        {isMobile ? renderMobDocumentFields() : renderDocumentFields()}
        <SaveButton className={!isMobile ? 'mt-15' : ''} />
      </div>
    </Stack>
  );
};

export default memo(Documents);
