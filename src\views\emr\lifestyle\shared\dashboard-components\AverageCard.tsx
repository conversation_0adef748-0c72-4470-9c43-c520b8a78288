import React from 'react';

interface AverageCardProps {
  title: string;
  value: number | string;
  unit: string;
  icon: React.ReactNode;
  bgColor: string;
  borderColor: string;
  disabled?: boolean;
  className?: string;
}

const AverageCard: React.FC<AverageCardProps> = ({
  title,
  value,
  unit,
  icon,
  bgColor,
  borderColor,
  disabled = false,
  className = '',
}) => {
  const formatValue = (val: number | string): string => {
    if (disabled) return 'xxxx';
    if (val === undefined || val === null) return '---';
    if (typeof val === 'string') return val;
    if (typeof val === 'number' && val >= 10000) {
      return Math.round(val / 1000) + 'k';
    }
    return val.toString();
  };

  const formatUnit = (unitText: string): string => {
    if (disabled) return 'xx';
    return unitText;
  };

  const formatTitle = (titleText: string): string => {
    if (disabled) return 'xxxx.xxx';
    return titleText;
  };

  return (
    <div
      className={`rounded-lg p-2 ${className}`}
      style={{
        backgroundColor: bgColor,
        borderColor: borderColor,
        borderWidth: '1px',
        borderStyle: 'solid',
        borderRadius: '8px',
        height: '81px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
      }}
    >
      {/* Title - Top Left */}
      <div
        className="text-sm font-medium"
        style={{
          fontSize: '14px',
          fontWeight: 500,
          color: disabled ? '#C2CDD6' : '#404040',
        }}
      >
        {formatTitle(title)}
      </div>

      {/* Bottom Row - Icon and Value aligned on same line */}
      <div className="flex items-center justify-between">
        {/* Icon - Bottom Left (only show if not disabled) */}
        {/* {!disabled && ( */}
        <div className="text-gray-600 flex items-center">{icon}</div>
        {/* )} */}

        {/* {disabled && <div className="text-white flex items-center"></div>} */}
        {/* Value and Unit - Bottom Right */}
        <div
          className="flex items-baseline gap-1"
          style={{ marginLeft: disabled ? '0' : 'auto' }}
        >
          <span
            className="font-medium"
            style={{
              fontSize: '32px',
              fontWeight: 500,
              color: disabled ? '#C2CDD6' : '#000000',
              lineHeight: '1',
            }}
          >
            {formatValue(value)}
          </span>
          <span
            className="text-sm"
            style={{
              fontSize: '14px',
              fontWeight: 400,
              color: disabled ? '#C2CDD6' : '#404040',
              lineHeight: '1',
            }}
          >
            {formatUnit(unit)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default AverageCard;
