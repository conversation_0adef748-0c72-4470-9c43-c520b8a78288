import React, { memo, useMemo, useState } from 'react';

import { Box, IconButton } from '@mui/material';

import EditableText from '@/lib/common/editable_text';

import { useDiagnosisStore } from '@/store/extraNoteDiagnosisStore';

import { noteModes } from '@/utils/constants/consultation';
import { isHtmlContentEmpty } from '@/utils/textUtil';

import ExpandNoteIcon from '@/assets/svg/ExpandNoteIcon';

import {
  StyledTypography,
  groupByFormattedDate,
} from '@/emr/components/consultation/Common';
import DiagnosisNoteModal from '@/emr/components/consultation/ExtraNoteModal';

const fixedSections = [
  { title: 'Summary', fieldName: 'prescription_summary' },
  { title: 'Medication', fieldName: 'prescription_medication' },
  { title: 'Co-Morbidity', fieldName: 'prescription_comorbidity' },
  { title: 'Notes', fieldName: 'prescription_notes' },
  { title: 'Allergic Info', fieldName: 'prescription_allergic_info' },
];

const { VIEW, CREATE } = noteModes;

const PrescriptionNotes = () => {
  const { records, mode, setMode } = useDiagnosisStore();

  const [open, setOpen] = useState(false);
  const [selectedTitle, setSelectedTitle] = useState<string | null>(null);
  const [displayTitle, setDisplayTitle] = useState<string | null>(null);
  const [showCancelButton, setShowCancelButton] = useState(false);

  const prescriptionRecords = useMemo(() => {
    if (!records?.records) return [];

    return records.records.filter((record) =>
      record.field?.toLowerCase().startsWith('prescription_')
    );
  }, [records]);

  return (
    <>
      <div className="flex-1 overflow-y-auto p-2">
        {fixedSections.map(({ title, fieldName }) => {
          const allRecordsForField =
            prescriptionRecords?.filter(
              (record) =>
                record?.field?.toLowerCase() === fieldName.toLowerCase()
            ) || [];

          const matchingRecords = allRecordsForField
            ?.filter((record) => !isHtmlContentEmpty(record.content))
            .sort((recordA, recordB) => {
              const dateA = new Date(recordA.timestamp)
                .toISOString()
                .split('T')[0];
              const dateB = new Date(recordB.timestamp)
                .toISOString()
                .split('T')[0];
              return dateA.localeCompare(dateB);
            });

          const groupedRecords = groupByFormattedDate(matchingRecords ?? []);

          return (
            <div
              key={`${title}-${fieldName}`}
              className="border border-[#DAE1E7] rounded-lg shadow-custom-xs p-1.5 xl:p-2.5 flex flex-col gap-1 w-full mb-2"
              style={{ minHeight: '80px' }}
            >
              <div className="flex items-center justify-between">
                <span className="font-medium text-sm xl:text-base -tracking-[2.2%]">
                  {title}
                </span>
                <IconButton
                  onClick={() => {
                    setSelectedTitle(fieldName);
                    setDisplayTitle(title);
                    setOpen(true);
                    setShowCancelButton(false);
                    const newMode =
                      (matchingRecords?.length ?? 0) > 0 &&
                      prescriptionRecords?.length > 0
                        ? VIEW
                        : CREATE;
                    if (mode !== newMode) {
                      setMode(newMode);
                    }
                  }}
                  size="small"
                >
                  <ExpandNoteIcon />
                </IconButton>
              </div>

              {Object.keys(groupedRecords).length > 0 ? (
                Object.keys(groupedRecords).map((date) => (
                  <div key={date} className="flex flex-row items-start w-full">
                    <div className="flex-grow max-w-[calc(100%-100px)]">
                      {groupedRecords[date]?.map((record) => (
                        <Box key={`${record.record_id}-${record.content}`}>
                          <EditableText
                            defaultValue={record.content}
                            editable={false}
                            emptyPlaceholder=""
                          />
                        </Box>
                      ))}
                    </div>
                    <div className="mx-2 w-[100px] text-right">
                      <StyledTypography variant="caption">
                        {date}
                      </StyledTypography>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-gray-500 text-sm">
                  Enter {title.toLowerCase()}
                </div>
              )}
            </div>
          );
        })}
      </div>
      <DiagnosisNoteModal
        open={open}
        onClose={() => setOpen(false)}
        selectedTitle={selectedTitle}
        setOpen={setOpen}
        showCancelButton={showCancelButton}
        setShowCancelButton={setShowCancelButton}
        displayTitle={displayTitle}
      />
    </>
  );
};

export default memo(PrescriptionNotes);
