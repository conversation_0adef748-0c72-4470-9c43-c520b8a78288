import { memo, useCallback, useEffect } from 'react';

import Image from 'next/image';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { getDoctorProfile } from '@/query/emr/doctor-profile/personal-info';

const TopBar = () => {
  const { setUser, setUserData, data: userData } = useUserStore();
  const { fetchDoctorProfileByEmail } = useDoctorStore();

  const handleFetchUser = useCallback(async () => {
    if (!userData?.id) {
      const response = await getDoctorProfile();
      if (response?.data?.length > 0) {
        setUser(response.data?.[0].name);
        setUserData(response.data?.[0]);
      }
    }
  }, [setUser, setUserData, userData?.id]);

  useEffect(() => {
    handleFetchUser();
  }, [handleFetchUser]);

  useEffect(() => {
    if (userData?.email) {
      fetchDoctorProfileByEmail(userData.email);
    }
  }, [userData?.email, fetchDoctorProfileByEmail]);

  return (
    <header className="h-10 bg-white p-1 flex items-center justify-between">
      <div className="w-8 h-8 rounded ml-2 flex items-center justify-center">
        <Image src="/favicon.svg" alt="logo" height={90} width={90} />
      </div>
    </header>
  );
};

export default memo(TopBar);
