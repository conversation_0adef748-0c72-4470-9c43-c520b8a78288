import { Checkbox, CheckboxProps, SxProps } from '@mui/material';

import { PackageData } from '@/store/emr/lab/package-store';

import { modalModes } from '@/types/emr/lab';

interface EmptyViewProps {
  packages: PackageData[];
}

type TestSelectCheckbox = CheckboxProps;

type Props = {
  useTwoColumns: boolean;
  modalMode: string;
};

export const EmptyView: React.FC<EmptyViewProps> = ({ packages }) => {
  return (
    <div className="p-4 flex-grow flex flex-col items-center justify-center text-gray-500">
      {packages.length > 0
        ? 'Select a package from the list or create a new one'
        : "No packages found. Click 'Add New Package' to create one."}
    </div>
  );
};

export const TestSelectCheckbox: React.FC<TestSelectCheckbox> = (props) => {
  return (
    <Checkbox
      size="small"
      sx={{
        transform: 'scale(0.75)',
        padding: '4px',
        color: 'black',
        '&.Mui-checked': { color: 'black' },
        ...props.sx,
      }}
      {...props}
    />
  );
};

export const getTableContainerProps = ({ useTwoColumns, modalMode }: Props) => {
  const sx: SxProps = {
    width: '100%',
    tableLayout: 'fixed',
    '& table': {
      width: '100%',
      tableLayout: 'fixed',
    },
    '& thead th': {
      borderLeft: '1px solid #64707D',
      ...(useTwoColumns
        ? modalMode === modalModes.DETAIL
          ? {
              '&:nth-of-type(1)': { width: '10%' },
              '&:nth-of-type(2)': { width: '40%' },
              '&:nth-of-type(3)': { width: '10%' },
              '&:nth-of-type(4)': { width: '40%' },
            }
          : {
              '&:nth-of-type(1)': { width: '40%' },
              '&:nth-of-type(2)': { width: '10%' },
              '&:nth-of-type(3)': { width: '40%' },
              '&:nth-of-type(4)': { width: '10%' },
            }
        : modalMode === modalModes.DETAIL
          ? {
              '&:nth-of-type(1)': { width: '20%' },
              '&:nth-of-type(2)': { width: '80%' },
            }
          : {
              '&:nth-of-type(1)': { width: '80%' },
              '&:nth-of-type(2)': { width: '20%' },
            }),
    },
    '& tbody td': {
      ...(useTwoColumns
        ? modalMode === modalModes.DETAIL
          ? {
              '&:nth-of-type(1)': { width: '10%' },
              '&:nth-of-type(2)': { width: '40%' },
              '&:nth-of-type(3)': { width: '10%' },
              '&:nth-of-type(4)': { width: '40%' },
            }
          : {
              '&:nth-of-type(1)': { width: '40%' },
              '&:nth-of-type(2)': { width: '10%' },
              '&:nth-of-type(3)': { width: '40%' },
              '&:nth-of-type(4)': { width: '10%' },
            }
        : modalMode === modalModes.DETAIL
          ? {
              '&:nth-of-type(1)': { width: '20%' },
              '&:nth-of-type(2)': { width: '80%' },
            }
          : {
              '&:nth-of-type(1)': { width: '80%' },
              '&:nth-of-type(2)': { width: '20%' },
            }),
    },
    '& tbody tr:nth-of-type(odd)': {
      backgroundColor: '#ffffff',
    },
    '& tbody tr:nth-of-type(even)': {
      backgroundColor: '#DAE1E7',
    },
  };

  return { sx };
};

interface NotFoundProps {
  message?: string;
  description?: string;
}

export const NotFound: React.FC<NotFoundProps> = ({
  message = 'No Packages Found',
  description = 'Click on "Add New Package" to add a new package.',
}) => {
  return (
    <div className="flex flex-grow min-h-0">
      <div className="w-full flex items-center justify-center text-gray-500">
        <div className="text-center">
          <p className="text-lg font-medium">{message}</p>
          <p className="text-sm mt-1">{description}</p>
        </div>
      </div>
    </div>
  );
};
