import {
  forwardRef,
  memo,
  ReactNode,
  useCallback,
  useEffect,
  useId,
  useImperativeHandle,
  useState,
} from 'react';

import { BlockNoteEditor, locales } from '@blocknote/core';
import { BlockNoteView } from '@blocknote/mantine';
import {
  blockTypeSelectItems,
  DragHandleButton,
  // FormattingToolbar,
  // FormattingToolbarController,
  SideMenu,
  SideMenuController,
  useCreateBlockNote,
} from '@blocknote/react';
import { debounce } from 'lodash';
import { toast } from 'sonner';

import { archivo } from '@/utils/fonts';

import './styles.scss';

import '@blocknote/core/fonts/inter.css';
import '@blocknote/mantine/style.css';

export type RichTextEditorProps = {
  id?: string;
  defaultValue?: string;
  bg?: 'white' | 'colored';
  maxHeight?: string;
  placeholder?: string;
  children?: ReactNode;
  onChange?: (text: string) => void;
  onImmediateChange?: (document: any) => void;
  onBlur?: () => void;
  className?: string;
  initialBlockType?:
    | 'paragraph'
    | 'bulletListItem'
    | 'numberedListItem'
    | 'checkListItem';
};

export type RichTextEditorRef = {
  editor: BlockNoteEditor;
};

const RichTextEditor = forwardRef(
  (
    {
      id,
      defaultValue = '',
      bg = 'colored',
      maxHeight = '',
      placeholder = 'Enter diagnosis',
      children,
      onChange = () => {},
      onImmediateChange = () => {},
      onBlur = () => {},
      className,
      initialBlockType = 'paragraph',
    }: RichTextEditorProps,
    ref
  ) => {
    const defaultId = useId();
    const localId = id || defaultId;

    const editor = useCreateBlockNote(
      {
        initialContent: [
          {
            id: 'placeholder-block',
            type: initialBlockType,
            content: '',
          },
        ],
        dictionary: {
          ...locales.en,
          placeholders: {
            ...locales.en.placeholders,
            default: placeholder,
          },
        },
      },
      [initialBlockType]
    );

    const allowedItems = [
      'paragraph',
      'bulletListItem',
      'numberedListItem',
      'checkListItem',
    ];
    const [blockTypeOptions] = useState(
      blockTypeSelectItems(editor.dictionary).filter((item) =>
        allowedItems.includes(item.type)
      )
    );

    const emitHTML = async () => {
      try {
        const htmlText = await editor.blocksToFullHTML(editor.document);

        // Clean up the HTML to remove nested empty structures
        const cleanedHTML = cleanupBlockNoteHTML(htmlText);
        onChange(cleanedHTML);
      } catch {
        onChange('');
        toast.error('Error processing text input');
      }
    };

    // Function to clean up BlockNote HTML output
    const cleanupBlockNoteHTML = (html: string): string => {
      if (!html) return '';

      // Create a temporary DOM element to manipulate the HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;

      // Simplified cleanup - remove style attributes but preserve text alignment
      const allElements = tempDiv.querySelectorAll('*');
      allElements.forEach((el) => {
        const style = el.getAttribute('style');
        if (style) {
          // Preserve text-align styles, remove others
          const textAlignMatch = style.match(/text-align:\s*([^;]+)/);
          if (textAlignMatch) {
            const alignment = textAlignMatch[1].trim();
            el.setAttribute('style', `text-align: ${alignment}`);
            // Also set data-text-alignment attribute for consistency
            if (el.classList.contains('bn-block-content')) {
              el.setAttribute('data-text-alignment', alignment);
            }
          } else {
            el.removeAttribute('style');
          }
        }

        // Ensure data-text-alignment is preserved
        const dataAlignment = el.getAttribute('data-text-alignment');
        if (dataAlignment) {
          // Keep the data attribute
        }
      });

      // Remove only clearly empty elements to avoid performance issues
      const emptyParagraphs = tempDiv.querySelectorAll(
        'p.bn-inline-content:empty'
      );
      emptyParagraphs.forEach((p) => p.remove());

      const emptyBlockContent = tempDiv.querySelectorAll(
        '.bn-block-content:empty'
      );
      emptyBlockContent.forEach((div) => div.remove());

      const emptyBlocks = tempDiv.querySelectorAll('.bn-block:empty');
      emptyBlocks.forEach((div) => div.remove());

      const emptyBlockOuters = tempDiv.querySelectorAll(
        '.bn-block-outer:empty'
      );
      emptyBlockOuters.forEach((div) => div.remove());

      return tempDiv.innerHTML;
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedHandleChange = useCallback(debounce(emitHTML, 100), [
      editor,
    ]);

    const handleChange = () => {
      debouncedHandleChange();
      // Only call onImmediateChange if it exists to avoid unnecessary calls
      if (onImmediateChange) {
        onImmediateChange(editor.document);
      }
    };

    useImperativeHandle(ref, () => {
      return {
        editor,
      };
    }, [editor]);

    // Load content only once when component mounts with defaultValue
    useEffect(() => {
      if (defaultValue && defaultValue.trim() !== '') {
        const loadContent = async () => {
          try {
            // Check if editor already has meaningful content to avoid overwriting
            const currentBlocks = editor.document;
            const hasExistingContent = currentBlocks.some(
              (block) =>
                block.content &&
                Array.isArray(block.content) &&
                block.content.length > 0
            );

            if (!hasExistingContent) {
              // First, let's try a simpler approach - just parse and insert the HTML
              const blocks = await editor.tryParseHTMLToBlocks(defaultValue);
              if (blocks && blocks.length > 0) {
                editor.replaceBlocks(editor.document, blocks);

                // After inserting, try to apply alignment by manipulating the DOM directly
                setTimeout(() => {
                  const tempDiv = document.createElement('div');
                  tempDiv.innerHTML = defaultValue;

                  const alignmentElements = tempDiv.querySelectorAll(
                    '[data-text-alignment]'
                  );

                  // Use getElementById instead of querySelector to avoid selector issues
                  const editorContainer = document.getElementById(localId);
                  const editorElement =
                    editorContainer?.querySelector('.bn-editor');

                  if (editorElement && alignmentElements.length > 0) {
                    const blockContents =
                      editorElement.querySelectorAll('.bn-block-content');

                    alignmentElements.forEach((alignEl, index) => {
                      const alignment = alignEl.getAttribute(
                        'data-text-alignment'
                      );
                      if (alignment && blockContents[index]) {
                        const blockContent = blockContents[
                          index
                        ] as HTMLElement;
                        blockContent.setAttribute(
                          'data-text-alignment',
                          alignment
                        );
                        blockContent.style.textAlign = alignment;

                        // Also apply to inline content
                        const inlineContent = blockContent.querySelector(
                          '.bn-inline-content'
                        ) as HTMLElement;
                        if (inlineContent) {
                          inlineContent.style.textAlign = alignment;
                        }
                      }
                    });
                  }
                }, 100);
              }
            }
          } catch (error) {
            console.error('Error loading initial content:', error);
          }
        };

        // Small delay to ensure editor is fully initialized
        setTimeout(loadContent, 50);
      }
      // Only run on mount, not when defaultValue changes to prevent loops
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
      <BlockNoteView
        id={localId}
        className={`
        rich-text-editor text-sm 
        ${bg} 
        ${className}
       
      `}
        style={{
          ...archivo.style,
          ...(maxHeight
            ? {
                maxHeight,
                overflow: 'auto',
              }
            : {}),
        }}
        editor={editor}
        sideMenu={false}
        slashMenu={false}
        formattingToolbar={true}
        onChange={handleChange}
        onBlur={onBlur}
      >
        <SideMenuController
          sideMenu={(props) => (
            <SideMenu {...props}>
              <DragHandleButton {...props} />
            </SideMenu>
          )}
        />
        {/* Temporarily using default formatting toolbar */}
        {/* <FormattingToolbarController
          formattingToolbar={() => (
            <FormattingToolbar blockTypeSelectItems={blockTypeOptions} />
          )}
        /> */}

        {children}
      </BlockNoteView>
    );
  }
);

RichTextEditor.displayName = 'RichTextEditor';

const MemoRichTextEditor = memo(RichTextEditor);

export default MemoRichTextEditor;
