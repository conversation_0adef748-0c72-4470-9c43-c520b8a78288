'use client';

import { memo, useMemo } from 'react';

import { routes } from '@/constants/routes';

import PageTab, { TabItem } from '@/views/emr/lifestyle/shared/page-tab';
import PageTitle from '@/views/emr/lifestyle/shared/PageTitle';

const SleepActivityLayout = ({ children }: { children: React.ReactNode }) => {
  const tabs = useMemo<TabItem[]>(
    () => [
      {
        label: 'Dashboard',
        path: routes.EMR_LIFESTYLE_SLEEP_ACTIVITY,
      },
      {
        label: 'Practice',
        path: routes.EMR_LIFESTYLE_SLEEP_ACTIVITY_PRACTICE,
      },
      {
        label: 'Attitude',
        path: routes.EMR_LIFESTYLE_SLEEP_ACTIVITY_ATTITUDE,
      },
      {
        label: 'Knowledge',
        path: routes.EMR_LIFESTYLE_SLEEP_ACTIVITY_KNOWLEDGE,
      },
    ],
    []
  );

  return (
    <div className="h-full flex flex-col">
      {/* Page Title */}
      <PageTitle title="Sleep Activity Consultation Timeline" />

      {/* Tab Navigation */}
      <PageTab tabs={tabs} />

      {/* Content */}
      <div className="flex-1 overflow-hidden">{children}</div>
    </div>
  );
};

export default memo(SleepActivityLayout);
