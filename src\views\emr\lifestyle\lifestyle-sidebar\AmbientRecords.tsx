import React, { memo, useRef } from 'react';

import { toast } from 'sonner';

import AddRecord from '@/lib/add_record';

import {
  useAmbientStore,
  ambientSelectors,
} from '@/store/emr/lifestyle/ambient-listening/ambient-store';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { foodIntakePatternStore } from '@/store/emr/lifestyle/nutrition/practice/food-intake-pattern-store';
import { exercisePatternStore } from '@/store/emr/lifestyle/physical-activity/practice/exercise-pattern-store';

import { LifestyleMode } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import NutritionAttitudeModal from '../nutrition/attitude/attitude-tab/AttitudeModal';
import DietaryRecallModal from '../nutrition/practice/24-hour-dietary-recall-tab/DietaryRecallModal';
import FoodFrequencyQuestionnaireModal from '../nutrition/practice/food-frequency-questionnaire/FoodFrequencyQuestionnaireModal';
import FoodIntakePatternModal from '../nutrition/practice/food-intake-patterns-tab/FoodIntakePatternModal';
import AttitudeModal from '../physical-activity/attitude/attitude-tab/AttitudeModal';
import KnowledgeModal from '../physical-activity/knowledge/knowledge-tab/KnowledgeModal';
import ExercisePatternModal from '../physical-activity/practice/exercise-patterns-tab/ExercisePatternModal';

const {
  NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS,
  NUTRITION_ATTITUDE,
  PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS,
  PHYSICAL_ACTIVITY_ATTITUDE,
  PHYSICAL_ACTIVITY_KNOWLEDGE,
  NUTRITION_PRACTICE_DIETARY_RECALL,
  NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE,
} = LifestyleSources;

// Update getFormModal to accept summary as a parameter
const getFormModal = (source: LifestyleSources | null, summary: any) => {
  const commonProps = {
    mode: LifestyleMode.CREATE,
    hideSaveButton: true,
    // Pass the ambient summary data as both initialValues and patientData
    initialValues: summary || {},
    patientData: summary || null,
  };

  switch (source) {
    case NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS:
      return <FoodIntakePatternModal {...commonProps} />;
    case NUTRITION_ATTITUDE:
      return <NutritionAttitudeModal {...commonProps} />;
    case PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS:
      return <ExercisePatternModal {...commonProps} />;
    case PHYSICAL_ACTIVITY_ATTITUDE:
      return <AttitudeModal {...commonProps} />;
    case NUTRITION_PRACTICE_DIETARY_RECALL:
      return <DietaryRecallModal {...commonProps} />;
    case PHYSICAL_ACTIVITY_KNOWLEDGE:
      return <KnowledgeModal {...commonProps} />;
    case NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE:
      return <FoodFrequencyQuestionnaireModal {...commonProps} />;
    default:
      return <></>;
  }
};

const getFormTitle = (source: LifestyleSources | null) => {
  switch (source) {
    case NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS:
      return 'Practice';
    case NUTRITION_ATTITUDE:
      return 'Attitude';
    case PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS:
      return 'Practice';
    case PHYSICAL_ACTIVITY_ATTITUDE:
      return 'Attitude';
    case PHYSICAL_ACTIVITY_KNOWLEDGE:
      return 'Knowledge';
    case NUTRITION_PRACTICE_DIETARY_RECALL:
      return 'Practice';
    case NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE:
      return 'Practice';
    default:
      return 'Add Record';
  }
};

const AmbientRecords = () => {
  const { source } = lifestyleStore();
  const { patientData } = exercisePatternStore();
  const { updating } = foodIntakePatternStore();
  const saveRef = useRef<(() => void) | null>(null);

  // Get conversation and summary from ambient store
  const conversation = useAmbientStore(ambientSelectors.selectConversation);
  const summary = useAmbientStore(ambientSelectors.selectSummary);
  const isLoading = useAmbientStore(ambientSelectors.selectIsLoading);
  const error = useAmbientStore(ambientSelectors.selectError);

  // Ref to hold the save/submit function from the modal
  const checkTodayEntryExists = () => {
    if (source !== PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS) {
      return false;
    }

    const today = new Date().toISOString().split('T')[0];

    return patientData.some((entry) => {
      if (!entry.created_on) return false;
      const entryDate = new Date(entry.created_on).toISOString().split('T')[0];
      return entryDate === today;
    });
  };

  const isTodayEntryExists = checkTodayEntryExists();
  const isButtonDisabled = !source || isTodayEntryExists;

  const getAddRecordProps = () => {
    const modalTitle = `Add ${getFormTitle(source)}`;

    // Get the form modal with the current summary
    // Pass the first conversation item or null if the array is empty
    const formModal = getFormModal(source, summary);

    // Clone the form modal and pass the saveRef
    const formModalWithRef = React.isValidElement(formModal)
      ? React.cloneElement(formModal, {
          onSaveRef: saveRef,
        } as { onSaveRef: React.MutableRefObject<(() => void) | null> })
      : formModal;

    return {
      modalTitle,
      customSummaryForm: formModalWithRef,
      summaryFormTitle: getFormTitle(source),
      summaryFormSubtitle: 'Details',
    };
  };

  const addRecordProps = getAddRecordProps();
  // Handler to trigger the save/submit function from the modal
  const handleAddRecordSave = async () => {
    if (saveRef.current) {
      try {
        // Create a promise that resolves when save is complete
        const savePromise = new Promise<void>((resolve, reject) => {
          try {
            // Call the save function and resolve the promise when it's done
            saveRef.current?.();
            // Use a small timeout to ensure the save operation has time to start
            setTimeout(resolve, 600);
          } catch (error) {
            reject(error);
          }
        });

        // Wait for the save to complete
        await savePromise;
      } catch (error) {
        console.error('Error in save operation:', error);
        toast.error('Error in saving record');
      }
    } else {
      toast.error('Save function not available');
    }
  };
  console.log(updating, 'updating ');
  return (
    <>
      <AddRecord
        // disabled={isButtonDisabled}
        isAmbientRecord={true}
        source={source || ''}
        {...addRecordProps}
        onSave={handleAddRecordSave}
        isLoading={updating}
      />
    </>
  );
};

export default memo(AmbientRecords);
