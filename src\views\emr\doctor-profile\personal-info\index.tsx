'use client';

import { useState, useCallback, useEffect } from 'react';

import {
  PERSONAL_INFO_TAB_STORE_KEY,
  TabValues,
  tabValues,
} from '@/constants/emr/doctor-profile/personal-info';

import EmergencyDetailsSection from '@/views/emr/doctor-profile/personal-info/EmergencyDetails';
import GeneralSection from '@/views/emr/doctor-profile/personal-info/GeneralDetails';
import { PersonalDetailsSection } from '@/views/emr/doctor-profile/personal-info/PersonalDetails';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import BankDetails from './BankDetails';
import Certification from './certification-tab';
import Documents from './Documents';
import EmploymentBackground from './employment-bg-tab';
import FamilyTab from './family-tab';
import LanguageTable from './languages-known-tab';
import MedicalInsuranceTab from './medical-insurance-tab';
import QualificationTab from './qualification-tab';
import SectionTitle from './SectionTitle';

const {
  GENERAL_DETAILS,
  BANK_DETAILS,
  CERTIFICATION,
  EMERGENCY_DETAILS,
  FAMILY_DETAILS,
  LANGUAGES_KNOWN,
  MEDICAL_INSURANCE,
  PERSONAL_DETAILS,
  QUALIFICATION,
  DOCUMENTS,
  EMPLOYMENT_BG,
} = tabValues;

const tabs = [
  {
    value: GENERAL_DETAILS,
    label: 'General Details',
    content: <GeneralSection />,
  },
  {
    value: PERSONAL_DETAILS,
    label: 'Personal Details',
    content: <PersonalDetailsSection />,
  },
  {
    value: EMERGENCY_DETAILS,
    label: 'Emergency Details',
    content: <EmergencyDetailsSection />,
  },
  {
    value: BANK_DETAILS,
    label: 'Bank Details',
    content: <BankDetails />,
  },
  { value: FAMILY_DETAILS, label: 'Family', content: <FamilyTab /> },
  {
    value: MEDICAL_INSURANCE,
    label: 'Medical Insurance',
    content: <MedicalInsuranceTab />,
  },
  {
    value: DOCUMENTS,
    label: 'Document',
    content: <Documents />,
  },
  {
    value: QUALIFICATION,
    label: 'Qualification',
    content: <QualificationTab />,
  },
  {
    value: CERTIFICATION,
    label: 'Certification',
    content: <Certification />,
  },
  {
    value: EMPLOYMENT_BG,
    label: 'Employment Background',
    content: <EmploymentBackground />,
  },
  {
    value: LANGUAGES_KNOWN,
    label: 'Languages Known',
    content: <LanguageTable />,
  },
];

export function ProfileForm() {
  const [activeTab, setActiveTab] = useState<TabValues>(GENERAL_DETAILS);

  const onTabChange = useCallback((tab?: string) => {
    if (tab) {
      setActiveTab(tab as TabValues);
      localStorage.setItem(PERSONAL_INFO_TAB_STORE_KEY, tab);
    }
  }, []);

  useEffect(() => {
    const activeTab =
      (localStorage.getItem(PERSONAL_INFO_TAB_STORE_KEY) as TabValues | null) ||
      GENERAL_DETAILS;
    onTabChange(activeTab);
  }, [onTabChange]);

  return (
    <div className="w-full h-full overflow-hidden md:px-2 py-2">
      <SectionTitle
        className="text-[20px] font-medium leading-[150%] ml-1 border-b border-gray-300 w-[100%]"
        title="Profile Info"
      />

      <Tabs
        defaultValue={activeTab}
        value={activeTab}
        className="w-full h-full  overflow-hidden "
        onValueChange={onTabChange}
      >
        <TabsList className="gap-1 overflow-x-auto overflow-y-hidden w-full flex justify-start scrollbar-hide">
          {tabs?.map((tab) => (
            <TabsTrigger
              key={tab?.value}
              value={tab?.value}
              className="text-[9px]  xl:text-[10.5px] 2xl:text-[11.8px] p-1 m-0 border-b-2 border-gray-300  data-[state=active]:border-b-[2px] data-[state=active]:border-b-[#1AA6F1]"
            >
              {tab?.label}
            </TabsTrigger>
          ))}
        </TabsList>
        {tabs?.map((tab) => (
          <TabsContent
            key={tab?.value}
            value={tab?.value}
            className="h-[calc(100vh-10rem)] overflow-auto pb-10 mt-3 "
          >
            {tab?.content}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
