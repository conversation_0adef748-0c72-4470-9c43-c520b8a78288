import { create } from 'zustand';

import {
  FilterType,
  NutritionAverageResponse,
  NutritionChartResponse,
  NutritionPercentageChartResponse,
  NutritionSummaryResponse,
} from '@/query/emr/lifestyle/nutrition-dashboard';

type NutritionDashboardState = {
  // Filter state
  activeFilter: FilterType;

  // Data states
  averageData: NutritionAverageResponse | null;
  chartData: {
    calories: NutritionChartResponse | null;
    carbs: NutritionChartResponse | null;
    protein: NutritionChartResponse | null;
    fat: NutritionChartResponse | null;
    fiber: NutritionChartResponse | null;
    micro: NutritionChartResponse | null;
    additional: NutritionChartResponse | null;
  };
  percentageData: NutritionPercentageChartResponse | null;
  macroSummary: NutritionSummaryResponse | null;
  microSummary: NutritionSummaryResponse | null;

  // Loading states
  loading: boolean;
  loadingAverage: boolean;
  loadingCharts: boolean;
  loadingPercentage: boolean;
  loadingMacroSummary: boolean;
  loadingMicroSummary: boolean;

  // Error states
  error: string | null;
  errorAverage: string | null;
  errorCharts: string | null;
  errorPercentage: string | null;
  errorMacroSummary: string | null;
  errorMicroSummary: string | null;
};

type NutritionDashboardActions = {
  // Filter actions
  setActiveFilter: (filter: FilterType) => void;

  // Data actions
  setAverageData: (data: NutritionAverageResponse | null) => void;
  setChartData: (data: {
    calories: NutritionChartResponse | null;
    carbs: NutritionChartResponse | null;
    protein: NutritionChartResponse | null;
    fat: NutritionChartResponse | null;
    fiber: NutritionChartResponse | null;
    micro: NutritionChartResponse | null;
    additional: NutritionChartResponse | null;
  }) => void;
  setPercentageData: (data: NutritionPercentageChartResponse | null) => void;
  setMacroSummary: (data: NutritionSummaryResponse | null) => void;
  setMicroSummary: (data: NutritionSummaryResponse | null) => void;

  // Loading actions
  setLoading: (loading: boolean) => void;
  setLoadingAverage: (loading: boolean) => void;
  setLoadingCharts: (loading: boolean) => void;
  setLoadingPercentage: (loading: boolean) => void;
  setLoadingMacroSummary: (loading: boolean) => void;
  setLoadingMicroSummary: (loading: boolean) => void;

  // Error actions
  setError: (error: string | null) => void;
  setErrorAverage: (error: string | null) => void;
  setErrorCharts: (error: string | null) => void;
  setErrorPercentage: (error: string | null) => void;
  setErrorMacroSummary: (error: string | null) => void;
  setErrorMicroSummary: (error: string | null) => void;

  // Reset actions
  reset: () => void;
};

export type NutritionDashboardStore = NutritionDashboardState &
  NutritionDashboardActions;

const initialState: NutritionDashboardState = {
  // Filter state
  activeFilter: 'last_7_days',

  // Data states
  averageData: null,
  chartData: {
    calories: null,
    carbs: null,
    protein: null,
    fat: null,
    fiber: null,
    micro: null,
    additional: null,
  },
  percentageData: null,
  macroSummary: null,
  microSummary: null,

  // Loading states
  loading: false,
  loadingAverage: false,
  loadingCharts: false,
  loadingPercentage: false,
  loadingMacroSummary: false,
  loadingMicroSummary: false,

  // Error states
  error: null,
  errorAverage: null,
  errorCharts: null,
  errorPercentage: null,
  errorMacroSummary: null,
  errorMicroSummary: null,
};

export const useNutritionDashboardStore = create<NutritionDashboardStore>(
  (set) => ({
    ...initialState,

    // Filter actions
    setActiveFilter: (filter) => set({ activeFilter: filter }),

    // Data actions
    setAverageData: (data) => set({ averageData: data }),
    setChartData: (data) => set({ chartData: data }),
    setPercentageData: (data) => set({ percentageData: data }),
    setMacroSummary: (data) => set({ macroSummary: data }),
    setMicroSummary: (data) => set({ microSummary: data }),

    // Loading actions
    setLoading: (loading) => set({ loading }),
    setLoadingAverage: (loading) => set({ loadingAverage: loading }),
    setLoadingCharts: (loading) => set({ loadingCharts: loading }),
    setLoadingPercentage: (loading) => set({ loadingPercentage: loading }),
    setLoadingMacroSummary: (loading) => set({ loadingMacroSummary: loading }),
    setLoadingMicroSummary: (loading) => set({ loadingMicroSummary: loading }),

    // Error actions
    setError: (error) => set({ error }),
    setErrorAverage: (error) => set({ errorAverage: error }),
    setErrorCharts: (error) => set({ errorCharts: error }),
    setErrorPercentage: (error) => set({ errorPercentage: error }),
    setErrorMacroSummary: (error) => set({ errorMacroSummary: error }),
    setErrorMicroSummary: (error) => set({ errorMicroSummary: error }),

    // Reset actions
    reset: () => set(initialState),
  })
);
