import React, { FC, memo, ReactNode, useRef, useEffect, useState } from 'react';

import { Box } from '@mui/material';

import { cn } from '@/lib/utils';

import colors from '@/utils/colors';

type Props = {
  fields: {
    label: string;
    content: ReactNode;
  }[];
  variant?: 'normal' | 'small';
};

const FieldSections: FC<Props> = ({ fields, variant = 'normal' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [rowIndexes, setRowIndexes] = useState<number[]>([]);

  useEffect(() => {
    if (containerRef.current) {
      const items = Array.from(
        containerRef.current.children
      ) as HTMLDivElement[];
      const rowPositions: number[] = [];
      let lastTop = items[0]?.offsetTop || 0;

      items.forEach((item, index) => {
        if (item.offsetTop > lastTop) {
          rowPositions.push(index);
          lastTop = item.offsetTop;
        }
      });

      setRowIndexes(rowPositions);
    }
  }, [fields]);

  return (
    <Box className="w-full overflow-hidden min-w-full">
      <div
        ref={containerRef}
        style={{ backgroundColor: colors.common.lightBlue }}
        className="flex flex-wrap rounded-lg border-2 md:border-black w-full min-w-full"
      >
        {fields.map(({ label, content }, index) => {
          const isFirstInRow = index === 0 || rowIndexes.includes(index);

          return (
            <React.Fragment key={label}>
              {rowIndexes.includes(index) && (
                <div className="w-full md:border-t md:border-black" />
              )}
              <div
                className={cn(
                  'flex flex-col items-start gap-1 border-black p-2',
                  'border-r md:border-r-0',
                  {
                    'md:border-l': !isFirstInRow,
                    'text-sm xl:px-2': variant === 'small',
                    'text-base xl:px-4': variant === 'normal',
                  }
                )}
              >
                <span
                  className={cn('whitespace-nowrap', {
                    'text-[0.8rem] md:text-sm': variant === 'small',
                  })}
                >
                  {label}
                </span>
                <div
                  className={cn({ 'flex items-start': variant === 'small' })}
                >
                  {content}
                </div>
              </div>
            </React.Fragment>
          );
        })}
      </div>
    </Box>
  );
};

export default memo(FieldSections);
