import React, { memo, useEffect, useMemo, useState, useCallback } from 'react';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useLifestyleFilterStore } from '@/store/emr/lifestyle/filter-store';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { exercisePatternStore } from '@/store/emr/lifestyle/physical-activity/practice/exercise-pattern-store';

import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import Accordion from '@/views/emr/lifestyle/shared/Accordion';

import TimeLine from '@/emr/components/consultation/TimeLine';
import FinalizeModal from '@/emr/components/lifestyle/lifestyle-forms/shared/FinalizeModal';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import ExercisePatternEditModal from './ExercisePatternEditModal';
import ExercisePatternTimelineForm from './ExercisePatternTimelineForm';

const ExercisePatterns = () => {
  const { setSource, modalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);
  const { getPatientData, patientData, loading, finalizeRecord, finalizing } =
    exercisePatternStore();
  const { fromDate, toDate } = useLifestyleFilterStore();

  const [openedAccordion, setOpenedAccordion] = useState<number | null>(null);
  const [recordToFinalize, setRecordToFinalize] =
    useState<QuestionnaireResponse | null>(null);
  const [editRecord, setEditRecord] = useState<QuestionnaireResponse | null>(
    null
  );

  useEffect(() => {
    setSource(LifestyleSources.PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS);
  }, [setSource]);

  useEffect(() => {
    getPatientData(fromDate, toDate);
  }, [getPatientData, fromDate, toDate]);

  useEffect(() => {
    if (!modalOpen) {
      getPatientData(fromDate, toDate);
    }
  }, [modalOpen, getPatientData, fromDate, toDate]);

  const handleFinalize = useCallback(
    async (id: string) => {
      await finalizeRecord(id);
      setRecordToFinalize(null);

      if (editRecord?.id === id) {
        setEditRecord({
          ...editRecord,
          status: LifestyleRecordStatus.FINALIZED,
        });
      }
    },
    [finalizeRecord, editRecord]
  );

  const timelineItems = useMemo(() => {
    return patientData.map((data, index) => ({
      content: (
        <Accordion
          key={`${data.id}-${data.created_on}`}
          date={data.created_on}
          open={openedAccordion === index}
          onToggle={() => {
            setOpenedAccordion(openedAccordion === index ? null : index);
          }}
          onFinalise={() => setRecordToFinalize(data)}
          finalised={data.status === LifestyleRecordStatus.FINALIZED}
          doctorName={data?.doctor?.name}
          designation={data?.doctor?.designation}
          department={data?.doctor?.department || profile?.general?.department}
          stepper={['Practice', 'Exercise Patterns']}
          className="bg-white rounded-lg shadow-sm"
          onExpand={() => setEditRecord(data)}
        >
          <ExercisePatternTimelineForm data={data} />
        </Accordion>
      ),
    }));
  }, [patientData, openedAccordion, profile?.general?.department]);

  return (
    <div className="h-full flex flex-col p-1 overflow-y-auto">
      <TimeLine items={timelineItems} loading={loading} />

      <FinalizeModal
        open={!!recordToFinalize}
        onClose={() => setRecordToFinalize(null)}
        onFinalize={() =>
          recordToFinalize?.id && handleFinalize(recordToFinalize.id)
        }
        loading={finalizing}
      />
      <ExercisePatternEditModal
        open={!!editRecord}
        onClose={() => setEditRecord(null)}
        formFields={editRecord}
        onFinalize={(record) => {
          if (record) {
            setRecordToFinalize(record);
          }
        }}
      />
    </div>
  );
};

export default memo(ExercisePatterns);
