'use client';

import { ToolbarConfig } from '@stagewise/toolbar';

const isBrowser = typeof window !== 'undefined';

export const stageWiseConfig: {
  init: () => void;
  stagewiseConfig: ToolbarConfig;
} = {
  stagewiseConfig: {
    plugins: [],
  },

  init: () => {
    if (!isBrowser) return;
    // if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
    //   initToolbar(stageWiseConfig.stagewiseConfig);
    // }
  },
};
