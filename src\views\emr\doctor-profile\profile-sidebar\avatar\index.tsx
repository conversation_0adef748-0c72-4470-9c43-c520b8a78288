import React, { useState, useEffect } from 'react';

import { cn } from '@/lib/utils';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useProfilePictureStore } from '@/store/emr/doctor-profile/profile-picture-store';

import EditIcon from '@/assets/svg/EditIcon';

import AvatarModal from './AvatarModal';
import profileImage from './profile-image.png';

export interface AvatarProps {
  name?: string;
  src?: string;
}

function getAltText(name?: string) {
  if (name) {
    return `${name} profile picture`;
  }
  return 'user profile picture';
}

export default function Avatar({ name, src }: AvatarProps) {
  const { doctorProfile } = useDoctorStore();
  const { fetchProfilePicture, previewUrl } = useProfilePictureStore();

  const [open, setOpen] = useState(false);

  const userId = doctorProfile?.id ?? '';

  useEffect(() => {
    if (userId) {
      fetchProfilePicture(userId);
    }
  }, [userId, fetchProfilePicture]);

  return (
    <>
      <div className="relative">
        <div
          className={cn(
            'h-24 w-24 md:h-25 md:w-25 xl:h-33 xl:w-33',
            'relative border-3 border-white',
            'rounded-full p-0.5 bg-white overflow-hidden'
          )}
        >
          <img
            className={cn('h-full w-full object-cover', 'rounded-full')}
            src={previewUrl || src || profileImage.src}
            alt={getAltText(name)}
          />
        </div>
        <button
          className={cn(
            'w-5 h-5 absolute right-1 bottom-1',
            'md:w-7 md:h-7',
            'bg-black',
            'rounded-full flex items-center justify-center text-white'
          )}
          onClick={() => setOpen(true)}
          type="button"
        >
          <EditIcon className="h-2 md:h-3 w-auto" />
        </button>
      </div>
      <AvatarModal open={open} onClose={() => setOpen(false)} />
    </>
  );
}
