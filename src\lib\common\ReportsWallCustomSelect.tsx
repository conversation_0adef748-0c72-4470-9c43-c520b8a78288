import * as Select from '@radix-ui/react-select';
import { BiChevronDown } from 'react-icons/bi';

const selectItems = [
  {
    label: 'All Lab Reports',
    value: 'all',
  },
  {
    label: 'Pathology',
    value: 'pathology',
  },
  {
    label: 'Biochemistry',
    value: 'biochemistry',
  },
  {
    label: 'Microbiology',
    value: 'microbiology',
  },
  {
    label: 'Hematology',
    value: 'hematology',
  },
  {
    label: 'Genetic',
    value: 'genetic',
  },
];

const ReportsWallCustomSelect = () => (
  <Select.Root>
    <Select.Trigger className="flex gap-5 items-center justify-between border border-black px-2 py-1 rounded-full">
      <Select.Value placeholder="Select a type" />
      <Select.Icon className="text-violet11">
        <BiChevronDown />
      </Select.Icon>
    </Select.Trigger>

    <Select.Portal>
      <Select.Content
        position="popper"
        align="end"
        sideOffset={8}
        className="overflow-hidden p-2.5 border border-black bg-[#FCFCFC] rounded-lg w-full flex-1"
      >
        <Select.Viewport>
          {selectItems.map((item, i) => (
            <>
              <Select.Item
                value={item.value}
                className="data-[state=checked]:text-[#1AA6F1] hover:cursor-pointer outline-none"
              >
                <Select.ItemText>{item.label}</Select.ItemText>
              </Select.Item>
              {i < selectItems.length - 1 && (
                <Select.Separator className="border-[0.25px] my-2.5 border-[#637D92]" />
              )}
            </>
          ))}
        </Select.Viewport>
        <Select.ScrollDownButton />
      </Select.Content>
    </Select.Portal>
  </Select.Root>
);

export default ReportsWallCustomSelect;
