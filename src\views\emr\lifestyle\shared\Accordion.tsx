import React from 'react';

import {
  Accordion as MuiAccordion,
  AccordionSummary,
  AccordionDetails,
  styled,
} from '@mui/material';

import AccordionTitle from './AccordionTitle';

const StyledAccordion = styled(MuiAccordion)<{
  width?: string;
}>(({ expanded, width }) => ({
  width: width || '100%',
  minWidth: '100%',
  backgroundColor: 'transparent',
  boxShadow: 'none',
  '&:before': {
    display: 'none',
  },
  ['& .MuiAccordionSummary-root']: {
    minHeight: 48,
    padding: '0 12px',
    borderRadius: '6px',
    backgroundColor: expanded ? '#B4E5FE' : '#E6F6FF',

    border: '1px solid #E0E0E0',
    '&.Mui-expanded': {
      minHeight: 48,
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
    },
  },

  ['& .MuiAccordionSummary-content']: {
    margin: 0,
    minHeight: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: '16px',
    flexWrap: 'wrap',
    '&.Mui-expanded': {
      margin: 0,
    },
  },

  ['& .MuiAccordionDetails-root']: {
    backgroundColor: 'white',
    padding: 8,
    border: '2px solid #E0E0E0',
    borderTop: 'none',
    borderBottomLeftRadius: '8px',
    borderBottomRightRadius: '8px',
  },
}));

interface AccordionProps {
  children: React.ReactNode;
  designation?: string;
  doctorName?: string;
  department?: string;
  stepper?: string[];
  date?: string;
  open: boolean;
  onToggle?: () => void;
  onFinalise?: () => void;
  onExpand?: () => void;
  onPrint?: () => void;
  finalised?: boolean;
  expand?: boolean;
  width?: string;
  className?: string;
  isKnowledge?: boolean;
  isAttitude?: boolean;
}

const Accordion: React.FC<AccordionProps> = ({
  children,
  designation,
  doctorName,
  department,
  stepper = [],
  date,
  open,
  onToggle,
  onFinalise,
  onExpand,
  onPrint,
  finalised = true,
  expand,
  width,
  className = '',
  isKnowledge = false,
  isAttitude = false,
}) => {
  return (
    <StyledAccordion
      className={`!h-full !w-full ${className}`}
      classes={{ heading: '!w-full' }}
      expanded={open}
      onChange={onToggle}
      width={width}
    >
      <AccordionSummary
        className="gap-1 w-full"
        classes={{
          root: '!w-full',
          content: '!w-full',
          expanded: '!w-full',
          gutters: '!w-full',
        }}
      >
        <AccordionTitle
          expand={expand}
          doctorName={doctorName}
          finalised={finalised}
          onExpand={onExpand}
          onFinalise={onFinalise}
          onPrint={onPrint}
          open={open}
          stepper={stepper}
          designation={designation}
          department={department}
          date={date}
          isKnowledge={isKnowledge}
          isAttitude={isAttitude}
        />
      </AccordionSummary>
      <AccordionDetails className="accordion-details">
        {children}
      </AccordionDetails>
    </StyledAccordion>
  );
};

export default Accordion;
