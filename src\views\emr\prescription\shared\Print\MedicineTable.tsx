import { Box } from '@mui/material';

import Table from '@/core/components/table';

import { TABLE_HEADERS, TABLE_STYLES } from './constants';

export const MedicineTable: React.FC<{ medicines: any[] }> = ({
  medicines,
}) => {
  const formatMedicineRow = (med: any, index: number) => ({
    id: med.id || index.toString(),
    no: { value: (index + 1).toString() },
    drugName: { value: med.genericName || med.GenericName || 'N/A' },
    genericName: { value: med.genericName || med.GenericName || 'N/A' },
    brandName: { value: med.brandName || med.BrandName || 'N/A' },
    strength: { value: med.strength || med.Strength || 'N/A' },

    frequency: { value: med.frequency || 'N/A' },
    duration: { value: med.duration || 'N/A' },
    quantity: { value: med.quantity || 'N/A' },
    route: { value: med.route || 'N/A' },
    instructions: { value: med.instructions || 'N/A' },
    cost: {
      value: (
        parseFloat(med.cost || '0') * parseFloat(med.quantity || '1')
      ).toFixed(2),
    },
  });
  const tableRows = medicines.map((med, index) =>
    formatMedicineRow(med, index)
  );

  return (
    <Box
      sx={{
        flex: 1,
        overflow: 'auto',
        display: 'flex',
        flexDirection: 'column',
        pb: 2,
      }}
    >
      <Box
        sx={{
          flex: '0 0 auto',
          overflow: 'hidden',
          minHeight: '200px',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Table
          headers={TABLE_HEADERS}
          rows={tableRows}
          stickyHeader
          tableContainerProps={{ sx: TABLE_STYLES }}
        />
      </Box>
    </Box>
  );
};
