import { DatePicker } from '@/lib/common/date_picker';

export type SelectConsultationDateProps = {
  value: Date;
  onChange: (date: Date) => void;
};

export default function SelectConsultationDateProps({
  value,
  onChange,
}: SelectConsultationDateProps) {
  const handleChange = (date?: Date) => {
    onChange(date || new Date());
  };

  return <DatePicker value={value} onChange={handleChange} />;
}
