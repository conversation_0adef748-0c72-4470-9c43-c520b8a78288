import React, { memo } from 'react';

import { Controller, FieldValues, UseControllerProps } from 'react-hook-form';

type InputProps = React.InputHTMLAttributes<HTMLInputElement>;

type Props<T extends FieldValues> = UseControllerProps<T> & InputProps;

const TableInput = <T extends FieldValues>({
  name,
  control,
  defaultValue,
  ...inputProps
}: Props<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}
      render={({ field: { onChange, onBlur, value, ref } }) => (
        <input
          {...inputProps}
          onChange={onChange}
          onBlur={onBlur}
          value={value}
          ref={ref}
          style={{ outline: 'none', border: 'none' }}
          className="p-2 text-sm h-full bg-transparent w-full m-0 min-h-[40px] disabled:bg-gray-200 disabled:cursor-not-allowed"
        />
      )}
    />
  );
};

export default memo(TableInput) as typeof TableInput;
