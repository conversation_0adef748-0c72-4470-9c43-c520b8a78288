/**
 * Validation utilities for various ID proof types
 */

/**
 * Formats PAN number to uppercase
 * @param value - The PAN number string
 * @returns Formatted PAN number in uppercase
 */
export const formatPAN = (value: string): string => {
  return value.toUpperCase().replace(/[^A-Z0-9]/g, '');
};

/**
 * Validates PAN number format
 * PAN format: ********** (5 letters + 4 digits + 1 letter)
 * @param val - The PAN number to validate
 * @returns true if valid, false otherwise
 */
export const panValidationYup = (val?: string): boolean => {
  if (!val || val.trim() === '') return true; // Allow empty values (optional field)

  const sanitizedValue = val.replace(/[^A-Z0-9]/g, '').toUpperCase();

  // Simple validation: just check PAN format (5 letters + 4 digits + 1 letter)
  if (sanitizedValue.length === 10) {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(sanitizedValue);
  }

  // If less than 10 characters, allow it (user is still typing)
  if (sanitizedValue.length < 10) {
    // Check if current input follows PAN pattern so far
    const partialPanRegex = /^[A-Z]{0,5}[0-9]{0,4}[A-Z]{0,1}$/;
    return partialPanRegex.test(sanitizedValue);
  }

  // Invalid if more than 10 characters
  return false;
};

/**
 * Formats passport number to uppercase and removes special characters
 * @param value - The passport number string
 * @returns Formatted passport number
 */
export const formatPassport = (value: string): string => {
  return value.toUpperCase().replace(/[^A-Z0-9]/g, '');
};

/**
 * Validates Indian passport number format
 * Indian passport format: A1234567 or ********* (1-2 letters + 6-7 digits)
 * @param val - The passport number to validate
 * @returns true if valid, false otherwise
 */
export const passportValidationYup = (val?: string): boolean => {
  if (!val) return false;

  const sanitizedValue = val.replace(/[^A-Z0-9]/g, '').toUpperCase();

  if (sanitizedValue.length < 8 || sanitizedValue.length > 9) {
    return false;
  }

  const passportRegex = /^[A-Z]{1,2}[0-9]{6,7}$/;
  if (!passportRegex.test(sanitizedValue)) {
    return false;
  }

  return true;
};

/**
 * Generic validation function that routes to appropriate validator
 * @param proofType - The type of proof (Aadhar card, Pan card, Passport)
 * @param value - The value to validate
 * @returns true if valid, false otherwise
 */
export const validateProofNumber = (
  proofType: string,
  value?: string
): boolean => {
  switch (proofType) {
    case 'Pan card':
      return panValidationYup(value);
    case 'Passport':
      return passportValidationYup(value);
    default:
      return true;
  }
};
