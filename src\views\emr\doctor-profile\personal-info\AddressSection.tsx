import { useEffect, useState } from 'react';

import { Controller, FieldError, useFormContext } from 'react-hook-form';

import FileInput from '@core/components/file-input';
import TextInput from '@core/components/text-input';

import { cn } from '@/lib/utils';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { usePostalStore } from '@/store/emr/doctor-profile/personal-info/pincode';

import { states } from '@/utils/constants/master';
import {
  emailPattern,
  enforceNumericInput,
  mobileNumberPattern,
  residentialPhonePattern,
  restrictMaxLength,
  restrictMobile,
} from '@/utils/validation';

import SectionTitle from '@/views/emr/doctor-profile/personal-info/SectionTitle';

import { Checkbox } from '@/components/ui/checkbox';

import { AddressDetails } from '@/types/emr/doctor-profile/personal-info';

import { EditableSelectField, SelectField } from './Components';

export interface AddressSectionProps {
  className?: string;
  type: 'permanent' | 'current';
  isFieldDisabled: (_fieldName: string) => boolean;
  renderEditIcon: (_fieldName: string) => JSX.Element | null;
}

type ErrorsType = {
  address: Record<
    'permanent' | 'current',
    Partial<Record<'mobile' | keyof AddressDetails, FieldError>>
  >;
};

export default function AddressSection(props: AddressSectionProps) {
  const {
    register,
    control,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = useFormContext();
  const {
    currentStreets,
    permanentStreets,
    setCurrentStreet,
    setPermanentStreet,
    locationDataByPincode,
    setPermanentPincode,
    setCurrentPincode,
    setHasCurrentPincode,
    setHasPermanentPincode,
  } = usePostalStore();

  const [isSameAsPermanent, setIsSameAsPermanent] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { doctorProfile } = useDoctorStore();

  const { type, isFieldDisabled, renderEditIcon } = props;

  const permanentAddress = watch('address.permanent');
  const permanentAddressProof = watch('address.permanent.proof');
  const permanentPincode = watch(`address.permanent.pinCode`);
  const permanentsSelectedStreets = watch(`address.permanent.street`);
  const currentPincode = watch(`address.current.pinCode`);
  const currentStreetValue = watch(`address.current.street`);

  // Effect for permanent pincode changes
  useEffect(() => {
    if (permanentPincode?.length === 6) {
      setPermanentPincode(permanentPincode);
    } else {
      setPermanentPincode('');
      setPermanentStreet('');
      setValue(`address.permanent.street`, '');
      setValue(`address.permanent.city`, '');
      setValue(`address.permanent.district`, '');
      setValue(`address.permanent.state`, '');
      setValue(`address.permanent.country`, '');
      usePostalStore.getState().locationDataByPincode.permanent = null;
    }
  }, [permanentPincode, setPermanentPincode, setPermanentStreet, setValue]);

  // Effect for current pincode changes
  useEffect(() => {
    // Don't fetch location data if we're syncing from permanent (same pincode)
    if (isSameAsPermanent && currentPincode === permanentPincode) {
      return;
    }

    if (currentPincode?.length === 6) {
      setCurrentPincode(currentPincode);
    } else {
      setCurrentPincode('');
      setValue(`address.current.street`, '');
      setValue(`address.current.city`, '');
      setValue(`address.current.district`, '');
      setValue(`address.current.state`, '');
      setValue(`address.current.country`, '');
      usePostalStore.getState().locationDataByPincode.current = null;
    }
  }, [
    currentPincode,
    setCurrentPincode,
    setValue,
    isSameAsPermanent,
    permanentPincode,
  ]);

  useEffect(() => {
    if (permanentsSelectedStreets) {
      setPermanentStreet(permanentsSelectedStreets);

      const selectedStreetObj = permanentStreets.find(
        (s) => s.value === permanentsSelectedStreets
      );

      if (selectedStreetObj?.city) {
        setValue(`address.permanent.city`, selectedStreetObj.city);
      }
    }
  }, [
    permanentsSelectedStreets,
    permanentStreets,
    setPermanentStreet,
    setValue,
  ]);

  useEffect(() => {
    if (currentStreetValue) {
      const selectedStreetObj = currentStreets.find(
        (s) => s.value === currentStreetValue
      );

      if (selectedStreetObj?.city) {
        setValue(`address.current.city`, selectedStreetObj.city);
      }
    }
  }, [currentStreetValue, currentStreets, setValue]);

  // Effect to sync location data when it changes - but NOT during address sync
  useEffect(() => {
    // Skip any updates if we're in the middle of syncing addresses OR if same as permanent is checked
    if (isSameAsPermanent || isUpdating) {
      return;
    }

    const updateIfValid = (
      type: 'permanent' | 'current',
      field: string,
      value: string | undefined
    ) => {
      if (!value) {
        return false;
      }

      const currentValue = getValues(`address.${type}.${field}`);

      if (currentValue !== undefined && currentValue !== '') {
        return false;
      }

      if (value !== currentValue) {
        setValue(`address.${type}.${field}`, value);
        return true;
      }

      return false;
    };

    // Process permanent address location data (only if not same as permanent)
    if (locationDataByPincode?.permanent && !isSameAsPermanent) {
      const { district, state, country, city } =
        locationDataByPincode.permanent;

      updateIfValid('permanent', 'district', district);
      updateIfValid('permanent', 'state', state);
      updateIfValid('permanent', 'country', country);
      updateIfValid('permanent', 'city', city);
    }

    // Process current address location data (only if not same as permanent)
    if (locationDataByPincode?.current && !isSameAsPermanent) {
      const { district, state, country, city } = locationDataByPincode.current;

      updateIfValid('current', 'district', district);
      updateIfValid('current', 'state', state);
      updateIfValid('current', 'country', country);
      updateIfValid('current', 'city', city);
    }
  }, [
    locationDataByPincode,
    setValue,
    isSameAsPermanent,
    getValues,
    isUpdating,
  ]);

  // Simplified sync effect for when checkbox is checked
  useEffect(() => {
    if (isSameAsPermanent && !isUpdating) {
      // All fields to sync from permanent to current (including empty ones)
      const fieldsToSync = [
        'home',
        'street',
        'phone',
        'mobile',
        'email',
        'pinCode',
      ];

      fieldsToSync.forEach((field) => {
        const value = permanentAddress?.[field] || ''; // Use empty string if undefined
        setValue(`address.current.${field}`, value);
      });

      // Handle proof (copy or clear if permanent doesn't have it)
      if (permanentAddressProof) {
        setValue(`address.current.proof.url`, permanentAddressProof.url || '');
        setValue(
          `address.current.proof.description`,
          permanentAddressProof.description || ''
        );
      } else {
        setValue(`address.current.proof.url`, '');
        setValue(`address.current.proof.description`, '');
      }

      // For location-based fields, prioritize permanent address form values over empty location data
      const locationFields: (keyof Pick<
        AddressDetails,
        'city' | 'district' | 'state' | 'country'
      >)[] = ['city', 'district', 'state', 'country'];

      locationFields.forEach((field) => {
        let valueToUse = '';

        // First priority: permanent address form value (if not empty)
        if (
          permanentAddress?.[field] &&
          permanentAddress[field].trim() !== ''
        ) {
          valueToUse = permanentAddress[field];
        }
        // Second priority: location data from store (if available and not empty)
        else if (
          locationDataByPincode.permanent?.[field] &&
          locationDataByPincode.permanent[field].trim() !== ''
        ) {
          valueToUse = locationDataByPincode.permanent[field];
        }
        // Otherwise use empty string
        else {
          valueToUse = '';
        }

        setValue(`address.current.${field}`, valueToUse);
      });
    }
  }, [
    isSameAsPermanent,
    permanentAddress,
    permanentAddressProof,
    locationDataByPincode.permanent,
    setValue,
    isUpdating,
  ]);

  // Single handleCheckboxChange function
  const handleCheckboxChange = async (checked: boolean) => {
    // Prevent multiple simultaneous updates
    if (isUpdating) {
      return;
    }

    setIsUpdating(true);

    // Set the checkbox state
    setIsSameAsPermanent(checked);

    if (checked) {
      // Copy basic fields from permanent to current
      const fieldsToSync = [
        'home',
        'street',
        'phone',
        'mobile',
        'email',
        'pinCode',
      ];

      fieldsToSync.forEach((field) => {
        const value = permanentAddress?.[field] || ''; // Use empty string if undefined/null
        setValue(`address.current.${field}`, value);
      });

      // Handle proof (copy or clear)
      if (permanentAddressProof) {
        setValue(`address.current.proof.url`, permanentAddressProof.url || '');
        setValue(
          `address.current.proof.description`,
          permanentAddressProof.description || ''
        );
      } else {
        setValue(`address.current.proof.url`, '');
        setValue(`address.current.proof.description`, '');
      }

      // For location-based fields, prioritize permanent address form values over empty location data
      const locationFields: (keyof Pick<
        AddressDetails,
        'city' | 'district' | 'state' | 'country'
      >)[] = ['city', 'district', 'state', 'country'];

      locationFields.forEach((field) => {
        let valueToUse = '';

        // First priority: permanent address form value (if not empty)
        if (
          permanentAddress?.[field] &&
          permanentAddress[field].trim() !== ''
        ) {
          valueToUse = permanentAddress[field];
        }
        // Second priority: location data from store (if available and not empty)
        else if (
          locationDataByPincode.permanent?.[field] &&
          locationDataByPincode.permanent[field].trim() !== ''
        ) {
          valueToUse = locationDataByPincode.permanent[field];
        }
        // Otherwise use empty string
        else {
          valueToUse = '';
        }

        setValue(`address.current.${field}`, valueToUse);
      });
    } else {
      // Clear proof fields first
      setValue(`address.current.proof.url`, '');
      setValue(`address.current.proof.description`, '');

      // Restore from doctor profile or clear fields
      const doctorCurrentAddress = doctorProfile?.personal?.address?.current;
      if (doctorCurrentAddress) {
        Object.keys(doctorCurrentAddress).forEach((key) => {
          const value = doctorCurrentAddress[key as keyof AddressDetails];
          if (value !== undefined) {
            setValue(`address.current.${key as keyof AddressDetails}`, value);
          }
        });
      } else {
        // Clear all current address fields
        const currentAddressKeys = [
          'home',
          'street',
          'city',
          'district',
          'state',
          'country',
          'phone',
          'mobile',
          'email',
          'pinCode',
        ];
        currentAddressKeys.forEach((key) => {
          setValue(`address.current.${key}`, '');
        });
      }
    }

    // Add a small delay to ensure all updates are processed
    setTimeout(() => {
      setIsUpdating(false);
    }, 100);
  };

  useEffect(() => {
    setHasPermanentPincode && setHasPermanentPincode(false);
    setHasCurrentPincode && setHasCurrentPincode(false);
  }, [setHasCurrentPincode, setHasPermanentPincode]);

  return (
    <div className={cn(props.className)}>
      <div className="flex justify-between items-center">
        <SectionTitle
          className="font-bold mb-4"
          title={type === 'current' ? 'Present' : 'Permanent'}
        />

        {type === 'current' && (
          <Checkbox
            label="Same as permanent"
            checked={isSameAsPermanent}
            onCheckedChange={handleCheckboxChange}
          />
        )}
      </div>

      <div className="flex flex-col w-full gap-4 md:gap-6">
        <TextInput
          key={`address.${type}.home`}
          label="Home"
          color="white"
          {...register(`address.${type}.home`)}
          disabled={isFieldDisabled(`address.${type}.home`)}
          endDecoration={renderEditIcon(`address.${type}.home`)}
          placeholder="House No/ Name"
        />
        <TextInput
          key="pinCode"
          label="Pin"
          color="white"
          {...register(`address.${type}.pinCode`)}
          disabled={isFieldDisabled(`address.${type}.pinCode`)}
          endDecoration={renderEditIcon(`address.${type}.pinCode`)}
          placeholder="685132"
          onKeyDown={restrictMaxLength(6)}
          onInput={enforceNumericInput({ maxLength: 6 })}
          pattern="[0-9]*"
          inputMode="numeric"
          onFocus={() => {
            if (type === 'current') {
              setHasCurrentPincode && setHasCurrentPincode(true);
            }
            if (type === 'permanent') {
              setHasPermanentPincode && setHasPermanentPincode(true);
            }
          }}
          onBlur={() => {
            if (type === 'current') {
              setHasCurrentPincode && setHasCurrentPincode(false);
            }
            if (type === 'permanent') {
              setHasPermanentPincode && setHasPermanentPincode(false);
            }
          }}
        />

        <EditableSelectField
          id={`address.${type}.street`}
          label="Street"
          options={
            type === 'current'
              ? currentStreets
              : type === 'permanent' ||
                  (type === 'current' && isSameAsPermanent)
                ? permanentStreets
                : []
          }
          value={watch(`address.${type}.street`)}
          wrapperClassName="flex-grow flex-1"
          onChange={(value) => setValue(`address.${type}.street`, value)}
          placeholder="Select"
          disabledInput={isFieldDisabled(`address.${type}.street`)}
          endDecoration={renderEditIcon(`address.${type}.street`)}
        />

        <TextInput
          key={`address.${type}.city`}
          label="City"
          color="white"
          {...register(`address.${type}.city`)}
          disabled={isFieldDisabled(`address.${type}.city`)}
          endDecoration={renderEditIcon(`address.${type}.city`)}
          placeholder="Chennai"
        />

        <TextInput
          key="district"
          label="District"
          color="white"
          {...register(`address.${type}.district`)}
          placeholder="Chennai"
          disabled={isFieldDisabled(`address.${type}.district`)}
          endDecoration={renderEditIcon(`address.${type}.district`)}
        />
        <SelectField
          id={`address.${type}.state`}
          label="State"
          options={states}
          value={watch(`address.${type}.state`)}
          wrapperClassName="flex-grow flex-1"
          onChange={(value) => setValue(`address.${type}.state`, value)}
          placeholder="Select"
          disabledInput={isFieldDisabled(`address.${type}.state`)}
          endDecoration={renderEditIcon(`address.${type}.state`)}
        />
        <TextInput
          key="country"
          label="Country"
          color="white"
          {...register(`address.${type}.country`)}
          disabled={isFieldDisabled(`address.${type}.country`)}
          endDecoration={renderEditIcon(`address.${type}.country`)}
          placeholder="India"
        />
        <TextInput
          key="phone"
          label="Residential Phone"
          color="white"
          {...register(`address.${type}.phone`, {
            pattern: residentialPhonePattern(),
          })}
          disabled={isFieldDisabled(`address.${type}.phone`)}
          endDecoration={renderEditIcon(`address.${type}.phone`)}
          placeholder="1234567890"
          errors={(errors as ErrorsType).address?.[type]?.phone}
          onKeyDown={restrictMaxLength(11)}
          onInput={enforceNumericInput({ maxLength: 11 })}
          pattern="[0-9]*"
          inputMode="numeric"
        />
        <TextInput
          key="mobile"
          label={type === 'permanent' ? 'Personal Mobile' : 'Official Mobile'}
          color="white"
          {...register(`address.${type}.mobile`, {
            pattern: mobileNumberPattern(),
          })}
          disabled={isFieldDisabled(`address.${type}.mobile`)}
          endDecoration={renderEditIcon(`address.${type}.mobile`)}
          placeholder="5520663388"
          onKeyDown={restrictMobile(10)}
          onInput={enforceNumericInput({ maxLength: 10 })}
          pattern="[0-9]*"
          inputMode="numeric"
          errors={(errors as ErrorsType).address?.[type]?.mobile}
        />
        <TextInput
          key="email"
          label={type === 'permanent' ? 'Personal Email' : 'Work Email'}
          color="white"
          {...register(`address.${type}.email`, {
            pattern: emailPattern(),
          })}
          disabled={isFieldDisabled(`address.${type}.email`)}
          endDecoration={renderEditIcon(`address.${type}.email`)}
          placeholder="<EMAIL>"
          errors={(errors as ErrorsType).address?.[type]?.email}
        />
        <Controller
          name={`address.${type}.proof.url`}
          control={control}
          render={({ field }) => (
            <FileInput
              label="Address Proof"
              value={field.value}
              onChange={(files) => field.onChange(files)}
              helperText={<AddressProofHelperText />}
              maxFileSize={6}
              allowedFileTypes={['image/jpeg', 'image/png', 'application/pdf']}
              fileTypeErrorMessage="*jpg/png/pdf files are allowed"
              showPreview
              isBoxStyle
            />
          )}
        />
        <TextInput
          key="proof.description"
          label="Proof Description"
          color="white"
          {...register(`address.${type}.proof.description`)}
          disabled={isFieldDisabled(`address.${type}.proof.description`)}
          endDecoration={renderEditIcon(`address.${type}.proof.description`)}
        />
      </div>
    </div>
  );
}

function AddressProofHelperText() {
  return (
    <div className="flex gap-2">
      <div>*jpg/png/pdf files are allowed</div>
      <div>*Maximum 6 MB</div>
    </div>
  );
}
