import React from 'react';

const DateIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
    >
      <path
        d="M1.91667 11.8332C1.59583 11.8332 1.32118 11.7189 1.09271 11.4905C0.864236 11.262 0.75 10.9873 0.75 10.6665V2.49984C0.75 2.179 0.864236 1.90435 1.09271 1.67588C1.32118 1.44741 1.59583 1.33317 1.91667 1.33317H2.5V0.166504H3.66667V1.33317H8.33333V0.166504H9.5V1.33317H10.0833C10.4042 1.33317 10.6788 1.44741 10.9073 1.67588C11.1358 1.90435 11.25 2.179 11.25 2.49984V10.6665C11.25 10.9873 11.1358 11.262 10.9073 11.4905C10.6788 11.7189 10.4042 11.8332 10.0833 11.8332H1.91667ZM1.91667 10.6665H10.0833V4.83317H1.91667V10.6665ZM1.91667 3.6665H10.0833V2.49984H1.91667V3.6665ZM6 7.1665C5.83472 7.1665 5.69618 7.1106 5.58438 6.9988C5.47257 6.88699 5.41667 6.74845 5.41667 6.58317C5.41667 6.41789 5.47257 6.27935 5.58438 6.16755C5.69618 6.05574 5.83472 5.99984 6 5.99984C6.16528 5.99984 6.30382 6.05574 6.41563 6.16755C6.52743 6.27935 6.58333 6.41789 6.58333 6.58317C6.58333 6.74845 6.52743 6.88699 6.41563 6.9988C6.30382 7.1106 6.16528 7.1665 6 7.1665ZM3.66667 7.1665C3.50139 7.1665 3.36285 7.1106 3.25104 6.9988C3.13924 6.88699 3.08333 6.74845 3.08333 6.58317C3.08333 6.41789 3.13924 6.27935 3.25104 6.16755C3.36285 6.05574 3.50139 5.99984 3.66667 5.99984C3.83194 5.99984 3.97049 6.05574 4.08229 6.16755C4.1941 6.27935 4.25 6.41789 4.25 6.58317C4.25 6.74845 4.1941 6.88699 4.08229 6.9988C3.97049 7.1106 3.83194 7.1665 3.66667 7.1665ZM8.33333 7.1665C8.16806 7.1665 8.02951 7.1106 7.91771 6.9988C7.8059 6.88699 7.75 6.74845 7.75 6.58317C7.75 6.41789 7.8059 6.27935 7.91771 6.16755C8.02951 6.05574 8.16806 5.99984 8.33333 5.99984C8.49861 5.99984 8.63715 6.05574 8.74896 6.16755C8.86076 6.27935 8.91667 6.41789 8.91667 6.58317C8.91667 6.74845 8.86076 6.88699 8.74896 6.9988C8.63715 7.1106 8.49861 7.1665 8.33333 7.1665ZM6 9.49984C5.83472 9.49984 5.69618 9.44394 5.58438 9.33213C5.47257 9.22032 5.41667 9.08178 5.41667 8.9165C5.41667 8.75123 5.47257 8.61269 5.58438 8.50088C5.69618 8.38907 5.83472 8.33317 6 8.33317C6.16528 8.33317 6.30382 8.38907 6.41563 8.50088C6.52743 8.61269 6.58333 8.75123 6.58333 8.9165C6.58333 9.08178 6.52743 9.22032 6.41563 9.33213C6.30382 9.44394 6.16528 9.49984 6 9.49984ZM3.66667 9.49984C3.50139 9.49984 3.36285 9.44394 3.25104 9.33213C3.13924 9.22032 3.08333 9.08178 3.08333 8.9165C3.08333 8.75123 3.13924 8.61269 3.25104 8.50088C3.36285 8.38907 3.50139 8.33317 3.66667 8.33317C3.83194 8.33317 3.97049 8.38907 4.08229 8.50088C4.1941 8.61269 4.25 8.75123 4.25 8.9165C4.25 9.08178 4.1941 9.22032 4.08229 9.33213C3.97049 9.44394 3.83194 9.49984 3.66667 9.49984ZM8.33333 9.49984C8.16806 9.49984 8.02951 9.44394 7.91771 9.33213C7.8059 9.22032 7.75 9.08178 7.75 8.9165C7.75 8.75123 7.8059 8.61269 7.91771 8.50088C8.02951 8.38907 8.16806 8.33317 8.33333 8.33317C8.49861 8.33317 8.63715 8.38907 8.74896 8.50088C8.86076 8.61269 8.91667 8.75123 8.91667 8.9165C8.91667 9.08178 8.86076 9.22032 8.74896 9.33213C8.63715 9.44394 8.49861 9.49984 8.33333 9.49984Z"
        fill="#012436"
      />
    </svg>
  );
};

export default DateIcon;
