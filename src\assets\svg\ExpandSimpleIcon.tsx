import * as React from 'react';
import { SVGProps } from 'react';

const ExpandSimpleIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="15"
    height="14"
    viewBox="0 0 15 14"
    fill="none"
  >
    <mask
      id="mask0_2947_9126"
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="15"
      height="14"
    >
      <rect x="0.79834" width="14" height="14" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_2947_9126)">
      <path
        d="M3.71484 11.0833V7.58332H4.88151V9.91666H7.21484V11.0833H3.71484ZM10.7148 6.41666V4.08332H8.38151V2.91666H11.8815V6.41666H10.7148Z"
        fill="#012436"
      />
    </g>
  </svg>
);

export default ExpandSimpleIcon;
