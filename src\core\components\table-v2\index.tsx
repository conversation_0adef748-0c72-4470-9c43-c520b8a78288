import React, { memo } from 'react';

import MuiTable from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';

import { TableBodyContentV2, TableHeaderV2 } from './Components';
import { StyledTableContainerV2, StyledTableHeadV2 } from './styled-components';
import { TableV2Props } from './types';

const TableV2: React.FC<TableV2Props> = ({
  headers,
  rows,
  loading,
  tableContainerProps = {},
  stickyHeader = false,
  noDataMessage,
  sortOptions,
}) => {
  return (
    <StyledTableContainerV2 {...tableContainerProps}>
      <MuiTable stickyHeader={stickyHeader}>
        <StyledTableHeadV2>
          <TableHeaderV2 headers={headers} sortOptions={sortOptions} />
        </StyledTableHeadV2>
        <TableBody>
          <TableBodyContentV2
            headers={headers}
            rows={rows}
            loading={loading}
            noDataMessage={noDataMessage}
          />
        </TableBody>
      </MuiTable>
    </StyledTableContainerV2>
  );
};

export default memo(TableV2);
