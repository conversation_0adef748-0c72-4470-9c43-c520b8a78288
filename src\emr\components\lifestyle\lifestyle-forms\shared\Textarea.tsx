import React, { FC, InputHTMLAttributes, memo, ReactNode } from 'react';

type Variant = 'gray' | 'white' | 'transparent';
export type TextareaProps = InputHTMLAttributes<HTMLTextAreaElement> & {
  label?: ReactNode;
  className?: string;
  variant?: Variant;
};

const inputStyles: Record<Variant, string> = {
  gray: 'text-black bg-gray-300 focus:ring-gray-300',
  white: 'text-gray-600 bg-white focus:ring-gray-300',
  transparent:
    'text-black bg-transparent border-none focus:border-none focus:ring-transparent ',
};

const disabledClasses =
  'disabled:text-black disabled:bg-transparent disabled:border-none disabled:focus:border-none disabled:focus:ring-transparent disabled:resize-none';

const Textarea: FC<TextareaProps> = ({
  className,
  label,
  variant = 'gray',
  value,
  onChange,
  disabled,
  ...props
}) => {
  return (
    <label className={`flex flex-col items-start gap-1 w-full ${className}`}>
      <span>{label}</span>
      <textarea
        className={`w-full h-full px-2 border border-transparent rounded-md outline-none focus:ring-2 ${disabledClasses} ${inputStyles[variant]}`}
        type="number"
        placeholder={disabled ? '' : 'Enter'}
        value={value}
        onChange={onChange}
        rows={4}
        disabled={disabled}
        draggable={!disabled}
        {...props}
      />
    </label>
  );
};

export default memo(Textarea);
