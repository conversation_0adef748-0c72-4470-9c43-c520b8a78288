'use client';

import { memo, useCallback, useEffect, useState } from 'react';

import { FormProvider, useForm, useFieldArray } from 'react-hook-form';

import dayjs from 'dayjs';
import { toast } from 'sonner';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDemographicsStore } from '@/store/emr/lifestyle/general/demographics';

import { calculateAge } from '@/utils/mrd/manage-patient/calculate-age';

import PenIcon from '@/assets/svg/PenIcon';

import {
  genderOptions,
  maritalStatusOptions,
} from '@/constants/mrd/manage-patient/select-options';

import AutoResizeTextArea from '@/views/emr/doctor-profile/personal-info/shared/AutoResizeTextArea';

import ControlledDatePicker from '@/components/controlled-inputs/ControlledDatePicker';
import ControlledRadio from '@/components/controlled-inputs/ControlledRadio';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';

import AppButton from '@/core/components/app-button';
import AppIcon from '@/core/components/app-icon';
import Loader from '@/core/components/app-loaders/Loader';
import {
  defaultValues,
  DemographicsForm,
} from '@/types/emr/lifestyle/demographics';

import ContactsTable from './ContactsTable';
import DemographicsView from './DemographicsView';

const Demographics = () => {
  const { patient } = useCurrentPatientStore();
  const {
    demographics,
    getPatientDemographics,
    createDemographics,
    updateDemographics,
    updating,
    clearStore,
    loading,
  } = useDemographicsStore();
  console.log(demographics);

  // Set initial view mode based on demographics.id
  const [isViewMode, setIsViewMode] = useState(() => !!demographics?.id);

  // Keep isViewMode in sync with demographics.id
  useEffect(() => {
    setIsViewMode(!!demographics?.id);
  }, [demographics?.id]);

  const methods = useForm<DemographicsForm>({ defaultValues });

  const { control, handleSubmit, register, reset } = methods;
  const contactFields = useFieldArray({
    control,
    name: 'contacts',
  });

  const [editFields, setEditFields] = useState({
    name: false,
    cmchId: false,
    age: false,
  });

  const [editedContacts, setEditedContacts] = useState<Set<number>>(new Set());

  const validateEmail = (email: string) => {
    // Allow any domain, but must end with .com
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.com$/i;
    return re.test(email);
  };

  const validatePhone = (phone: string) => {
    return /^\d{0,10}$/.test(phone);
  };

  const onSubmit = useCallback(
    async (data: DemographicsForm) => {
      const isCreateMode = !demographics?.id;
      const originalPatientContact = patient?.contact;

      // Check if contacts are exactly the original patient contact (not edited/added)
      const isUnchangedPatientContact =
        isCreateMode &&
        originalPatientContact &&
        data.contacts &&
        data.contacts.length === 1 &&
        (data.contacts[0].phone?.trim() || '') ===
          (originalPatientContact.phone?.trim() || '') &&
        (data.contacts[0].email?.trim() || '') ===
          (originalPatientContact.email?.trim() || '');

      // If unchanged, allow save without further validation
      if (isUnchangedPatientContact) {
        // Validate date of birth - check if it's a future date
        if (data.dob) {
          const dobDate = new Date(data.dob);
          const today = new Date();
          today.setHours(23, 59, 59, 999); // Set to end of today for accurate comparison

          // Check if the date is valid
          if (isNaN(dobDate.getTime())) {
            toast.error('Please enter a valid date of birth');
            return;
          }

          if (dobDate > today) {
            toast.error('Date of birth cannot be in the future');
            return;
          }
        }

        // Determine age value: form value > patient value > calculated value > never empty
        const ageValue =
          data.age && data.age.trim() !== ''
            ? data.age
            : patient?.age && patient.age.trim() !== ''
              ? patient.age
              : patient?.dob
                ? calculateAge(patient.dob)
                : '';

        if (demographics) {
          await updateDemographics({
            ...data,
            id: demographics?.id,
            maritalStatus: data?.maritalStatus?.value ?? '',
          });
        } else {
          await createDemographics({
            ...data,
            dob: data.dob || patient?.dob || '',
            maritalStatus: data?.maritalStatus?.value ?? '',
            age: ageValue,
            cmchId: (data.cmchId || patient?.id) ?? '',
          });
        }
        setIsViewMode(true);
        return;
      }

      // Validate date of birth - check if it's a future date
      if (data.dob) {
        const dobDate = new Date(data.dob);
        const today = new Date();
        today.setHours(23, 59, 59, 999); // Set to end of today for accurate comparison

        // Check if the date is valid
        if (isNaN(dobDate.getTime())) {
          toast.error('Please enter a valid date of birth');
          return;
        }

        if (dobDate > today) {
          toast.error('Date of birth cannot be in the future');
          return;
        }
      }

      // Usual validation for edited/added contacts
      if (!data.contacts || data.contacts.length === 0) {
        toast.error('Please add at least one contact');
        return;
      }

      // Validate each contact
      for (let i = 0; i < data.contacts.length; i++) {
        const contact = data.contacts[i];
        const phone = contact.phone?.trim() || '';
        const email = contact.email?.trim() || '';

        // Check if this is masked data (contains asterisks)
        const isMasked = phone.includes('*') || email.includes('*');

        // Skip validation for masked data
        if (isMasked) {
          continue;
        }

        // For non-masked data, ensure at least one field is provided
        if (!phone && !email) {
          toast.error(
            `Row ${i + 1}: Please provide either phone number or email`
          );
          return;
        }

        // If phone is provided, validate it
        if (phone) {
          // Check if phone contains only numbers and is 10 digits or less
          if (!/^\d{0,10}$/.test(phone)) {
            toast.error(
              `Row ${i + 1}: Phone number must be 10 digits or less and contain only numbers`
            );
            return;
          }

          // Check if phone has at least 10 digits if provided
          if (phone && phone.length > 0 && phone.length < 10) {
            toast.error(
              `Row ${i + 1}: Phone number must be at least 10 digits`
            );
            return;
          }
        }

        // If email is provided, validate it
        if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
          toast.error(`Row ${i + 1}: Please enter a valid email address`);
          return;
        }

        // Skip validation for create mode if contact matches original patient data
        if (isCreateMode && originalPatientContact) {
          const originalPhone = originalPatientContact.phone?.trim() || '';
          const originalEmail = originalPatientContact.email?.trim() || '';

          if (phone === originalPhone && email === originalEmail) {
            continue; // Skip validation for original patient data
          }
        }

        // Check if at least one of phone or email is provided
        if (!phone && !email) {
          toast.error(
            `Row ${i + 1}: Please provide either phone number or email`
          );
          return;
        }

        // Validate phone format (if provided)
        if (phone && !validatePhone(phone)) {
          toast.error(
            `Row ${i + 1}: Phone number must be 10 digits or less and contain only numbers`
          );
          return;
        }

        // Validate email format (if provided)
        if (email && !validateEmail(email)) {
          toast.error(`Row ${i + 1}: Please enter a valid email address`);
          return;
        }
      }

      // Validate duplicate contacts: only if BOTH phone and email are the same in two or more rows
      const comboCount = new Map();
      for (let i = 0; i < (data.contacts || []).length; i++) {
        const contact = data.contacts[i];
        const phone = contact.phone?.trim() || '';
        const email = contact.email?.trim() || '';

        // Skip duplicate validation for create mode if contact matches original patient data
        if (isCreateMode && originalPatientContact) {
          const originalPhone = originalPatientContact.phone?.trim() || '';
          const originalEmail = originalPatientContact.email?.trim() || '';

          if (phone === originalPhone && email === originalEmail) {
            continue; // Skip duplicate check for original patient data
          }
        }

        const key = `${phone}||${email}`;
        if (comboCount.has(key)) {
          comboCount.set(key, comboCount.get(key) + 1);
        } else {
          comboCount.set(key, 1);
        }
      }

      // Check for duplicate phone/email combinations
      let hasDuplicates = false;
      for (const [key, count] of Array.from(comboCount.entries())) {
        if (key !== '||' && count > 1) {
          hasDuplicates = true;
          break;
        }
      }

      if (hasDuplicates) {
        toast.error('Duplicate phone and email found in multiple rows');
        return;
      }

      // Validate date of birth - check if it's a future date
      if (data.dob) {
        const dobDate = new Date(data.dob);
        const today = new Date();
        today.setHours(23, 59, 59, 999); // Set to end of today for accurate comparison

        // Check if the date is valid
        if (isNaN(dobDate.getTime())) {
          toast.error('Please enter a valid date of birth');
          return;
        }

        if (dobDate > today) {
          toast.error('Date of birth cannot be in the future');
          return;
        }
      }

      // Determine age value: form value > patient value > calculated value > never empty
      const ageValue =
        data.age && data.age.trim() !== ''
          ? data.age
          : patient?.age && patient.age.trim() !== ''
            ? patient.age
            : patient?.dob
              ? calculateAge(patient.dob)
              : '';

      if (demographics) {
        await updateDemographics({
          ...data,
          id: demographics?.id,
          maritalStatus: data?.maritalStatus?.value ?? '',
        });
      } else {
        await createDemographics({
          ...data,
          dob: patient?.dob ?? '',
          maritalStatus: data?.maritalStatus?.value ?? '',
          age: ageValue,
          cmchId: patient?.id ?? '',
        });
      }
      setIsViewMode(true);
    },
    [createDemographics, demographics, patient, updateDemographics]
  );

  const handleReset = useCallback(() => {
    if (demographics && demographics.id) {
      // Edit mode: always use demographics contacts, or a blank row if none
      reset({
        ...demographics,
        contacts:
          demographics.contacts && demographics.contacts.length > 0
            ? demographics.contacts.map((contact) => ({
                phone: contact?.phone,
                email: contact?.email,
              }))
            : [{ phone: '', email: '' }],
        maritalStatus: maritalStatusOptions?.find(
          (option) => option.value === demographics?.maritalStatus
        ),
        age:
          demographics?.age && demographics.age.trim() !== ''
            ? demographics.age
            : demographics?.dob
              ? calculateAge(demographics.dob)
              : '',
      });
    } else {
      // Create mode: always reset contacts to patient phone/email (if available)
      const contacts =
        patient?.contact?.phone || patient?.contact?.email
          ? [{ phone: patient?.contact?.phone, email: patient?.contact?.email }]
          : [{ phone: '', email: '' }];

      // Find marital status option that matches patient's marital status (case-insensitive)
      const patientMaritalStatus = maritalStatusOptions?.find(
        (option) =>
          option.value.toLowerCase() === patient?.maritalStatus?.toLowerCase()
      );

      reset({
        age:
          patient?.age && patient.age.trim() !== ''
            ? patient.age
            : patient?.dob
              ? calculateAge(patient.dob)
              : '',
        cmchId: patient?.id,
        dob: patient?.dob,
        maritalStatus: patientMaritalStatus || null,
        name: patient?.name,
        sex: patient?.sex || 'male', // Default to male if not specified
        contacts,
      });
    }
  }, [demographics, patient, reset]);

  const handleCancel = useCallback(() => {
    handleReset(); // Reset form to original data
    setIsViewMode(true); // Switch back to view mode
  }, [handleReset]);

  useEffect(() => {
    handleReset();
  }, [handleReset]);

  useEffect(() => {
    getPatientDemographics();
  }, [getPatientDemographics]);

  useEffect(() => {
    return () => {
      clearStore();
    };
  }, [clearStore]);

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center h-full">
        <Loader />
      </div>
    );
  }

  return (
    <FormProvider {...methods}>
      {isViewMode ? (
        <div className="flex flex-col h-full">
          <DemographicsView />
          <div className="p-4">
            <div className="flex justify-end">
              <AppButton
                variant="outlined"
                onClick={() => setIsViewMode(false)}
                endIcon={<AppIcon icon="carbon:edit" />}
                sx={{ minWidth: 150, height: 32 }}
              >
                Edit Details
              </AppButton>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col h-full relative">
          <form
            onSubmit={handleSubmit(onSubmit, (errors) => {
              toast.error('Form is invalid. Please check all fields.');
            })}
            className="flex flex-col flex-grow h-full"
          >
            <div className="flex-grow p-6 mb-15 space-y-6 overflow-y-auto max-h-[calc(100vh-180px)]">
              {/* First Row */}
              <div className="grid grid-cols-12 gap-2">
                <div className="col-span-12 md:col-span-4 flex items-end gap-2 relative">
                  <div className="flex-1">
                    <AutoResizeTextArea
                      label="Patient name"
                      name="name"
                      control={control}
                      labelClassName='className="mb-0 md:mb-0 text-sm md:text-sm'
                      disabled={!editFields.name}
                      textareaClassName={`!text-sm !border-[#C2CDD6] ${!editFields.name ? '!text-gray-500' : '!text-black'}`}
                    />
                  </div>
                  {!editFields.name && (
                    <span
                      onClick={() =>
                        setEditFields((f) => ({ ...f, name: true }))
                      }
                      className="cursor-pointer absolute right-2 bottom-4"
                    >
                      <PenIcon className="w-4 h-4" />
                    </span>
                  )}
                </div>
                <div className="col-span-12 md:col-span-4 flex items-end gap-2 relative">
                  <div className="flex-1">
                    <AutoResizeTextArea
                      label="CMCH ID"
                      name="cmchId"
                      control={control}
                      labelClassName='className="mb-0 md:mb-0 text-sm md:text-sm'
                      disabled={!editFields.cmchId}
                      textareaClassName={`!text-sm !border-[#C2CDD6] ${!editFields.cmchId ? '!text-gray-500' : '!text-black'}`}
                    />
                  </div>
                  {!editFields.cmchId && (
                    <span
                      onClick={() =>
                        setEditFields((f) => ({ ...f, cmchId: true }))
                      }
                      className="cursor-pointer absolute right-2 bottom-4"
                    >
                      <PenIcon className="w-4 h-4" />
                    </span>
                  )}
                </div>
                <div className="col-span-6 md:col-span-2">
                  <ControlledDatePicker
                    label="Date of birth"
                    name="dob"
                    control={control}
                    fieldHeight={35}
                    maxDate={dayjs()}
                  />
                </div>
                <div className="col-span-6 md:col-span-2 flex items-end gap-2 relative">
                  <div className="flex-1">
                    <AutoResizeTextArea
                      label="Age"
                      name="age"
                      control={control}
                      labelClassName='className="mb-0 md:mb-0 text-sm md:text-sm'
                      textareaClassName={`!text-sm !border-[#C2CDD6] ${!editFields.age ? '!text-gray-500' : '!text-black'}`}
                      disabled={!editFields.age}
                    />
                  </div>
                  {!editFields.age && (
                    <span
                      onClick={() =>
                        setEditFields((f) => ({ ...f, age: true }))
                      }
                      className="cursor-pointer absolute right-2 bottom-4"
                    >
                      <PenIcon className="w-4 h-4" />
                    </span>
                  )}
                </div>
              </div>

              {/* Second Row */}
              <div className="grid grid-cols-12 gap-2">
                <div className="col-span-12 md:col-span-4">
                  <ControlledRadio
                    label="Gender"
                    name="sex"
                    control={control}
                    options={genderOptions}
                    row={true}
                    className="gender-radio-group"
                    sx={{
                      '& .MuiFormGroup-root': {
                        flexDirection: 'row !important',
                        flexWrap: 'nowrap !important',
                        gap: 10,
                        minWidth: '250px',
                      },
                      '& .MuiFormControlLabel-root': {
                        marginRight: '0 !important',
                        marginLeft: '0 !important',
                        whiteSpace: 'nowrap',
                        minWidth: 'auto',
                        flex: '0 0 auto',
                      },
                      '& .MuiFormControlLabel-label': {
                        fontSize: '14px',
                      },
                    }}
                  />
                </div>
                <div className="col-span-12 md:col-span-4 md:col-start-5">
                  <ControlledSelectField
                    label="Marital status"
                    name="maritalStatus"
                    control={control}
                    options={maritalStatusOptions}
                    placeholder="Select marital status"
                  />
                </div>
              </div>
              <div className="space-y-4">
                <div className="mb-1 text-sm text-[#001926]">
                  Contact Information
                </div>
                <ContactsTable
                  contacts={methods.getValues().contacts}
                  fieldArray={contactFields}
                  register={register}
                  onPhoneKeyDown={(
                    e: React.KeyboardEvent<HTMLInputElement>
                  ) => {
                    // Allow: backspace, delete, tab, escape, enter, numbers, and decimal points
                    if (
                      [46, 8, 9, 27, 13, 110, 190].includes(e.keyCode) ||
                      // Allow: Ctrl+A, Command+A
                      (e.keyCode === 65 &&
                        (e.ctrlKey === true || e.metaKey === true)) ||
                      // Allow: Ctrl+C, Command+C
                      (e.keyCode === 67 &&
                        (e.ctrlKey === true || e.metaKey === true)) ||
                      // Allow: Ctrl+V, Command+V
                      (e.keyCode === 86 &&
                        (e.ctrlKey === true || e.metaKey === true)) ||
                      // Allow: Ctrl+X, Command+X
                      (e.keyCode === 88 &&
                        (e.ctrlKey === true || e.metaKey === true)) ||
                      // Allow: home, end, left, right
                      (e.keyCode >= 35 && e.keyCode <= 39)
                    ) {
                      // let it happen, don't do anything
                      return;
                    }
                    // Ensure that it is a number and stop the keypress if not
                    if (
                      (e.shiftKey || e.keyCode < 48 || e.keyCode > 57) &&
                      (e.keyCode < 96 || e.keyCode > 105)
                    ) {
                      e.preventDefault();
                    }
                  }}
                  onPhoneInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                    // Limit to 10 digits
                    if (e.target.value.length > 10) {
                      e.target.value = e.target.value.slice(0, 10);
                    }
                  }}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 p-6 sticky bottom-0 z-10 shadow-lg bg-white">
              <AppButton
                type="button"
                variant="outlined"
                onClick={handleCancel}
                disabled={updating}
                sx={{ minWidth: 150, height: 32 }}
              >
                Cancel
              </AppButton>
              <AppButton
                type="submit"
                loading={updating}
                disabled={updating}
                sx={{ minWidth: 150, height: 32 }}
              >
                Save Changes
              </AppButton>
            </div>
          </form>
        </div>
      )}
    </FormProvider>
  );
};

export default memo(Demographics);
