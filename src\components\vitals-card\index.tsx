import React, { FC, memo, ReactNode } from 'react';

import { FaAngleDown, FaAngleUp, FaMinus } from 'react-icons/fa6';

import { getOrDefault } from '@/utils/common';

import { VitalCardProps, VitalImprovement, VitalStatus } from './types';

const vitalImprovementIcon: Record<VitalImprovement, ReactNode> = {
  improved: <FaAngleUp />,
  worsened: <FaAngleDown />,
  stable: <FaMinus />,
};

const vitalStatusClass: Record<VitalStatus, string> = {
  normal: 'bg-[#06C6A7]',
  low: 'bg-[#E4626F]',
  high: 'bg-[#E4626F]',
  unknown: 'bg-[#C2CDD6]',
};

const VitalsCard: FC<VitalCardProps> = ({
  loading,
  label,
  value,
  unit,
  status,
  improvement,
  time,
  percentage,
}) => {
  if (loading) {
    return (
      <div className="w-[90px] h-[69px] border rounded-base animate-pulse bg-primary/20 flex flex-col flex-grow-0 flex-shrink-0" />
    );
  }

  return (
    <div className="w-[90px] h-[69px] border-2 rounded-base flex flex-col flex-grow-0 flex-shrink-0 overflow-hidden">
      <div className="flex flex-col p-1 pb-0.5 h-[calc(100%-18px)] w-full overflow-hidden">
        <span className="-tracking-[2.2%] text-xs">{label}</span>
        <div className="flex gap-0.5 items-baseline">
          <span className="text-lg font-light -tracking-[2.2%]">
            {getOrDefault(value, '--')}
          </span>
          <span className="text-xs -tracking-[2.2%] font-extralight text-gray-500">
            {unit}
          </span>
        </div>
      </div>
      <div
        className={`h-[18px] flex items-center justify-between gap-2 px-1 ${status && vitalStatusClass[status]}`}
      >
        <span className="text-xs -tracking-[2.2%] !font-[100]">
          {improvement && vitalImprovementIcon[improvement]}
        </span>
        <span className="font-light text-[10px] truncate capitalize -tracking-[2.2%]">
          {time ?? percentage}
        </span>
      </div>
    </div>
  );
};

export default memo(VitalsCard);
