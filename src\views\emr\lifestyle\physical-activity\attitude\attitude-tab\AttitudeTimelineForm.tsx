import { FC, memo, useEffect, useMemo } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AttitudeForm from './AttitudeForm';

type Props = {
  data: QuestionnaireResponse;
};

const AttitudeTimelineForm: FC<Props> = ({ data }) => {
  const methods = useForm<QuestionnaireResponse>({
    defaultValues: data,
    mode: 'onChange',
  });

  const formFields = useMemo(() => {
    if (!data?.questions?.length) return [];
    return data.questions;
  }, [data]);

  useEffect(() => {
    if (data) {
      const transformedData = {
        ...data,
        questions: data.questions?.map((questionGroup, _groupIndex) => ({
          ...questionGroup,
          fields: questionGroup.fields?.map((field, _fieldIndex) => {
            if (field.value !== undefined) {
              return {
                ...field,
              };
            }
            return field;
          }),
        })),
      };

      const formData: any = {};
      data.questions?.forEach((questionGroup, groupIndex) => {
        if (!formData.questions) formData.questions = [];
        if (!formData.questions[groupIndex])
          formData.questions[groupIndex] = { fields: [] };

        questionGroup.fields?.forEach((field, fieldIndex) => {
          if (field.value !== undefined) {
            if (!formData.questions[groupIndex].fields[fieldIndex]) {
              formData.questions[groupIndex].fields[fieldIndex] = {};
            }
            formData.questions[groupIndex].fields[fieldIndex].value =
              field.value;
          }
        });
      });

      methods.reset({ ...transformedData, ...formData });
    }
  }, [data, methods]);

  return (
    <FormProvider {...methods}>
      <AttitudeForm formFields={formFields} readonly variant="timeline" />
    </FormProvider>
  );
};

export default memo(AttitudeTimelineForm);
