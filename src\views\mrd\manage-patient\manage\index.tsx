'use client';

import { memo, useCallback, useEffect } from 'react';

import { FormProvider, useForm, useWatch } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import { BiCheck } from 'react-icons/bi';
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from 'react-icons/md';

import { useRouter } from 'next/navigation';

import AppTab from '@core/components/app-tab';
import AppTitle from '@core/components/app-title';

import usePaymentPermissions from '@/hooks/usePaymentPermissions';

import { useCustomiseEmrStore } from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useManagePatientStore } from '@/store/mrd/manage-patient/manage';
import { useOrganizationStore } from '@/store/organizationStore';
import { usePaymentStore } from '@/store/payments';
import { useUserStore } from '@/store/userStore';

import { states } from '@/utils/constants/master';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { isFileList } from '@/utils/fileTypeChecks';

import { countryOptions } from '@/constants/mrd/manage-patient/country';
import {
  genderOptions,
  insuranceProvidersOptions,
  maritalStatusOptions,
  proofOptions,
} from '@/constants/mrd/manage-patient/select-options';
import { routes } from '@/constants/routes';

import PaymentConfirmationModal from '@/views/mrd/payment/PaymentConfirmationModal';
import PaymentFailureModal from '@/views/mrd/payment/PaymentFailureModal';

import { useFileUpload } from '@/emr/hooks/use-file-upload';

import AppButton from '@/core/components/app-button';
import { BaseOption } from '@/types';
import { Patient } from '@/types/mrd/manage-patient/patient';
import {
  PatientDetails,
  patientDetailsDefaultData,
} from '@/types/mrd/manage-patient/patient-details';

import ConfirmRegistration from './confirm-registration';
import GeneralDetails from './general-details';
import Insurance from './insurance';
import { tabValidationSchemas } from './validation';
import Verification from './verification';

type Props = {
  id?: string;
};

const tabs = [
  { label: 'General Details', content: <GeneralDetails /> },
  {
    label: 'Upload Patient Verification',
    content: <Verification />,
  },
  { label: 'Insurance Details', content: <Insurance /> },
  { label: 'Confirm Registration', content: <ConfirmRegistration /> },
];

const RegisterPatient = ({ id }: Props) => {
  const {
    currentTab,
    setCurrentTab,
    getPatientById,
    patient,
    updating,
    updatePatient,
    createPatient,
    setUploading,
    reset: resetStoreState,
  } = useManagePatientStore();
  const { fetchUserPermissions } = useUserStore();

  const { data: userData } = useUserStore();
  const { organization, fetchOrganization } = useOrganizationStore();
  const { doctorProfile } = useDoctorStore();
  const { fetchCustomiseEmr } = useCustomiseEmrStore();
  const { isPaymentEnabled } = usePaymentPermissions();
  const {
    createOrder,
    setShowPaymentModal,
    setShowFailureModal,
    setShowPatientSuccessModal,
    showPaymentModal,
    showFailureModal,
    showPatientSuccessModal,
    isCreatingOrder,
    currentOrder,
    resetPaymentState,
    openRazorpayPayment,
    handlePaymentFailure,
  } = usePaymentStore();

  const userId = userData?.id;
  const organizationId = userData?.organizationId;

  const { fileUpload } = useFileUpload({ userId, type: 'patientIdProof' });

  const { back } = useRouter();
  const router = useRouter();

  const methods = useForm<PatientDetails>({
    defaultValues: patientDetailsDefaultData,
    // @ts-ignore - TypeScript has issues with dynamic Yup schemas
    resolver: yupResolver(tabValidationSchemas[currentTab]),
    mode: 'onChange', // ✅ validate as user types
    reValidateMode: 'onChange',
  });

  const { handleSubmit, trigger, reset } = methods;

  const isPatientRegistrationPaymentEnabled = isPaymentEnabled(
    'patientRegistrationEnabled'
  );

  useEffect(() => {
    if (organizationId && !organization) {
      fetchOrganization(organizationId);
    }
  }, [organizationId, organization, fetchOrganization]);

  useEffect(() => {
    if (currentTab === 3 && organizationId) {
      fetchOrganization(organizationId);
    }
  }, [currentTab, organizationId, fetchOrganization]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        await fetchUserPermissions();
        // If needed, you can use the permissions here immediately

        if (doctorProfile?.id) {
          await fetchCustomiseEmr(doctorProfile.id);
        }
      } catch (error) {
        console.error('Error in fetchData:', error);
      }
    };
    fetchData();
  }, [doctorProfile?.id, fetchCustomiseEmr, fetchUserPermissions]);

  const handleProceedToPay = useCallback(async () => {
    const patientData = methods.getValues();

    if (!organization?.registrationFee || !organizationId) {
      return;
    }

    const paymentData = {
      amount: organization.registrationFee,
      currency: 'INR',
      patientId: patientData.name || 'TEMP_ID',
      paymentType: 'patient_registration',
      organizationId,
      description: 'Patient Registration Fee',
      metadata: {},
    };

    const order = await createOrder(paymentData);

    if (order?.success && order.data) {
      setShowPaymentModal(false);

      await openRazorpayPayment(
        order,
        patientData.name || 'Patient',
        async (response) => {
          // Payment success callback - immediately proceed with patient registration
          try {
            const patientData = methods.getValues();
            setUploading(true);
            const processFileUpload = async (
              fileData: any
            ): Promise<string> => {
              if (isFileList(fileData) && fileData.length) {
                return await fileUpload(fileData[0]);
              } else if (fileData instanceof File) {
                return await fileUpload(fileData);
              }
              return fileData || '';
            };

            const patientIdProof = await processFileUpload(
              patientData.proof?.url
            );
            const insuranceIdProof = await processFileUpload(
              patientData.insurance?.url
            );

            const finalData: Patient = {
              ...patientData,
              dob: patientData.dob
                ? formatDate(patientData.dob, DateFormats.DATE_YYYY_MM_DD)
                : '',
              sex: (patientData.sex as BaseOption)?.value ?? '',
              maritalStatus:
                (patientData.maritalStatus as BaseOption)?.value ?? '',
              address: {
                ...patientData.address,
                state: (patientData.address?.state as BaseOption)?.value ?? '',
                country:
                  (patientData.address?.country as BaseOption)?.value ?? '',
              },
              proof: {
                ...patientData.proof,
                type: (patientData.proof?.type as BaseOption)?.value ?? '',
                url: patientIdProof,
              },
              insurance: {
                ...patientData.insurance,
                provider:
                  (patientData.insurance?.provider as BaseOption)?.value ?? '',
                url: insuranceIdProof,
              },
              contact: {
                phone: patientData.contact?.phone || '',
                email: patientData.contact?.email || '',
              },
            };

            await createPatient(finalData);

            router.push(routes.MRD_MANAGE_PATIENTS);
          } catch (error) {
            console.error('Error submitting form:', error);
          } finally {
            setUploading(false);
          }
        },
        (error) => {
          console.error('Payment failed:', error);
          handlePaymentFailure(error.message || 'Payment failed');
        }
      );
    } else {
      setShowPaymentModal(false);
    }
  }, [
    organization,
    organizationId,
    createOrder,
    setShowPaymentModal,
    // setShowSuccessModal,
    openRazorpayPayment,
    handlePaymentFailure,
    router,
    createPatient,
    methods,
  ]);

  const handlePatientRegistrationComplete = useCallback(() => {
    setShowPatientSuccessModal(false);
    resetPaymentState();
    back();
  }, [setShowPatientSuccessModal, resetPaymentState, back]);

  const handleRetryPayment = useCallback(() => {
    setShowFailureModal(false);
    setShowPaymentModal(true);
  }, [setShowFailureModal, setShowPaymentModal]);

  const onSubmit = useCallback(
    async (data: PatientDetails) => {
      try {
        setUploading(true);
        const processFileUpload = async (fileData: any): Promise<string> => {
          if (isFileList(fileData) && fileData.length) {
            return await fileUpload(fileData[0]);
          } else if (fileData instanceof File) {
            return await fileUpload(fileData);
          }
          return fileData;
        };

        const proof = await processFileUpload(data?.proof?.url);
        const insuranceIdProof = await processFileUpload(data.insurance?.url);

        const finalData: Patient = {
          ...data,
          dob: data.dob
            ? formatDate(data.dob, DateFormats.DATE_YYYY_MM_DD)
            : '',
          sex: (data.sex as BaseOption)?.value ?? '',
          maritalStatus: (data.maritalStatus as BaseOption)?.value ?? '',
          address: {
            ...data.address,
            state: (data.address.state as BaseOption)?.value ?? '',
            country: (data.address.country as BaseOption)?.value ?? '',
          },
          proof: {
            ...data.proof,
            type: (data?.proof?.type as BaseOption)?.value ?? '',
            url: proof,
          },
          insurance: {
            ...data.insurance,
            provider: (data?.insurance?.provider as BaseOption)?.value ?? '',
            url: insuranceIdProof,
          },
        };

        if (id) {
          await updatePatient(finalData);
          // Broadcast patient update event
          const { sendMessage } = await import('@/utils/broadcast-channel');
          sendMessage({
            type: 'patient-updated',
            patientId: id,
          });
        } else {
          await createPatient(finalData);
        }
        back();
      } catch (error) {
        console.error('Error submitting form:', error);
      } finally {
        setUploading(false);
      }
    },
    [setUploading, id, back, fileUpload, updatePatient, createPatient]
  );

  const handleClickPrev = useCallback(() => {
    if (currentTab === 0) {
      back();
      return;
    }
    setCurrentTab(currentTab - 1);
  }, [back, currentTab, setCurrentTab]);

  const handleClickNext = useCallback(async () => {
    if (currentTab === 3) {
      if (id) {
        await handleSubmit(onSubmit)();
      } else if (
        !organization?.registrationFee ||
        !isPatientRegistrationPaymentEnabled
      ) {
        await handleSubmit(onSubmit)();
      } else {
        // Show payment modal for new patients with registration fee and payment enabled in customize EMR
        setShowPaymentModal(true);
      }
      return;
    }

    const isValid = await trigger(undefined, { shouldFocus: true });
    if (isValid) {
      // Clear errors only for valid fields
      const errors = methods.formState.errors;
      Object.keys(errors).forEach((field) => {
        if (!errors[field as keyof PatientDetails]) {
          methods.clearErrors(field as keyof PatientDetails);
        }
      });

      setCurrentTab(currentTab + 1);
    }
  }, [
    currentTab,
    setCurrentTab,
    trigger,
    methods,
    handleSubmit,
    onSubmit,
    organization,
    setShowPaymentModal,
    isPatientRegistrationPaymentEnabled,
    id,
  ]);

  const handleReset = useCallback(() => {
    if (!patient) return;
    // @ts-ignore - TypeScript has issues with dynamic Yup schemas
    reset({
      ...patient,
      dob: patient?.dob
        ? typeof patient.dob === 'string'
          ? patient.dob
          : patient.dob.toISOString()
        : undefined,
      height: patient?.height?.toString(),
      weight: patient?.weight?.toString(),
      sex: genderOptions.find((option) => option.value === patient.sex),
      maritalStatus: maritalStatusOptions.find(
        (option) => option.value === patient.maritalStatus
      ),
      address: {
        ...(typeof patient?.address === 'string'
          ? {
              houseName: patient?.address,
            }
          : patient?.address),
        state: states.find(
          (option) =>
            option.value ===
            (typeof patient?.address === 'string'
              ? ''
              : patient?.address?.state)
        ),
        country: countryOptions.find(
          (option) =>
            option.value ===
            (typeof patient?.address === 'string'
              ? ''
              : patient?.address?.country)
        ),
      },
      proof: {
        ...patient?.proof,
        type: proofOptions.find(
          (option) => option.value === patient?.proof?.type
        ),
      },
      insurance: {
        ...patient?.insurance,
        provider: insuranceProvidersOptions.find(
          (option) => option.value === patient?.insurance?.provider
        ),
      },
    });
  }, [reset, patient]);

  useEffect(() => {
    if (id) {
      getPatientById(id);
    }
  }, [id, getPatientById]);

  useEffect(() => {
    handleReset();
  }, [handleReset]);

  useEffect(() => {
    return () => resetStoreState();
  }, [resetStoreState]);

  // Clear errors for required fields when they are filled
  const { control, clearErrors, formState } = methods;
  const { errors } = formState;

  const name = useWatch({ control, name: 'name' });
  const dob = useWatch({ control, name: 'dob' });
  const sex = useWatch({ control, name: 'sex' });

  useEffect(() => {
    if (name && errors.name) {
      clearErrors('name');
    }
  }, [name, errors.name, clearErrors]);

  useEffect(() => {
    if (dob && errors.dob) {
      clearErrors('dob');
    }
  }, [dob, errors.dob, clearErrors]);

  useEffect(() => {
    if (sex && errors.sex) {
      clearErrors('sex');
    }
  }, [sex, errors.sex, clearErrors]);

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)} className="w-full h-full">
        <div className="flex flex-col gap-base w-full h-6">
          <AppTitle>Patient Details</AppTitle>
        </div>
        <div className="flex-1 w-full h-[calc(100%-4rem)] overflow-y-auto">
          <AppTab
            tabs={tabs}
            activeTab={currentTab}
            onTabChange={setCurrentTab}
            scrollButtons={false}
            allowScrollButtonsMobile={false}
            readonly
            tabProps={{ disableRipple: true }}
          />
        </div>
        <div className="flex gap-base justify-end w-full h-10">
          <AppButton
            sx={{ minWidth: 180 }}
            variant="outlined"
            size="small"
            startIcon={
              currentTab === 0 ? undefined : <MdOutlineKeyboardArrowLeft />
            }
            onClick={handleClickPrev}
          >
            {currentTab === 0 ? 'Cancel' : 'Previous'}
          </AppButton>

          {currentTab === 3 ? (
            !id &&
            organization?.registrationFee &&
            isPatientRegistrationPaymentEnabled ? (
              <AppButton
                sx={{ minWidth: 180 }}
                size="small"
                onClick={handleClickNext}
                loading={updating}
                type="button"
              >
                Proceed to payment
              </AppButton>
            ) : (
              <AppButton
                sx={{ minWidth: 180 }}
                size="small"
                endIcon={<BiCheck />}
                onClick={handleClickNext}
                loading={updating}
                type="button"
              >
                {id ? 'Update Patient' : 'Confirm Registration'}
              </AppButton>
            )
          ) : (
            <AppButton
              sx={{ minWidth: 180 }}
              size="small"
              endIcon={<MdOutlineKeyboardArrowRight />}
              onClick={handleClickNext}
              loading={updating}
              type="button"
            >
              Next
            </AppButton>
          )}
        </div>
      </form>

      {/* Payment Modals */}
      <PaymentConfirmationModal
        open={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        onProceedToPay={handleProceedToPay}
        loading={isCreatingOrder}
      />

      <PaymentFailureModal
        open={showFailureModal}
        onClose={() => setShowFailureModal(false)}
        onRetry={handleRetryPayment}
        errorMessage={
          <>
            Transaction failed!
            <br />
            Please try again later.
          </>
        }
        showCloseButton={false}
      />
    </FormProvider>
  );
};

export default memo(RegisterPatient);
