import { useState } from 'react';

import { FaMicrophone, FaStop } from 'react-icons/fa';

export default function AmbientListening() {
  const [isListening, setIsListening] = useState(false);

  const toggleListening = () => {
    setIsListening(!isListening);
    // TODO: Implement actual recording functionality
  };

  return (
    <button
      onClick={toggleListening}
      className={`w-full py-2 px-3 rounded text-sm font-medium transition-colors flex items-center justify-center gap-2 ${
        isListening
          ? 'bg-red-600 text-white hover:bg-red-700'
          : 'bg-gray-400 text-white hover:bg-gray-500'
      }`}
    >
      {isListening ? (
        <>
          <FaStop className="w-4 h-4" />
          Stop Recording
        </>
      ) : (
        <>
          <FaMicrophone className="w-4 h-4" />
          Ambient Listening
        </>
      )}
    </button>
  );
}
