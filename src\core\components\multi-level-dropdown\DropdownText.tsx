import { forwardRef, InputHTMLAttributes } from 'react';

import { FieldError } from 'react-hook-form';

export interface TextInputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  errors?: FieldError;
  endDecoration?: React.ReactNode;
  inputClassName?: string;
  fieldClassName?: string;
  isTruncated?: boolean;
  maxLength?: number;
  selectValue?: string;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}

const MultiLevelTextInput = forwardRef<HTMLInputElement, TextInputProps>(
  (
    {
      className,
      errors,
      label,
      endDecoration,
      inputClassName,
      fieldClassName,
      disabled,
      value,
      selectValue,
      onKeyDown,
      ...rest
    },
    ref
  ) => {
    return (
      <label
        className={`relative flex flex-col gap-2.5 text-[#001926] ${className}`}
      >
        {label && <span>{label}</span>}
        <div className={`relative ${inputClassName}`}>
          <input
            ref={ref}
            className={`rounded-md py-2 px-4.5 pr-13 truncate block border border-[#637D92] w-full ${fieldClassName}`}
            disabled={disabled}
            title={(value as string) || selectValue}
            onKeyDown={onKeyDown}
            {...rest}
          />
          {endDecoration && (
            <div className="absolute right-4 top-1/2 -translate-y-1/2 ">
              {endDecoration}
            </div>
          )}
        </div>
        {errors?.message && (
          <span className="absolute  top-[105%] left-0 text-red-500 text-xs">
            {errors.message}
          </span>
        )}
      </label>
    );
  }
);

MultiLevelTextInput.displayName = 'TextInput';

export default MultiLevelTextInput;
