import { memo } from 'react';

import { BiX } from 'react-icons/bi';
import { PiWarning } from 'react-icons/pi';

import AsyncPatientSearch from '@/lib/common/AsyncPatientSearch';
import Loading from '@/lib/common/loading';

import { usePatientInfoStore } from '@/store/emr/patient-info';
import { useRecentSearchStore } from '@/store/searchHistoryStore';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { formatAddress } from '@/utils/mrd/manage-patient/format-address';

import VitalSummary from '@/emr/components/vital-data';

import PatientSelectionButton from './PatientSelectionButton';
import ViewConsultationButton from './ViewConsultationButton';

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

interface PatientSearchProps {
  handleSelectChange: (e: any) => void;
  isActivePatient: boolean;
  togglePatientSelection: () => void;
  onClosePatient: () => void;
  hasSelectedPatient: boolean;
}

const PatientSearch: React.FC<PatientSearchProps> = ({
  handleSelectChange,
  isActivePatient,
  togglePatientSelection,
  onClosePatient,
  hasSelectedPatient,
}) => {
  const { recentSearches } = useRecentSearchStore();
  const {
    isSearching,
    isError,
    patient: searchPatient,
    vitals,
    isLoadingVitals,
  } = usePatientInfoStore();

  return (
    <div className="w-[64%] p-4 2xl:w-[66%] h-full bg-white flex flex-col overflow-hidden  rounded-base ">
      <div className="w-full flex items-center gap-2 justify-between">
        <span className="text-[18px] pb-2 font-bold -tracking-[2.2%]">
          Patient Info
        </span>
        {hasSelectedPatient && (
          <button
            onClick={onClosePatient}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            aria-label="Close patient"
          >
            <BiX size={24} />
          </button>
        )}
      </div>
      <div className="rounded-lg   bg-[#FCFCFC] flex-1 grid grid-rows-12 gap-3 h-full">
        <div className="row-span-1 ">
          <AsyncPatientSearch
            onChange={(e: any) => handleSelectChange(e)}
            defaultOptions={recentSearches?.emr}
            value={searchPatient}
            getOptionLabel={(option) => option?.name}
            getOptionValue={(option) => option?.name}
          />
        </div>

        {isSearching && (
          <div className="flex justify-center items-center h-full row-span-8">
            <Loading />
          </div>
        )}
        {isError && (
          <div className="flex justify-center gap-1 font-medium text-sm  text-black/50 bg-red-100 overflow-y-auto">
            <PiWarning className="text-lg" />
            <span>No Patient Found</span>
          </div>
        )}

        {!isSearching && searchPatient && (
          <div className=" grid-cols-5 border-[#DAE1E7] shadow-custom-x flex flex-col gap-1 2xl:gap-1 row-span-11 rounded-lg bg-white  overflow-y-hidden">
            {/* Header Section - Name, ID, Last Visited */}

            {/* Patient Details Section */}
            <div className="py-2 pt-0 flex colspan-3 flex-col justify-evenly flex-1 overflow-y-auto">
              <div className="flex flex-col gap-2  h-full overflow-y-auto">
                {/* Row 1: Name, Patient ID, Last Visited */}
                <div className="grid grid-cols-3 gap-4 border-b border-[#DAE1E7] pb-2 pt-2">
                  <div className="flex flex-col gap-1">
                    <span className="text-sm font-medium text-[#012436] -tracking-[2.2%]">
                      Name
                    </span>
                    <span className="text-sm font-bold text-[#012436] -tracking-[2.2%]">
                      {searchPatient?.name}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-sm font-medium text-[#012436] -tracking-[2.2%]">
                      Patient ID
                    </span>
                    <span className="text-sm font-bold text-[#012436] -tracking-[2.2%]">
                      {searchPatient?.id}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-sm font-medium text-[#012436] -tracking-[2.2%]">
                      Last Visited
                    </span>
                    <span className="text-sm font-bold text-[#012436] -tracking-[2.2%]">
                      {searchPatient?.last_consultation_date
                        ? formatDate(
                            searchPatient?.last_consultation_date,
                            DATE_DD_MM_YYYY_SLASH
                          )
                        : 'N/A'}
                    </span>
                  </div>
                </div>

                {/* Row 2: Age, Gender, Marital Status */}
                <div className="grid grid-cols-6 gap-2 border-b border-[#DAE1E7] pb-2 pt-2">
                  <div className="flex flex-col gap-1 xl:gap-2">
                    <span className="text-sm font-medium text-[#012436] -tracking-[2.2%]">
                      Age
                    </span>
                    <span className="text-sm font-normal text-[#012436] -tracking-[2.2%]">
                      {searchPatient?.age}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1 xl:gap-2">
                    <span className="text-sm font-medium text-[#012436] -tracking-[2.2%]">
                      Gender
                    </span>
                    <span className="text-sm font-normal text-[#012436] -tracking-[2.2%]">
                      {searchPatient?.sex}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1 xl:gap-2">
                    <span className="text-sm font-medium text-[#012436] -tracking-[2.2%]">
                      Marital Status
                    </span>
                    <span className="text-sm font-normal text-[#012436] -tracking-[2.2%]">
                      {searchPatient?.maritalStatus || 'N/A'}
                    </span>
                  </div>
                </div>

                {/* Row 3: Phone Number, Address */}
                <div className="grid grid-cols-6 gap-2 pt-2">
                  <div className="flex flex-col gap-1 xl:gap-2">
                    <span className="text-sm font-medium text-[#012436] -tracking-[2.2%]">
                      Phone Number
                    </span>
                    <span className="text-sm font-normal text-[#012436] -tracking-[2.2%]">
                      {searchPatient?.contact?.phone}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1 xl:gap-2 run dev min-w-[400px]">
                    <span className="text-sm font-medium text-[#012436] -tracking-[2.2%]">
                      Address
                    </span>
                    <span className="text-sm font-normal text-[#012436] whitespace-pre-line">
                      {formatAddress(searchPatient?.address)}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1 xl:gap-2">
                    {/* Empty third column for alignment */}
                  </div>
                </div>
              </div>
            </div>

            {/* Footer Section - Keep original */}
            <div className="flex w-full gap-2 border-t py-2 overflow-visible mb-30 md:mb-4">
              <VitalSummary
                isLoading={isLoadingVitals}
                vitals={vitals}
                containerClassName="w-full"
              />
              <div className="flex items-center justify-center gap-1">
                {searchPatient && (
                  <>
                    <ViewConsultationButton patientId={searchPatient.id} />
                    <PatientSelectionButton
                      isActive={isActivePatient}
                      onClick={togglePatientSelection}
                      className="mt-3"
                    />
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(PatientSearch);
