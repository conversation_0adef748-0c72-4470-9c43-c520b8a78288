import React from 'react';
import type { SVGProps } from 'react';

const FluentFood32Filled = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    viewBox="0 0 32 32"
    {...props}
  >
    <path
      fill="#000"
      d="M18 9.5A7.5 7.5 0 0 1 25.5 2h.5a1 1 0 0 1 1 1v12c0 .158.04.668.118 1.466c.074.77.175 1.743.283 2.78l.013.123c.27 2.598.586 5.646.586 6.631a4 4 0 1 1-8 0c0-.838.227-3.367.458-5.738c.159-1.635.324-3.24.431-4.262H20.5a2.5 2.5 0 0 1-2.5-2.5zm-3.026-6.727a1 1 0 0 0-1.035-.771c-.53.03-.949.467-.949 1.004v6a.995.995 0 0 1-1.99.03V3a1 1 0 1 0-2 0v6.065a.995.995 0 0 1-1.988-.06V3.007a1.007 1.007 0 0 0-1.986-.233C5.004 2.866 4 7.14 4 10a5.99 5.99 0 0 0 2.27 4.7c.496.394.73.823.73 1.182a1 1 0 0 1-.007.114c-.06.504-.307 2.594-.539 4.753C6.226 22.872 6 25.16 6 26a4 4 0 0 0 8 0c0-.839-.226-3.128-.454-5.251c-.232-2.159-.48-4.25-.54-4.753a1 1 0 0 1-.006-.114c0-.359.234-.788.73-1.182A5.99 5.99 0 0 0 16 10c0-2.862-1.007-7.144-1.026-7.227m-1.08-.768l.02-.001h-.008l-.008.001z"
    />
  </svg>
);

export default FluentFood32Filled;
