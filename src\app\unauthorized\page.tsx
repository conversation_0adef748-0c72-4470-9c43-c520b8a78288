'use client';

import { Lock } from 'lucide-react';

import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';

export default function UnauthorizedPage() {
  const router = useRouter();

  const handleLogout = async () => {
    try {
      const { logout } = await import('@core/lib/auth/services');
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center p-6 bg-gray-50">
      <div className="p-8 max-w-md w-full text-center bg-white rounded-lg shadow-md">
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-50 mb-4">
          <Lock className="h-8 w-8 text-red-600" />
        </div>
        <h3 className="text-xl font-semibold text-gray-800 mb-2">
          Access Denied
        </h3>
        <p className="text-gray-600 mb-6">
          You don&apos;t have permission to access this application. Please
          contact your administrator if you believe this is an error.
        </p>
        <Button
          onClick={handleLogout}
          variant="destructive"
          className="px-8 py-3 text-sm font-medium w-full sm:w-auto"
        >
          Sign out and return to login
        </Button>
      </div>
    </div>
  );
}
