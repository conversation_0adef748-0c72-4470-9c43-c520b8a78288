import React, { FC, memo } from 'react';

import { MdOutlineRemoveRedEye } from 'react-icons/md';

import { cn } from '@/lib/utils';

type Props = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  showPreviewOption: boolean;
};

const PreviewButton: FC<Props> = ({
  showPreviewOption,
  className,
  ...props
}) => {
  return (
    <button
      type="button"
      className={cn(
        'absolute bottom-0 left-0 h-3 w-full',
        'bg-black/45 hover:bg-black/75 text-sm text-white',
        'flex items-center gap-1 p-2 py-3',
        'transition-opacity opacity-100',
        'focus:outline-none focus:ring-0 focus:ring-offset-0 ',
        { 'opacity-0': !showPreviewOption },
        className
      )}
      {...props}
    >
      <MdOutlineRemoveRedEye size={18} />
      Preview
    </button>
  );
};

export default memo(PreviewButton);
