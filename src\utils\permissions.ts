import { useUserStore } from '@/store/userStore';

/**
 * Hook to check if the current user has any of the specified permissions
 * @param requiredPermissions Array of permission keys to check
 * @returns boolean indicating if the user has any of the required permissions
 */
export const useHasPermission = (requiredPermissions: string[]): boolean => {
  const { permissions = [] } = useUserStore();
  return requiredPermissions.some(permission => 
    permissions.includes(permission)
  );
};

/**
 * Hook to check if the current user has all of the specified permissions
 * @param requiredPermissions Array of permission keys to check
 * @returns boolean indicating if the user has all of the required permissions
 */
export const useHasAllPermissions = (requiredPermissions: string[]): boolean => {
  const { permissions = [] } = useUserStore();
  return requiredPermissions.every(permission => 
    permissions.includes(permission)
  );
};
