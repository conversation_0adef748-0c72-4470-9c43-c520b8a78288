import React from 'react';

import { FieldErrors, FieldValues } from 'react-hook-form';

import { Box } from '@mui/material';

import colors from '@/utils/colors';

interface ReusableErrorMessageProps<T extends FieldValues> {
  errors: FieldErrors<T>;
  fieldKey: keyof T;
}

const FieldValidationError = <T extends FieldValues>({
  errors,
  fieldKey,
}: ReusableErrorMessageProps<T>) => {
  const fieldErrors = errors?.[fieldKey] as
    | FieldErrors<FieldValues>
    | undefined;

  const hasErrors = fieldErrors
    ? Object.values(fieldErrors).some((error) => !!error)
    : false;

  if (hasErrors) {
    return (
      <Box
        sx={{
          color: colors.common.redRose,
          textAlign: 'right',
          fontSize: '0.875rem',
        }}
      >
        *Fill all the mandatory fields
      </Box>
    );
  }

  return null;
};

export default FieldValidationError;
