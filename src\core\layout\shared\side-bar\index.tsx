'use client';

import React, { memo, useMemo } from 'react';

import clsx from 'clsx';

import usePermission from '@/hooks/use-permission';

import NavLink from './NavLink';
import type { SidebarProps } from './types';

const Sidebar = ({
  items = [],
  highlightColor,
  renderBottom,
}: SidebarProps) => {
  const { hasDepartment } = usePermission();

  const filteredItems = useMemo(() => {
    return items.filter((item) => {
      if (item?.department && item.department.length > 0) {
        return hasDepartment(item.department);
      }
      return true;
    });
  }, [items, hasDepartment]);

  return (
    <div
      className={clsx(
        'flex flex-col flex-grow-0 flex-shrink-0',
        'bg-white rounded-base shadow-base',
        'h-full min-h-full w-17 min-w-17 max-w-17',
        'overflow-y-auto overflow-x-hidden'
      )}
    >
      <div
        className={clsx(
          'flex flex-col flex-shrink-0 flex-grow-0 items-center',
          'w-full min-w-full max-w-full'
        )}
      >
        {filteredItems.map((item) => (
          <NavLink
            key={item.path}
            icon={item.icon}
            href={item.path}
            text={item?.label}
            disabled={item.disabled || false}
            highlightColor={highlightColor}
          />
        ))}
      </div>
      {renderBottom && renderBottom()}
    </div>
  );
};

export default memo(Sidebar);
