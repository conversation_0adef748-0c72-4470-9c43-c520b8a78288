import React, { memo } from 'react';

import { useFormContext } from 'react-hook-form';

import { useOrganizationStore } from '@/store/organizationStore';

import { getOrDefault } from '@/utils/common';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { formatAddress } from '@/utils/mrd/manage-patient/format-address';

import KeyValuePair from '@/views/mrd/manage-patient/shared/KeyValuePair';

import ControlledImageUploader from '@/components/controlled-inputs/ControlledImageUploader';

import { PatientDetails } from '@/types/mrd/manage-patient/patient-details';

const ConfirmRegistration = () => {
  const { getValues, control } = useFormContext<PatientDetails>();
  const { organization } = useOrganizationStore();

  const patient = getValues();

  return (
    <div className="w-full h-full flex flex-col gap-base overflow-y-auto">
      <div className="flex gap-base py-base border-b">
        <KeyValuePair label="Patient Name" value={patient.name} />
        <KeyValuePair
          label="Patient ID (Auto Generated)"
          value={getOrDefault(patient.id, 'To be generated')}
        />
      </div>
      <div className="flex gap-base py-base border-b">
        <KeyValuePair
          label="Date Of Birth"
          value={formatDate(patient.dob, DateFormats.DATE_DD_MM_YYYY_SLASH)}
        />
        <KeyValuePair label="Age" value={getOrDefault(patient.age)} />
        <KeyValuePair
          label="Gender"
          value={getOrDefault(patient.sex?.label, 'N/A')}
        />
        <KeyValuePair
          label="Height"
          value={patient.height ? `${patient.height} cms` : 'N/A'}
        />
        <KeyValuePair
          label="Weight"
          value={patient.weight ? `${patient.weight} kgs` : 'N/A'}
        />
      </div>
      <div className="flex gap-base py-base border-b">
        <KeyValuePair
          label="Phone Number"
          value={getOrDefault(patient.contact?.phone, 'N/A')}
        />
        <KeyValuePair
          label="Email"
          value={getOrDefault(patient.contact?.email, 'N/A')}
        />
        <KeyValuePair
          label="Address"
          value={getOrDefault(
            formatAddress({
              ...patient.address,
              country: patient?.address?.country?.label,
              state: patient?.address?.state?.label,
            }),
            'N/A'
          )}
        />
        <KeyValuePair
          label="Patient ID Proof"
          value={
            <ControlledImageUploader
              name="proof.url"
              control={control}
              accept=".png, .jpg, .jpeg, .pdf"
              maxSizeInMB={5}
              showError={false}
              readonly
            />
          }
        />
      </div>
      <div className="flex gap-base py-base">
        <KeyValuePair
          label="Insurance Provider"
          value={getOrDefault(patient.insurance?.provider?.label, 'N/A')}
        />
        <KeyValuePair
          label="Insurance Number"
          value={getOrDefault(patient.insurance?.id, 'N/A')}
        />
        <KeyValuePair
          label="Insurance Proof"
          value={
            <ControlledImageUploader
              name="insurance.url"
              control={control}
              accept=".png, .jpg, .jpeg, .pdf"
              maxSizeInMB={5}
              showError={false}
              readonly
            />
          }
        />
      </div>
      {organization?.registrationFee && (
        <div className="sticky bottom-0 left-0 right-0 bg-white border-t py-2">
          <div className="flex justify-end">
            <div className="flex items-center gap-4 px-4">
              <KeyValuePair
                label="Registration Fee"
                value={`₹${organization.registrationFee}`}
                align="right"
                className="font-medium text-lg"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default memo(ConfirmRegistration);
