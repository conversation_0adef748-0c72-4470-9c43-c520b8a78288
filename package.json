{"name": "arca-emr", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format:all": "prettier . --write", "prepare": "husky"}, "dependencies": {"@azure/monitor-opentelemetry-exporter": "1.0.0-beta.30", "@azure/msal-browser": "^3.17.0", "@azure/msal-react": "^2.0.19", "@blocknote/core": "^0.15.5", "@blocknote/mantine": "^0.15.5", "@blocknote/react": "^0.15.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.9.0", "@mui/material": "^6.4.0", "@mui/x-date-pickers": "^7.23.6", "@opentelemetry/api": "^1.9.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@tanstack/react-query": "^5.85.5", "@vercel/otel": "^1.13.0", "aadhaar-validator": "^1.1.0", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.379.0", "microsoft-cognitiveservices-speech-sdk": "^1.36.0", "next": "^14.2.30", "radix-ui": "^1.0.1", "razorpay": "^2.9.6", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-date-range": "^2.0.1", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.52.2", "react-icons": "^5.2.0", "react-markdown": "^9.0.1", "react-select": "^5.10.1", "react-sortablejs": "^6.1.4", "react-to-print": "^3.1.1", "recharts": "^3.1.2", "sass": "^1.77.8", "sharp": "^0.34.2", "sonner": "^1.5.0", "sortablejs": "^1.15.3", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0", "zustand": "^4.5.2"}, "devDependencies": {"@iconify/react": "^6.0.0", "@stagewise/toolbar": "^0.6.2", "@svgr/webpack": "^8.1.0", "@types/lodash": "^4.17.4", "@types/node": "^20", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^18", "@types/react-icons": "^2.2.7", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.2.0", "eslint": "^8", "eslint-config-next": "14.2.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}}