# Lifestyle Module - ARCA EMR

Development guidelines for the lifestyle consultation module.

## Module Structure

The lifestyle module follows the 4-layer architecture:

1. **Routes**: `app/(private)/emr/lifestyle/`
2. **Components**: `views/emr/lifestyle/` (note: `emr/` folder is legacy)
3. **State**: `store/emr/lifestyle/`
4. **Queries**: `query/emr/lifestyle/`

## Sub-modules

- **General**: Basic lifestyle information
- **Nutrition**: Dietary habits and nutrition assessment
- **Physical Activity**: Exercise and activity patterns
- **Mental Health**: Psychological wellbeing assessment
- **Sleep Activity**: Sleep patterns and quality
- **Spiritual Activity**: Spiritual practices and beliefs

## Implementation Patterns

Each sub-module contains:

- `layout.tsx` - Sub-module navigation and layout
- `page.tsx` - Main content area
- Sub-tabs: `attitude/`, `practice/`, `knowledge/`

## Components

- `LifestyleNote.tsx` - Note-taking component
- `LifestyleTabs.tsx` - Main navigation tabs
- `lifestyle-forms/` - Form components for each sub-module
- `mobile/` - Mobile-specific components

This module serves as the reference implementation for other EMR consultation modules.
