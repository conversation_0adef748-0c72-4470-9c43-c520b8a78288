import React from 'react';

import { DropdownIndicatorProps, GroupBase, components } from 'react-select';

import { IoMdSearch } from 'react-icons/io';

function DropdownIndicator<
  Option,
  IsMulti extends boolean,
  Group extends GroupBase<Option>,
>({
  hasValue,
  isFocused,
  isDisabled,
  ...props
}: DropdownIndicatorProps<Option, IsMulti, Group>) {
  if (hasValue || isDisabled) {
    return null;
  } else if (isFocused) {
    return (
      <components.DropdownIndicator
        hasValue={hasValue}
        isFocused={isFocused}
        isDisabled={isDisabled}
        {...props}
      />
    );
  } else {
    return <IoMdSearch className="w-5.5 h-5.5 mr-2" />;
  }
}

export default React.memo(DropdownIndicator) as typeof DropdownIndicator;
