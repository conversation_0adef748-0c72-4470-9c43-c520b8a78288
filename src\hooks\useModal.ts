import { ReactNode } from 'react';

import { useStatusModalStore, ModalConfig } from '@/store/status-modal-store';

interface UseModalOptions {
  duration?: number; // Auto-close duration in milliseconds (0 = no auto-close)
  className?: string; // Custom CSS classes for the modal
  title?: ReactNode; // Optional title
  showIcon?: boolean; // Whether to show the icon
}

export const useModal = () => {
  const {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirmation,
    hideModal,
    showModal,
  } = useStatusModalStore();

  const success = (message: ReactNode, options?: UseModalOptions) => {
    showSuccess(message, options);
  };

  const error = (message: ReactNode, options?: UseModalOptions) => {
    showError(message, options);
  };

  const warning = (message: ReactNode, options?: UseModalOptions) => {
    showWarning(message, options);
  };

  const info = (message: ReactNode, options?: UseModalOptions) => {
    showInfo(message, options);
  };

  const confirmation = (
    message: ReactNode,
    onConfirm: () => void,
    onCancel?: () => void,
    options?: UseModalOptions
  ) => {
    showConfirmation(message, onConfirm, onCancel, options);
  };

  const custom = (config: ModalConfig) => {
    showModal(config);
  };

  return {
    success,
    error,
    warning,
    info,
    confirmation,
    custom,
    hide: hideModal,
  };
};

// Alternative export for direct usage (similar to toast.success)
export const modal = {
  success: (message: ReactNode, options?: UseModalOptions) => {
    useStatusModalStore.getState().showSuccess(message, options);
  },
  error: (message: ReactNode, options?: UseModalOptions) => {
    useStatusModalStore.getState().showError(message, options);
  },
  warning: (message: ReactNode, options?: UseModalOptions) => {
    useStatusModalStore.getState().showWarning(message, options);
  },
  info: (message: ReactNode, options?: UseModalOptions) => {
    useStatusModalStore.getState().showInfo(message, options);
  },
  confirmation: (
    message: ReactNode,
    onConfirm: () => void,
    onCancel?: () => void,
    options?: UseModalOptions
  ) => {
    useStatusModalStore
      .getState()
      .showConfirmation(message, onConfirm, onCancel, options);
  },
  custom: (config: ModalConfig) => {
    useStatusModalStore.getState().showModal(config);
  },
  hide: () => {
    useStatusModalStore.getState().hideModal();
  },
};

// Backward compatibility exports
export const useSuccessModal = () => {
  const { success, hide } = useModal();
  return { success, hide };
};

export const successModal = {
  success: modal.success,
  hide: modal.hide,
};
