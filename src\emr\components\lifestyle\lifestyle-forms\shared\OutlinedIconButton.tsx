import { FC } from 'react';

import { IconButton, IconButtonProps } from '@mui/material';
import { styled } from '@mui/material/styles';

export interface OutLinedIconButtonProps extends IconButtonProps {
  showBorder?: boolean;
}

const StyledIconButton = styled(IconButton, {
  shouldForwardProp: (prop) => prop !== 'showBorder',
})<{ showBorder?: boolean }>(({ theme, showBorder }) => ({
  backgroundColor: '#ECEFF1',
  border: showBorder ? '1px solid #000000ff' : 'none',
  width: 32,
  height: 32,
  color: 'black',
  '&:hover': {
    backgroundColor: '#CFD8DC',
  },
  '&:disabled': {
    border: showBorder ? '1px solid #ccc' : 'none',
  },
  [theme.breakpoints.down('sm')]: {
    width: 24,
    height: 24,
  },
}));

const OutLinedIconButton: FC<OutLinedIconButtonProps> = ({
  onClick,
  showBorder = true,
  ...props
}) => {
  return (
    <StyledIconButton
      size="small"
      onClick={onClick}
      showBorder={showBorder}
      {...props}
    />
  );
};

export default OutLinedIconButton;
