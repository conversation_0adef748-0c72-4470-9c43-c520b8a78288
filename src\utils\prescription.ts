import { PrescriptionItem } from '@/types/emr/prescription';

const searchFields: (keyof PrescriptionItem)[] = [
  'drugForm',
  'genericName',
  'brandName',
  'strength',
];

export const searchMedicines = (
  medicines: PrescriptionItem[],
  search: string
) => {
  return medicines.filter((medicine) => {
    return searchFields.some((field) => {
      const value = medicine[field];
      if (typeof value === 'string') {
        return value.toLowerCase().includes(search.toLowerCase());
      }
      return false;
    });
  });
};
