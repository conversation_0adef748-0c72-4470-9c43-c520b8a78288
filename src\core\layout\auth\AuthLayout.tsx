import { ReactNode } from 'react';

type AuthLayoutProps = {
  children: ReactNode;
  title: string;
  subtitle?: string;
};

export const AuthLayout = ({ children, title, subtitle }: AuthLayoutProps) => {
  return (
    <div className="flex min-h-screen bg-white">
      {/* Left side with blue pattern */}
      <div className="hidden md:flex md:w-4/6 items-center justify-center bg-[#f0f7ff] relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/images/auth-background.png')] bg-cover bg-center"></div>
        <div className="relative z-10 text-center p-8">
          <img
            src="/images/auth-logo.png"
            alt="ARCA HEALTH SPHERE"
            className="h-34 w-auto mx-auto mb-6"
          />
        </div>
      </div>

      {/* Right side with form */}
      <div className="flex-1 flex flex-col justify-center items-center p-8 max-w-md mx-auto">
        <div className="w-full space-y-8">
          <div className="text-center space-y-2">
            <h2 className="text-3xl font-bold text-gray-900">{title}</h2>
            {subtitle && <p className="text-gray-500 text-sm">{subtitle}</p>}
          </div>

          <div className="mt-8">{children}</div>
        </div>
      </div>
    </div>
  );
};
