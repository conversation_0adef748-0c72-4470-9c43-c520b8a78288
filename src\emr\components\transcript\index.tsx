import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDuration, formatDate } from '@/utils/dateUtils/dayUtils';

// Define the Conversation interface locally
interface Conversation {
  speaker: 'doctor' | 'patient';
  message: string;
}

export interface TranscriptProps {
  conversation?: Conversation[];
  doctorName?: string;
  patientName?: string;
  date?: string;
  duration?: number;
}

export default function Transcript({
  conversation,
  doctorName = 'Doctor',
  patientName = 'Patient',
  date,
  duration,
}: TranscriptProps) {
  // Use current date if no date provided
  const displayDate =
    date || formatDate(new Date(), DateFormats.DATE_DD_MM_YYYY_SLASH);
  if (!conversation || !conversation?.length) {
    return <div>No transcript generated!</div>;
  }

  return (
    <div className="w-full space-y-6 bg-white p-2">
      {/* Consultation Details Section */}
      <div className="space-y-4 pb-6 border-b border-gray-200">
        <h2
          className="text-base font-medium"
          style={{ color: '#012436', fontSize: '16px', fontWeight: 500 }}
        >
          Consultation Details:
        </h2>

        <div className="grid grid-cols-2 gap-x-8 gap-y-2 w-[50%]">
          {/* Doctor Name */}
          <div className="flex items-center gap-2">
            <span
              className="text-xs font-normal"
              style={{ color: '#64707D', fontSize: '12px' }}
            >
              Doctor:
            </span>
            <span
              className="text-sm font-medium text-black"
              style={{ fontSize: '14px' }}
            >
              {doctorName}
            </span>
          </div>

          {/* Date */}
          <div className="flex items-center gap-2">
            <span
              className="text-xs font-normal"
              style={{ color: '#64707D', fontSize: '12px' }}
            >
              Date:
            </span>
            <span
              className="text-sm font-medium text-black"
              style={{ fontSize: '14px' }}
            >
              {displayDate}
            </span>
          </div>

          {/* Patient Name */}
          <div className="flex items-center gap-2">
            <span
              className="text-xs font-normal"
              style={{ color: '#64707D', fontSize: '12px' }}
            >
              Patient:
            </span>
            <span
              className="text-sm font-medium text-black"
              style={{ fontSize: '14px' }}
            >
              {patientName}
            </span>
          </div>

          {/* Duration */}
          <div className="flex items-center gap-2">
            <span
              className="text-xs font-normal"
              style={{ color: '#64707D', fontSize: '12px' }}
            >
              Duration:
            </span>
            <span
              className="text-sm font-medium text-black"
              style={{ fontSize: '14px' }}
            >
              {duration ? formatDuration(duration) : 'Not available'}
            </span>
          </div>
        </div>
      </div>

      {/* AI Transcript Section */}
      <div className="space-y-4 ">
        <h3
          className="text-base font-medium"
          style={{ color: '#012436', fontSize: '16px', fontWeight: 500 }}
        >
          AI Transcript:
        </h3>

        {/* Conversation */}
        <div className="space-y-4">
          {conversation?.map((c, index) => (
            <div
              key={`${c.message?.slice(0, 10)}-${index}`}
              className="space-y-1"
            >
              <h4
                className="text-base font-medium"
                style={{
                  fontSize: '16px',
                  fontWeight: 500,
                  color: c.speaker === 'doctor' ? '#000000' : '#02537D',
                }}
              >
                {c.speaker === 'doctor' ? doctorName : patientName}:
              </h4>
              <p
                className="text-black  leading-relaxed"
                style={{ fontSize: '12px' }}
              >
                {c.message}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
