{"plugins": ["import"], "rules": {"import/order": ["warn", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index", "object", "type", "unknown"], "warnOnUnassignedImports": true, "pathGroups": [{"pattern": "react", "group": "external", "position": "before"}, {"pattern": "react-*", "group": "external", "position": "before"}, {"pattern": "next", "group": "external", "position": "after"}, {"pattern": "next/*", "group": "external", "position": "after"}, {"pattern": "@core/**", "group": "internal", "position": "before"}, {"pattern": "@/lib/**", "group": "internal", "position": "after"}, {"pattern": "@/hooks/**", "group": "internal", "position": "after"}, {"pattern": "@/store/**", "group": "internal", "position": "after"}, {"pattern": "@/query/**", "group": "internal", "position": "after"}, {"pattern": "@/utils/**", "group": "internal", "position": "after"}, {"pattern": "@/assets/**", "group": "internal", "position": "after"}, {"pattern": "@/helpers/**", "group": "internal", "position": "after"}, {"pattern": "@/constants/**", "group": "internal", "position": "after"}, {"pattern": "@/views/**", "group": "internal", "position": "after"}, {"pattern": "@/components/**", "group": "internal", "position": "after"}, {"pattern": "@/emr/**", "group": "internal", "position": "after"}, {"pattern": "@/mrd/**", "group": "internal", "position": "after"}, {"pattern": "@/**", "group": "internal", "position": "after"}, {"pattern": "./*", "group": "sibling", "position": "before"}, {"pattern": "../**", "group": "parent", "position": "after"}, {"pattern": "**/*.css", "group": "unknown", "position": "after"}, {"pattern": "**/*.scss", "group": "unknown", "position": "after"}], "pathGroupsExcludedImportTypes": ["react"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}]}}