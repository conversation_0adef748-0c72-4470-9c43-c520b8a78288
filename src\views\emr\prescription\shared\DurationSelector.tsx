import React, { useState, useEffect } from 'react';

import { Control, Controller, FieldPath } from 'react-hook-form';

import {
  Box,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import { MdArrowDropDown } from 'react-icons/md';

import colors from '@/utils/colors';
import {
  DURATION_CODE_MAPPING,
  DURATION_TYPE_MAPPING,
  DURATION_TYPES,
  DurationCodeType,
  DurationType,
} from '@/utils/constants/prescription';
import { regex } from '@/utils/constants/regex';
import { allowOnlyNumbers, allowOnlyNumbersOnPaste } from '@/utils/validation';

import CustomModal from '@/core/components/modal';

import { Prescription } from '../NewPrescription';

import CustomTextField from './TableCustomTextField';
interface DurationSelectorProps {
  name: `prescription.${number}.${string}`;
  control: Control<Prescription>;
  rules?: { [key: string]: any };
  clearErrors: (
    name?: FieldPath<Prescription> | FieldPath<Prescription>[]
  ) => void;
  isNotValid?: boolean;
  setValue: (name: `prescription.${number}.${string}`, value: string) => void;
}

const { DURATION_REGEX } = regex;

const DurationSelector: React.FC<DurationSelectorProps> = ({
  name,
  control,
  rules,
  clearErrors,
  isNotValid,
  setValue,
}) => {
  const [open, setOpen] = useState(false);
  const [durationValue, setDurationValue] = useState('');
  const [durationType, setDurationType] = useState<DurationType>(
    DURATION_TYPES[0]
  );
  const [displayValue, setDisplayValue] = useState('Select');
  const [hasValue, setHasValue] = useState(false);

  useEffect(() => {
    const fieldValue =
      control._formValues?.prescription?.[name.split('.')[1]]?.[
        name.split('.')[2]
      ];

    if (fieldValue) {
      setDurationValue(fieldValue);
      setDisplayValue(fieldValue);
    } else {
      setDurationValue('');
      setDisplayValue('');
    }
  }, [control._formValues, name]);

  const handleOpen = () => {
    setOpen(true);
    const field =
      control._formValues?.prescription?.[name.split('.')[1]]?.[
        name.split('.')[2]
      ];
    if (field) {
      const match = field.match(DURATION_REGEX);
      if (match && match.length >= 3) {
        const [_, value, typeCode] = match;
        setDurationValue(value);

        if (typeCode === 'D' || typeCode === 'W' || typeCode === 'M') {
          const validTypeCode = typeCode as DurationCodeType;
          setDurationType(DURATION_TYPE_MAPPING[validTypeCode]);
        } else {
          setDurationType(DURATION_TYPES[0]);
        }
      }
    }
  };

  const handleClose = () => {
    if (durationValue) {
      let code = durationValue;
      code += DURATION_CODE_MAPPING[durationType];
      setDisplayValue(code);
      setHasValue(true);

      const parts = name.split('.');
      const index = parseInt(parts[1]);
      const field = parts[2];

      const currentValues = { ...control._formValues };
      if (currentValues.prescription && currentValues.prescription[index]) {
        currentValues.prescription[index][field] = code;

        control._subjects.state.next({
          ...currentValues,
        });
      }
      setValue(name, code);
      if (clearErrors) {
        clearErrors(name);
      }
    }

    setOpen(false);
  };

  return (
    <div>
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({}) => (
          <>
            <CustomTextField
              value={displayValue}
              onClick={handleOpen}
              placeholder="Select"
              endAdornmentIcon={
                <MdArrowDropDown
                  size={18}
                  color={isNotValid ? ` ${colors.common.redRose} ` : undefined}
                />
              }
              color={
                hasValue
                  ? 'black'
                  : isNotValid
                    ? ` ${colors.common.redRose} `
                    : 'gray'
              }
            />
            <CustomModal
              open={open}
              onClose={handleClose}
              title="Duration"
              width="410px"
              minHeight="85px"
              titleBoxSx={{ mb: 0 }}
              titleTypographySx={{ fontSize: '18px', fontWeight: 600 }}
              showDivider={false}
              contentSx={{ mb: 0 }}
              content={
                <Box pt={3} display="flex" alignItems="center" gap={1}>
                  <TextField
                    autoComplete="off"
                    variant="standard"
                    value={durationValue}
                    onChange={(e) => setDurationValue(e.target.value)}
                    onKeyDown={
                      allowOnlyNumbers as React.KeyboardEventHandler<HTMLDivElement>
                    }
                    onPaste={
                      allowOnlyNumbersOnPaste as React.ClipboardEventHandler<HTMLDivElement>
                    }
                    sx={{
                      width: '100px',
                      '& .MuiInputBase-root::before': {
                        borderBottom: '1px solid black',
                      },
                      '& .MuiInputBase-root::after': {
                        borderBottom: '1px solid black',
                      },
                    }}
                    InputProps={{
                      disableUnderline: false,
                    }}
                  />

                  <RadioGroup
                    value={durationType}
                    onChange={(e) =>
                      setDurationType(e.target.value as DurationType)
                    }
                    row
                    sx={{ ml: 1 }}
                  >
                    {DURATION_TYPES.map((label) => (
                      <FormControlLabel
                        key={label}
                        value={label}
                        control={
                          <Radio
                            size="small"
                            sx={{
                              color: 'black',
                              '&.Mui-checked': {
                                color: 'black',
                              },
                            }}
                          />
                        }
                        label={label}
                        sx={{
                          '.MuiFormControlLabel-label': {
                            fontSize: '14px',
                          },
                        }}
                      />
                    ))}
                  </RadioGroup>
                </Box>
              }
            />
          </>
        )}
      />
    </div>
  );
};

export default DurationSelector;
