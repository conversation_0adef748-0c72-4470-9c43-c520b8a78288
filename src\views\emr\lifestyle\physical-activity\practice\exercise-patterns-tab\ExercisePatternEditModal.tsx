import { memo, useState } from 'react';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import AppModal from '@/core/components/app-modal';
import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AccordionTitle from '../../../shared/AccordionTitle';
import FinalizeModal from '../../../shared/FinalizeModal';

import ExercisePatternModal from './ExercisePatternModal';

type Props = {
  open: boolean;
  onClose: () => void;
  formFields: QuestionnaireResponse | null;
  onFinalize: (data: QuestionnaireResponse | null) => void;
};

const ExercisePatternEditModal = ({
  open,
  onClose,
  formFields,
  onFinalize,
}: Props) => {
  const profile = useDoctorStore((state) => state.doctorProfile);
  const [showFinalizeModal, setShowFinalizeModal] = useState(false);

  return (
    <AppModal
      open={open}
      onClose={onClose}
      title=""
      classes={{
        root: 'w-[80vw] h-[80vh] flex flex-col min-h-0',
        body: 'flex-1 h-full flex flex-col min-h-0 !p-0',
        header: '!hidden',
      }}
    >
      <>
        <div className="h-full flex flex-col">
          <div className="flex items-center w-full h-10 p-base bg-[#B4E5FE] rounded-base flex-shrink-0">
            <AccordionTitle
              expand={true}
              doctorName={formFields?.doctor?.name}
              finalised={formFields?.status === LifestyleRecordStatus.FINALIZED}
              onExpand={onClose}
              onFinalise={() => {
                setShowFinalizeModal(true);
              }}
              open={open}
              stepper={['Practice', 'Exercise Patterns']}
              designation={formFields?.doctor?.designation}
              department={
                formFields?.doctor?.department || profile?.general?.department
              }
              date={formFields?.created_on}
            />
          </div>
          <div className="flex-1 w-full p-base overflow-hidden">
            <ExercisePatternModal
              patientData={formFields}
              mode={LifestyleMode.VIEW}
              onAfterSubmit={() => {
                onClose();
              }}
            />
          </div>
        </div>

        <FinalizeModal
          open={showFinalizeModal}
          onClose={() => setShowFinalizeModal(false)}
          onFinalize={() => {
            onFinalize?.(formFields);
            setShowFinalizeModal(false);
          }}
        />
      </>
    </AppModal>
  );
};

export default memo(ExercisePatternEditModal);
