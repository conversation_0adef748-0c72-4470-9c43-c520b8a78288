# Payment Flow Test Guide

## Overview

This document outlines how to test the complete payment flow for patient registration.

## Test Scenario

1. **Navigate to Patient Registration**: Go to MRD > Manage Patients > Register New Patient
2. **Fill Patient Details**: Complete all 4 tabs (General Details, Upload Patient Verification, Insurance Details, Confirm Registration)
3. **Verify Registration Fee Display**: In the "Confirm Registration" tab, check if the registration fee is displayed at the bottom
4. **Click "Proceed to Payment"**: This should open the payment confirmation modal instead of directly submitting
5. **Payment Confirmation Modal**: Verify the modal shows:
   - Patient name
   - Organization name
   - Registration fee amount
   - "Cancel" and "Proceed to Pay" buttons
6. **Click "Proceed to Pay"**: This should:
   - Create a payment order via API call to `payment/v0.1/payments/create-order`
   - Close the confirmation modal
   - Load Razorpay SDK and open Razorpay payment modal directly in the application
7. **Razorpay Payment Modal**: Complete payment in the Razorpay modal or cancel it
8. **Payment Success/Failure**:
   - If payment is successful, the payment success modal should appear
   - If payment fails or is cancelled, the payment failure modal should appear
9. **Payment Success Modal**: Should show:
   - Success message
   - Amount paid
   - Payment ID (if available)
   - "Continue" button
10. **Payment Failure Modal**: Should show:
    - Failure message
    - Amount
    - Error details
    - "Cancel" and "Retry Payment" buttons
11. **Click "Continue"** (from success modal): Should:
    - Close payment success modal
    - Proceed with patient registration
    - Show patient registration success modal
12. **Click "Retry Payment"** (from failure modal): Should:
    - Close failure modal
    - Show payment confirmation modal again
13. **Patient Registration Success**: Should show:
    - Success message
    - Patient name
    - Patient ID (if available)
    - "Done" button
14. **Click "Done"**: Should navigate back to patient search page

## API Endpoints Used

- `GET /organization/v0.1/organization?organizationId={id}` - Fetch organization details including registrationFee
- `POST payment/v0.1/payments/create-order` - Create payment order with Razorpay
- `POST /patient` - Create new patient (existing endpoint)

## API Response Format

The payment order creation returns:

```json
{
  "success": true,
  "data": {
    "orderId": "order_R4fg",
    "paymentId": "75064d0f-b00f-4ddb-870",
    "keyId": "rzp_test",
    "amount": 20000,
    "currency": "INR",
    "status": "created",
    "description": "Patient Registration Fee"
  }
}
```

## Key Components

- `PaymentConfirmationModal` - Shows payment details and confirmation
- `PaymentSuccessModal` - Shows payment success status
- `PaymentFailureModal` - Shows payment failure with retry option
- `PatientRegistrationSuccessModal` - Shows final registration success
- `usePaymentStore` - Manages payment state
- `useOrganizationStore` - Manages organization data
- `razorpay.ts` - Utility for Razorpay SDK integration

## Expected Behavior

- If organization has no `registrationFee`, the flow should skip payment and proceed directly to patient registration
- If organization has `registrationFee`, the payment flow should be triggered
- Razorpay SDK should load automatically when payment is initiated
- All modals should be properly styled and responsive
- Error handling should show appropriate toast messages and failure modal
- Loading states should be displayed during API calls
- Payment success/failure should be handled properly with appropriate modals

## Environment Variables Required

- `NEXT_PUBLIC_RAZORPAY_KEY_ID` - Razorpay public key for frontend
- `RAZORPAY_KEY_SECRET` - Razorpay secret key (server-side only)
- `RAZORPAY_WEBHOOK_SECRET` - For payment verification webhooks

## Test Data Requirements

- Organization must have `registrationFee` field set to a positive number
- User must have valid `organizationId` in their profile
- Patient form must be completely filled out before reaching step 4
- Valid Razorpay test credentials must be configured
