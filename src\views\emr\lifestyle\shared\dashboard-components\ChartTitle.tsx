import React, { ReactNode } from 'react';

interface ChartTitleProps {
  title: string;
  children?: ReactNode;
  showBorder?: boolean;
  className?: string;
  rightContent?: ReactNode;
}

export const ChartTitle: React.FC<ChartTitleProps> = ({
  title,
  children,
  showBorder = true,
  className = '',
  rightContent,
}) => {
  return (
    <div
      className={`flex items-center justify-between ${showBorder ? 'border-b' : ''} ${className}`}
    >
      <h3 className="text-base font-medium text-[#001926]">{title}</h3>
      <div className="flex items-center gap-4">
        {rightContent}
        {children}
      </div>
    </div>
  );
};

interface LegendItemProps {
  color: string;
  label: string;
  className?: string;
  onClick?: () => void;
  active?: boolean;
}

export const LegendItem: React.FC<LegendItemProps> = ({
  color,
  label,
  className = '',
  onClick,
  active = true,
}) => {
  return (
    <div
      className={`flex items-center cursor-pointer ${className}`}
      onClick={onClick}
    >
      <div
        className="w-2.5 h-2.5 mr-2 rounded-full flex-shrink-0"
        style={{
          backgroundColor: active ? color : `${color}4D`,
          opacity: active ? 1 : 0.6,
        }}
      />
      <span className="text-sm text-[#64707D]">{label}</span>
    </div>
  );
};

export default ChartTitle;
