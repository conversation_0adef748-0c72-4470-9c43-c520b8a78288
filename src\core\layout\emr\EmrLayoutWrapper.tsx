'use client';

import dynamic from 'next/dynamic';
import { usePathname } from 'next/navigation';

import { ProtectedRoute } from '@/components/permission/protected-route';

// Import the layout wrapper with dynamic import to avoid SSR issues
const EmrLayoutWrapper = dynamic(() => import('@/core/layout/emr'), {
  ssr: false,
});

// This is the client component that handles the permission checks
export default function EmrLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Check if the current route is the EMR module root
  const isEmrRoot = pathname === '/emr';

  // If it's the EMR root, we don't need to check permissions here
  // as the home page will handle the redirection based on permissions
  if (isEmrRoot) {
    return <EmrLayoutWrapper>{children}</EmrLayoutWrapper>;
  }

  // For all other EMR routes, require EMR access permission
  return (
    <ProtectedRoute requiredPermissions={['emr.access']} redirectPath="/">
      <EmrLayoutWrapper>{children}</EmrLayoutWrapper>
    </ProtectedRoute>
  );
}
