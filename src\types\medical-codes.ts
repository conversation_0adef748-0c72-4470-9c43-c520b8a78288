type Buckets = {
  module: Record<string, number>;
};

type LanguageNames = {
  [key: string]: string;
};

type SnomedFSN = {
  term: string;
  lang: string;
};

type SnomedPT = {
  term: string;
  lang: string;
};

type SnomedConcept = {
  conceptId: string;
  active: boolean;
  definitionStatus: string;
  moduleId: string;
  fsn: SnomedFSN;
  pt: SnomedPT;
  id: string;
};

type SnomedItems = {
  term: string;
  active: boolean;
  languageCode: string;
  module: string;
  concept: SnomedConcept;
};

export type SnomedApiResponse = {
  buckets: Buckets;
  languageNames: LanguageNames;
  bucketConcepts: {};
  last: boolean;
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  numberOfElements: number;
  items: SnomedItems[];
};

export type MatchingPVs = {
  propertyId: string;
  label: string;
  score: number;
  important: boolean;
  foundationUri: string;
  propertyValueType: number;
};

export type DestinationEntities = {
  id: string;
  title: string;
  stemId: string;
  isLeaf: boolean;
  postcoordinationAvailability: number;
  hasCodingNote: boolean;
  hasMaternalChapterLink: boolean;
  hasPerinatalChapterLink: boolean;
  matchingPVs: MatchingPVs[];
  propertiesTruncated: boolean;
  isResidualOther: boolean;
  isResidualUnspecified: boolean;
  chapter: string;
  theCode: string;
  score: number;
  titleIsASearchResult: boolean;
  titleIsTopScore: boolean;
  entityType: number;
  important: boolean;
  descendants: [];
};

export type ICDApiResponse = {
  error: boolean;
  errorMessage: string | null;
  resultChopped: boolean;
  wordSuggestionsChopped: boolean;
  guessType: number;
  uniqueSearchId: string;
  words: string[] | null;
  destinationEntities: DestinationEntities[];
};
