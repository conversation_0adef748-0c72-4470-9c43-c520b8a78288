import React, { memo } from 'react';

import AppButton from '@/core/components/app-button';
import AppModal from '@/core/components/app-modal';

interface LabTestPaymentConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  onOrderAndPrint: () => void;
  onProceedToPay: () => void;
  loading?: boolean;
  totalCost: string;
  hideOrderAndPrint?: boolean;
  title?: string;
}

const LabTestPaymentConfirmationModal: React.FC<
  LabTestPaymentConfirmationModalProps
> = ({
  open,
  onClose,
  onOrderAndPrint,
  onProceedToPay,
  loading = false,
  totalCost,
  hideOrderAndPrint = false,
  title = 'Confirmation to Order Tests',
}) => {
  if (hideOrderAndPrint) {
    return (
      <AppModal
        open={open}
        onClose={onClose}
        classes={{
          root: 'w-96 max-w-md',
          header: 'hidden',
          modal: '!p-6 !rounded-lg',
        }}
      >
        <div className="text-center py-8">
          <p className="text-base font-medium text-gray-900 mb-8">
            Are you sure you want to proceed <br />
            with payment?
          </p>
          <div className="flex gap-3">
            <AppButton
              variant="outlined"
              onClick={onClose}
              fullWidth
              disabled={loading}
              className="!border-gray-300 !text-gray-700 hover:!bg-gray-50"
            >
              Cancel
            </AppButton>
            <AppButton
              onClick={onProceedToPay}
              fullWidth
              loading={loading}
              className="!bg-slate-800 hover:!bg-slate-900"
            >
              Proceed
            </AppButton>
          </div>
        </div>
      </AppModal>
    );
  }

  return (
    <AppModal
      open={open}
      onClose={onClose}
      title={title}
      classes={{
        root: 'w-86 max-w-md px-2',
        header: '!border-b-0 ',
        modal: '!p-0',
      }}
    >
      <>
        <div className="flex gap-3 mt-0 mb-2">
          <AppButton
            variant="outlined"
            onClick={onOrderAndPrint}
            fullWidth
            disabled={loading}
          >
            Order & Print
          </AppButton>
          <AppButton onClick={onProceedToPay} fullWidth loading={loading}>
            Proceed to Pay
          </AppButton>
        </div>
      </>
    </AppModal>
  );
};

export default memo(LabTestPaymentConfirmationModal);
