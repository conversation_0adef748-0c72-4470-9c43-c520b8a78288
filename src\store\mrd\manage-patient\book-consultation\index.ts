import { create } from 'zustand';

import { modal } from '@/hooks/useModal';

import {
  createAppointment,
  deleteAppointment,
  getAllDoctors,
  getAppointmentType,
  getFutureAppointments,
  updateAppointment,
} from '@/query/mrd/manage-patient/book-consultation';

import { getErrorMessage } from '@/utils/error-message';

import { ConsultationView } from '@/constants/mrd/manage-patient/consultation';

import { Consultation, Doctors } from '@/types/mrd/manage-patient/consultation';

import { useDoctorStore } from '../../queue/doctor';

type BookConsultationStoreState = {
  doctors: Doctors[];
  appointments: Consultation[];
  updatingAppointment: boolean;
  view: ConsultationView;
  futureAppointmentsLoading: boolean;
  deleting: boolean;
};

type BookConsultationStoreActions = {
  fetchAllDoctors: () => Promise<void>;
  createAppointment: (data: Consultation) => Promise<void>;
  createAppointments: (data: Consultation[]) => Promise<void>;
  setView: (view: ConsultationView) => void;
  reset: () => void;
  getFutureAppointments: (patientId: string) => void;
  getAppointmentType: (
    patientId: string,
    doctorId: string,
    appointmentId: string
  ) => Promise<string>;
  deleteAppointment: (appointmentId: string) => void;
  updateAppointment: (
    appointment: Partial<Consultation> & { queueId: string }
  ) => void;
};

type BookConsultationStore = BookConsultationStoreState &
  BookConsultationStoreActions;

const initialState: BookConsultationStoreState = {
  doctors: [],
  appointments: [],
  updatingAppointment: false,
  view: ConsultationView.form,
  futureAppointmentsLoading: false,
  deleting: false,
};

export const useBookConsultationStore = create<BookConsultationStore>(
  (set, get) => ({
    ...initialState,
    fetchAllDoctors: async () => {
      try {
        const doctors = await getAllDoctors();
        set({ doctors });
      } catch (error) {
        console.error(error);
      }
    },
    createAppointment: async (data: Consultation) => {
      set({ updatingAppointment: true });
      try {
        const appointment = await createAppointment(data);
        set({ appointments: [...get().appointments, appointment] });
      } catch (error) {
        console.error(error);
      } finally {
        set({ updatingAppointment: false });
      }
    },
    updateAppointment: async ({ queueId, ...appointment }) => {
      set({ updatingAppointment: true });
      try {
        if (!queueId) {
          throw new Error('Queue ID is required');
        }
        const updatedAppointment = await updateAppointment({
          queueId,
          ...appointment,
        });
        set((state) => {
          const appointmentMap = new Map(
            state.appointments.map((appt) => [appt.id, appt])
          );
          appointmentMap.set(updatedAppointment.id, updatedAppointment);
          return { appointments: Array.from(appointmentMap.values()) };
        });
        modal.success('Appointment updated successfully');
      } catch (error) {
        console.error(error);
      } finally {
        set({ updatingAppointment: false });
      }
    },
    createAppointments: async (data: Consultation[]) => {
      set({ updatingAppointment: true });
      try {
        const processedAppointments = await Promise.all(
          data.map(({ queueId, ...item }) => {
            if (queueId) {
              return updateAppointment({ queueId, ...item });
            }
            return createAppointment(item);
          })
        );

        const lastAppointment =
          processedAppointments?.[processedAppointments?.length - 1];
        const filterDate = lastAppointment?.date;
        const doctorId = lastAppointment?.doctorId;
        const doctor = get().doctors.find((item) => item.id === doctorId);

        if (filterDate && doctor) {
          useDoctorStore.getState().setDate(filterDate);
          useDoctorStore.getState().setSelectedDoctor(doctor);
        }

        set((state) => {
          const appointmentMap = new Map(
            state.appointments.map((appt) => [appt.id, appt])
          );
          processedAppointments.forEach((appt: Consultation) => {
            appointmentMap.set(appt.id, appt);
          });
          return { appointments: Array.from(appointmentMap.values()) };
        });

        modal.success('Appointment Booked Successfully');
      } catch (error) {
        console.error(error);
        modal.error(getErrorMessage(error, 'Failed to save appointments'));
      } finally {
        set({ updatingAppointment: false });
      }
    },

    getFutureAppointments: async (patientId: string) => {
      set({ futureAppointmentsLoading: true });
      try {
        const appointments = await getFutureAppointments(patientId);
        set({ appointments });
      } catch (error) {
        console.error(error);
      } finally {
        set({ futureAppointmentsLoading: false });
      }
    },

    getAppointmentType: async (patientId, doctorId, appointmentId) => {
      try {
        const type = await getAppointmentType(
          patientId,
          doctorId,
          appointmentId
        );
        return type.visitType;
      } catch (error) {
        console.error(error);
        return 'New';
      }
    },

    deleteAppointment: async (appointmentId: string) => {
      set({ deleting: true });
      try {
        await deleteAppointment(appointmentId);
        set({
          appointments: get().appointments.filter(
            (item) => item.id !== appointmentId
          ),
        });
        modal.success('Appointment deleted successfully');
        return null;
      } catch (error) {
        console.error(error);
        modal.error(getErrorMessage(error, 'Failed to delete appointment'));
      } finally {
        set({ deleting: false });
      }
    },

    setView: (view: ConsultationView) => set({ view }),
    reset: () => {
      const { futureAppointmentsLoading: _, ...rest } = initialState;
      set({ ...rest });
    },
  })
);
