import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { PatientI } from '../types/index';

type currentPatientStoreType = {
  patient: PatientI | null;
  isPatientFromQueue: boolean;
  isActivePatient: boolean;
  setPatient: (_data: PatientI | null) => void;
  setIsPatientFromQueue: (isFromQueue: boolean) => void;
  setIsActivePatient: (isActive: boolean) => void;
  updateIsActivePatient: (params: {
    currentAppointmentPatientId?: string | null;
    searchPatientId?: string | null;
    patientId?: string | null;
    isPatientFromQueue?: boolean;
  }) => void;
  clear: () => void;
};

export const useCurrentPatientStore = create<currentPatientStoreType>()(
  persist(
    (set, get) => ({
      patient: null,
      isPatientFromQueue: false,
      isActivePatient: false,

      setPatient: (data) => {
        set({ patient: data });
      },

      setIsPatientFromQueue: (isFromQueue) => {
        set({ isPatientFromQueue: isFromQueue });
        // Recalculate isActivePatient when isPatientFromQueue changes

        if (isFromQueue) {
          set({ isActivePatient: false });
        }
      },

      setIsActivePatient: (isActive) => {
        set({ isActivePatient: isActive });
      },

      updateIsActivePatient: (params) => {
        const {
          currentAppointmentPatientId,
          searchPatientId,
          patientId,
          isPatientFromQueue = get().isPatientFromQueue,
        } = params;

        // Update isPatientFromQueue if provided
        if (params.isPatientFromQueue !== undefined) {
          set({ isPatientFromQueue: params.isPatientFromQueue });
        }

        // Calculate isActivePatient
        if (isPatientFromQueue) {
          set({ isActivePatient: false });
        } else {
          const isActive =
            currentAppointmentPatientId === searchPatientId ||
            (patientId || get().patient?.id) === searchPatientId;
          set({ isActivePatient: isActive });
        }
      },

      clear: () =>
        set({
          patient: null,
          isPatientFromQueue: false,
          isActivePatient: false,
        }),
    }),
    {
      name: 'current-patient-store',
    }
  )
);
