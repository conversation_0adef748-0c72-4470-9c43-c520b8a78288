# MUI Theme Setup with Tailwind Integration

This document explains how the Material-UI (MUI) theme is configured to work seamlessly with your existing Tailwind CSS setup in the ARCA EMR project.

## Overview

The MUI theme has been configured to:

- Use the same color palette as your Tailwind CSS variables
- Support both light and dark modes automatically
- Maintain consistent typography using Archivo and Inter fonts
- Match spacing, shadows, and border radius from Tailwind
- Provide custom accent colors (pink and green)

## File Structure

```
src/core/theme/
├── index.ts                 # Main exports
├── colors.ts               # Color utilities and definitions
├── mui-theme.ts            # Theme configuration
├── mui-theme-types.ts      # TypeScript type extensions
└── use-theme-colors.ts     # Custom hook for theme colors

src/core/providers/
└── MuiThemeProvider.tsx    # Theme provider component
```

## Usage

### Basic Usage

The MUI theme is automatically available throughout your app via the `MuiThemeProvider` in `AppProviders.tsx`.

```tsx
import { Button, Card, Typography } from '@mui/material';

// Components will automatically use the theme
const MyComponent = () => (
  <Card>
    <Typography variant="h5">Hello World</Typography>
    <Button variant="contained" color="primary">
      Click me
    </Button>
  </Card>
);
```

### Using Theme Colors

Use the custom hook to access theme colors:

```tsx
import { useThemeColors } from '@/core/theme/use-theme-colors';

const MyComponent = () => {
  const colors = useThemeColors();

  return (
    <div
      style={{ backgroundColor: colors.background, color: colors.foreground }}
    >
      <span style={{ color: colors.accentPink }}>Pink accent</span>
      <span style={{ color: colors.accentGreen }}>Green accent</span>
    </div>
  );
};
```

### Theme Mode Control

Control light/dark mode programmatically:

```tsx
import { useTheme } from '@/core/providers/MuiThemeProvider';

const ThemeToggle = () => {
  const { mode, toggleTheme, setTheme } = useTheme();

  return (
    <div>
      <p>Current mode: {mode}</p>
      <button onClick={toggleTheme}>Toggle Theme</button>
      <button onClick={() => setTheme('light')}>Light Mode</button>
      <button onClick={() => setTheme('dark')}>Dark Mode</button>
    </div>
  );
};
```

### Custom Colors

Access custom accent and chart colors:

```tsx
import { useTheme } from '@mui/material/styles';

const MyComponent = () => {
  const theme = useTheme();

  return (
    <div>
      <div style={{ color: theme.palette.accent.pink }}>Pink accent</div>
      <div style={{ color: theme.palette.accent.green }}>Green accent</div>
      <div style={{ color: theme.palette.chart.chart1 }}>Chart color 1</div>
    </div>
  );
};
```

## Color Mapping

The MUI theme colors are mapped to match your Tailwind CSS variables:

| Tailwind Variable    | MUI Theme Property                 | Description             |
| -------------------- | ---------------------------------- | ----------------------- |
| `--primary`          | `theme.palette.primary.main`       | Primary brand color     |
| `--secondary`        | `theme.palette.secondary.main`     | Secondary color         |
| `--background`       | `theme.palette.background.default` | Page background         |
| `--foreground`       | `theme.palette.text.primary`       | Primary text color      |
| `--card`             | `theme.palette.background.paper`   | Card background         |
| `--destructive`      | `theme.palette.error.main`         | Error/destructive color |
| `--border`           | `theme.palette.divider`            | Border color            |
| `--muted-foreground` | `theme.palette.text.secondary`     | Secondary text          |

## Typography

The theme uses your configured fonts:

- **Headings (h1-h6)**: Archivo font family
- **Body text**: Inter font family
- **Buttons**: Inter font family with medium weight

## Component Overrides

Several MUI components have been customized to match Tailwind styling:

- **Buttons**: Use Tailwind border radius, no text transform
- **Cards**: Use Tailwind border radius and shadow
- **Text Fields**: Use Tailwind border radius and colors
- **Modals**: Custom backdrop styling

## Dark Mode Synchronization

The MUI theme automatically syncs with Tailwind's dark mode:

- Detects the `dark` class on the HTML element
- Falls back to system preference if no class is set
- Updates MUI theme when Tailwind dark mode changes
- Provides methods to programmatically control theme mode

## Best Practices

1. **Use the theme colors**: Always use theme colors instead of hardcoded values
2. **Leverage the custom hook**: Use `useThemeColors()` for consistent color access
3. **Test both modes**: Ensure components work in both light and dark modes
4. **Follow MUI patterns**: Use MUI's sx prop and styled components when possible

## Example Component

Here's a complete example showing best practices:

```tsx
import React from 'react';
import { Card, CardContent, Typography, Button, Box } from '@mui/material';
import { useThemeColors } from '@/core/theme/use-theme-colors';

const ExampleCard = () => {
  const colors = useThemeColors();

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          Example Card
        </Typography>
        <Typography variant="body2" color="text.secondary">
          This card uses the MUI theme that matches your Tailwind setup.
        </Typography>
        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
          <Button variant="contained" color="primary">
            Primary Action
          </Button>
          <Button
            variant="outlined"
            sx={{ color: colors.accentPink, borderColor: colors.accentPink }}
          >
            Pink Accent
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ExampleCard;
```

This setup ensures that your MUI components will have a consistent look and feel with your existing Tailwind-styled components while providing the full power of Material-UI's component library.
