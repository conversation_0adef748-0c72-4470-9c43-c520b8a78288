import { useEffect, useState } from 'react';

import {
  useForm,
  useFormContext,
  useFieldArray,
  FormProvider,
  FieldError,
} from 'react-hook-form';
import { UseFormReturn } from 'react-hook-form';

import TextInput from '@core/components/text-input';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { getNestedValue } from '@/utils/emr/doctor-profile/personal-info';
import {
  emailPattern,
  mobileNumberPattern,
  preventNonAlphabeticInput,
  residentialPhonePattern,
  restrictMobile,
} from '@/utils/validation';

import PencilIcon from '@/assets/svg/PencilIcon';

import {
  profileTabs,
  relationships,
} from '@/constants/emr/doctor-profile/personal-info';

import { EmergencyContact } from '@/types/emr/doctor-profile/personal-info';

import { SelectField } from './Components';
import SectionTitle from './SectionTitle';

import AutoResizeTextArea from './shared/AutoResizeTextArea';
import SaveButton from './shared/SaveButton';

type FormData = {
  emergencyContacts: EmergencyContact[];
};

const defaultEmergencyContacts = [
  {
    name: '',
    relation: '',
    city: '',
    contactNumber: '',
    email: '',
  },
  {
    name: '',
    relation: '',
    city: '',
    contactNumber: '',
    email: '',
  },
];

type DetailsFormProps = {
  formKey: string;
  index: number;
  remove: (_index: number) => void;
  setEditableField: React.Dispatch<React.SetStateAction<Set<string>>>;
  editableField: Set<string>;
  isSubmitted: boolean;
  methods: UseFormReturn<FormData>;
};

export default function EmergencyDetailsSection() {
  const { data: userData } = useUserStore();

  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    doctorProfile,
    createDoctorProfile,
    updateDoctorProfile,
    fetchDoctorProfileByEmail,
    setTabName,
  } = useDoctorStore();

  const methods = useForm<FormData>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      emergencyContacts: defaultEmergencyContacts,
    },
  });

  const { control, handleSubmit, reset } = methods;

  const { fields, remove } = useFieldArray({
    control,
    name: 'emergencyContacts',
  });

  useEffect(() => {
    if (doctorProfile) {
      let emergencyContacts: EmergencyContact[] = defaultEmergencyContacts;
      if (doctorProfile?.emergencyContacts?.length) {
        emergencyContacts = doctorProfile?.emergencyContacts;
      }
      reset({
        emergencyContacts: emergencyContacts,
      });
    }
  }, [doctorProfile, reset]);

  const onSubmit = async (data: FormData) => {
    try {
      const payload = { emergencyContacts: [...data?.emergencyContacts] };
      if (doctorProfile && doctorProfile?.id) {
        await updateDoctorProfile(doctorProfile?.id, payload);
        setEditableField(new Set());
        setIsSubmitted(true);
      } else {
        await createDoctorProfile({ ...payload, username: userData?.email });
        setEditableField(new Set());
        setIsSubmitted(true);
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  useEffect(() => {
    setTabName(profileTabs.EMERGENCY_DETAILS);
    if (userData?.email) {
      fetchDoctorProfileByEmail(userData?.email);
    }
  }, [userData?.email, fetchDoctorProfileByEmail, setTabName]);

  return (
    <div className="w-full mb-3 pr-2">
      <SectionTitle className="mb-5 mt-1" title="Emergency Details" />
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mt-2 flex flex-col gap-6 md:gap-8">
            {fields.map((field, index) => (
              <DetailsForm
                key={field.id}
                formKey={`emergencyContacts[${index}]`}
                index={index}
                remove={remove}
                setEditableField={setEditableField}
                editableField={editableField}
                isSubmitted={isSubmitted}
                methods={methods}
              />
            ))}
          </div>

          <SaveButton className="mt-15" />
        </form>
      </FormProvider>
    </div>
  );
}

function DetailsForm(props: DetailsFormProps) {
  const { index, setEditableField, editableField, isSubmitted, methods } =
    props;

  const {
    register,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();

  const { doctorProfile } = useDoctorStore();

  type ErrorsType = {
    emergencyContacts?: {
      [index: number]: {
        mobile?: FieldError;
        email?: FieldError;
        contactNumber?: FieldError;
      };
    };
  };

  const handleEditClick = (fieldName: string, index: number) => {
    setEditableField(
      (prev: Set<string>) => new Set(prev.add(`${fieldName}-${index}`))
    );
  };

  const isFieldDisabled = (fieldName: string, index?: number) => {
    if (
      editableField.has(
        index !== undefined ? `${fieldName}-${index}` : fieldName
      )
    ) {
      return false;
    }

    const formValues = methods.getValues() as FormData;
    const fieldValue =
      index !== undefined
        ? formValues?.emergencyContacts?.[index]?.[
            fieldName as keyof EmergencyContact
          ]
        : formValues?.[fieldName as keyof FormData];

    const doctorFieldValue =
      index !== undefined
        ? doctorProfile?.emergencyContacts?.[index]?.[
            fieldName as keyof EmergencyContact
          ]
        : getNestedValue(doctorProfile?.emergencyContacts, fieldName);

    return (
      !!doctorFieldValue ||
      (isSubmitted && !!fieldValue && fieldValue === doctorFieldValue)
    );
  };

  const renderEditIcon = (fieldName: string, index: number) => {
    return isFieldDisabled(fieldName, index) ? (
      <button
        className="mt-2"
        type="button"
        onClick={() => handleEditClick(fieldName, index)}
      >
        <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
      </button>
    ) : null;
  };

  return (
    <div className="flex flex-col gap-4 md:gap-6 mb-3 md:mb-0">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        <AutoResizeTextArea
          control={methods.control}
          name={`emergencyContacts.${index}.name`}
          label={`Name (Person ${index + 1})`}
          color="white"
          placeholder="Name"
          onKeyDown={preventNonAlphabeticInput}
          disabled={isFieldDisabled('name', index)}
          endDecoration={renderEditIcon('name', index)}
        />
        <SelectField
          id={`emergencyContacts[${index}].relation`}
          label="Relation"
          options={relationships}
          value={watch(`emergencyContacts[${index}].relation`)}
          wrapperClassName="flex-grow flex-1"
          onChange={(value) =>
            setValue(`emergencyContacts[${index}].relation`, value)
          }
          placeholder="--- Select Relation ---"
          disabledInput={isFieldDisabled(`relation`, index)}
          endDecoration={renderEditIcon(`relation`, index)}
        />
        <TextInput
          label="City"
          color="white"
          {...register(`emergencyContacts[${index}].city`)}
          placeholder="Kochi"
          onKeyDown={preventNonAlphabeticInput}
          disabled={isFieldDisabled('city', index)}
          endDecoration={renderEditIcon('city', index)}
        />
      </div>

      <div className="grid gap-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        <TextInput
          label="Residence Phone"
          color="white"
          {...register(`emergencyContacts[${index}].contactNumber`, {
            pattern: residentialPhonePattern(),
          })}
          placeholder="01234567899"
          errors={
            (errors as ErrorsType)?.emergencyContacts?.[index]?.contactNumber
          }
          disabled={isFieldDisabled('contactNumber', index)}
          endDecoration={renderEditIcon('contactNumber', index)}
          onKeyDown={restrictMobile(11)}
          onInput={restrictMobile(11)}
          pattern="[0-9]*"
          inputMode="numeric"
        />

        <TextInput
          label="Mobile"
          color="white"
          {...register(`emergencyContacts[${index}].mobile`, {
            pattern: mobileNumberPattern(),
          })}
          errors={(errors as ErrorsType)?.emergencyContacts?.[index]?.mobile}
          placeholder="9876543210"
          onKeyDown={restrictMobile(10)}
          onInput={restrictMobile(10)}
          pattern="[0-9]*"
          inputMode="numeric"
          disabled={isFieldDisabled('mobile', index)}
          endDecoration={renderEditIcon('mobile', index)}
        />
        <TextInput
          label="Email"
          color="white"
          {...register(`emergencyContacts[${index}].email`, {
            pattern: emailPattern(),
          })}
          errors={(errors as ErrorsType)?.emergencyContacts?.[index]?.email}
          placeholder="<EMAIL>"
          disabled={isFieldDisabled('email', index)}
          endDecoration={renderEditIcon('email', index)}
          inputMode="email"
        />
      </div>
    </div>
  );
}
