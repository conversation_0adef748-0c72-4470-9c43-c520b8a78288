import { Box } from '@mui/material';

import { usePackageSelectorStore } from '@/store/emr/lab/package-store';
import { useUserStore } from '@/store/userStore';

import PenIcon from '@/assets/svg/PenIcon';

import { commonButtonProps } from '@/constants/emr/lab';
import { PERMISSION_KEYS } from '@/constants/permission-keys';

import PrimaryButton from '@/core/components/primary-button';
import { modalModes } from '@/types/emr/lab';
import { prescriptionPackageTypes } from '@/types/emr/prescription';

const { DETAIL, CREATE, ADD } = modalModes;

export const PackageModalFooter = () => {
  const {
    modalMode,
    setModalMode,
    updateExistingPackage,
    createNewPackage,
    selectedPackage,
    addSelectedPackageToTest,
    startCreatePackage,
    isCreating,
    activePackageType,
  } = usePackageSelectorStore();

  const { permissions } = useUserStore();
  const hasManagePermission = permissions.includes(
    PERMISSION_KEYS.EMR_TEST_PACKAGE_MANAGE
  );

  const handleSavePackage = () => {
    if (modalMode === modalModes.CREATE && selectedPackage) {
      updateExistingPackage();
    } else {
      createNewPackage();
    }
  };

  return (
    <div className="pb-2 px-4 flex justify-between relative">
      <PrimaryButton
        onClick={() => startCreatePackage(modalModes.CREATE)}
        className={`${commonButtonProps.className} !min-w-[110px]`}
        variant="outlined"
        disabled={
          !hasManagePermission &&
          activePackageType === prescriptionPackageTypes.DEPARTMENT
        }
      >
        Add New Package <span className="mr-1">+</span>
      </PrimaryButton>
      {modalMode === CREATE || modalMode === ADD ? (
        <PrimaryButton
          onClick={handleSavePackage}
          {...commonButtonProps}
          className={`${commonButtonProps.className} !min-w-[110px]`}
          isLoading={isCreating}
        >
          Save Package
        </PrimaryButton>
      ) : modalMode === DETAIL ? (
        <Box display="flex" gap={1}>
          <PrimaryButton
            variant="outlined"
            onClick={() => setModalMode(CREATE)}
            {...commonButtonProps}
            className={`${commonButtonProps.className} !min-w-[110px]`}
          >
            Edit test
            <PenIcon className="w-3 h-4" />
          </PrimaryButton>
          <PrimaryButton
            onClick={addSelectedPackageToTest}
            {...commonButtonProps}
            className={`${commonButtonProps.className} !min-w-[110px]`}
          >
            Add to test
          </PrimaryButton>
        </Box>
      ) : null}
    </div>
  );
};
