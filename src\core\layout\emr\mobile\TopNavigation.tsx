import React, { FC, memo, useCallback } from 'react';

import { IconButton } from '@mui/material';
import { AiOutlineClose } from 'react-icons/ai';
import { IoIosArrowBack } from 'react-icons/io';

import { useRouter, usePathname } from 'next/navigation';

import { useMobileMenuStore } from '@/store/mobileMenuStore';

import { menu } from '@/utils/constants/mobileMenu';

import MobileMenu from './MobileMenu';

type Props = {
  onClickBack?: () => void;
};

const TopNavigation: FC<Props> = ({ onClickBack }) => {
  const { back } = useRouter();
  const pathname = usePathname();
  const [previousPath, setPreviousPath] = React.useState(pathname);
  const [isRouteChanging, setIsRouteChanging] = React.useState(false);

  const {
    setIsProfileMenu,
    setMobileMenuPage,
    mobileMenuPage,
    setAnchorEl,
    isFromMenu,
    setMenuClick,
    isMenuClick,
    setSelectedRoute,
    setIsMenuOpen,
    isNavigating,
    isPageLoading,
    setIsFromProfile,
    isFromProfile,
  } = useMobileMenuStore();

  const handleBackClick = useCallback(() => {
    if (onClickBack) {
      onClickBack();
    } else {
      back();
    }
  }, [back, onClickBack]);

  React.useEffect(() => {
    if (pathname !== previousPath) {
      setIsRouteChanging(true);
      const timer = setTimeout(() => {
        setIsRouteChanging(false);
        setPreviousPath(pathname);
        if (mobileMenuPage === menu.PROFILE_MENU) {
          setIsProfileMenu(false);
          setMobileMenuPage('');
          setIsMenuOpen(false);
          setAnchorEl(null);
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [
    pathname,
    previousPath,
    mobileMenuPage,
    setIsProfileMenu,
    setMobileMenuPage,
    setIsMenuOpen,
    setAnchorEl,
  ]);

  const handleMenuClose = useCallback(
    (e: React.MouseEvent) => {
      if (isRouteChanging || isNavigating || isPageLoading) {
        e.preventDefault();
        return;
      }
      if (!isMenuClick) {
        handleBackClick();
        setTimeout(() => {
          setIsProfileMenu(false);
          setAnchorEl(null);
          setMobileMenuPage('');
          setIsMenuOpen(false);
          setMenuClick(false);
        }, 100);
      } else if (isMenuClick && isFromProfile) {
        setMobileMenuPage(menu.PROFILE_MENU);
      } else {
        setIsProfileMenu(false);
        setAnchorEl(null);
        setMobileMenuPage('');
        setIsMenuOpen(false);
        setMenuClick(false);
        setIsFromProfile(false);
      }
    },
    [
      isRouteChanging,
      isNavigating,
      isPageLoading,
      isMenuClick,
      isFromProfile,
      handleBackClick,
      setIsProfileMenu,
      setAnchorEl,
      setMobileMenuPage,
      setIsMenuOpen,
      setMenuClick,
      setIsFromProfile,
    ]
  );

  const handleIconClick = useCallback(
    (e: React.MouseEvent) => {
      if (isRouteChanging || isNavigating || isPageLoading) {
        e.preventDefault();
        return;
      }

      const resetMenuState = () => {
        setIsProfileMenu(false);
        setMobileMenuPage('');
        setIsMenuOpen(false);
      };

      if (mobileMenuPage === menu.PROFILE_MENU && !isMenuClick) {
        handleBackClick();
        setTimeout(() => {
          resetMenuState();
        }, 100);
      } else if (isFromMenu && mobileMenuPage !== menu.PROFILE_MENU) {
        setIsProfileMenu(true);
        setMobileMenuPage(menu.PROFILE_MENU);
        setMenuClick(false);
        setSelectedRoute('');
        setIsMenuOpen(true);
      } else if (
        (mobileMenuPage === menu.PROFILE_MENU && isMenuClick) ||
        (isFromMenu && mobileMenuPage === menu.PROFILE_MENU)
      ) {
        resetMenuState();
        setAnchorEl(null);
      } else {
        handleBackClick();
      }
    },
    [
      isRouteChanging,
      isNavigating,
      isPageLoading,
      mobileMenuPage,
      isMenuClick,
      isFromMenu,
      setIsProfileMenu,
      setMobileMenuPage,
      setIsMenuOpen,
      handleBackClick,
      setMenuClick,
      setSelectedRoute,
      setAnchorEl,
    ]
  );

  return (
    <div className="relative">
      <div className="absolute top-0 left-0 z-[9999]">
        {mobileMenuPage === menu.MAIN_MENU ? (
          <IconButton
            onClick={handleMenuClose}
            disabled={isRouteChanging || isNavigating || isPageLoading}
            className={
              isRouteChanging || isNavigating || isPageLoading
                ? 'opacity-50'
                : ''
            }
          >
            <AiOutlineClose size={18} />
          </IconButton>
        ) : (
          <IconButton
            onClick={handleIconClick}
            disabled={isRouteChanging || isNavigating || isPageLoading}
            className={
              isRouteChanging || isNavigating || isPageLoading
                ? 'opacity-50'
                : ''
            }
          >
            <IoIosArrowBack />
          </IconButton>
        )}
      </div>
      <div className="flex items-center justify-end py-0.5 relative">
        <MobileMenu />
      </div>
    </div>
  );
};

export default memo(TopNavigation);
