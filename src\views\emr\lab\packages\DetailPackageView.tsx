import { useEffect } from 'react';

import Loading from '@/lib/common/loading';

import {
  TestItem,
  usePackageSelectorStore,
} from '@/store/emr/lab/package-store';
import { useTestStore } from '@/store/emr/lab/reports-store';

import Table from '@/core/components/table';
import { Header, Row } from '@/core/components/table/types';
import { modalModes } from '@/types/emr/lab';

import { getTableContainerProps } from './Components';

type DetailPackageViewProps = {
  tableHeaders: Header[];
  testRows: Row[];
};

type PackageTestListProps = {
  testItems: TestItem[];
  tableHeaders: Header[];
  testRows: Row[];
  emptyMessage: string;
};

const MAX_ITEMS_PER_COLUMN = 15;

export const DetailPackageView: React.FC<DetailPackageViewProps> = ({
  tableHeaders,
  testRows,
}) => {
  const { selectedSearchTests, removeSelectedSearchTest } = useTestStore();
  const { activePackageType, testItems } = usePackageSelectorStore();

  useEffect(() => {
    const activePackageItems = selectedSearchTests.filter(
      (item) => item.pageKey === activePackageType
    );

    activePackageItems.forEach((item) => {
      removeSelectedSearchTest(item.id as string);
    });
  }, [selectedSearchTests, activePackageType, removeSelectedSearchTest]);

  return (
    <div className="flex flex-col px-4 flex-grow">
      <PackageTestList
        testItems={testItems}
        tableHeaders={tableHeaders}
        testRows={testRows}
        emptyMessage="No tests in this package."
      />
    </div>
  );
};

export const PackageTestList: React.FC<PackageTestListProps> = ({
  testItems,
  tableHeaders,
  testRows,
  emptyMessage,
}) => {
  const { isLoading, modalMode } = usePackageSelectorStore();

  const showLoading = modalMode === modalModes.DETAIL && isLoading;
  const useTwoColumns = testItems.length > MAX_ITEMS_PER_COLUMN;
  const tableContainerProps = getTableContainerProps({
    useTwoColumns,
    modalMode,
  });

  return (
    <div className="flex-grow overflow-auto">
      {showLoading ? (
        <div className="flex justify-center items-center w-full h-full py-10">
          <Loading />
        </div>
      ) : testItems.length > 0 ? (
        <div
          className="flex justify-start w-full"
          style={{ minHeight: '200px' }}
        >
          <div
            className={useTwoColumns ? 'w-full' : 'w-1/2'}
            style={{
              maxWidth: useTwoColumns ? '100%' : '50%',
              minWidth: useTwoColumns ? '100%' : '50%',
            }}
          >
            <Table
              headers={tableHeaders}
              rows={testRows}
              stickyHeader
              tableContainerProps={tableContainerProps}
            />
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center w-full h-full text-gray-500">
          {emptyMessage}
        </div>
      )}
    </div>
  );
};
