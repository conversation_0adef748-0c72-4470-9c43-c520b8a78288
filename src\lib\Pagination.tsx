import React from 'react';

import { ChevronLeft, ChevronRight } from 'lucide-react';

import { Button } from '@/components/ui/button';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  className?: string;
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  className = '',
}: PaginationProps) {
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  // Format numbers with leading zero
  const formatNumber = (num: number) => {
    return num.toString().padStart(2, '0');
  };

  return (
    <div className={`flex justify-center w-full py-2 ${className}`}>
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          className="h-7 w-7 p-0 rounded-full border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 transition-colors"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4 text-gray-600" />
        </Button>

        <div className="flex items-center justify-center text-sm font-medium ">
          <span className="text-sm font-medium ">
            {formatNumber(currentPage)}
          </span>
          <span className="mx-2 text-sm font-medium text-[#64707D]">
            out of
          </span>
          <span className="text-sm font-medium ">
            {formatNumber(totalPages)}
          </span>
        </div>

        <Button
          variant="outline"
          size="sm"
          className="h-7 w-7 p-0 rounded-full border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 transition-colors"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          <ChevronRight className="h-4 w-4 text-gray-600" />
        </Button>
      </div>
    </div>
  );
}

// Demo component to show how it looks
export default function PaginationDemo() {
  const [currentPage, setCurrentPage] = React.useState(1);
  const totalPages = 3;

  return (
    <div className="p-8 bg-gray-50 min-h-screen flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-sm p-8 w-full max-w-md">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={30}
          pageSize={10}
          onPageChange={setCurrentPage}
          onPageSizeChange={() => {}}
        />
      </div>
    </div>
  );
}
