# Folder Structure Guide - ARCA EMR

Feature-based organization pattern for ARCA EMR project.

## 4-Layer Feature Organization

For new features, follow this structure:

1. **Routes**: `app/(private)/emr/[feature-name]/`
2. **Components**: `views/emr/[feature-name]/` or `core/components/`
3. **State**: `store/emr/[feature-name]/`
4. **Queries**: `query/emr/[feature-name]/`

**Important**: `emr/` folder is legacy - use `views/` or `core/components/` instead.

## Example: Lifestyle Feature Structure

```
# Routes
app/(private)/emr/lifestyle/
├── layout.tsx
├── page.tsx
├── nutrition/
│   ├── layout.tsx
│   ├── page.tsx
│   ├── attitude/page.tsx
│   └── practice/page.tsx
└── physical-activity/
    ├── layout.tsx
    ├── page.tsx
    ├── attitude/page.tsx
    └── practice/page.tsx

# Components (NEW: use views/ instead of emr/)
views/emr/lifestyle/
├── LifestyleNote.tsx
├── lifestyle-forms/
└── mobile/

# State
store/emr/lifestyle/
├── lifestyle-data-store.ts
└── navigation-store.ts

# Queries
query/emr/lifestyle/
└── index.ts
```

## Naming Conventions

- Folders: `kebab-case`
- Routes: `layout.tsx`, `page.tsx`
- Components: `PascalCase.tsx`
- Stores: `kebab-case.ts`

## Checklist for New Features

- [ ] Routes in `app/(private)/emr/[feature-name]/`
- [ ] Components in `views/emr/[feature-name]/` (NOT `emr/components/`)
- [ ] State in `store/emr/[feature-name]/`
- [ ] Queries in `query/emr/[feature-name]/`
- [ ] Follow lifestyle/doctor-profile patterns
