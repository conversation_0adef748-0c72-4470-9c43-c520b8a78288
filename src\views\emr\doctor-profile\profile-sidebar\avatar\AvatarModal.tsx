import React, { useCallback, useRef } from 'react';

import Box from '@mui/material/Box';
import Modal from '@mui/material/Modal';
import { IoCloseSharp } from 'react-icons/io5';

import Loading from '@/lib/common/loading';
import { cn } from '@/lib/utils';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useProfilePictureStore } from '@/store/emr/doctor-profile/profile-picture-store';

import OutLinedIconButton from '@/emr/components/lifestyle/lifestyle-forms/shared/OutlinedIconButton';

import AppButton from '@/core/components/app-button';

import profileImage from './profile-image.png';

interface AvatarModalProps {
  open: boolean;
  onClose: () => void;
}

const AvatarModal: React.FC<AvatarModalProps> = ({ open, onClose }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    isUploading,
    isDeleting,
    previewUrl,
    uploadProfilePicture,
    deleteProfilePicture,
    onImagePreviewError,
  } = useProfilePictureStore();
  const { doctorProfile } = useDoctorStore();

  const onFileChange = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && doctorProfile?.id) {
        const file = e.target.files[0];
        await uploadProfilePicture(doctorProfile.id, file);
      }
    },
    [uploadProfilePicture, doctorProfile?.id]
  );

  const onDeleteClick = useCallback(() => {
    deleteProfilePicture(doctorProfile?.id ?? '');
  }, [deleteProfilePicture, doctorProfile?.id]);

  return (
    <Modal open={open} onClose={onClose}>
      <Box
        className={cn(
          'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
          'bg-white rounded-md',
          'w-[70vw] md:w-[50vw] lg:w-[24vw]',
          'p-4'
        )}
      >
        <div className={cn('flex flex-col items-center gap-4 md:gap-6')}>
          <div className="w-full flex justify-between items-center border-b pb-2">
            <span className="font-medium text-nowrap text-sm">
              Profile Picture Settings
            </span>
            <OutLinedIconButton
              onClick={onClose}
              sx={{ p: 0.4, width: 20, height: 20 }}
            >
              <IoCloseSharp className="text-black" />
            </OutLinedIconButton>
          </div>
          <img
            src={previewUrl || profileImage.src}
            alt="Profile Preview"
            className={cn(
              'rounded-full object-cover',
              'h-20 w-20',
              'sm:h-26 sm:w-26',
              'md:h-30 md:w-30',
              'xl:h-34 xl:w-34'
            )}
            onError={onImagePreviewError}
          />
          <div className="flex flex-col w-full gap-3">
            <AppButton
              onClick={() => {
                if (fileInputRef.current) {
                  fileInputRef.current.click();
                }
              }}
              loading={isUploading}
              sx={{ py: 3 }}
            >
              Upload Image
            </AppButton>
            <button
              className={cn(
                'w-full',
                'px-1 py-1',
                'md:px-2 md:py-2',
                'bg-gray-400',
                'rounded',
                'hover:bg-gray-500',
                'disabled:bg-gray-200',
                'flex items-center gap-2 justify-center'
              )}
              onClick={onDeleteClick}
              disabled={isDeleting}
            >
              Delete &nbsp;&nbsp;
              {isDeleting && <Loading />}
            </button>
          </div>
          <input
            id="avatar-file-input"
            type="file"
            className="hidden"
            accept="image/*"
            onChange={onFileChange}
            ref={fileInputRef}
          />
        </div>
      </Box>
    </Modal>
  );
};

export default AvatarModal;
