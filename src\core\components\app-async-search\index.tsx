'use client';

import React, { forwardRef, memo } from 'react';

import type { GroupBase, components } from 'react-select';

import { useTheme } from '@mui/material';
import AsyncSelect, { AsyncProps } from 'react-select/async';

import { getStyles } from './styles';

export interface AppAsyncSearchOption {
  [key: string]: any;
}

export interface AppAsyncSearchProps<T extends AppAsyncSearchOption>
  extends AsyncProps<T, false, GroupBase<T>> {
  defaultOptions?: T[];
  placeholder?: string;
  components?: Partial<typeof components>;
  renderOption?: (option: T) => React.ReactNode;
}

const AppAsyncSearch = <T extends AppAsyncSearchOption>(
  {
    defaultOptions = [],
    placeholder = 'Search...',
    components: customComponents,
    renderOption,
    ...rest
  }: AppAsyncSearchProps<T>,
  ref: React.Ref<any>
) => {
  const theme = useTheme();

  const CustomOption = renderOption
    ? (props: any) => {
        const { data, innerRef, innerProps } = props;
        return (
          <div ref={innerRef} {...innerProps}>
            {renderOption(data)}
          </div>
        );
      }
    : undefined;

  return (
    <AsyncSelect<T>
      ref={ref}
      defaultOptions={defaultOptions}
      isClearable
      cacheOptions
      styles={getStyles<T>(theme)}
      placeholder={placeholder}
      menuPortalTarget={
        typeof window !== 'undefined' ? document.body : undefined
      }
      components={{
        ...(CustomOption && { Option: CustomOption }),
        IndicatorSeparator: null,
        ...customComponents,
      }}
      {...rest}
    />
  );
};

export default memo(forwardRef(AppAsyncSearch)) as <
  T extends AppAsyncSearchOption,
>(
  props: AppAsyncSearchProps<T> & { ref?: React.Ref<any> }
) => React.ReactElement;
