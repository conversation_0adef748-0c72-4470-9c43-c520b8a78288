import { ComponentSettings, OverrideComponent } from '@core/theme/types';

const MuiRadio = (
  _settings: ComponentSettings
): OverrideComponent['MuiRadio'] => ({
  styleOverrides: {
    root: {
      color: '#000000 !important',
      '&.Mui-checked': {
        color: '#000000 !important',
        fontWeight: 'bold',
      },
      '&.Mui-disabled': {
        color: '#000000 !important',
        opacity: '1 !important',
      },
      '&.Mui-disabled.Mui-checked': {
        color: '#000000 !important',
        opacity: '1 !important',
        fontWeight: 'bold',
        '& .MuiSvgIcon-root': {
          fontWeight: 'bold',
          strokeWidth: '2px',
        },
      },
      '& .MuiSvgIcon-root': {
        color: '#000000 !important',
      },
      '&.Mui-disabled .MuiSvgIcon-root': {
        color: '#000000 !important',
        opacity: '1 !important',
      },
      '&.Mui-checked .MuiSvgIcon-root': {
        fontWeight: 'bold',
        strokeWidth: '2px',
      },
    },
  },
});

export default MuiRadio;
