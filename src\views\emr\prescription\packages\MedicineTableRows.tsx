import React from 'react';

import { IoClose } from 'react-icons/io5';

import colors from '@/utils/colors';

import { TestSelectCheckbox } from '@/views/emr/lab/packages/Components';

import OutlinedIconButton from '@/emr/components/lifestyle/lifestyle-forms/shared/OutlinedIconButton';

import { RowV2 } from '@/core/components/table-v2/types';
import { MedicineItem } from '@/types/emr/prescription/package';

const commonCellProps = {
  sx: {
    minWidth: 25,
    width: 25,
    padding: '0px 5px !important',
  },
};

const actionButtonCellProps = {
  sx: {
    minWidth: 10,
    width: 10,
    padding: '0px !important',
  },
};

export const getMedicineRowsForCreateMode = (
  medicines: MedicineItem[],
  removeMedicine: (medicineId: string | number) => void
) => {
  if (!medicines) return [];

  return medicines.map((medicine) => ({
    medicineName: {
      value: medicine.medicineName,
      cellProps: commonCellProps,
    },
    brandName: {
      value: medicine.brandName,
      cellProps: commonCellProps,
    },
    strength: {
      value: medicine.strength,
      cellProps: commonCellProps,
    },
    drugForm: {
      value: medicine.drugForm,
      cellProps: commonCellProps,
    },
    action: {
      value: (
        <div className="flex w-full items-center justify-center p-2">
          <OutlinedIconButton
            sx={{ width: 20, height: 20, p: 0.4 }}
            onClick={() => removeMedicine(medicine.id)}
          >
            <IoClose />
          </OutlinedIconButton>
        </div>
      ),
      cellProps: actionButtonCellProps,
    },
  }));
};

export const getMedicineRowsForDetailMode = (
  medicines: MedicineItem[],
  toggleMedicineSelection: (medicineId: string | number) => void
): RowV2[] => {
  return medicines.map((medicine) => ({
    medicineName: {
      value: medicine.medicineName,
      cellProps: commonCellProps,
    },
    brandName: {
      value: medicine.brandName,
      cellProps: commonCellProps,
    },
    strength: {
      value: medicine.strength,
      cellProps: commonCellProps,
    },
    drugForm: {
      value: medicine.drugForm,
      cellProps: commonCellProps,
    },
    select: {
      value: (
        <div
          className="flex w-full items-center justify-center cursor-pointer"
          onClick={() => toggleMedicineSelection(medicine.id)}
        >
          <TestSelectCheckbox
            checked={medicine.selected}
            size="medium"
            sx={{
              color: colors.common.navyBlue,
              padding: '4px',
              '&.Mui-checked': { color: colors.common.navyBlue },
            }}
          />
        </div>
      ),
      cellProps: actionButtonCellProps,
    },
  }));
};
