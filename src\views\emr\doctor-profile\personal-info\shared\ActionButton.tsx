import React, { FC, memo, ReactNode } from 'react';

import { IconButton } from '@mui/material';
import { IoIosCloseCircleOutline } from 'react-icons/io';

import colors from '@/utils/colors';

import { AkarIconsEdit } from '@/assets/svg/AkarIconsEdit';
import { FluentDelete32Filled } from '@/assets/svg/FluentDelete32Filled';

type ActionFor = 'edit' | 'delete' | 'close';

const getActionIcon = (
  actionFor: ActionFor,
  iconSize: number | string = 18
): ReactNode => {
  switch (actionFor) {
    case 'edit':
      return (
        <AkarIconsEdit
          width={iconSize}
          height={'auto'}
          fontSize={iconSize}
          color={colors.common.navyBlue}
        />
      );
    case 'delete':
      return (
        <FluentDelete32Filled
          width={iconSize}
          height={'auto'}
          fontSize={iconSize}
          color={colors.common.red}
        />
      );
    case 'close':
      return (
        <IoIosCloseCircleOutline
          size={iconSize}
          color={colors.common.navyBlue}
        />
      );
    default:
      return null;
  }
};

type Props = {
  actionFor: ActionFor;
  onClick?: () => void;
  disabled?: boolean;
  iconSize?: number | string;
};

const ActionButton: FC<Props> = ({
  actionFor,
  onClick,
  disabled = false,
  iconSize = 18,
}) => {
  return (
    <IconButton size="small" onClick={onClick} disabled={disabled}>
      {getActionIcon(actionFor, iconSize)}
    </IconButton>
  );
};

export default memo(ActionButton);
