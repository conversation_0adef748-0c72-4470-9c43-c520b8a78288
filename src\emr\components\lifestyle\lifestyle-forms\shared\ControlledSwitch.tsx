import React, { memo } from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import { SwitchProps, FormControlLabel, Box, Typography } from '@mui/material';

import { CustomSwitch } from './CustomSwitch';

type Props<T extends FieldValues> = SwitchProps & {
  control: Control<T>;
  name: Path<T>;
  label?: string | string[];
};

const ControlledSwitch = <T extends FieldValues>({
  control,
  name,
  label,
  ...rest
}: Props<T>) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, onChange, ...field } }) => {
        const isArrayLabel = Array.isArray(label);

        return (
          <Box display="flex" alignItems="center" gap={1} p={1}>
            {isArrayLabel ? (
              <>
                <Typography variant="body2">{label[0]}</Typography>
                <CustomSwitch
                  {...field}
                  {...rest}
                  checked={value === label[1]}
                  onChange={(e) =>
                    onChange(e.target.checked ? label[1] : label[0])
                  }
                />
                <Typography variant="body2">{label[1]}</Typography>
              </>
            ) : (
              <FormControlLabel
                control={
                  <CustomSwitch
                    {...field}
                    {...rest}
                    checked={Boolean(value)}
                    onChange={(e) => onChange(e.target.checked)}
                  />
                }
                label={label}
                labelPlacement="start"
              />
            )}
          </Box>
        );
      }}
    />
  );
};

export default memo(ControlledSwitch);
