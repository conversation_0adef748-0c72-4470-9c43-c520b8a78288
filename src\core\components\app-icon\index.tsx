import React, { FC, memo } from 'react';

import { Icon } from '@iconify/react';
import { SvgIcon, SvgIconProps } from '@mui/material';

type Props = SvgIconProps & {
  icon: string;
};

/**
 * Renders an application icon using the provided Iconify icon name.
 *
 * @param icon - The Iconify icon name (e.g., "mdi:home") to display.
 * @param props - Additional props to pass to the SvgIcon component.
 *
 * @returns A React element displaying the specified Iconify icon within an SvgIcon wrapper.
 */
const AppIcon: FC<Props> = memo(({ icon, ...props }) => {
  return (
    <SvgIcon {...props}>
      <Icon icon={icon} />
    </SvgIcon>
  );
});

AppIcon.displayName = 'AppIcon';

export default AppIcon;
