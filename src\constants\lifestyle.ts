export const NutritionAndDietForm = {
  DIETARY_ASSESSMENT: 'Dietary Assessment Summary',
  LIFESTYLE_AWARENESS_ADAPTATION:
    'Lifestyle Awareness & Adaptation Questionnaire',
  NUTRITION_MONITORING_SHEET: 'Nutrition Monitoring Sheet Questionnaire',
} as const;

export type NutritionAndDietFormType =
  (typeof NutritionAndDietForm)[keyof typeof NutritionAndDietForm];

export type LifestyleFormsType = NutritionAndDietFormType | string;

export const lifestyleModes = {
  EDIT: 'edit',
  CREATE: 'create',
  VIEW: 'view',
} as const;

export const lifestyleFormMode = {
  EDITABLE: 'editable',
  FINALIZED: 'finalized',
};

export type LifestyleFormModeType =
  (typeof lifestyleFormMode)[keyof typeof lifestyleFormMode];

export type LifestyleModesType =
  (typeof lifestyleModes)[keyof typeof lifestyleModes];

export const mobileViews = {
  NOW_CONSULTING: 'Now Consulting',
  ADD_RECORD_MANUALLY: 'Add Record Manually',
  AMBIENT_LISTENING: 'Ambient Listening',
  RECORDING_CONSULTATION: 'Recording Consultation',
  TIMELINE: 'Timeline',
} as const;

export type MobileViewType =
  | (typeof mobileViews)[keyof typeof mobileViews]
  | string;

export const renderFormModes = {
  TRANSCRIPT_MODE: 'transcript',
  RECORD_MODE: 'record',
} as const;

export type RenderFormModesTypes =
  (typeof renderFormModes)[keyof typeof renderFormModes];

export const lifestyleSectionTypes = {
  SECTION: 'section',
  TEXT_AREA_SECTION: 'text_area_section',
  TABLE: 'table',
} as const;

export type LifestyleSectionTypes =
  (typeof lifestyleSectionTypes)[keyof typeof lifestyleSectionTypes];

export const lifestyleQuestionTypes = {
  GROUP: 'group',
  NUMBER: 'number',
  TEXT: 'text',
  TEXT_AREA: 'text_area',
  SWITCH: 'switch',
  TIME_RANGE: 'time_range',
  FIELD_ARRAY_TEXT: 'field_array_text',
  FIELD_ARRAY_TIME_RANGE: 'field_array_time_range',
  TABLE_HEADER: 'table_header',
  TABLE_ROW: 'table_row',
  MULTI_TIME_RANGE: 'multi_time_range',
  MULTI_TEXT: 'multi_text',
  MULTI_NUMBER: 'multi_number',
  TABLE_HEADER_BOLD: 'table_header_bold',
  RADIO_GROUP: 'radio_group',
  RADIO: 'radio',
  CHECKBOX: 'checkbox',
} as const;

export type LifestyleQuestionTypes =
  (typeof lifestyleQuestionTypes)[keyof typeof lifestyleQuestionTypes];
