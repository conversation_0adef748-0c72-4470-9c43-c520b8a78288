import { Box, Typography } from '@mui/material';

import CustomModal from '@/core/components/modal';
import PrimaryButton from '@/core/components/primary-button';

interface ClearConfirmationModalProps {
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export const ClearConfirmationModal: React.FC<ClearConfirmationModalProps> = ({
  open,
  onConfirm,
  onCancel,
}) => (
  <CustomModal
    open={open}
    onClose={onCancel}
    width="27vw"
    minHeight="10px"
    maxHeight="18vh"
    showDivider={false}
    content={
      <Box p={1} textAlign="center">
        <Typography sx={{ fontSize: 16 }}>
          Do you want clear all the prescriptions?
        </Typography>
      </Box>
    }
    actionsSx={{
      justifyContent: 'center',
      mr: undefined,
    }}
    actions={
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          gap: 1,
          width: '100%',
        }}
      >
        <PrimaryButton
          type="button"
          onClick={onCancel}
          className="capitalize text-black bg-[#C2CDD6] text-md h-7 w-full"
        >
          Cancel
        </PrimaryButton>
        <PrimaryButton
          type="button"
          onClick={onConfirm}
          className="capitalize text-md h-7 w-full"
        >
          Yes
        </PrimaryButton>
      </Box>
    }
  />
);
