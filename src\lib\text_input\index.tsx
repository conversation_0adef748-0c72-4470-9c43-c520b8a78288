/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the new input component instead.
 */
import {
  ChangeEventHandler,
  HTMLInputTypeAttribute,
  ReactNode,
  useId,
} from 'react';

import { cn } from '../utils';

export type TextInputProps = {
  id?: string;
  className?: string;
  label?: string;
  defaultValue?: string;
  value?: string;
  type?: HTMLInputTypeAttribute;
  placeholder?: string;
  readOnly?: boolean;
  disabled?: boolean;
  color?: 'white' | 'grey';
  variant?: 'bordered' | 'filled';
  labelClassname?: string;
  inputClassname?: string;
  startDecoration?: ReactNode;
  endDecoration?: ReactNode;
  onChange?: (value?: string) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
};

/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the app text field component instead.
 */
export default function TextInput({
  id,
  className,
  label,
  labelClassname = '',
  inputClassname = '',
  defaultValue,
  value,
  type,
  placeholder,
  readOnly,
  disabled,
  color = 'grey',
  variant = 'filled',
  startDecoration,
  endDecoration,
  onChange = () => {},
  ...rest
}: TextInputProps) {
  const defaultId = useId();
  const localId = id || defaultId;

  const handleChange: ChangeEventHandler<HTMLInputElement> = (e) => {
    onChange(e.target.value);
  };

  return (
    <div className={cn('inline-flex flex-col  gap-2.5', className)}>
      {label && (
        <label
          className={cn('text-[13px] text-nowrap', labelClassname)}
          htmlFor={localId}
        >
          {label}
        </label>
      )}

      <div className="flex justify-between items-center  relative">
        <div className="absolute left-4 bg-white top-1/2 -translate-y-1/2">
          {startDecoration}
        </div>

        <input
          className={cn([
            'p-2.5 h-10.5 rounded  read-only:cursor-default',
            'read-only:bg-white flex-grow',
            color === 'white' ? 'bg-white' : 'bg-[#E8EBED]',
            variant === 'bordered' &&
              'border border-[#637D92] placeholder:text-[#637D92]',
            inputClassname,
          ])}
          id={localId}
          placeholder={placeholder}
          type={type || 'text'}
          defaultValue={defaultValue}
          value={value}
          disabled={disabled}
          readOnly={readOnly}
          onChange={handleChange}
          {...rest}
        />

        <div className="absolute right-4 top-1/2 -translate-y-1/2 bg-white">
          {endDecoration}
        </div>
      </div>
    </div>
  );
}
