import React, { SVGProps } from 'react';

const LifestyleIconMob = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width={16}
      height={22}
      viewBox="0 0 16 22"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fill="currentColor"
        d="M10.0004 21V16L7.90039 14L7.12539 17.45C7.05872 17.7167 6.91289 17.9208 6.68789 18.0625C6.46289 18.2042 6.21706 18.25 5.95039 18.2L1.00039 17.2C0.717057 17.15 0.500391 17.0083 0.350391 16.775C0.200391 16.5417 0.150391 16.2833 0.200391 16C0.250391 15.7167 0.392057 15.4958 0.625391 15.3375C0.858724 15.1792 1.11706 15.1333 1.40039 15.2L5.20039 16L6.80039 7.9L5.00039 8.6V11C5.00039 11.2833 4.90456 11.5208 4.71289 11.7125C4.52122 11.9042 4.28372 12 4.00039 12C3.71706 12 3.47956 11.9042 3.28789 11.7125C3.09622 11.5208 3.00039 11.2833 3.00039 11V7.95C3.00039 7.75 3.05456 7.57083 3.16289 7.4125C3.27122 7.25417 3.41706 7.13333 3.60039 7.05L6.95039 5.6C7.53372 5.35 7.96289 5.1875 8.23789 5.1125C8.51289 5.0375 8.76706 5 9.00039 5C9.35039 5 9.67539 5.09167 9.97539 5.275C10.2754 5.45833 10.5171 5.7 10.7004 6L11.7004 7.6C12.0504 8.16667 12.5046 8.65833 13.0629 9.075C13.6212 9.49167 14.2671 9.76667 15.0004 9.9C15.2837 9.95 15.5212 10.075 15.7129 10.275C15.9046 10.475 16.0004 10.7167 16.0004 11C16.0004 11.2833 15.9046 11.5167 15.7129 11.7C15.5212 11.8833 15.2921 11.9583 15.0254 11.925C14.1254 11.7917 13.2837 11.5125 12.5004 11.0875C11.7171 10.6625 11.0504 10.1333 10.5004 9.5L9.90039 12.5L11.7004 14.2C11.8004 14.3 11.8754 14.4125 11.9254 14.5375C11.9754 14.6625 12.0004 14.7917 12.0004 14.925V21C12.0004 21.2833 11.9046 21.5208 11.7129 21.7125C11.5212 21.9042 11.2837 22 11.0004 22C10.7171 22 10.4796 21.9042 10.2879 21.7125C10.0962 21.5208 10.0004 21.2833 10.0004 21ZM10.5004 4.5C9.95039 4.5 9.47956 4.30417 9.08789 3.9125C8.69622 3.52083 8.50039 3.05 8.50039 2.5C8.50039 1.95 8.69622 1.47917 9.08789 1.0875C9.47956 0.695833 9.95039 0.5 10.5004 0.5C11.0504 0.5 11.5212 0.695833 11.9129 1.0875C12.3046 1.47917 12.5004 1.95 12.5004 2.5C12.5004 3.05 12.3046 3.52083 11.9129 3.9125C11.5212 4.30417 11.0504 4.5 10.5004 4.5Z"
      />
    </svg>
  );
};

export default LifestyleIconMob;
