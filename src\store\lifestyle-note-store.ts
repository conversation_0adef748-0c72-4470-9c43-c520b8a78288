import { create } from 'zustand';

import {
  createLifestyleNote,
  getLifestyleNotes,
  updateLifestyleNotes,
} from '@/query/lifestyle';

import { ModeType, noteModes } from '@/utils/constants/consultation';

export interface LifestyleNoteRecords {
  id?: string;
  patientId?: string;
  author?: string;
  created_on?: string;
  updated_on?: string;
  note?: string;
  tags?: string[];
  type?: string;
}

interface LifestyleNoteStoreState {
  records: LifestyleNoteRecords[];
  loading: boolean;
  error: string | null;
  mode: ModeType;
  selectedRecord: LifestyleNoteRecords | null;

  getLifestyleNotes: (_patientId: string) => Promise<void>;
  createLifestyleNote: (
    _data: Omit<LifestyleNoteRecords, 'id' | 'created_on'>
  ) => Promise<void>;
  updateLifestyleNotes: (
    _id: string,
    _data: Partial<LifestyleNoteRecords>
  ) => Promise<void>;
  setMode: (_mode: ModeType) => void;
  setSelectedRecord: (_record: LifestyleNoteRecords | null) => void;
  resetRecord: () => void;
}

export const useLifestyleNoteStore = create<LifestyleNoteStoreState>((set) => ({
  records: [],
  loading: false,
  error: null,
  mode: noteModes.NONE,
  selectedRecord: null,

  getLifestyleNotes: async (patientId) => {
    try {
      const response = await getLifestyleNotes(patientId);
      set({ records: response?.data ?? [] });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  createLifestyleNote: async (data) => {
    set({ loading: true, error: null });
    try {
      const response = await createLifestyleNote(data);
      set((state) => ({
        loading: false,
        records: [...state?.records, response?.data],
      }));
    } catch (error: any) {
      set({ error: error.message, loading: false });
    }
  },

  updateLifestyleNotes: async (id, data) => {
    set({ loading: true, error: null });

    try {
      const response = await updateLifestyleNotes(id, data);

      set((state) => {
        const updatedRecords = state.records.map((record) =>
          record.id === response.data.id ? response.data : record
        );

        return {
          loading: false,
          records: updatedRecords,
        };
      });
    } catch (error: any) {
      set({ error: error.message, loading: false });
    }
  },

  setMode: (mode) => set({ mode }),
  setSelectedRecord: (record) => set({ selectedRecord: record }),
  resetRecord: () => set({ records: [] }),
}));
