import { TabPanelProps } from './types';

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      className="w-full h-full"
      {...other}
    >
      {value === index && <>{children}</>}
    </div>
  );
}

export default TabPanel;
