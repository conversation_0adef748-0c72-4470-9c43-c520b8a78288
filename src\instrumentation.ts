import { AzureMonitorTraceExporter } from '@azure/monitor-opentelemetry-exporter';
import { registerOTel } from '@vercel/otel';

export async function register() {
  const serviceName = process.env.OTEL_SERVICE_NAME || 'arca-emr-app';
  const exporters = [];

  // Check if Azure App Insights is configured
  if (process.env.APPLICATIONINSIGHTS_CONNECTION_STRING) {
    exporters.push(
      new AzureMonitorTraceExporter({
        connectionString: process.env.APPLICATIONINSIGHTS_CONNECTION_STRING,
      })
    );
  }

  // For Grafana testing, we can use console exporter or OTLP
  // Note: For production Grafana, you'd configure OTLP exporter here

  registerOTel({
    serviceName,
    traceExporter: exporters.length > 0 ? exporters[0] : undefined, // Use first exporter if available
  });
}
