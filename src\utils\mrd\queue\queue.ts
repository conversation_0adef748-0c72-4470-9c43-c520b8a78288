import { useDoctorStore } from '@/store/mrd/queue/doctor';

import { GetPatientQueueRes, updateQueueItemById } from '@/query/patient';
import { reorderQueue as reorderQueueAPI } from '@/query/patient';

import {
  AppointmentStatus,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';

export type ReorderQueueParams = {
  doctorId: string;
  appointment: GetPatientQueueRes;
};

export async function reorderQueue({
  doctorId,
  appointment,
}: ReorderQueueParams) {
  const doctorStore = useDoctorStore.getState();

  const reordered = {
    ...appointment,
    queues: appointment.queues.map((item, index) => {
      return {
        ...item,
        queuePosition: index + 1,
      };
    }),
  };

  doctorStore.replaceAppointments({
    doctorId,
    appointment: reordered,
  });

  const reorderAPIParams = {
    appointments: reordered.queues.map((item) => {
      return {
        queueId: item.queueId,
        time: item.time,
        status: item.status,
        patientStatus: item.patientStatus,
        queuePosition: item.queuePosition,
      };
    }),
  };

  await reorderQueueAPI(reorderAPIParams);

  return reordered;
}

export type ChangeQueueItemStatusParams = {
  doctorId: string;
  queueId: string;
  status: AppointmentStatus;
  patientStatus: PatientStatus;
};

export function getQueueItem(params: { doctorId: string; queueId: string }): {
  appointmentId: string;
  item: GetPatientQueueRes['queues'][number];
} | null {
  const store = useDoctorStore.getState();
  const appointments = store.appointmentsByDoctor[params.doctorId];

  if (!appointments) {
    return null;
  }

  for (const appointment of appointments) {
    const found = appointment.queues.find(
      (item) => item.queueId === params.queueId
    );

    if (found) {
      return {
        appointmentId: appointment.appointmentId,
        item: found,
      };
    }
  }

  return null;
}

export async function changeQueueItemStatus(
  params: ChangeQueueItemStatusParams
) {
  const doctorStore = useDoctorStore.getState();

  const res = getQueueItem({
    doctorId: params.doctorId,
    queueId: params.queueId,
  });

  if (!res) {
    return;
  }

  doctorStore.updateQueueItem({
    doctorId: params.doctorId,
    appointmentId: res.appointmentId,
    queueItem: {
      ...res.item,
      patientStatus: params.patientStatus,
      status: params.status,
    },
  });

  await updateQueueItemById({
    queueId: params.queueId,
    payload: {
      time: res.item.time,
      status: params.status,
      patientStatus: params.patientStatus,
      queuePosition: res.item.queuePosition,
    },
  });
}
