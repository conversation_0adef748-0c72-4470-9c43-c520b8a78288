import { api } from '@/core/lib/interceptor';

export const getPhysicalActivityDashboard = async (
  patientId: string,
  customStartDate: string,
  customEndDate: string,
  token: string
) => {
  const { data } = await api.get(
    '/lifestyle/v0.1/physical-activity/dashboard',
    {
      params: {
        patientId,
        dateFilter: 'custom',
        customStartDate,
        customEndDate,
      },
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return data;
};
