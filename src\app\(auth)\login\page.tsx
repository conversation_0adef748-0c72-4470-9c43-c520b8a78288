'use client';

import { useCallback, useEffect, useState } from 'react';

import { useIsAuthenticated } from '@azure/msal-react';
import { toast } from 'sonner';

import { useRouter } from 'next/navigation';

import { login, getActiveAccount } from '@core/lib/auth/services';

import Loading from '@/lib/common/loading';

import { getDoctorProfile } from '@/query/emr/doctor-profile/personal-info';

import { LOCAL_STORAGE_KEYS } from '@/constants/local-storage';
import { PERMISSION_KEYS } from '@/constants/permission-keys';
import { routes } from '@/constants/routes';

import { AuthLayout } from '@/core/layout/auth/AuthLayout';

const LoginPage = () => {
  const isAuthenticated = useIsAuthenticated();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const fetchUserPermissions = useCallback(async (): Promise<string[]> => {
    try {
      // First, ensure we have an active account
      const account = getActiveAccount();
      if (!account) {
        throw new Error('No active account found');
      }

      // Fetch doctor profile which includes permissions
      const response = await getDoctorProfile();

      // Initialize permissions array
      let permissions: string[] = [];

      // Handle the response format from getDoctorProfile
      if (response?.data && response.data.length > 0) {
        const userData = response.data[0];
        // Check for permissions in both permissionKeys and permissions properties
        if (userData.permissionKeys) {
          permissions = Array.isArray(userData.permissionKeys)
            ? userData.permissionKeys
            : [userData.permissionKeys];
        } else if (userData.permissions) {
          permissions = Array.isArray(userData.permissions)
            ? userData.permissions
            : [userData.permissions];
        }
      }

      // Update state with the permissions

      return permissions;
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      toast.error('Failed to load user profile and permissions');
      return [];
    }
  }, []);

  const handleUserPermissions = useCallback(
    async (permissions: string[]) => {
      try {
        // Check for redirectTo in localStorage and redirect if present
        const redirectTo =
          typeof window !== 'undefined'
            ? localStorage.getItem(LOCAL_STORAGE_KEYS.REDIRECT_TO)
            : null;
        if (redirectTo) {
          localStorage.removeItem(LOCAL_STORAGE_KEYS.REDIRECT_TO);
          await router.replace(redirectTo);
          return;
        }

        const hasMRDAccess = permissions.includes(PERMISSION_KEYS.MRD_ACCESS);
        const hasEMRAccess = permissions.includes(PERMISSION_KEYS.EMR_ACCESS);

        // If user has both permissions, redirect to app selection
        if (hasMRDAccess && hasEMRAccess) {
          await router.replace(routes.SELECT_APP);
          return;
        }

        // If user has only MRD access
        if (hasMRDAccess) {
          await router.replace(routes.MRD_DASHBOARD);
          return;
        }

        // If user has only EMR access
        if (hasEMRAccess) {
          await router.replace(routes.EMR_DASHBOARD);
          return;
        }

        // If we get here, no valid permissions were found
        // Only redirect to unauthorized if getDoctorProfile succeeded (permissions array is empty)
        if (Array.isArray(permissions) && permissions.length === 0) {
          await router.replace(routes.UNAUTHORIZED);
        }
        // If getDoctorProfile failed, axios interceptor will handle redirect to login
        return;
      } catch (error) {
        console.error('Error handling user permissions:', error);
        toast.error('Error processing your permissions');
        router.replace(routes.UNAUTHORIZED);
      }
    },
    [router]
  );

  const handleLogin = useCallback(async () => {
    setIsLoading(true);
    try {
      await login();
      // After successful login, fetch user permissions
      const permissions = await fetchUserPermissions();
      await handleUserPermissions(permissions);
    } catch (error) {
      console.error('Login failed:', error);
      toast.error('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [fetchUserPermissions, handleUserPermissions, login]);

  useEffect(() => {
    if (isAuthenticated) {
      const initializeAuth = async () => {
        const permissions = await fetchUserPermissions();
        handleUserPermissions(permissions);
      };
      initializeAuth();
    }
  }, [isAuthenticated, fetchUserPermissions, handleUserPermissions]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loading />
      </div>
    );
  }

  // Redirect based on permissions if authenticated
  if (isAuthenticated) {
    // This will be handled by the useEffect hook
    return (
      <div className="flex items-center justify-center h-screen">
        <Loading />
      </div>
    );
  }

  // Show login button by default
  return (
    <AuthLayout title="Welcome to ArcaAI" subtitle="Sign in to continue">
      <button
        onClick={handleLogin}
        className="w-full flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loading />
            <span className="ml-2">Signing in...</span>
          </>
        ) : (
          'Sign in'
        )}
      </button>
    </AuthLayout>
  );
};

export default LoginPage;
