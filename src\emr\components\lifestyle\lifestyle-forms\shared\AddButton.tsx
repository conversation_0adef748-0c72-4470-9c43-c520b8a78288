import React, { FC, memo } from 'react';

import { IconButton, IconButtonProps } from '@mui/material';
import { styled } from '@mui/material/styles';
import { MdOutlineAdd } from 'react-icons/md';

const StyledIconButton = styled(IconButton)({
  backgroundColor: 'black',
  color: 'white',
  borderRadius: '50%',
  '&.MuiIconButton-sizeSmall': {
    width: 30,
    height: 30,
  },
  '&.MuiIconButton-sizeMedium': {
    width: 40,
    height: 40,
  },
  '&.MuiIconButton-sizeLarge': {
    width: 50,
    height: 50,
  },
  '&:hover': {
    backgroundColor: '#333',
  },
});

const AddButton: FC<IconButtonProps> = ({
  className,
  size = 'small',
  ...props
}) => {
  return (
    <StyledIconButton size={size} {...props} className={className}>
      <MdOutlineAdd className="text-2xl" />
    </StyledIconButton>
  );
};

export default memo(AddButton);
