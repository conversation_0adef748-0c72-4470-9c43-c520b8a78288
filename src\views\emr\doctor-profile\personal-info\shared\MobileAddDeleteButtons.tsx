import React, { FC, memo } from 'react';

import { Grid2, Grid2Props, IconButton, IconButtonProps } from '@mui/material';

import colors from '@/utils/colors';

import { FluentDelete32Filled } from '@/assets/svg/FluentDelete32Filled';

import AddButton from '@/emr/components/lifestyle/lifestyle-forms/shared/AddButton';

type Props = Grid2Props & {
  onAdd?: IconButtonProps['onClick'];
  onDelete?: IconButtonProps['onClick'];
};

const MobileAddDeleteButtons: FC<Props> = ({ onAdd, onDelete, ...rest }) => {
  return (
    <Grid2 container spacing={1} justifyContent="flex-end" p={1} {...rest}>
      {onDelete && (
        <IconButton size="small" onClick={onDelete}>
          <FluentDelete32Filled
            width={20}
            height={20}
            fontSize={18}
            color={colors.common.red}
          />
        </IconButton>
      )}
      {onAdd && <AddButton onClick={onAdd} />}
    </Grid2>
  );
};

export default memo(MobileAddDeleteButtons);
