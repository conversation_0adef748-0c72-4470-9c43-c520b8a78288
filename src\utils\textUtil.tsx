import { regex } from './constants/regex';

export const renderItalicText = (text: string) => {
  const parts = text.split(regex.BRACKET_TEXT_REGEX);

  return parts.map((part, index) => {
    return regex.BRACKET_TEXT_REGEX.test(part) ? (
      <span key={index} style={{ fontStyle: 'italic' }}>
        {part}
      </span>
    ) : (
      part
    );
  });
};

export const normalizeString = (str: string) =>
  str
    .toLowerCase()
    .replace(/\s*\(.*?\)/g, '')
    .replace(/\s+(\w)/g, (_, c) => c.toUpperCase())
    .replace(/[^a-zA-Z0-9]/g, '');

export const isHtmlContentEmpty = (htmlString?: string): boolean => {
  if (!htmlString) return true;

  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlString;

  return tempDiv.innerText.trim() === '';
};
