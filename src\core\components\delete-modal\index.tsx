import { FC, memo, ReactNode } from 'react';

import {
  Box,
  ButtonProps,
  Grid2,
  Modal,
  ModalOwnProps,
  Typography,
} from '@mui/material';

import AppButton from '../app-button';

export type DeleteModalProps = Omit<ModalOwnProps, 'children'> & {
  onClose: () => void;
  onDelete: () => void;
  isLoading?: boolean;
  confirmationMessage?: ReactNode;
  bodyContent?: ReactNode;
  deleteButtonText?: string;
  cancelButtonText?: string;
  deleteButtonProps?: ButtonProps;
  cancelButtonProps?: ButtonProps;
  classes?: {
    container?: string;
    title?: string;
    body?: string;
    actions?: string;
  };
};

const DeleteModal: FC<DeleteModalProps> = ({
  onClose,
  onDelete,
  confirmationMessage = 'Are you sure you want to delete this record? This action cannot be undone.',
  bodyContent = null,
  deleteButtonText = 'Delete',
  cancelButtonText = 'Cancel',
  deleteButtonProps = {},
  cancelButtonProps = {},
  isLoading = false,
  classes = {},
  ...props
}: DeleteModalProps) => {
  return (
    <Modal onClose={onClose} {...props}>
      <Box
        className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg p-4 w-70 flex flex-col gap-base ${classes.container || ''}`}
      >
        <Typography className={classes.title}>{confirmationMessage}</Typography>
        <Box className={classes.body}>{bodyContent}</Box>
        <Grid2
          container
          justifyContent={'center'}
          gap={2}
          className={classes.actions}
        >
          <AppButton
            variant={'outlined'}
            disableRipple={isLoading}
            onClick={() => {
              if (!isLoading) {
                onClose();
              }
            }}
            sx={{ minWidth: 120 }}
            {...cancelButtonProps}
          >
            {cancelButtonText}
          </AppButton>
          <AppButton
            variant={'outlined'}
            onClick={onDelete}
            loading={isLoading}
            color="error"
            sx={{ minWidth: 120 }}
            {...deleteButtonProps}
          >
            {deleteButtonText}
          </AppButton>
        </Grid2>
      </Box>
    </Modal>
  );
};

export default memo(DeleteModal);
