import { memo } from 'react';

import { usePrescriptionPackageStore } from '@/store/emr/prescription/package';

import DeleteModal from '@/core/components/delete-modal';

const DeleteConfirmationModal = () => {
  const {
    showDeleteConfirmationModal,
    setShowDeleteConfirmationModal,
    deletePackage,
    isDeleting,
    selectedPackage,
  } = usePrescriptionPackageStore();

  const handleDelete = () => {
    deletePackage();
  };

  const handleCancel = () => {
    setShowDeleteConfirmationModal(false);
  };

  return (
    <DeleteModal
      open={showDeleteConfirmationModal}
      onClose={handleCancel}
      onDelete={handleDelete}
      isLoading={isDeleting}
      confirmationMessage={`Do you want to delete ${selectedPackage?.name}?`}
    />
  );
};

export default memo(DeleteConfirmationModal);
