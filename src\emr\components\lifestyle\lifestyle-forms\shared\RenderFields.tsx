import React, { FC, useCallback } from 'react';

import { Control, Path } from 'react-hook-form';

import { Box } from '@mui/material';

import Image from 'next/image';

import useIsMobile from '@/hooks/use-mobile-layout';

import { handleNumericInput } from '@/utils/validation';

import FluentFoodGrains20Filled from '@/assets/svg/FluentFoodGrains20Filled';

import {
  lifestyleQuestionTypes,
  lifestyleSectionTypes,
} from '@/constants/lifestyle';

import { LifestyleQuestion, Questions, Sections } from '@/emr/types/lifestyle';

import ControlledInput from './ControlledInput';
import ControlledSwitch from './ControlledSwitch';
import ControlledTextarea from './ControlledTextarea';
import FieldArrayText from './FieldArrayText';
import FieldArrayTimeRange from './FieldArrayTimeRange';
import FieldSections from './FieldSections';
import RenderCheckbox from './RenderCheckbox';
import RenderRadio from './RenderRadio';
import TitleWithIcon from './TitleWithIcon';

import LifestyleTable from './table/LifestyleTable';

type Props = {
  control: Control<any>;
  isReadOnly?: boolean;
  isMaximized?: boolean;
  icon?: string;
  sectionTitle?: string;
  questions: Questions[];
  type?: Sections['type'];
  name: Path<LifestyleQuestion>;
};

const { SECTION, TABLE, TEXT_AREA_SECTION } = lifestyleSectionTypes;
const {
  CHECKBOX,
  FIELD_ARRAY_TEXT,
  FIELD_ARRAY_TIME_RANGE,
  GROUP,
  RADIO,
  TEXT,
  TEXT_AREA,
  NUMBER,
  SWITCH,
} = lifestyleQuestionTypes;

const RenderFields: FC<Props> = ({
  control,
  isReadOnly = false,
  isMaximized = false,
  questions,
  icon,
  sectionTitle,
  type,
  name,
}) => {
  const isMobile = useIsMobile();

  const renderSectionTitle = useCallback(() => {
    if (!sectionTitle) return null;

    if (icon && sectionTitle) {
      return (
        <TitleWithIcon
          title={sectionTitle}
          icon={<Image src={icon} width={10} height={10} alt={sectionTitle} />}
        />
      );
    }
    return (
      <TitleWithIcon title={sectionTitle} icon={<FluentFoodGrains20Filled />} />
    );
  }, [sectionTitle, icon]);

  const renderInputFields = useCallback(
    (
      question: Questions,
      appendName: string,
      additionProps: object = {}
    ): JSX.Element | null => {
      if (question?.type === NUMBER) {
        return (
          <ControlledInput
            label={question.unit}
            control={control}
            name={`${appendName}.value`}
            onKeyDown={handleNumericInput}
            disabled={isReadOnly}
            variant={question.inputVariant}
            key={appendName}
            placeholder="0000"
            {...additionProps}
          />
        );
      } else if (question?.type === TEXT) {
        return (
          <ControlledInput
            label={question.unit}
            control={control}
            name={`${appendName}.value`}
            disabled={isReadOnly}
            variant={question.inputVariant}
            key={appendName}
            placeholder="0000"
            {...additionProps}
          />
        );
      } else if (question?.type === TEXT_AREA) {
        return (
          <ControlledTextarea
            label={question.question}
            control={control}
            name={`${appendName}.value`}
            disabled={isReadOnly}
            variant={question.inputVariant}
            key={appendName}
            rows={6}
            {...additionProps}
          />
        );
      } else if (question?.type === SWITCH) {
        return (
          <ControlledSwitch
            control={control}
            name={`${appendName}.value`}
            label={question?.options ?? question.question}
            key={appendName}
            disabled={isReadOnly}
          />
        );
      } else if (question?.type === RADIO) {
        return (
          <RenderRadio
            name={`${appendName}` as Path<LifestyleQuestion>}
            isReadOnly={isReadOnly}
            question={question}
            key={appendName}
          />
        );
      } else if (question?.type === CHECKBOX) {
        return (
          <RenderCheckbox
            name={`${appendName}` as Path<LifestyleQuestion>}
            isReadOnly={isReadOnly}
            question={question}
            key={appendName}
          />
        );
      } else if (question?.type === FIELD_ARRAY_TEXT) {
        return (
          <FieldArrayText
            control={control}
            name={`${appendName}.value`}
            label={question.question}
            isReadOnly={isReadOnly}
            key={appendName}
          />
        );
      } else if (question?.type === FIELD_ARRAY_TIME_RANGE) {
        return (
          <FieldArrayTimeRange
            control={control}
            name={`${appendName}.value`}
            label={question.question}
            isReadOnly={isReadOnly}
            key={appendName}
          />
        );
      } else if (question?.type === GROUP) {
        return (
          <div
            className="flex gap-2 w-full flex-wrap md:flex-nowrap"
            key={appendName}
          >
            {question?.sub_questions?.map((q, i) => {
              return renderInputFields(
                q as Questions,
                `${appendName}.sub_questions.${i}`,
                additionProps
              );
            })}
          </div>
        );
      } else {
        return null;
      }
    },
    [control, isReadOnly]
  );

  const renderMainSections = useCallback(() => {
    if (type === SECTION) {
      return (
        <FieldSections
          variant={isMobile ? 'small' : 'normal'}
          fields={
            questions?.map((q, i) => ({
              label: q.question,
              content: (
                <>
                  {renderInputFields(q, `${name}.${i}`, {
                    className: 'max-w-20',
                  })}
                </>
              ),
            })) || []
          }
        />
      );
    }
    if (type === TEXT_AREA_SECTION) {
      return (
        <Box className="flex flex-col md:flex-row gap-2">
          {questions?.map((q, i) => renderInputFields(q, `${name}.${i}`))}
        </Box>
      );
    }
    if (type === TABLE) {
      return (
        <LifestyleTable
          questions={questions}
          appendName={name}
          isReadOnly={isReadOnly}
          isMaximized={isMaximized}
        />
      );
    }
    return (
      <Box>
        {questions?.map((q, i) => renderInputFields(q, `${name}.${i}`))}
      </Box>
    );
  }, [
    questions,
    isMobile,
    renderInputFields,
    isReadOnly,
    isMaximized,
    name,
    type,
  ]);

  return (
    <Box>
      {renderSectionTitle()}
      {renderMainSections()}
    </Box>
  );
};

export default RenderFields;
