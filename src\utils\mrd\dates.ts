import { format, isDate } from 'date-fns';

import { DateFormats } from '@/utils/dateUtils/dateFormats';

/**
 * Format date according to the app standard
 */
export function formatDate(
  date: Date | string,
  withTime: boolean = false
): string {
  const dateInput = isDate(date) ? date : new Date(date);

  if (withTime) {
    return format(+dateInput, DateFormats.FULL_DATE_WITH_TIME);
  }

  return format(+dateInput, DateFormats.YYYY_MM_DD);
}
