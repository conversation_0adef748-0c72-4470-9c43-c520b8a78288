type Contact = {
  phone: string;
  email?: string;
};

type Insurance = {
  provider?: string;
  id?: string;
  url?: string;
};

export type Address =
  | {
      houseName?: string;
      pin?: string;
      street?: string;
      city: string;
      state?: string;
      country?: string;
      post?: string;
      district?: string;
    }
  | string;

export type Proof = {
  type?: string;
  url?: string;
  aadharNumber?: string;
  abhaNumber?: string;
};

export type Patient = {
  id?: string;
  name: string;
  sex: string;
  dob: Date | string;
  height?: number | string;
  weight?: number | string;
  address: Address;
  contact: Contact;
  insurance: Insurance;
  maritalStatus: string;
  last_consultation_date?: string;
  guardian?: string;
  proof: Proof;
  abha?: string;
  aadhar?: string;
  age?: string;
};

export type SearchPatient = {
  id: string;
  name: string;
};
