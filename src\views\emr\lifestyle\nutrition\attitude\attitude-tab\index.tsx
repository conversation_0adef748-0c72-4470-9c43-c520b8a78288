import React, { memo, useEffect, useMemo, useState, useCallback } from 'react';

import { useLifestyleFilterStore } from '@/store/emr/lifestyle/filter-store';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { nutritionAttitudeStore } from '@/store/emr/lifestyle/nutrition/attitude/attitude-store';

import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import Accordion from '@/views/emr/lifestyle/shared/Accordion';

import TimeLine from '@/emr/components/consultation/TimeLine';
import FinalizeModal from '@/emr/components/lifestyle/lifestyle-forms/shared/FinalizeModal';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AttitudeEditModal from './AttitudeEditModal';
import AttitudeTimelineForm from './AttitudeTimelineForm';

const AttitudeTab = () => {
  const { setSource } = lifestyleStore();
  const { getPatientData, patientData, loading, finalizeRecord, finalizing } =
    nutritionAttitudeStore();
  const { fromDate, toDate } = useLifestyleFilterStore();

  const [openedAccordion, setOpenedAccordion] = useState<number | null>(null);
  const [recordToFinalize, setRecordToFinalize] =
    useState<QuestionnaireResponse | null>(null);
  const [editRecord, setEditRecord] = useState<QuestionnaireResponse | null>(
    null
  );

  useEffect(() => {
    setSource(LifestyleSources.NUTRITION_ATTITUDE);
  }, [setSource]);

  useEffect(() => {
    getPatientData(fromDate, toDate);
  }, [getPatientData, fromDate, toDate]);

  const handleFinalize = useCallback(
    async (id: string) => {
      await finalizeRecord(id);
      setRecordToFinalize(null);

      if (editRecord?.id === id) {
        setEditRecord({
          ...editRecord,
          status: LifestyleRecordStatus.FINALIZED,
        });
      }
    },
    [finalizeRecord, editRecord]
  );

  const timelineItems = useMemo(() => {
    return patientData?.map((item, index) => ({
      id: item.id || index.toString(),
      date: item.created_on || '',
      content: (
        <Accordion
          key={item.id || index}
          open={openedAccordion === index}
          onToggle={() =>
            setOpenedAccordion(openedAccordion === index ? null : index)
          }
          doctorName={item.doctor?.name}
          finalised={item.status === LifestyleRecordStatus.FINALIZED}
          onExpand={() => setEditRecord(item)}
          onFinalise={() => setRecordToFinalize(item)}
          stepper={['Attitude']}
          designation={item.doctor?.designation}
          department={item.doctor?.department}
          date={item.created_on}
          isAttitude={true}
        >
          <AttitudeTimelineForm data={item} />
        </Accordion>
      ),
    }));
  }, [patientData, openedAccordion]);

  return (
    <div className="h-full flex flex-col p-1 overflow-y-auto">
      <TimeLine items={timelineItems} loading={loading} />

      <FinalizeModal
        open={!!recordToFinalize}
        onClose={() => setRecordToFinalize(null)}
        onFinalize={() =>
          recordToFinalize?.id && handleFinalize(recordToFinalize.id)
        }
        loading={finalizing}
      />
      <AttitudeEditModal
        open={!!editRecord}
        onClose={() => setEditRecord(null)}
        formFields={editRecord}
        onFinalize={(record) => {
          if (record?.id) {
            handleFinalize(record.id);
          }
        }}
      />
    </div>
  );
};

export default memo(AttitudeTab);
