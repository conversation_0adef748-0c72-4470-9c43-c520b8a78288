.lifestyle-sidebar {
  .card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .tab-button {
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .notes-section {
    .note-item {
      transition: all 0.2s ease-in-out;

      &:hover {
        background-color: #f8f9fa;
      }
    }
  }

  .ambient-button {
    transition: all 0.3s ease-in-out;

    &.recording {
      animation: pulse 2s infinite;
    }
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
  }
}
