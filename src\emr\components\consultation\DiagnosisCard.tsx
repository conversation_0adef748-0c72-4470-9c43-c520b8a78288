import React from 'react';

import { Box, IconButton } from '@mui/material';

import EditableText from '@/lib/common/editable_text';

import PenIcon from '@/assets/svg/PenIcon';

import ActivityStatusSelector from './ActivityStatusSelector';
import { GroupedRecords, StyledTypography } from './Common';
import DiagnosisStatusSelector from './DiagnosisStatusSelector';

const formatDate = (timestamp: string) => {
  return new Date(timestamp).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

interface DiagnosisCardProps {
  diagnosisRecords: GroupedRecords;
  onExpand?: () => void;
  disableExpand?: boolean;
}

const DiagnosisCard: React.FC<DiagnosisCardProps> = ({
  diagnosisRecords,
  onExpand,
  disableExpand = false,
}) => {
  const isHtmlContentEmpty = (html: string) => {
    if (!html) return true;
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return !doc.body.textContent?.trim();
  };

  return (
    <div className="rounded-base border bg-white flex flex-col m-3 px-4 py-2 min-h-[100px] max-h-[150px]">
      <div className="flex items-center justify-between pb-1">
        <span className="font-medium text-lg -tracking-[2.2%] text-[#001926]">
          Diagnosis
        </span>
        <IconButton
          onClick={onExpand}
          size="small"
          disabled={disableExpand}
          sx={{
            '&.Mui-disabled': {
              color: 'gray',
              opacity: 0.5,
            },
            '&:hover': {
              backgroundColor: 'transparent',
            },
          }}
        >
          <PenIcon className="w-4 h-4 text-gray-100" />
        </IconButton>
      </div>
      <Box className="overflow-y-auto flex-1">
        {Object.keys(diagnosisRecords).length > 0 ? (
          [...Object.keys(diagnosisRecords)]
            .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
            .flatMap((date) => {
              const validRecords = diagnosisRecords[date]?.filter(
                (record) => !isHtmlContentEmpty(record.content)
              );

              if (!validRecords || validRecords.length === 0) return [];

              return validRecords.map((record, index) => (
                <React.Fragment key={`${date}-${record.record_id}-${index}`}>
                  <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-2">
                      <DiagnosisStatusSelector
                        value={record.diagnosisStatus || 'provisional'}
                        onChange={() => {}}
                        disabled={false}
                        className="h-6 text-sm min-w-[70px]"
                        isSavedRecord={true}
                      />
                      <ActivityStatusSelector
                        value={record.activityStatus || 'active'}
                        onChange={() => {}}
                        disabled={false}
                        className="h-6 text-sm min-w-[55px]"
                      />
                      <div className="ml-2">
                        <EditableText
                          defaultValue={record.content}
                          editable={false}
                          emptyPlaceholder=""
                        />
                      </div>
                    </div>

                    <div className="text-right mr-4">
                      <StyledTypography variant="caption">
                        {index === 0 ? formatDate(date) : ''}
                      </StyledTypography>
                    </div>
                  </div>
                </React.Fragment>
              ));
            })
        ) : (
          <div className="text-gray-400 text-center py-4">
            No diagnosis found
          </div>
        )}
      </Box>
    </div>
  );
};

export default DiagnosisCard;
