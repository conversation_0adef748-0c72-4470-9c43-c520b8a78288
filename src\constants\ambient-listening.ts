export const recordingStates = {
  IDLE: 'idle',
  RECORDING: 'recording',
  PAUSED: 'paused',
  LOADING: 'loading',
} as const;

export type RecordingState =
  (typeof recordingStates)[keyof typeof recordingStates];

export const RecordingLanguage = {
  English: 'en-IN',
  Tamil: 'ta-IN',
  Telugu: 'te-IN',
  Hindi: 'hi-IN',
  Malayalam: 'ml-IN',
  Kannada: 'kn-IN',
  Bengali: 'bn-IN',
} as const;

export const languageOptions = Object.entries(RecordingLanguage).map(
  ([key, value]) => ({
    label: key,
    value,
  })
);

export const waves = [
  {
    timeModifier: 4,
    lineWidth: 1,
    amplitude: -25,
    wavelength: 25,
  },
  {
    timeModifier: 2,
    lineWidth: 1,
    amplitude: -10,
    wavelength: 30,
  },
  {
    timeModifier: 1,
    lineWidth: 1,
    amplitude: -30,
    wavelength: 30,
  },
  {
    timeModifier: 3,
    lineWidth: 1,
    amplitude: 40,
    wavelength: 40,
  },
  {
    timeModifier: 0.5,
    lineWidth: 1,
    amplitude: -60,
    wavelength: 60,
  },
  {
    timeModifier: 1.3,
    lineWidth: 1,
    amplitude: -40,
    wavelength: 40,
  },
];

export const currentModal = {
  INITIAL: '',
  RECORD_CONSULTATION: 'record-consultation',
  LOADING: 'loading',
  SHOW_SUMMARY: 'show-summary',
  LANGUAGE_SELECTION: 'language-selection',
} as const;

export type CurrentModal = (typeof currentModal)[keyof typeof currentModal];
