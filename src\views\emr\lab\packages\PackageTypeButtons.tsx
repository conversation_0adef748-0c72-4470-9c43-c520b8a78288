import { PackageType, packageTypes } from '@/types/emr/lab';

type PackageTypeButtonsProps = {
  activePackageType: PackageType;
  onSelectType: (type: PackageType) => void;
  disabled?: boolean;
};

const BUTTON_CLASSES = {
  active: 'bg-black text-white',
  inactive: 'bg-white text-black',
};

export const PackageTypeButtons: React.FC<PackageTypeButtonsProps> = ({
  activePackageType,
  onSelectType,
  disabled,
}) => (
  <div className="flex gap-1 w-full justify-between">
    <button
      onClick={() => onSelectType(packageTypes.DEPARTMENT)}
      disabled={disabled}
      className={`py-1 w-1/2 min-w-30 rounded-md text-sm border font-medium transition-colors duration-200 disabled:opacity-50 ${
        activePackageType === packageTypes.DEPARTMENT
          ? BUTTON_CLASSES.active
          : BUTTON_CLASSES.inactive
      }`}
    >
      Dept. Packages
    </button>

    <button
      onClick={() => onSelectType(packageTypes.USER)}
      disabled={disabled}
      className={`py-1 w-1/2 min-w-30 rounded-md text-sm border font-medium transition-colors duration-200 disabled:opacity-50 ${
        activePackageType === packageTypes.USER
          ? BUTTON_CLASSES.active
          : BUTTON_CLASSES.inactive
      }`}
    >
      User Packages
    </button>
  </div>
);
