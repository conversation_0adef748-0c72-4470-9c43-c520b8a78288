import React, { useEffect } from 'react';

import { Box, Modal } from '@mui/material';

import Loading from '@/lib/common/loading';

import {
  usePackageSelectorStore,
  PackageData,
} from '@/store/emr/lab/package-store';
import { useTestStore } from '@/store/emr/lab/reports-store';

import {
  getPackageTableHeaders,
  MAX_ITEMS_PER_COLUMN,
  modalStyle,
} from '@/constants/emr/lab';

import { modalModes, PackageType } from '@/types/emr/lab';
import { testReportTabs } from '@/types/emr/lab';

import { EmptyView, NotFound } from './Components';
import { CreatePackageForm } from './CreatePackageForm';
import { DetailPackageView } from './DetailPackageView';
import { PackageList } from './PackageList';
import { PackageModalFooter } from './PackageModalFooter';
import { PackageModalHeader } from './PackageModalHeader';
import { PackageTypeButtons } from './PackageTypeButtons';
import {
  getTestRowsForCreateMode,
  getTestRowsForDetailMode,
} from './TestTableRows';

const { DETAIL, CREATE, ADD } = modalModes;
const { TEST_RESULTS } = testReportTabs;

const PackageSelector: React.FC<{ disabled?: boolean }> = ({ disabled }) => {
  const {
    labDepartments,
    fetchLabDepartments,
    selectedSearchTests,
    activeTab,
  } = useTestStore();

  const {
    isModalOpen,
    modalMode,
    packages,
    activePackageType,
    selectedPackage,
    testItems,
    openModal,
    closeModal,
    setSelectedPackage,
    removeTest,
    toggleTestSelection,
    addMultipleTests,
    getPackagesByType,
    fetchPackages,
    isLoading,
  } = usePackageSelectorStore();

  const filteredPackages = getPackagesByType();

  useEffect(() => {
    if (labDepartments.length === 0) {
      fetchLabDepartments();
    }
  }, [fetchLabDepartments, labDepartments]);

  useEffect(() => {
    if (isModalOpen && activePackageType) {
      fetchPackages();
    }
  }, [isModalOpen, activePackageType, fetchPackages]);

  useEffect(() => {
    if (
      isModalOpen &&
      (modalMode === CREATE || modalMode === ADD) &&
      selectedSearchTests &&
      selectedSearchTests.length > 0
    ) {
      const packageTests = selectedSearchTests
        .filter(
          (test) =>
            test.id !== undefined &&
            typeof test.name === 'string' &&
            test.pageKey === activePackageType
        )
        .map((test) => ({
          id: test.id as string | number,
          name: test.name as string,
        }));

      if (packageTests.length > 0) {
        addMultipleTests(packageTests);
      }
    }
  }, [
    selectedSearchTests,
    modalMode,
    isModalOpen,
    addMultipleTests,
    activePackageType,
  ]);

  const handlePackageTypeSelect = (type: PackageType) => {
    openModal(type);
  };

  const handlePackageSelect = (pkg: PackageData) => {
    setSelectedPackage(pkg);
  };

  const handleCloseModal = () => {
    closeModal();
  };

  const testRows = React.useMemo(() => {
    if (!testItems?.length) return [];

    const useTwoColumns = testItems.length > MAX_ITEMS_PER_COLUMN;

    if (modalMode === DETAIL) {
      return getTestRowsForDetailMode(
        testItems,
        useTwoColumns,
        toggleTestSelection
      );
    } else {
      return getTestRowsForCreateMode(testItems, useTwoColumns, removeTest);
    }
  }, [testItems, modalMode, removeTest, toggleTestSelection]);

  const tableHeaders = React.useMemo(() => {
    return getPackageTableHeaders(modalMode, testItems.length);
  }, [modalMode, testItems.length]);

  return (
    <>
      <PackageTypeButtons
        activePackageType={activePackageType}
        onSelectType={handlePackageTypeSelect}
        disabled={disabled}
      />
      <Modal open={isModalOpen} onClose={handleCloseModal} closeAfterTransition>
        <Box sx={modalStyle}>
          <PackageModalHeader
            activePackageType={activePackageType}
            modalMode={modalMode}
            selectedPackage={selectedPackage}
            onClose={handleCloseModal}
          />
          {isLoading ? (
            <div className="flex flex-grow min-h-0 items-center justify-center">
              <Loading />
            </div>
          ) : filteredPackages.length === 0 && modalMode === modalModes.VIEW ? (
            <NotFound />
          ) : (
            <div className="flex flex-grow min-h-0">
              <PackageList
                packages={filteredPackages}
                selectedPackage={selectedPackage}
                onSelectPackage={handlePackageSelect}
              />

              <div className="w-3/4 flex flex-col overflow-auto">
                {modalMode === CREATE || modalMode === ADD ? (
                  <CreatePackageForm
                    tableHeaders={tableHeaders}
                    testRows={testRows}
                  />
                ) : modalMode === DETAIL ? (
                  <DetailPackageView
                    tableHeaders={tableHeaders}
                    testRows={testRows}
                  />
                ) : (
                  <EmptyView packages={packages} />
                )}
              </div>
            </div>
          )}

          <PackageModalFooter />
        </Box>
      </Modal>
    </>
  );
};

export default PackageSelector;
