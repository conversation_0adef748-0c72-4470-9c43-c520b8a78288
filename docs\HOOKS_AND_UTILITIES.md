# Hooks and Utilities Documentation - ARCA EMR

## Overview

This document covers the custom hooks and utility functions that provide reusable functionality across the ARCA EMR application.

## Custom Hooks (`src/hooks/`)

### usePermission

**Purpose**: Manages user permissions and department access control.

**Location**: `src/hooks/use-permission.ts`

**Features**:

- Permission checking based on user roles
- Department-based access control
- Integration with doctor profile data

**Usage**:

```tsx
import usePermission from '@/hooks/use-permission';

const { hasPermission, hasDepartment } = usePermission();

// Check if user has specific permissions
const canEditPatient = hasPermission(['EDIT_PATIENT']);

// Check if user belongs to specific department
const isCardiologist = hasDepartment(['CARDIOLOGY']);
```

### useBroadcastChannel

**Purpose**: Cross-tab communication for real-time updates.

**Location**: `src/hooks/use-broadcast-channel.ts`

**Features**:

- Real-time data synchronization across browser tabs
- Queue updates
- Patient status changes
- Session management

**Usage**:

```tsx
import useBroadcastChannel from '@/hooks/use-broadcast-channel';

const { sendMessage, isListening } = useBroadcastChannel('patient-updates');

// Send update to other tabs
sendMessage({ type: 'PATIENT_UPDATED', patientId: '123' });
```

### useFileUpload

**Purpose**: File upload functionality with progress tracking and validation.

**Location**: `src/hooks/use-file-upload.ts`

**Features**:

- Multiple file upload support
- Progress tracking
- File type validation
- Error handling

**Usage**:

```tsx
import useFileUpload from '@/hooks/use-file-upload';

const { uploadFiles, progress, isUploading, error } = useFileUpload();

const handleUpload = async (files: File[]) => {
  const results = await uploadFiles(files, {
    allowedTypes: ['image/*', 'application/pdf'],
    maxSize: 10 * 1024 * 1024, // 10MB
  });
};
```

### useMobileLayout

**Purpose**: Responsive layout management and mobile detection.

**Location**: `src/hooks/use-mobile-layout.ts`

**Features**:

- Mobile device detection
- Responsive breakpoint management
- Layout adaptation

**Usage**:

```tsx
import useMobileLayout from '@/hooks/use-mobile-layout';

const { isMobile, isTablet, breakpoint } = useMobileLayout();

return (
  <div className={isMobile ? 'mobile-layout' : 'desktop-layout'}>{content}</div>
);
```

### usePDF

**Purpose**: PDF generation and printing functionality for medical documents.

**Location**: `src/hooks/use-pdf.ts`

**Features**:

- Medical report generation
- Prescription printing
- Patient record exports

### useModal

**Purpose**: Modal state management and control.

**Location**: `src/hooks/useModal.ts`

**Features**:

- Modal open/close state
- Multiple modal management
- Keyboard handling

**Usage**:

```tsx
import useModal from '@/hooks/useModal';

const { isOpen, openModal, closeModal } = useModal();

return (
  <>
    <button onClick={openModal}>Open Patient Details</button>
    <Modal isOpen={isOpen} onClose={closeModal}>
      {/* Modal content */}
    </Modal>
  </>
);
```

### useSearchParams

**Purpose**: URL search parameter management with type safety.

**Location**: `src/hooks/use-search-params.ts`

**Features**:

- Type-safe parameter handling
- Automatic serialization/deserialization
- History management

## Utility Functions

### Common Utilities (`src/utils/common.ts`)

**Mathematical Utilities**:

```typescript
// Ensure minimum value
const getMin = (value?: number, min: number = 1) => Math.max(value || min, min);

// Ensure maximum value
const getMax = (value?: number, max: number = 1) => Math.min(value || max, max);

// Clamp value between min and max
const getMinMax = (value?: number, min: number = 1, max: number = 1) =>
  getMin(getMax(value, max), min);

// Get value or default
const getOrDefault = <T>(
  value: T | undefined | null,
  defaultValue: T = '' as T
): T =>
  value === undefined || value === null || value === '' ? defaultValue : value;
```

### Date Utilities (`src/helpers/dates.ts`)

**Age Calculation**:

```typescript
import { calculateAge } from '@/helpers/dates';

const patientAge = calculateAge('1990-05-15'); // Returns age in years
```

**Date Formatting**:

- Medical date formats
- Relative time calculations
- Timezone handling

### Validation Utilities (`src/utils/validation.ts`)

**Medical Data Validation**:

- Patient ID validation
- Medical code validation
- Phone number formatting
- Address validation

**Usage**:

```typescript
import { validatePatientId, formatPhoneNumber } from '@/utils/validation';

const isValidId = validatePatientId(patientId);
const formattedPhone = formatPhoneNumber(rawPhoneNumber);
```

### Color Utilities (`src/utils/colors.ts`)

**Theme Color Management**:

```typescript
import colors from '@/utils/colors';

const primaryColor = colors.primary.main;
const errorColor = colors.error.light;
```

### String Utilities (`src/utils/string.ts`)

**Text Processing**:

- Text truncation
- Case conversion
- Search highlighting
- Medical term formatting

### Format Utilities (`src/utils/format-value.ts`)

**Data Formatting**:

- Currency formatting
- Number formatting
- Medical unit conversion
- Percentage calculations

## Medical-Specific Utilities

### Vitals Utilities (`src/helpers/vitals.ts`)

**Vital Signs Processing**:

- Blood pressure validation
- Temperature conversion
- BMI calculation
- Normal range checking

**Usage**:

```typescript
import { calculateBMI, isNormalBP } from '@/helpers/vitals';

const bmi = calculateBMI(weight, height);
const isHealthyBP = isNormalBP(systolic, diastolic);
```

### Medical Code Utilities (`src/utils/medical-codes.ts`)

**ICD/CPT Code Management**:

- Code validation
- Code lookup
- Category mapping
- Search functionality

### Prescription Utilities (`src/utils/prescription.ts`)

**Prescription Processing**:

- Dosage calculations
- Drug interaction checking
- Prescription formatting
- Refill management

## Error Handling Utilities

### Error Message Utilities (`src/utils/error-message.ts`)

**User-Friendly Error Messages**:

```typescript
import { getErrorMessage } from '@/utils/error-message';

const userMessage = getErrorMessage(apiError, 'Failed to save patient data');
```

**Features**:

- API error translation
- Validation error formatting
- Fallback message handling

## Session Management

### Session Utilities (`src/utils/session.ts`)

**Session Handling**:

- Session timeout management
- Idle detection
- Auto-logout functionality
- Session persistence

### Broadcast Channel Utilities (`src/utils/broadcast-channel.ts`)

**Cross-Tab Communication**:

- Message broadcasting
- Event synchronization
- State synchronization

## File Handling Utilities

### File Type Checking (`src/utils/fileTypeChecks.ts`)

**File Validation**:

```typescript
import {
  isImageFile,
  isPDFFile,
  getFileExtension,
} from '@/utils/fileTypeChecks';

const isValidImage = isImageFile(file);
const isPDF = isPDFFile(file);
const extension = getFileExtension(filename);
```

### Text Processing (`src/utils/textUtil.tsx`)

**Text Manipulation**:

- Rich text processing
- Medical text formatting
- Search highlighting
- Content sanitization

## Performance Utilities

### Debounce and Throttle

**Search Optimization**:

```typescript
import { debounce } from 'lodash.debounce';

const debouncedSearch = debounce(searchFunction, 300);
```

### Memoization Helpers

**Expensive Calculation Caching**:

- Medical calculation caching
- Search result caching
- Component memoization

## Usage Guidelines

### Best Practices

1. **Import Consistency**: Use absolute imports with `@/` prefix
2. **Type Safety**: All utilities include TypeScript types
3. **Error Handling**: Include proper error handling in utilities
4. **Performance**: Use memoization for expensive operations
5. **Testing**: Include unit tests for complex utility functions

### Common Patterns

**Hook Composition**:

```tsx
const usePatientManagement = () => {
  const { hasPermission } = usePermission();
  const { uploadFiles } = useFileUpload();
  const { isMobile } = useMobileLayout();

  return {
    canEditPatient: hasPermission(['EDIT_PATIENT']),
    uploadPatientDocuments: uploadFiles,
    isMobileView: isMobile,
  };
};
```

**Utility Chaining**:

```typescript
const processPatientData = (rawData: any) => {
  return pipe(
    validatePatientData,
    formatPatientFields,
    calculateDerivedFields
  )(rawData);
};
```

## Integration Examples

### Form Integration

```tsx
const PatientForm = () => {
  const { hasPermission } = usePermission();
  const { uploadFiles } = useFileUpload();

  const canEdit = hasPermission(['EDIT_PATIENT']);

  return (
    <form>
      <AppTextField
        disabled={!canEdit}
        value={patientName}
        onChange={setPatientName}
      />
      <FileUpload onUpload={uploadFiles} />
    </form>
  );
};
```

### Data Processing

```tsx
const PatientList = () => {
  const { isMobile } = useMobileLayout();
  const processedPatients = patients.map((patient) => ({
    ...patient,
    age: calculateAge(patient.dob),
    formattedPhone: formatPhoneNumber(patient.phone),
    displayName: isMobile ? truncateText(patient.name, 20) : patient.name,
  }));

  return <Table data={processedPatients} />;
};
```

## Related Documentation

- [Core Components](./CORE_COMPONENTS.md) - Component integration
- [Core Architecture](./CORE_ARCHITECTURE.md) - System architecture
- [Code Format Rules](./CODE_FORMAT_RULES.md) - Coding standards
