import React, { memo, useEffect, useMemo } from 'react';

import { Box } from '@mui/material';
import dayjs from 'dayjs';

import Loading from '@/lib/common/loading';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { usePrescriptionStore } from '@/store/emr/prescription';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { PrescriptionHistory } from '@/types/emr/prescription';

import HistoryCard from './HistoryCard';

const { DATE_YYYY_MM_DD, DATE_DD_MM_YYYY_SLASH } = DateFormats;

const getDate = (date: string) => {
  const dayjsDate = dayjs(date);

  if (dayjsDate.isSame(dayjs(), 'day')) {
    return 'Today';
  }
  return dayjsDate.format(DATE_DD_MM_YYYY_SLASH);
};

const getGroupedHistory = (history: PrescriptionHistory[]) =>
  history.reduce<Record<string, PrescriptionHistory[]>>((acc, curr) => {
    const date = getDate(curr.updated_on);

    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(curr);
    return acc;
  }, {});

const HistoryList = () => {
  const { onExpand } = usePrescriptionStore();
  const {
    getPrescriptionHistory,
    prescriptionHistory,
    isLoading,
    customEndDate,
    customStartDate,
    historyFilter,
    historySearchQuery,
  } = usePrescriptionStore();
  const { patient } = useCurrentPatientStore();

  const history = useMemo(
    () => getGroupedHistory(prescriptionHistory),
    [prescriptionHistory]
  );

  const params = useMemo(() => {
    return {
      patientId: patient?.id || '',
      dateFilter: historyFilter,
      customEndDate: customEndDate
        ? formatDate(customEndDate, DATE_YYYY_MM_DD)
        : undefined,
      customStartDate: customStartDate
        ? formatDate(customStartDate, DATE_YYYY_MM_DD)
        : undefined,
      searchText: historySearchQuery,
    };
  }, [
    patient?.id,
    historyFilter,
    customEndDate,
    customStartDate,
    historySearchQuery,
  ]);

  useEffect(() => {
    patient?.id && getPrescriptionHistory(params);
  }, [getPrescriptionHistory, patient?.id, params]);

  return (
    <Box className="flex flex-col gap-3 h-fit">
      {isLoading ? (
        <div className="flex h-full w-full justify-center items-center min-h-[50vh]">
          <Loading />
        </div>
      ) : (
        <>
          {Object.keys(history).length > 0 ? (
            Object.keys(history).map((date) => (
              <Box key={date} className="flex flex-col gap-2">
                <Box className="flex flex-col gap-2">
                  {history[date].map((item) => (
                    <HistoryCard
                      key={item.id}
                      {...item}
                      onExpand={() => onExpand(item)}
                    />
                  ))}
                </Box>
              </Box>
            ))
          ) : (
            <div className="flex h-full w-full justify-center items-center min-h-[50vh] text-gray-500">
              No prescription history found
            </div>
          )}
        </>
      )}
    </Box>
  );
};

export default memo(HistoryList);
