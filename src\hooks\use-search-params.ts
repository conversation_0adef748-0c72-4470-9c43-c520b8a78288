import { useRouter, useSearchParams } from 'next/navigation';

interface UpdateSearchParams {
  (newParams: { [key: string]: string | number }): void;
}

const useUpdateSearchParams = (): UpdateSearchParams => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const updateSearchParams: UpdateSearchParams = (newParams) => {
    const params = new URLSearchParams(searchParams);
    Object.keys(newParams).forEach((key) => {
      params.set(key, encodeURIComponent(String(newParams[key])));
    });

    router.push(`?${params.toString()}`, { scroll: false });
  };

  return updateSearchParams;
};

export default useUpdateSearchParams;
