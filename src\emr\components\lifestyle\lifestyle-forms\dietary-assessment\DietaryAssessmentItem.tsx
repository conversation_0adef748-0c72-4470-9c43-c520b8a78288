import { memo } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import colors from '@/utils/colors';

import { LifestyleQuestion } from '@/emr/types/lifestyle';

import PatientDetails from '../shared/PatientDetails';
import RenderFields from '../shared/RenderFields';

type Props = {
  data: LifestyleQuestion;
};

const DietaryAssessmentItem: React.FC<Props> = ({ data }) => {
  const methods = useForm({ defaultValues: data });

  const { control } = methods;

  return (
    <FormProvider {...methods}>
      <div className="flex flex-col gap-3 p-1 md:p-2 w-full md:w-fit xl:w-full bg-gray-200">
        <PatientDetails cardBgColor={colors.common.lightGray} />
        <div className="flex flex-col gap-2">
          {data.sections?.map((q, i) => (
            <RenderFields
              key={`${q.section_title}-${i}`}
              questions={q?.questions || []}
              icon={q?.icon}
              sectionTitle={q?.section_title}
              type={q?.type}
              control={control}
              isReadOnly
              name={`sections.${i}.questions`}
            />
          ))}
        </div>
      </div>
    </FormProvider>
  );
};

export default memo(DietaryAssessmentItem);
