import React, { FC, memo } from 'react';

import { Modal } from '@mui/material';

type Props = {
  open: boolean;
  children: React.ReactNode;
  onClose?: () => void;
};

const FormModal: FC<Props> = ({ open, children, onClose }) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      className="flex items-center justify-center"
    >
      <div className="w-[60vw] bg-white p-2 rounded-lg min-h-[70vh] max-h-[90vh] shadow-custom-xs flex flex-col">
        {children}
      </div>
    </Modal>
  );
};

export default memo(FormModal);
