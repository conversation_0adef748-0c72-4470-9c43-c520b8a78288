import { BaseOption, Replace } from '@/types';
import { Contact } from '@/types/mrd/manage-patient/patient-details';

export type Demographics = {
  id: string;
  name: string;
  cmchId: string;
  dob: string;
  age: string;
  sex: string;
  maritalStatus: string;
  contacts: Contact[];
};

export type DemographicsForm = Replace<
  Demographics,
  {
    id?: string;
    maritalStatus: BaseOption | null;
  }
>;

export const defaultValues: DemographicsForm = {
  name: '',
  cmchId: '',
  dob: '',
  age: '',
  sex: 'male',
  maritalStatus: null,
  contacts: [
    {
      phone: '',
      email: '',
    },
  ],
};
