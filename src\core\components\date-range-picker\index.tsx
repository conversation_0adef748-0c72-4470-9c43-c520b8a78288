import React, {
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import {
  DateRange as ReactDateRange,
  Range,
  RangeKeyDict,
} from 'react-date-range';

import { ClickAwayListener, IconButton } from '@mui/material';
import dayjs from 'dayjs';
import { FaRegCalendarAlt } from 'react-icons/fa';

import { formatDate } from '@/utils/dateUtils/dayUtils';

import TextInput from '../text-input';
import Tooltip from '../tooltip';

import './styles.scss';
import { DateRangePickerProps } from './types';

import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';

const DateRangePicker = forwardRef<HTMLInputElement, DateRangePickerProps>(
  (props, ref) => {
    const {
      inputProps,
      renderInput,
      disabled,
      inputWrapperClassName = 'min-w-[200px]',
      endAdornment,
      onClickEndAdornment,
      endAdornmentProps,
      value,
      onChange,
      format,
      separator = '-',
      calendarIcon,
      setIsCalendarOpen,
      renderFooter,
      isCalendarOpen,
      maxDate,
      ..._rest
    } = props;
    const {
      onClick,
      disabled: inputDisabled = disabled,
      ...inputPropsRest
    } = inputProps || {};

    const [isOpen, setIsOpen] = useState(false);

    const inputValue = useMemo<string>(() => {
      if (value?.from && value?.to) {
        return `${formatDate(dayjs(value.from), format)} ${separator} ${formatDate(dayjs(value.to), format)}`;
      }
      if (value?.from) {
        return `${formatDate(dayjs(value.from), format)} ${separator} `;
      }
      if (value?.to) {
        return ` ${separator} ${formatDate(dayjs(value.to), format)}`;
      }
      return '';
    }, [format, separator, value?.from, value?.to]);

    const dateRange = useMemo<Range[] | undefined>(() => {
      return [
        {
          startDate: dayjs(value?.from).toDate() ?? null,
          endDate: dayjs(value?.to).toDate() ?? null,
          key: 'selection',
        },
      ];
    }, [value]);

    const onSelect = useCallback(
      (selected: RangeKeyDict) => {
        if (onChange) {
          if (!selected?.selection) {
            onChange({ from: null, to: null });
          } else {
            onChange({
              from: selected?.selection.startDate,
              to: selected?.selection.endDate,
            });
          }
        }
      },
      [onChange]
    );

    const onInputClick = useCallback(
      (e: React.MouseEvent<HTMLInputElement, MouseEvent>) => {
        setIsOpen((prev) => !prev);
        onClick && onClick(e);
      },
      [onClick]
    );

    const handleClickEndAdornment = useCallback(
      (e: React.MouseEvent) => {
        if (!disabled && !endAdornment) {
          setIsOpen((prev) => !prev);
        }
        onClickEndAdornment && onClickEndAdornment(e);
      },
      [onClickEndAdornment, disabled, endAdornment]
    );

    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          setIsOpen(false);
        }
      };

      document.addEventListener('keydown', handleKeyDown);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }, []);

    useEffect(() => {
      setIsCalendarOpen?.(isOpen);
    }, [isOpen, setIsCalendarOpen]);

    useEffect(() => {
      if (isCalendarOpen !== undefined && isCalendarOpen !== isOpen) {
        setIsOpen(isCalendarOpen);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isCalendarOpen]);

    const renderPopover = useCallback(() => {
      return (
        <ClickAwayListener onClickAway={() => setIsOpen(false)}>
          <div className="w-full">
            <ReactDateRange
              ranges={dateRange}
              onChange={onSelect}
              moveRangeOnFirstSelection={false}
              maxDate={maxDate}
            />
            {renderFooter && renderFooter()}
          </div>
        </ClickAwayListener>
      );
    }, [maxDate, dateRange, onSelect, renderFooter]);

    const renderEndAdornment = useCallback(() => {
      if (endAdornment) {
        return (
          <IconButton
            size="small"
            onClick={handleClickEndAdornment}
            {...endAdornmentProps}
          >
            {endAdornment}
          </IconButton>
        );
      } else if (!disabled) {
        return (
          <IconButton
            size="small"
            onClick={handleClickEndAdornment}
            {...endAdornmentProps}
          >
            {calendarIcon || <FaRegCalendarAlt />}
          </IconButton>
        );
      } else {
        return null;
      }
    }, [
      disabled,
      endAdornment,
      endAdornmentProps,
      handleClickEndAdornment,
      calendarIcon,
    ]);

    const renderInputField = useCallback(() => {
      if (renderInput) {
        return renderInput({
          onClick: onInputClick,
          disabled: inputDisabled,
          endDecoration: renderEndAdornment(),
          value: inputValue,
          ...inputPropsRest,
        });
      }
      return (
        <TextInput
          ref={ref}
          onClick={onInputClick}
          readOnly
          disabled={inputDisabled}
          endDecoration={renderEndAdornment()}
          iconClassName="!right-1 bg-white"
          value={inputValue}
          {...inputPropsRest}
        />
      );
    }, [
      inputDisabled,
      inputPropsRest,
      inputValue,
      onInputClick,
      renderEndAdornment,
      renderInput,
      ref,
    ]);

    return (
      <Tooltip
        open={isOpen}
        title={renderPopover()}
        arrow={false}
        hidden={disabled}
        wrapperClassName={`w-full ${inputWrapperClassName}`}
      >
        {renderInputField()}
      </Tooltip>
    );
  }
);
DateRangePicker.displayName = 'DateRangePicker';
export default DateRangePicker;
