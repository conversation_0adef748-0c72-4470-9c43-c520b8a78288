import React from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { Box } from '@mui/material';

import colors from '@/utils/colors';

import { LifestyleQuestion } from '@/emr/types/lifestyle';

import PatientDetails from '../shared/PatientDetails';
import RenderFields from '../shared/RenderFields';

type Props = {
  data: LifestyleQuestion;
  isMaximized?: boolean;
};

const AwarenessAdaptationItem: React.FC<Props> = ({ data, isMaximized }) => {
  const methods = useForm<LifestyleQuestion>({
    defaultValues: data ?? {},
  });

  const { control } = methods;

  return (
    <FormProvider {...methods}>
      <Box sx={{ margin: 'auto', px: 1, py: 1, gap: 3 }}>
        <PatientDetails cardBgColor={colors.common.lightGray} />
        <div className="flex flex-col gap-2">
          {data?.sections?.map((q, i) => (
            <RenderFields
              key={q.section_title}
              control={control}
              isReadOnly
              isMaximized={isMaximized}
              questions={q.questions}
              icon={q.icon}
              sectionTitle={q.section_title}
              type={q.type}
              name={`sections.${i}.questions`}
            />
          ))}
        </div>
      </Box>
    </FormProvider>
  );
};

export default AwarenessAdaptationItem;
