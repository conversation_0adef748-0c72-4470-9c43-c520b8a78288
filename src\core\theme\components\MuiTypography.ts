import { ComponentSettings, OverrideComponent } from '@core/theme/types';

const MuiTypography = ({
  typography,
}: ComponentSettings): OverrideComponent['MuiTypography'] => ({
  styleOverrides: {
    root: {
      letterSpacing: '-0.288px',
    },
    h4: {
      fontSize: 18,
      letterSpacing: '-0.396px',
      fontWeight: typography.h1?.fontWeight,
    },
    h5: {
      fontSize: 18,
      letterSpacing: '-0.288px',
      fontWeight: 600,
    },
    subtitle1: {
      fontSize: 14,
      letterSpacing: '-0.288px',
      fontWeight: 700,
    },
    subtitle2: {
      fontSize: 14,
      letterSpacing: '-0.288px',
      fontWeight: 500,
    },
    body2: {
      fontSize: 16,
      fontWeight: 400,
    },
    paragraph: {
      fontSize: 12,
      fontWeight: 400,
    },
  },
});

export default MuiTypography;
