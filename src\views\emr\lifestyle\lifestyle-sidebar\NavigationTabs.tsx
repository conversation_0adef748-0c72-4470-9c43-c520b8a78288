import { PiNoteDuotone } from 'react-icons/pi';

import { usePathname } from 'next/navigation';

import BrainIcon from '@/assets/svg/BrainIcon';
import FluentFoodApple20Regular from '@/assets/svg/FluentFoodApple20Regular';
import MeditationIcon from '@/assets/svg/MeditationIcon';
import RunningIcon from '@/assets/svg/RunningIcon';
import SleepIcon from '@/assets/svg/SleepIcon';

import { routes } from '@/constants/routes';

import TabItem from './TabItem';

const lifestyleCategories = [
  {
    label: 'General',
    path: routes.EMR_LIFESTYLE_GENERAL,
    icon: <PiNoteDuotone />,
    disabled: false,
  },
  {
    label: 'Nutrition',
    path: routes.EMR_LIFESTYLE_NUTRITION,
    icon: <FluentFoodApple20Regular />,
    disabled: false,
  },
  {
    label: 'Physical Activity',
    path: routes.EMR_LIFESTYLE_PHYSICAL_ACTIVITY,
    icon: <RunningIcon />,
    disabled: false,
  },
  {
    label: 'Mental Health',
    path: routes.EMR_LIFESTYLE_MENTAL_HEALTH,
    icon: <BrainIcon />,
    disabled: true, // Temporarily disabled
  },
  {
    label: 'Sleep Activity',
    path: routes.EMR_LIFESTYLE_SLEEP_ACTIVITY,
    icon: <SleepIcon />,
    disabled: true, // Temporarily disabled
  },
  {
    label: 'Spiritual Activity',
    path: routes.EMR_LIFESTYLE_SPIRITUAL_ACTIVITY,
    icon: <MeditationIcon />,
    disabled: true, // Temporarily disabled
  },
];

export default function NavigationTabs() {
  const path = usePathname();

  return (
    <div className="p-2">
      <div className="flex flex-col gap-1">
        {lifestyleCategories.map((category) => (
          <TabItem
            key={category.label}
            category={category}
            isActive={path.startsWith(category.path)}
          />
        ))}
      </div>
    </div>
  );
}
