import React, { useCallback, useEffect, useState } from 'react';

import { Modal, Box, Typography, Divider } from '@mui/material';
import { AiFillFilePdf } from 'react-icons/ai';
import { BiExpandAlt } from 'react-icons/bi';
import { FaArrowLeft, FaArrowRight } from 'react-icons/fa';
import { IoCloseSharp } from 'react-icons/io5';
import { toast } from 'sonner';

import Loading from '@/lib/common/loading';
import { cn } from '@/lib/utils';

import usePdf from '@/hooks/use-pdf';

import { previewTestResult } from '@/query/emr/lab';

import OutLinedIconButton, {
  OutLinedIconButtonProps,
} from '@/emr/components/lifestyle/lifestyle-forms/shared/OutlinedIconButton';

import AppButton from '@/core/components/app-button';
import { FileMetaData } from '@/types/emr/lab';

type Props = {
  disabled?: boolean;
  fileMetaData: FileMetaData[];
  iconButtonProps?: OutLinedIconButtonProps;
};

const ExpandModalButton: React.FC<Props> = ({
  disabled,
  fileMetaData = [],
  iconButtonProps = {},
}) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [fileData, setFileData] = useState<Blob | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { previewPdf } = usePdf();

  const currentFile =
    fileMetaData && fileMetaData.length > 0 ? fileMetaData[currentIndex] : null;

  const handleOpen = () => {
    setOpen(true);
    setCurrentIndex(0);
  };

  const handleClose = () => {
    setOpen(false);
    setImagePreview(null);
    setFileData(null);
    setLoading(false);
  };

  const handlePrev = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : prev));
    setImagePreview(null);
    setFileData(null);
  };

  const handleNext = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    setCurrentIndex((prev) =>
      prev < fileMetaData.length - 1 ? prev + 1 : prev
    );
    setImagePreview(null);
    setFileData(null);
  };

  const openPdfPreview = useCallback(
    (base64Data: string) => {
      if (!base64Data) return;
      try {
        previewPdf(base64Data);
      } catch (error) {
        console.error('Error opening PDF:', error);
        toast.error('Failed to open PDF preview');
      }
    },
    [previewPdf]
  );

  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
    });
  };

  const handlePreview = useCallback(async () => {
    if (!currentFile || !open) return;

    setLoading(true);
    try {
      const blob = await previewTestResult(currentFile.id);
      setFileData(blob);

      if (blob instanceof Blob) {
        const base64Data = await blobToBase64(blob);

        const fileType = currentFile.fileType.toLowerCase();

        if (fileType.includes('pdf') || fileType === 'application/pdf') {
          setFileData(blob);
        } else if (fileType.startsWith('image/')) {
          setImagePreview(base64Data);
        }
      } else {
        console.error('Expected blob response but got:', typeof blob);
        toast.error('Invalid response format');
      }
    } catch (error) {
      console.error('Error previewing test result:', error);
      toast.error('Failed to load preview');
    } finally {
      setLoading(false);
    }
  }, [currentFile, open]);

  useEffect(() => {
    if (open) {
      handlePreview();
    }
  }, [handlePreview, open]);

  const handleOpenPdf = useCallback(async () => {
    if (!fileData || !(fileData instanceof Blob)) return;

    try {
      const base64Data = await blobToBase64(fileData);
      openPdfPreview(base64Data);
    } catch (error) {
      console.error('Error converting PDF for preview:', error);
      toast.error('Failed to open PDF preview');
    }
  }, [fileData, openPdfPreview]);

  const renderFilePreview = useCallback(() => {
    if (loading) return <Loading />;

    if (!currentFile) {
      return (
        <Typography variant="body1" color="text.secondary">
          No file selected
        </Typography>
      );
    }

    if (imagePreview) {
      return (
        <img
          src={imagePreview}
          alt="Test Result"
          className="w-auto h-auto max-w-full max-h-[70vh] object-contain rounded-lg"
          draggable={false}
        />
      );
    }

    const isPdf =
      currentFile?.fileType?.toLowerCase().includes('pdf') ||
      currentFile?.fileType?.toLowerCase() === 'application/pdf';

    if (isPdf && fileData) {
      return (
        <div className="text-center flex flex-col items-center">
          <div className="flex items-center justify-center mb-5 text-red-500">
            <AiFillFilePdf size={64} />
          </div>
          <Typography variant="body1" className="mb-2">
            {currentFile?.fileName}
          </Typography>
          <button
            type="button"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            onClick={handleOpenPdf}
          >
            <AiFillFilePdf className="mr-2" />
            Open PDF
          </button>
        </div>
      );
    }

    return (
      <Typography variant="body1" color="text.secondary">
        {fileData
          ? 'No preview available for this file type'
          : 'No file data available'}
      </Typography>
    );
  }, [fileData, currentFile, imagePreview, loading, handleOpenPdf]);

  return (
    <>
      <OutLinedIconButton
        onClick={handleOpen}
        disabled={disabled || fileMetaData.length === 0}
        {...iconButtonProps}
      >
        <BiExpandAlt />
      </OutLinedIconButton>
      <Modal open={open} onClose={handleClose}>
        <Box
          className={cn(
            'absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]',
            'w-[70%] max-h-[90vh] min-h-[90vh] overflow-auto',
            'flex flex-col'
          )}
        >
          <div className="flex justify-end items-center h-12 p-2">
            <OutLinedIconButton onClick={handleClose}>
              <IoCloseSharp />
            </OutLinedIconButton>
          </div>
          <Divider />
          <div className="bg-white p-4 w-full h-[calc(90vh-7rem)] min-h-[calc(90vh-7rem)] flex-1 rounded-md shadow-md flex justify-center items-center">
            {renderFilePreview()}
          </div>
          {fileMetaData && fileMetaData.length > 1 && (
            <div className="flex items-center gap-2 justify-center p-4 border-t h-12">
              <AppButton
                variant="contained"
                onClick={handlePrev}
                disabled={currentIndex === 0}
                style={{ minWidth: 32 }}
                size="large"
              >
                <FaArrowLeft />
              </AppButton>
              <span className="text-sm text-white select-none bg-primary px-5 py-1.5 rounded-base">
                {currentIndex + 1} / {fileMetaData.length}
              </span>
              <AppButton
                variant="contained"
                onClick={handleNext}
                disabled={currentIndex === fileMetaData.length - 1}
                style={{ minWidth: 32 }}
                size="large"
              >
                <FaArrowRight />
              </AppButton>
            </div>
          )}
        </Box>
      </Modal>
    </>
  );
};

export default ExpandModalButton;
