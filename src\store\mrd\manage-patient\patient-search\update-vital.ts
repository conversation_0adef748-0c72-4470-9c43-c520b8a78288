import { toast } from 'sonner';
import { create } from 'zustand';

import { updateVitals } from '@/query/mrd/manage-patient/search-patient';

import { getErrorMessage } from '@/utils/error-message';

import { Vitals } from '@/types/mrd/manage-patient/vitals';

import { useMrdPatientSearch } from '.';

type UpdateVitalStoreState = {
  vital: Vitals | null;
  open: boolean;
  updating: boolean;
};

type UpdateVitalStoreActions = {
  toggleModal: () => void;
  updateVitals: (vitals: Partial<Vitals>) => void;
};

type UpdateVitalStore = UpdateVitalStoreState & UpdateVitalStoreActions;

const initialState: UpdateVitalStoreState = {
  vital: null,
  open: false,
  updating: false,
};

export const useUpdateVitalStore = create<UpdateVitalStore>((set, get) => ({
  ...initialState,
  toggleModal: () => set((state) => ({ open: !state.open })),
  updateVitals: async (vitals) => {
    const patientId = useMrdPatientSearch.getState().patient?.id;
    if (!patientId) {
      return;
    }
    set({ updating: true });
    try {
      await updateVitals(patientId, vitals);
      toast.success('Vitals updated successfully');
    } catch (error) {
      console.error(error);
      toast.error(getErrorMessage(error, 'Failed to update vitals'));
    } finally {
      useMrdPatientSearch.getState().refreshPatient();
      set({ updating: false });
      get().toggleModal();
    }
  },
}));
