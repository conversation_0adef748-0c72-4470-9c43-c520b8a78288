import { mapObjectToArray } from '@/helpers/utils';

import departments from '@/constants/departments';
import { routes } from '@/constants/routes';

type NavItems = {
  label: string;
  path: string;
  department?: string[];
  permission?: string[];
};

const navItems: NavItems[] = [
  { label: 'Patient Info', path: routes.EMR_PATIENT_INFO },
  { label: 'Consultation', path: routes.EMR_CONSULTATION },
  { label: 'Reports', path: routes.EMR_REPORTS },
  {
    label: 'Life Style',
    path: routes.EMR_LIFE_STYLE,
    department: mapObjectToArray(departments.lifestyle),
  },
  { label: 'Prescription', path: routes.EMR_PRESCRIPTION },
  { label: 'Profile', path: routes.EMR_PROFILE },
  { label: 'Manage Patients', path: routes.MRD_MANAGE_PATIENTS },
  { label: 'Messages', path: routes.MRD_MESSAGES },
  { label: 'Dashboard', path: routes.MRD_DASHBOARD },
];

export default navItems;
