import { Lock } from 'lucide-react';

export default function NoAccessMessage() {
  return (
    <div className="w-full h-full flex items-center justify-center p-6">
      <div className="p-8 max-w-md w-full text-center">
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-50 mb-4">
          <Lock className="h-8 w-8 text-red-600" />
        </div>
        <h3 className="text-xl font-semibold text-gray-800 mb-2">
          Access Denied
        </h3>
        <p className="text-gray-600 mb-6">
          You don&apos;t have permission to view this page.
        </p>
      </div>
    </div>
  );
}
