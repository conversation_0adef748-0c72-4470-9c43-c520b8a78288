import { api } from '@/core/lib/interceptor';
import {
  MedicalHistoryAddiction,
  MedicalHistoryAddictionForm,
} from '@/types/emr/lifestyle/medical-history-addiction';

const baseUrl = '/lifestyle/v0.1/patient/lifestyle/medical-history-addiction';

export const getPatientMedicalHistoryAddiction = async (patientId: string) => {
  const { data } = await api.get<MedicalHistoryAddiction>(baseUrl, {
    params: { patientId },
  });
  return data;
};

export const createPatientMedicalHistoryAddiction = async (
  patientId: string,
  medicalHistory: MedicalHistoryAddictionForm
) => {
  const { data } = await api.post<MedicalHistoryAddiction>(
    baseUrl,
    medicalHistory,
    {
      params: { patientId },
    }
  );
  return data;
};

export const updatePatientMedicalHistoryAddiction = async (
  id: string,
  medicalHistory: MedicalHistoryAddictionForm
) => {
  const { data } = await api.put<MedicalHistoryAddiction>(
    baseUrl,
    medicalHistory,
    {
      params: { id },
    }
  );
  return data;
};
