import { memo } from 'react';

import { BiPlay, BiX } from 'react-icons/bi';

import { useUserStore } from '@/store/userStore';

interface TitleSectionProps {
  handleClickNextPatient: () => void;
  onClosePatient: () => void;
  hasSelectedPatient: boolean;
}

const TitleSection = ({
  handleClickNextPatient,
  onClosePatient,
  hasSelectedPatient,
}: TitleSectionProps) => {
  const { queueInfo } = useUserStore();

  return (
    <div className="flex flex-grow-0 gap-5 h-10">
      <div className="w-[64%] 2xl:w-[66%] flex items-center gap-2 justify-between">
        <span className="text-xl font-medium -tracking-[2.2%]">
          Patient Info
        </span>
        {hasSelectedPatient && (
          <button
            onClick={onClosePatient}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            aria-label="Close patient"
          >
            <BiX size={24} />
          </button>
        )}
      </div>

      <div className="w-[37%] 2xl:w-[33%]  flex justify-between max-h-10 overflow-hidden ">
        <div className="flex justify-between gap-6 items-center flex-grow ">
          <div className="border border-[#DAE1E7] rounded-lg flex-grow flex justify-evenly items-center gap-5 shadow-custom-xs py-1 px-4 max-h-full">
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="font-light -tracking-[2.2%] text-[#637D92] text-[13px] xl:text-base">
                In Queue
              </span>
              <span className="font-medium text-black text-[13px] xl:text-base">
                {queueInfo.inQueue}
              </span>
            </div>

            <div className="border flex-1 bg-black rounded-full h-5 min-w-[1px] max-w-[1px] flex-shrink-0"></div>

            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="font-light -tracking-[2.2%] text-[#637D92] text-[13px] xl:text-base">
                Completed
              </span>
              <span className="font-medium text-black text-[13px] xl:text-base">
                {queueInfo.completed}
              </span>
            </div>
          </div>
          <button
            className="flex items-center  justify-center gap-2 py-1 px-4 sm:px-4 text-white rounded-full transition-colors bg-[#012436] hover:opacity-90 flex-shrink-0 max-h-full"
            onClick={handleClickNextPatient}
          >
            <span className="text-[13px] xl:text-base whitespace-nowrap">
              Next Patient
            </span>
            <BiPlay className="text-lg" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default memo(TitleSection);
