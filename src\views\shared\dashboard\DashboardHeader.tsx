import React from 'react';

import RefreshIcon from '@/assets/svg/dashboard/RefreshIcon';

interface Props {
  greeting: string;
  lastUpdated: string;
  onRefresh: () => Promise<void>;
  isLoading: boolean;
  lastUpdatedMargin?: string;
}

const DashboardHeader: React.FC<Props> = ({
  greeting,
  lastUpdated,
  onRefresh,
  isLoading,
  lastUpdatedMargin = 'mb-3',
}) => {
  return (
    <>
      <div className="flex items-center justify-between mb-2">
        <h1
          className="font-bold"
          style={{
            fontSize: '34px',
            fontWeight: 700,
            color: '#001926',
            lineHeight: '1.2',
          }}
        >
          {greeting}
        </h1>

        <button
          onClick={onRefresh}
          className="flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-lg transition-colors"
          disabled={isLoading}
        >
          <span
            style={{
              fontSize: '14px',
              fontWeight: 400,
              color: '#000000',
            }}
          >
            Refresh
          </span>
          <RefreshIcon className={`${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      <div className={lastUpdatedMargin}>
        <p
          style={{
            fontSize: '18px',
            fontWeight: 400,
            color: '#001926',
            lineHeight: '1.2',
          }}
        >
          Last Updated: {lastUpdated}
        </p>
      </div>
    </>
  );
};

export default DashboardHeader;
