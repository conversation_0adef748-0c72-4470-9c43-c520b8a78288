import React from 'react';

const CalendarIcon = () => {
  return (
    <svg
      width="18"
      height="20"
      viewBox="0 0 18 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        opacity="0.2"
        d="M17.25 3.5V7.25H0.75V3.5C0.75 3.30109 0.829018 3.11032 0.96967 2.96967C1.11032 2.82902 1.30109 2.75 1.5 2.75H16.5C16.6989 2.75 16.8897 2.82902 17.0303 2.96967C17.171 3.11032 17.25 3.30109 17.25 3.5Z"
        fill="#001926"
      />
      <path
        d="M16.5 2H14.25V1.25C14.25 1.05109 14.171 0.860322 14.0303 0.71967C13.8897 0.579018 13.6989 0.5 13.5 0.5C13.3011 0.5 13.1103 0.579018 12.9697 0.71967C12.829 0.860322 12.75 1.05109 12.75 1.25V2H5.25V1.25C5.25 1.05109 5.17098 0.860322 5.03033 0.71967C4.88968 0.579018 4.69891 0.5 4.5 0.5C4.30109 0.5 4.11032 0.579018 3.96967 0.71967C3.82902 0.860322 3.75 1.05109 3.75 1.25V2H1.5C1.10218 2 0.720644 2.15804 0.43934 2.43934C0.158035 2.72064 0 3.10218 0 3.5V18.5C0 18.8978 0.158035 19.2794 0.43934 19.5607C0.720644 19.842 1.10218 20 1.5 20H16.5C16.8978 20 17.2794 19.842 17.5607 19.5607C17.842 19.2794 18 18.8978 18 18.5V3.5C18 3.10218 17.842 2.72064 17.5607 2.43934C17.2794 2.15804 16.8978 2 16.5 2ZM3.75 3.5V4.25C3.75 4.44891 3.82902 4.63968 3.96967 4.78033C4.11032 4.92098 4.30109 5 4.5 5C4.69891 5 4.88968 4.92098 5.03033 4.78033C5.17098 4.63968 5.25 4.44891 5.25 4.25V3.5H12.75V4.25C12.75 4.44891 12.829 4.63968 12.9697 4.78033C13.1103 4.92098 13.3011 5 13.5 5C13.6989 5 13.8897 4.92098 14.0303 4.78033C14.171 4.63968 14.25 4.44891 14.25 4.25V3.5H16.5V6.5H1.5V3.5H3.75ZM16.5 18.5H1.5V8H16.5V18.5ZM7.5 10.25V16.25C7.5 16.4489 7.42098 16.6397 7.28033 16.7803C7.13968 16.921 6.94891 17 6.75 17C6.55109 17 6.36032 16.921 6.21967 16.7803C6.07902 16.6397 6 16.4489 6 16.25V11.4631L5.58563 11.6713C5.4076 11.7603 5.2015 11.7749 5.01268 11.712C4.82385 11.649 4.66776 11.5137 4.57875 11.3356C4.48974 11.1576 4.47509 10.9515 4.53803 10.7627C4.60097 10.5739 4.73635 10.4178 4.91437 10.3287L6.41437 9.57875C6.52876 9.52151 6.65589 9.49448 6.78367 9.50022C6.91145 9.50596 7.03563 9.54429 7.14442 9.61155C7.25322 9.67882 7.343 9.77279 7.40523 9.88454C7.46747 9.99629 7.50009 10.1221 7.5 10.25ZM13.0463 13.1047L11.25 15.5H12.75C12.9489 15.5 13.1397 15.579 13.2803 15.7197C13.421 15.8603 13.5 16.0511 13.5 16.25C13.5 16.4489 13.421 16.6397 13.2803 16.7803C13.1397 16.921 12.9489 17 12.75 17H9.75C9.61072 17 9.47418 16.9612 9.3557 16.888C9.23722 16.8148 9.14147 16.71 9.07918 16.5854C9.01689 16.4608 8.99052 16.3214 9.00303 16.1826C9.01554 16.0439 9.06643 15.9114 9.15 15.8L11.8481 12.2028C11.9095 12.1211 11.9535 12.0277 11.9775 11.9284C12.0015 11.8291 12.0049 11.7259 11.9876 11.6252C11.9703 11.5245 11.9325 11.4284 11.8767 11.3428C11.8209 11.2572 11.7482 11.1839 11.6631 11.1274C11.5779 11.0709 11.4821 11.0324 11.3816 11.0143C11.281 10.9961 11.1778 10.9987 11.0783 11.0219C10.9788 11.0451 10.885 11.0884 10.8028 11.1491C10.7206 11.2098 10.6517 11.2867 10.6003 11.375C10.5525 11.463 10.4876 11.5406 10.4093 11.6031C10.3311 11.6656 10.2411 11.7118 10.1447 11.739C10.0483 11.7661 9.94745 11.7737 9.84807 11.7613C9.74869 11.7489 9.65281 11.7166 9.56609 11.6665C9.47936 11.6165 9.40355 11.5495 9.34312 11.4696C9.28269 11.3898 9.23887 11.2986 9.21424 11.2015C9.18962 11.1044 9.18468 11.0034 9.19973 10.9044C9.21478 10.8054 9.24951 10.7104 9.30188 10.625C9.54962 10.1963 9.93188 9.86124 10.3894 9.67184C10.8469 9.48245 11.3541 9.44926 11.8324 9.57743C12.3107 9.7056 12.7333 9.98797 13.0348 10.3808C13.3363 10.7736 13.4998 11.2548 13.5 11.75C13.5016 12.2391 13.3421 12.7152 13.0463 13.1047Z"
        fill="#001926"
      />
    </svg>
  );
};

export default CalendarIcon;
