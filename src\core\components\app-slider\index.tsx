import React, { useCallback } from 'react';

import { FieldError } from 'react-hook-form';

import { SliderProps, Typography, Box } from '@mui/material';

import { cn } from '@/lib/utils';

import InputLabel from '../input-label';

import { Indicator, MuiSlider } from './styled-components';

export interface AppSliderProps extends Omit<SliderProps, 'onChange'> {
  value?: number | number[];
  onChange?: (value: number | number[]) => void;
  label?: string;
  helperText?: string;
  errors?: FieldError;
  minLabel?: string;
  maxLabel?: string;
  required?: boolean;
  showIndicator?: boolean;
  labelClassName?: string;
  labelVariant?: 'horizontal' | 'vertical';
  customStyles?: any;
}

const AppSlider: React.FC<AppSliderProps> = ({
  value,
  onChange,
  label,
  helperText,
  errors,
  min = 0,
  max = 10,
  step = 1,
  minLabel,
  maxLabel,
  required = false,
  showIndicator = false,
  labelVariant = 'horizontal',
  labelClassName,
  customStyles,
  ...rest
}) => {
  const handleChange = (_: Event, newValue: number | number[]) => {
    if (onChange) {
      onChange(newValue);
    }
  };
  const renderIndicator = useCallback(() => {
    if (!showIndicator) return null;

    const isReadOnly = !!rest.disabled;
    const showIndicatorValue =
      (Array.isArray(value) ? value[0] : (value ?? 0)) ?? 0;

    return (
      <Indicator className="flex flex-col gap-1">
        <div className="flex justify-between items-end w-full relative">
          {Array.from({ length: max + 1 }, (_, i) => {
            const isMarked = showIndicatorValue >= i;
            return (
              <div
                key={i}
                className={`w-0.5 h-5 bg-sky-400 transition-all duration-300 ${
                  isReadOnly || isMarked ? 'opacity-0' : 'opacity-100'
                }`}
                style={{ zIndex: 5 }}
              />
            );
          })}
        </div>

        <div
          className={`flex justify-between w-full ${isReadOnly ? 'mt-0.25' : ''}`}
        >
          {Array.from({ length: max + 1 }, (_, i) => (
            <Typography
              key={i}
              variant="body1"
              fontWeight={400}
              color="textSecondary"
            >
              {i}
            </Typography>
          ))}
        </div>
      </Indicator>
    );
  }, [showIndicator, max, value, rest.disabled]);
  return (
    <Box
      className={cn('w-full space-y-2', customStyles?.wrapperClassName, {
        'flex flex-col': labelVariant === 'vertical',
        'flex flex-row gap-base items-center': labelVariant === 'horizontal',
      })}
    >
      {label && (
        <InputLabel
          label={label}
          required={required}
          className={cn('mb-2', labelClassName)}
        />
      )}
      <div className="w-full space-y-2">
        <Box sx={{ px: 1, width: '100%', position: 'relative', minHeight: 30 }}>
          <MuiSlider
            value={value ?? min}
            onChange={handleChange}
            min={min}
            max={max}
            step={step}
            disabled={rest.disabled}
            valueLabelFormat={(value) => value}
            sx={{
              '& .MuiSlider-track': {
                background: 'linear-gradient(to left, #0288d1, #b3e5fc)', // left→right
                opacity: 1,
              },

              '&.Mui-disabled .MuiSlider-track': {
                background: 'linear-gradient(to left, #b0b0b0, #e0e0e0)', // gray fading
              },

              '& .MuiSlider-rail': {
                backgroundColor: '#E6F6FF',
                opacity: 1,
              },

              '&.Mui-disabled .MuiSlider-rail': {
                backgroundColor: '#f5f5f5',
              },

              '& .MuiSlider-thumb': {
                backgroundColor: '#0288d1',
              },
              '&.Mui-disabled .MuiSlider-thumb': {
                backgroundColor: '#9e9e9e',
              },
              '& .MuiSlider-valueLabel': {
                backgroundColor: 'primary.main',
                '&:before': { display: 'none' },
              },
            }}
            {...rest}
          />

          {renderIndicator()}
        </Box>
        <Box display="flex" justifyContent="space-between" mt={0.5}>
          {minLabel && (
            <Typography variant="caption" color="textSecondary">
              {minLabel}
            </Typography>
          )}
          {maxLabel && (
            <Typography variant="caption" color="textSecondary">
              {maxLabel}
            </Typography>
          )}
        </Box>
        {(helperText || errors?.message) && (
          <Typography
            variant="caption"
            color={errors ? 'error' : 'textSecondary'}
            display="block"
            mt={1}
          >
            {errors?.message || helperText}
          </Typography>
        )}
      </div>
    </Box>
  );
};

export default AppSlider;
