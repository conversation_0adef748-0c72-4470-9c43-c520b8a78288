import { Switch } from '@mui/material';
import { styled } from '@mui/material/styles';

export const CustomSwitch = styled(Switch)(({ theme }) => ({
  width: 34,
  height: 16,
  padding: 0,
  '& .MuiSwitch-switchBase': {
    padding: 2,

    '& + .MuiSwitch-track': {
      backgroundColor: theme.palette.error.dark,
      opacity: 1,
    },

    '&.Mui-checked': {
      transform: 'translateX(18px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        backgroundColor: theme.palette.success.dark,
        opacity: 1,
      },
    },
  },
  '& .MuiSwitch-thumb': {
    width: 12,
    height: 12,
    backgroundColor: '#fff',
  },
  '& .MuiSwitch-track': {
    borderRadius: 16 / 2,
    backgroundColor: '#e5e7eb',
    opacity: 1,
  },
}));
