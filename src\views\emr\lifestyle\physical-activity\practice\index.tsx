'use client';

import { memo } from 'react';

import AppTab from '@/core/components/app-tab';

import ExercisePatternsTab from './exercise-patterns-tab';

const tabs = [
  { label: ' Exercise Patterns', content: <ExercisePatternsTab /> },
];

const PhysicalActivityPracticeView = () => {
  return (
    <div className="h-full flex flex-col p-2">
      <AppTab tabs={tabs} scrollButtons={false} />
    </div>
  );
};

export default memo(PhysicalActivityPracticeView);
