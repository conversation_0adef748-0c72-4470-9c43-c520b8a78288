import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { LOCAL_STORAGE_KEYS } from '@/constants/local-storage';

type NutritionPracticeState = {
  activeTab: number;
};

type NutritionPracticeActions = {
  setActiveTab: (tabIndex: number) => void;
  resetStore: () => void;
};

type NutritionPracticeStore = NutritionPracticeState & NutritionPracticeActions;

const TOTAL_TABS = 3;
const DEFAULT_TAB = 0;

const initialState: NutritionPracticeState = {
  activeTab: DEFAULT_TAB,
};

const validateTabIndex = (tabIndex: number): number => {
  return Math.max(0, Math.min(tabIndex, TOTAL_TABS - 1));
};

export const useNutritionPracticeStore = create<NutritionPracticeStore>()(
  persist(
    (set) => ({
      ...initialState,
      setActiveTab: (tabIndex: number) =>
        set({
          activeTab: validateTabIndex(tabIndex),
        }),
      resetStore: () => set(initialState),
    }),
    { name: LOCAL_STORAGE_KEYS.NUTRITION_PRACTICE }
  )
);
