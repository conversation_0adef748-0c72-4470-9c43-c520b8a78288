import { useEffect } from 'react';

import { InputAdornment, TextField } from '@mui/material';

import { usePackageSelectorStore } from '@/store/emr/lab/package-store';

import PenIcon from '@/assets/svg/PenIcon';

import { textFieldSx } from '@/constants/emr/lab';

import TestSearch from '@/views/emr/lab/new-test/TestSearch';

import { Header, Row } from '@/core/components/table/types';
import { modalModes } from '@/types/emr/lab';

import { PackageTestList } from './DetailPackageView';

type CreatePackageFormProps = {
  tableHeaders: Header[];
  testRows: Row[];
};

export const CreatePackageForm: React.FC<CreatePackageFormProps> = ({
  tableHeaders,
  testRows,
}) => {
  const {
    activePackageType,
    newPackageName,
    testItems,
    setNewPackageName,
    modalMode,
    selectedPackage,
  } = usePackageSelectorStore();

  useEffect(() => {
    if (modalMode === modalModes.CREATE && selectedPackage?.name) {
      setNewPackageName(selectedPackage.name);
    }
  }, [selectedPackage?.name, modalMode, setNewPackageName]);

  return (
    <div className="flex flex-col px-4  py-1 flex-grow">
      <div className="flex items-center mb-1">
        <TestSearch pageKey={activePackageType as string} />
      </div>
      <div className="mb-4 flex items-center">
        <div className="w-1/2">
          <TextField
            fullWidth
            size="small"
            placeholder="Name your package"
            variant="outlined"
            value={newPackageName}
            onChange={(e) => setNewPackageName(e.target.value)}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <PenIcon className="w-3 h-4" />
                </InputAdornment>
              ),
            }}
            sx={textFieldSx}
          />
        </div>
      </div>
      <PackageTestList
        testItems={testItems}
        tableHeaders={tableHeaders}
        testRows={testRows}
        emptyMessage="Search and select tests to add them to your package."
      />
    </div>
  );
};
