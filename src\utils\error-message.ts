import { FieldError, FieldErrors } from 'react-hook-form';

import { AxiosError } from 'axios';

export const getErrorMessage = (
  error: unknown,
  fallback = 'An error occurred'
): string => {
  if (!error) return fallback;

  if (error instanceof AxiosError) {
    if (error.response) {
      const { data, status } = error.response;
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        const message = (data as { message?: unknown }).message;
        if (typeof message === 'string') return message;
        return JSON.stringify(data);
      }
      return `Error code: ${status}`;
    }
    return error.message;
  }

  if (error instanceof Error) return error.message;

  if (typeof error === 'string') return error;

  if (typeof error === 'number') return `Error code: ${error}`;

  if (typeof error === 'function') {
    try {
      return getErrorMessage(error());
    } catch {
      return fallback;
    }
  }

  if (Array.isArray(error)) {
    return error.map((item) => getErrorMessage(item, '')).join(', ');
  }

  if (typeof error === 'object') {
    const message = (error as { message?: unknown }).message;
    if (typeof message === 'string') return message;
    try {
      return JSON.stringify(error);
    } catch {
      return fallback;
    }
  }

  return fallback;
};

export const getErrorMessages = <T extends Object>(
  errors: FieldErrors<T>
): string[] => {
  const messages: string[] = [];

  const extractMessages = (errObj: FieldErrors<T>) => {
    for (const key in errObj) {
      const error = errObj?.[key as keyof T] as any;

      if (!error) continue;

      if ((error as FieldError).message) {
        messages.push((error as FieldError).message as string);
      }

      if (
        typeof error === 'object' &&
        !Array.isArray(error) &&
        error !== null
      ) {
        extractMessages(error);
      }
      if (
        typeof error === 'object' &&
        !Array.isArray(error) &&
        error !== null
      ) {
        extractMessages(error);
      }

      if (Array.isArray(error)) {
        error.forEach((item) => {
          if (typeof item === 'object' && item !== null) {
            extractMessages(item);
          }
        });
      }
    }
  };

  extractMessages(errors);
  return messages;
};
