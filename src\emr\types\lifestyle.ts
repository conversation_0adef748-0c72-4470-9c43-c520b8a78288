import {
  LifestyleFormModeType,
  LifestyleQuestionTypes,
  LifestyleSectionTypes,
} from '@/constants/lifestyle';

export const lifestyleSource = {
  DIETARY_ASSESSMENT_SOURCE: 'dietary_assessment',
  NUTRITION_MONITORING_SHEET_SOURCE: 'nutrition_monitoring_sheet',
  AWARENESS_ADAPTATION_SOURCE: 'lifestyle_awareness_and_adaptation',
} as const;

export type LifestyleSource =
  (typeof lifestyleSource)[keyof typeof lifestyleSource];

export type SubQuestions = {
  order?: number;
  question?: string;
  options?: string[];
  type?: LifestyleQuestionTypes;
  unit?: string;
  value?: any;
  sub_questions?: Omit<SubQuestions, 'sub_questions'>[];
  inputVariant?: 'gray' | 'white' | 'transparent';
  headerKey?: string;
  defaultValue?: any;
};

export type Questions = {
  order: number;
  question: string;
  options?: string[];
  type: LifestyleQuestionTypes;
  unit?: string;
  value?: any;
  sub_questions?: SubQuestions[];
  inputVariant?: 'gray' | 'white' | 'transparent';
  headerKey?: string;
};

export type Sections = {
  section_title: string;
  icon?: string;
  questions: Questions[];
  type?: LifestyleSectionTypes;
  section_id?: string;
};

export type LifestyleQuestion = {
  source: LifestyleSource;
  sections: Sections[];
  id: string;
  updated_on: string;
  created_on?: string;
  status?: LifestyleFormModeType;
};
