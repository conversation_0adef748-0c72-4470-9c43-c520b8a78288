import { FC, memo, useEffect, useMemo } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { Questionnaire } from '@/types/emr/lifestyle/questionnaire';

import DietaryRecallForm from './DietaryRecallForm';

type Props = {
  data: Questionnaire;
};

const DietaryRecallTimelineForm: FC<Props> = ({ data }) => {
  const methods = useForm<Questionnaire>({
    defaultValues: data,
    mode: 'onChange',
  });

  const formFields = useMemo(() => {
    if (!data?.questions?.length) return [];
    return data.questions;
  }, [data]);

  useEffect(() => {
    if (data) {
      methods.reset(data);
    }
  }, [data, methods]);

  return (
    <FormProvider {...methods}>
      <DietaryRecallForm formFields={formFields} readonly />
    </FormProvider>
  );
};

export default memo(DietaryRecallTimelineForm);
