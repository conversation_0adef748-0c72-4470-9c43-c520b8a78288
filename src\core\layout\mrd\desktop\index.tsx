import { FC } from 'react';

import AppLayout from '@core/layout/shared/AppLayout';

import colors from '@/utils/colors';

import mrdNavigation from '@/core/configs/mrd/navigation-desktop';

const DesktopLayout: FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AppLayout navItem={mrdNavigation} highlightColor={colors.sidebar.pink}>
      {children}
    </AppLayout>
  );
};

export default DesktopLayout;
