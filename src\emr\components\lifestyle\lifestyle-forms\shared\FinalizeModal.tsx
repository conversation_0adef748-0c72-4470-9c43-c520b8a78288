import { Modal } from '@mui/material';
import { IoMdCheckmark, IoMdClose } from 'react-icons/io';

import Loading from '@/lib/common/loading';

type Props = {
  open: boolean;
  onClose: () => void;
  onFinalize: () => void;
  loading?: boolean;
};

const FinalizeModal: React.FC<Props> = ({
  open,
  onClose,
  onFinalize,
  loading = false,
}) => {
  return (
    <Modal open={open} onClose={onClose} style={{ zIndex: 10000 }}>
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg p-4 w-[85dvw] sm:w-90">
        <div className="flex flex-col gap-3">
          <span className="text-base text-center sm:text-2xl font-bold">
            Confirmation
          </span>
          <span className="text-xs sm:text-base">
            Are you sure you want to finalize the record? This step is
            irreversible.
          </span>
          <div className="flex gap-3 sm:gap-2 justify-center">
            <button
              type="button"
              className="border border-primary text-xs sm:text-base  text-primary px-4 py-1.5 rounded-full flex items-center gap-2 text-semibold"
              onClick={onFinalize}
              disabled={loading}
            >
              Yes {loading ? <Loading /> : <IoMdCheckmark />}
            </button>
            <button
              type="button"
              className="border border-primary text-xs sm:text-base text-primary px-4 py-1.5 rounded-full flex items-center gap-2 text-semibold"
              onClick={onClose}
            >
              No <IoMdClose />
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default FinalizeModal;
