export const TABLE_HEADERS = [
    { key: 'no', header: 'No', cellProps: { width: '5%' } },
    { key: 'drugName', header: 'Drug Name', cellProps: { width: '12%' } },
    { key: 'genericName', header: 'Generic Name', cellProps: { width: '12%' } },
    { key: 'brandName', header: 'Brand Name', cellProps: { width: '12%' } },
    { key: 'strength', header: 'Strength', cellProps: { width: '8%' } },
    { key: 'measure', header: 'Nos/ml/gm', cellProps: { width: '8%' } },
    { key: 'frequency', header: 'Frequency', cellProps: { width: '8%' } },
    { key: 'duration', header: 'Duration', cellProps: { width: '8%' } },
    { key: 'quantity', header: 'QTY', cellProps: { width: '5%' } },
    { key: 'route', header: 'Route', cellProps: { width: '7%' } },
    { key: 'instructions', header: 'Instructions', cellProps: { width: '15%' } },
    { key: 'cost', header: 'Cost', cellProps: { width: '10%' } },
  ];
  
  export const TABLE_STYLES = {
    flex: 1,
    boxShadow: 'none',
    overflow: 'auto',
    maxHeight: 'calc(100vh - 500px)',
    minHeight: '200px',
    '& .MuiTable-root': {
      borderCollapse: 'separate',
      borderSpacing: 0,
      width: '100%',
      tableLayout: 'fixed',
    },
    '& .MuiTableCell-head': {
      backgroundColor: '#64707D',
      fontWeight: 'bold',
      borderBottom: '1px solid #e0e0e0',
      '&:not(:last-child)': {
        borderRight: '1px solid #ffffff',
      },
    },
    '& .MuiTableRow-root': {
      '&:nth-of-type(odd)': {
        backgroundColor: '#DAE1E7',
      },
      '&:nth-of-type(even)': {
        backgroundColor: '#ffffff',
      },
      '& th, & td': {
        borderBottom: '1px solid #e0e0e0',
        '&:not(:last-child)': {
          borderRight: '1px solid #ffffff',
        },
      },
      '& th': {
        backgroundColor: '#64707D',
        color: '#ffffff',
        '&:not(:last-child)': {
          borderRight: '1px solid #ffffff',
        },
      },
    },
  };