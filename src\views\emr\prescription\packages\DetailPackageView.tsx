import React from 'react';

import Loading from '@/lib/common/loading';

import { usePrescriptionPackageStore } from '@/store/emr/prescription/package';

import TableV2 from '@/core/components/table-v2';
import { HeaderV2, RowV2 } from '@/core/components/table-v2/types';
import { prescriptionModalModes } from '@/types/emr/prescription';

import PackageMedicineSearch from './PackageMedicineSearch';

interface DetailPackageViewProps {
  tableHeaders: HeaderV2[];
  medicineRows: RowV2[];
}

export const DetailPackageView: React.FC<DetailPackageViewProps> = ({
  tableHeaders,
  medicineRows,
}) => {
  const { isLoading, modalMode } = usePrescriptionPackageStore();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loading />
      </div>
    );
  }

  const isEditMode = modalMode === prescriptionModalModes.CREATE;

  return (
    <div className="p-4 flex flex-col h-full">
      {isEditMode && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Search and Add More Medicines
          </label>
          <PackageMedicineSearch />
        </div>
      )}

      <div className="flex-1 overflow-auto">
        <TableV2
          headers={tableHeaders}
          rows={medicineRows}
          stickyHeader
          tableContainerProps={{
            sx: {
              maxHeight: '500px',
            },
          }}
        />
      </div>
    </div>
  );
};
