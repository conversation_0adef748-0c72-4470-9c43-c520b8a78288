import { lighten } from '@mui/material/styles';

import { ComponentSettings, OverrideComponent } from '@core/theme/types';

const MuiButton = ({
  palette,
}: ComponentSettings): OverrideComponent['MuiButton'] => ({
  styleOverrides: {
    root: {
      borderRadius: 'var(--radius, 0.5rem)',
      textTransform: 'none',
      fontWeight: 500,
    },
    contained: {
      backgroundColor: palette.primary.main,
      color: palette.primary.contrastText,
      boxShadow: '0px 4px 12px rgba(0,0,0,0.08)',
      '&:hover': {
        backgroundColor: lighten(palette.primary.main, 0.1),
        boxShadow: '0px 6px 16px rgba(0,0,0,0.12)',
      },
    },
  },
  defaultProps: {
    variant: 'contained',
    disableElevation: true,
  },
});

export default MuiButton;
