import { Modal } from '@mui/material';
import { IoMdClose } from 'react-icons/io';

import Loading from '@/lib/common/loading';

import { FluentDelete32Filled } from '@/assets/svg/FluentDelete32Filled';

type Props = {
  open: boolean;
  onClose: () => void;
  onDelete: () => void;
  isLoading?: boolean;
};

const DeleteModal: React.FC<Props> = ({
  open,
  onClose,
  onDelete,
  isLoading = false,
}) => {
  return (
    <Modal open={open} onClose={onClose}>
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg p-4 w-90">
        <div className="flex flex-col gap-3">
          <span className="text-2xl font-bold text-center w-full">
            Delete Confirmation
          </span>
          <span className="text-center">
            Are you sure you want to delete this record? This action cannot be
            undone.
          </span>
          <div className="flex gap-2 w-full justify-center">
            <button
              type="button"
              className="border border-gray-600 text-gray-600 px-4 py-1.5 rounded-full flex items-center gap-2 text-semibold"
              onClick={onDelete}
            >
              Yes
              {isLoading ? (
                <Loading />
              ) : (
                <FluentDelete32Filled
                  width={18}
                  height={'auto'}
                  fontSize={18}
                />
              )}
            </button>
            <button
              type="button"
              className="border border-gray-600 text-gray-600 px-4 py-1.5 rounded-full flex items-center gap-2 text-semibold"
              onClick={onClose}
            >
              No <IoMdClose />
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteModal;
