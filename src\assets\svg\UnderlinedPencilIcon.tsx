import React from 'react';
import type { SVGProps } from 'react';

function UnderlinedPencilIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={20}
      height={20}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 10 12"
      fill="currentColor"
      {...props}
    >
      <path
        d="M0 11.5V9.5H10V11.5H0ZM2 7.5H2.7L6.6 3.6125L5.8875 2.9L2 6.8V7.5ZM1 8.5V6.375L6.6 0.7875C6.69167 0.695833 6.79792 0.625 6.91875 0.575C7.03958 0.525 7.16667 0.5 7.3 0.5C7.43333 0.5 7.5625 0.525 7.6875 0.575C7.8125 0.625 7.925 0.7 8.025 0.8L8.7125 1.5C8.8125 1.59167 8.88542 1.7 8.93125 1.825C8.97708 1.95 9 2.07917 9 2.2125C9 2.3375 8.97708 2.46042 8.93125 2.58125C8.88542 2.70208 8.8125 2.8125 8.7125 2.9125L3.125 8.5H1Z"
        fill="#012436"
      />
    </svg>
  );
}

export default UnderlinedPencilIcon;
