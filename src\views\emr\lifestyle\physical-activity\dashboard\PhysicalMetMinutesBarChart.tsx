import React, { useMemo } from 'react';

import { useLifestyleFilterStore } from '@/store/emr/lifestyle/filter-store';

import NutritionBarChart from '@/views/emr/lifestyle/shared/dashboard-components/NutritionBarChart';

interface MetMinutesChartPoint {
  date: string;
  value: number;
}

interface PhysicalMetMinutesBarChartProps {
  data: MetMinutesChartPoint[];
}

const PhysicalMetMinutesBarChart: React.FC<PhysicalMetMinutesBarChartProps> = ({
  data,
}) => {
  const { fromDate, toDate } = useLifestyleFilterStore();

  const chartData = useMemo(() => {
    if (data.length > 0) {
      return data.map((d) => ({
        ...d,
        metMinutes: d.value,
        date: d.date,
      }));
    }

    let days = 7;
    if (fromDate && toDate) {
      const diffTime = Math.abs(
        new Date(toDate).getTime() - new Date(fromDate).getTime()
      );
      days = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    } else {
      const urlParams = new URLSearchParams(window.location.search);
      const filter = urlParams.get('filter');
      if (filter === '7d') days = 7;
      else if (filter === '15d') days = 15;
      else if (filter === '30d') days = 30;
    }

    const today = new Date();
    return Array.from({ length: days }, (_, i) => {
      const date = new Date(today);
      date.setDate(today.getDate() - (days - 1 - i));
      return {
        date: date.toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'short',
        }),
        metMinutes: 0,
        value: 0,
      };
    });
  }, [data, fromDate, toDate]);
  return (
    <NutritionBarChart
      data={chartData}
      dataKey="metMinutes"
      colors={{ top: '#D76A81', bottom: '#BA324F' }}
      title="MET Minutes"
      unit="min"
      height={320}
      showLegend={false}
      yAxisDomain={data.length === 0 ? [0, 50] : undefined}
    />
  );
};

export default PhysicalMetMinutesBarChart;
