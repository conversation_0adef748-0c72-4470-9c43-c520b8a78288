import { FC, memo, useState } from 'react';

import { Controller } from 'react-hook-form';

import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Radio,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Typography,
  TableCellProps,
} from '@mui/material';

import { cn } from '@/lib/utils';

import AppIcon from '@/core/components/app-icon';
import {
  StyledTableContainerV2,
  StyledTableHeadV2,
} from '@/core/components/table-v2/styled-components';
import { FieldGroup } from '@/types/emr/lifestyle/questionnaire';

import { FieldComponentProps } from './types';

interface FrequencyTableFieldProps extends Partial<FieldComponentProps> {
  sections: FieldGroup[];
  control: any;
  readonly?: boolean;
  expandedSections?: string[];
  onExpandAll?: () => void;
  onCollapseAll?: () => void;
  onSectionToggle?: (sectionId: string) => void;
  isExpanded?: boolean;
}

const Headers = {
  FOOD_ITEM: 'Food Item',
  DAILY: 'Daily',
  WEEKLY: 'Weekly (1,2,3,4,5)',
  MONTHLY: 'Monthly (1,2)',
  RARELY: 'Rarely',
  NEVER: 'Never',
} as const;

type Headers = (typeof Headers)[keyof typeof Headers];

const { DAILY, FOOD_ITEM, MONTHLY, NEVER, RARELY, WEEKLY } = Headers;

const commonStyling = { padding: '6px 12px', fontSize: '12px' };

const cellStyling: Record<string, TableCellProps> = {
  [FOOD_ITEM]: { sx: { ...commonStyling, minWidth: 120, maxWidth: 120 } },
  [DAILY]: { sx: { ...commonStyling, minWidth: 50, maxWidth: 50 } },
  [WEEKLY]: { sx: { ...commonStyling, minWidth: 160, maxWidth: 160 } },
  [MONTHLY]: { sx: { ...commonStyling, minWidth: 120, maxWidth: 120 } },
  [RARELY]: { sx: { ...commonStyling, minWidth: 50, maxWidth: 50 } },
  [NEVER]: { sx: { ...commonStyling, minWidth: 50, maxWidth: 50 } },
};

const FrequencyTableField: FC<FrequencyTableFieldProps> = ({
  sections,
  control,
  readonly,
  expandedSections: propExpandedSections,
  onSectionToggle,
  isExpanded = false,
}) => {
  const [internalExpandedSections, setInternalExpandedSections] = useState<
    string[]
  >(['cereals_grains']);

  const expandedSections = propExpandedSections ?? internalExpandedSections;

  const handleSectionToggle = (sectionId: string) => {
    if (onSectionToggle) {
      onSectionToggle(sectionId);
    } else {
      setInternalExpandedSections((prev) =>
        prev.includes(sectionId)
          ? prev.filter((id) => id !== sectionId)
          : [...prev, sectionId]
      );
    }
  };

  const firstField = sections[0]?.fields?.[0] as any;
  const frequencyColumns = firstField?.columns || [];

  const columnHeaders = [
    { key: 'food_item', label: 'Food Item' },
    ...frequencyColumns.map((col: any) => ({
      key: col.header,
      label: col.header,
      options: col.option || [],
    })),
  ];

  return (
    <div className="w-full">
      <StyledTableContainerV2
        sx={{
          ...(readonly && {
            '& .MuiTableHead-root .MuiTableCell-root': {
              backgroundColor: '#64707D',
            },
          }),
        }}
      >
        <Table size="small" stickyHeader>
          <StyledTableHeadV2>
            <TableRow>
              {columnHeaders.map((header) => (
                <TableCell
                  key={header.key}
                  align="center"
                  {...(cellStyling?.[header?.label] || {})}
                >
                  {header.label}
                </TableCell>
              ))}
            </TableRow>
          </StyledTableHeadV2>
        </Table>

        {sections.map((section, sectionIndex) => (
          <Accordion
            key={section.id}
            expanded={expandedSections.includes(section.id)}
            onChange={() => handleSectionToggle(section.id)}
            sx={{
              boxShadow: 'none',
              borderLeft: '1px solid #e5e7eb',
              borderRight: '1px solid #e5e7eb',
              borderBottom: '1px solid #e5e7eb',
              borderTop: 'none',
              backgroundColor: 'white',
              '&:before': { display: 'none' },
              '&.Mui-expanded': {
                margin: 0,
                borderLeft: '1px solid #e5e7eb',
                borderRight: '1px solid #e5e7eb',
                borderBottom: '1px solid #e5e7eb',
              },
            }}
            classes={{ heading: 'bg-transparent !h-12 !flex items-center' }}
          >
            <AccordionSummary
              expandIcon={
                <AppIcon icon="icon-park-outline:down" className="text-black" />
              }
              classes={{
                content: '',
                expanded: cn(
                  '!min-h-10 !h-10 !m-0 flex items-center ',
                  isExpanded ? '!bg-[#C2CDD6]' : '!bg-white'
                ),
                root: `min-h-10 !bg-white !m-0`,
                expandIconWrapper: 'min-h-10 flex items-center',
              }}
              sx={{
                '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
                  transform: 'rotate(180deg)',
                },
              }}
            >
              <Typography variant="subtitle1" fontWeight="medium">
                {section.title}
              </Typography>
            </AccordionSummary>

            <AccordionDetails classes={{ root: '!p-0' }}>
              <Table size="small">
                <TableBody>
                  {section.fields?.map((field, fieldIndex) => (
                    <TableRow key={field.id}>
                      {/* Food Item Column */}
                      <TableCell
                        {...cellStyling?.[FOOD_ITEM]}
                        align="left"
                        sx={{
                          ...(cellStyling?.[FOOD_ITEM]?.sx ?? {}),
                          p: '!0px',
                          backgroundColor: isExpanded
                            ? fieldIndex % 2 === 0
                              ? '#DAE1E7'
                              : 'white'
                            : fieldIndex % 2 === 0
                              ? '#E6F6FF'
                              : 'white',
                        }}
                      >
                        <div className="flex justify-start text-left">
                          {field.label}
                        </div>
                      </TableCell>

                      {frequencyColumns.map((column: any, colIndex: number) => (
                        <TableCell
                          key={`${field.id}_${column.header}_${colIndex}`}
                          id={`${field.id}_${column.header}_${colIndex}`}
                          align="center"
                          {...cellStyling?.[column?.header]}
                          sx={{
                            ...(cellStyling?.[column?.header]?.sx ?? {}),
                            p: '!0px',
                            backgroundColor: isExpanded
                              ? fieldIndex % 2 === 0
                                ? '#DAE1E7'
                                : 'white'
                              : fieldIndex % 2 === 0
                                ? '#E6F6FF'
                                : 'white',
                          }}
                        >
                          <div className="flex gap-1 justify-center">
                            {column.option?.map((option: string) => (
                              <Controller
                                key={`${field.id}_${option}`}
                                name={`questions.0.sections.${sectionIndex}.fields.${fieldIndex}.value`}
                                control={control}
                                render={({ field: controllerField }) => {
                                  const isChecked =
                                    controllerField.value === option;

                                  return (
                                    <Radio
                                      value={option}
                                      checked={isChecked}
                                      size="small"
                                      onChange={(e) =>
                                        !readonly &&
                                        controllerField.onChange(e.target.value)
                                      }
                                      disableRipple={readonly}
                                      disableTouchRipple={readonly}
                                      disabled={readonly}
                                      // Replace the Radio component's sx prop with this updated styling:

                                      sx={{
                                        color: '#000000 !important',
                                        '&.Mui-checked': {
                                          color: '#000000 !important',
                                        },
                                        '&.Mui-disabled': {
                                          color: '#000000 !important',
                                          opacity: 1, // Changed from 4 to 1 to maintain black color
                                          outline: 'none',
                                          '& .MuiSvgIcon-root': {
                                            color: '#000000 !important', // Add this to ensure icon is black
                                            fontSize: '5px', // Changed from '5px' to normal size
                                            opacity: '1 !important',
                                          },
                                        },
                                        '&.Mui-disabled.Mui-checked': {
                                          color: '#000000 !important',
                                          opacity: '1 !important',
                                          outline: 'none',
                                          '& .MuiSvgIcon-root': {
                                            color: '#000000 !important',
                                            fontSize: '5px',
                                            opacity: '1 !important',
                                          },
                                        },
                                        '& .MuiSvgIcon-root': {
                                          color: '#000000 !important',
                                        },
                                        '&.Mui-disabled .MuiSvgIcon-root': {
                                          color: '#000000 !important',
                                          opacity: '1 !important',
                                          fontSize: '1px',
                                          outline: 'none',
                                        },
                                      }}
                                    />
                                  );
                                }}
                              />
                            ))}
                          </div>
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </AccordionDetails>
          </Accordion>
        ))}
      </StyledTableContainerV2>
    </div>
  );
};

export default memo(FrequencyTableField);
