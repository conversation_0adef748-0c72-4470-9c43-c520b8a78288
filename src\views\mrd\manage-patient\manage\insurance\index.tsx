import { memo } from 'react';

import { useFormContext } from 'react-hook-form';

import { formatToNumber } from '@/utils/format-value';

import { insuranceProvidersOptions } from '@/constants/mrd/manage-patient/select-options';

import ControlledImageUploader from '@/components/controlled-inputs/ControlledImageUploader';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';
import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import { PatientDetails } from '@/types/mrd/manage-patient/patient-details';

const Insurance = () => {
  const { control } = useFormContext<PatientDetails>();
  return (
    <div className="py-base flex flex-col gap-base">
      <div className="flex gap-base w-[65%]">
        <ControlledSelectField
          name="insurance.provider"
          control={control}
          label="Select Insurance Provider"
          placeholder="Select"
          options={insuranceProvidersOptions}
          initiallyReadonly
        />
        <ControlledTextField
          name="insurance.id"
          control={control}
          label="Insurance Id"
          placeholder="0000 0000 000"
          initiallyReadonly
          fullWidth
          formatValue={formatToNumber}
        />
      </div>
      <div>
        <ControlledImageUploader
          name="insurance.url"
          control={control}
          label="Insurance Proof"
          accept=".png, .jpg, .jpeg, .pdf"
          maxSizeInMB={5}
          indicationLabel="(The file size must be less than 5MB.)"
          showError={false}
        />
      </div>
    </div>
  );
};

export default memo(Insurance);
