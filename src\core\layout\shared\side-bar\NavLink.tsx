import { type FC, memo, useMemo } from 'react';

import { SvgIcon } from '@mui/material';
import clsx from 'clsx';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { cn } from '@/lib/utils';

import colors from '@/utils/colors';

import type { NavButtonProps } from './types';

const NavLink: FC<NavButtonProps> = ({
  icon,
  text,
  href,
  disabled = false,
  highlightColor = colors.common.navyBlue,
}) => {
  const pathname = usePathname();

  const isActive = useMemo(() => pathname.startsWith(href), [pathname, href]);

  const containerClass = useMemo(
    () =>
      clsx(
        'flex flex-col items-center justify-center flex-shrink-0 flex-grow-0',
        'gap-1 p-0.5 w-full h-16 rounded-base',
        'transition-all duration-100 ease-in-out',
        {
          'text-white': isActive && !disabled,
          'bg-gray-200 opacity-60 cursor-not-allowed pointer-events-none':
            disabled,
        }
      ),
    [isActive, disabled]
  );

  const iconClass = useMemo(
    () =>
      cn('text-lg flex items-center justify-center w-6 h-6', {
        'text-primary': disabled || !isActive,
        'text-white': isActive && !disabled,
      }),
    [disabled, isActive]
  );

  const textClass = useMemo(
    () =>
      cn(
        'text-[10px] -tracking-[2.2%] font-archivo text-center leading-tight w-full flex justify-center',
        {
          'text-primary': disabled || !isActive,
          'text-white': isActive && !disabled,
        }
      ),
    [disabled, isActive]
  );

  return (
    <Link
      href={href}
      aria-disabled={disabled}
      className={containerClass}
      style={
        isActive && !disabled ? { backgroundColor: highlightColor } : undefined
      }
    >
      <span className={iconClass}>
        <SvgIcon color="inherit">{icon}</SvgIcon>
      </span>
      <span
        className={textClass}
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
        }}
      >
        {text}
      </span>
    </Link>
  );
};

export default memo(NavLink);
