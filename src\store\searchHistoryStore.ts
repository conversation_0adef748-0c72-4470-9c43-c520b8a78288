import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { PatientI } from '@/types';

interface RecentSearchStore {
  recentSearches: Record<'mrd' | 'emr', PatientI[]>;
  addSearch: (_type: 'mrd' | 'emr', _newSearch: PatientI) => void;
}

const RECENT_PATIENT_SEARCHES_KEY = 'recent-patient-searches';

const initialRecentSearches = {
  mrd: [],
  emr: [],
};

export const useRecentSearchStore = create(
  persist<RecentSearchStore>(
    (set, get) => ({
      recentSearches: initialRecentSearches,
      addSearch: (type, newSearch) => {
        const recentSearches = get().recentSearches[type];
        const updatedSearches = [
          newSearch,
          ...(recentSearches ?? []).filter(
            (search) => search?.id !== newSearch?.id
          ),
        ]?.slice(0, 10);
        set({
          recentSearches: { ...get().recentSearches, [type]: updatedSearches },
        });
      },
    }),
    {
      name: RECENT_PATIENT_SEARCHES_KEY,
    }
  )
);
