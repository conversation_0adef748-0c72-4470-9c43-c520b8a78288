import { FilterByType } from '@/utils/filter-by-util';

import { arcaAxios } from '@/core/lib/interceptor';
import {
  PrescriptionHistory,
  PrescriptionItem,
  PrescriptionUpdatePayload,
} from '@/types/emr/prescription';

export interface Medicine {
  id: string;
  drugForm: string;
  genericName: string;
  brandName: string;
  strength: string;
}
export interface PrescriptionPayload {
  patientId?: string;
  doctor?: string;
  medicines: PrescriptionItem[];
  status?: 'Paid' | 'Unpaid';
}

export type GetPrescriptionHistoryParams = {
  patientId: string;
  dateFilter: FilterByType;
  customStartDate?: string;
  customEndDate?: string;
  searchText?: string;
};

export const searchMedicinesAPI = async (
  searchTerm: string,
  organizationId?: string
) => {
  return await arcaAxios.post(`/medicine/search`, {
    pagesize: 10,
    continuetoken: null,
    searchText: searchTerm,
    ...(organizationId ? { organizationId } : {}),
  });
};

export const createNewPrescription = async (data: PrescriptionPayload) => {
  return await arcaAxios.post<PrescriptionHistory>('/prescriptions', data);
};

export const getPrescriptionHistory = async (
  params: GetPrescriptionHistoryParams
) => {
  return await arcaAxios.get(`/prescriptions`, { params });
};

export const getPrescriptionById = async (id: string) => {
  return await arcaAxios.get(`/prescriptions/details?prescriptionId=${id}`);
};

export const searchPrescriptionHistory = async (
  searchTerm: string,
  patientId: string
): Promise<PrescriptionHistory[]> => {
  const response = await arcaAxios.post<{ items: PrescriptionHistory[] }>(
    `/prescriptions/search`,
    {
      pagesize: 1,
      continuetoken: null,
      searchText: searchTerm,
      patientId: patientId,
    }
  );
  return response?.data?.items || [];
};

export const updatePrescription = async (
  id: string,
  data: PrescriptionUpdatePayload
) => {
  return await arcaAxios.patch(`/prescriptions?prescriptionId=${id}`, data);
};
