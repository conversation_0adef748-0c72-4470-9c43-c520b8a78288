import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

type FileUploadProps = {
  userId: string;
  type: string;
};

export const useFileUpload = ({ userId, type }: FileUploadProps) => {
  const { uploadDocument } = useDoctorStore();

  const fileUpload = async (file?: File | string) => {
    if (file instanceof File) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', userId);
      formData.append('doc_type', type);
      const docUrl = await uploadDocument(formData);
      return docUrl;
    }

    return file ?? '';
  };

  return { fileUpload };
};
