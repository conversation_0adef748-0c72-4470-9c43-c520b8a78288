'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';

import Box from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';

import { HorizontalWrapper } from './styled-components';
import TabPanel from './TabPanel';
import { AppTabProps } from './types';

function a11yProps(index: number) {
  return {
    'aria-controls': `full-width-tabpanel-${index}`,
  };
}

export default function AppTab({
  tabs,
  disabled = false,
  orientation = 'horizontal',
  panelContainerProps = {},
  wrapperProps = {},
  variant = 'standard',
  onTabChange,
  activeTab,
  readonly,
  tabProps = {},
  scrollButtons = false,
  ...props
}: AppTabProps) {
  const { className = '', ...rest } = wrapperProps;
  const theme = useTheme();

  const activeTabValue = useMemo(() => {
    if (activeTab) {
      return Number(activeTab);
    }
    return 0;
  }, [activeTab]);

  const [value, setValue] = useState<number>(activeTabValue);

  const handleTabChange = useCallback(
    (event: React.SyntheticEvent, newValue: number) => {
      if (readonly) return;
      setValue(newValue);
      if (onTabChange) onTabChange(newValue);
    },
    [onTabChange, readonly]
  );

  useEffect(() => {
    setValue(activeTabValue);
  }, [activeTabValue]);

  return (
    <HorizontalWrapper
      className={`${orientation + className} ${variant}`}
      {...rest}
    >
      <Tabs
        variant="scrollable"
        indicatorColor="secondary"
        textColor="inherit"
        aria-label="full width tabs"
        orientation={orientation}
        className={`${orientation}-tabs`}
        value={value}
        onChange={handleTabChange}
        scrollButtons={scrollButtons}
        id="app-tab"
        {...props}
      >
        {tabs?.map((tab, index) => (
          <Tab
            key={index}
            label={tab?.label}
            disabled={disabled || tab?.disabled}
            id={tab?.id}
            {...tabProps}
            {...a11yProps(index)}
          />
        ))}
      </Tabs>
      <Box {...panelContainerProps} className={`${orientation}-panels`}>
        {tabs?.map((tab, index) => (
          <TabPanel
            key={index}
            value={value}
            index={index}
            dir={theme?.direction}
          >
            {tab?.content}
          </TabPanel>
        ))}
      </Box>
    </HorizontalWrapper>
  );
}
