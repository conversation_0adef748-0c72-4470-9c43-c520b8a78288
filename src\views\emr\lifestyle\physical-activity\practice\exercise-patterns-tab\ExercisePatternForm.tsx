import { FC, memo } from 'react';

import { LiaDumbbellSolid } from 'react-icons/lia';

import { LifestyleMode } from '@/constants/emr/lifestyle';

import RenderFields from '@/views/emr/lifestyle/shared/render-fields';

import { FieldGroup } from '@/types/emr/lifestyle/questionnaire';

type Props = {
  formFields: FieldGroup[];
  readonly?: boolean;
  showHeading?: boolean;
  mode?: LifestyleMode;
  patientData?: any[];
  variant?: 'modal' | 'timeline';
};

const ExercisePatternForm: FC<Props> = ({
  formFields,
  readonly,
  showHeading = true,
  mode,
  patientData = [],
  variant,
}) => {
  const renderIcon = (icon: string) => {
    if (icon === 'dumbbell') {
      return <LiaDumbbellSolid className="text-2xl text-black" />;
    }
    return <span className="text-2xl">{icon}</span>;
  };

  if (!formFields || formFields.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        No exercise pattern data available
      </div>
    );
  }

  return (
    <div className={`space-y-6 p-4 ${readonly ? 'pointer-events-none' : ''}`}>
      {formFields?.map((section, index) => (
        <div key={section.id || index} className="space-y-6">
          {showHeading && (
            <div className="flex items-center space-x-2">
              {section.icon && renderIcon(section.icon)}
              <h3 className="text-lg font-medium">{section.title}</h3>
            </div>
          )}
          <div>
            <RenderFields
              fields={section?.fields}
              namePrefix={`questions.${index}.fields`}
              readonly={readonly}
              mode={mode}
              patientData={patientData}
              variant={variant}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default memo(ExercisePatternForm);
