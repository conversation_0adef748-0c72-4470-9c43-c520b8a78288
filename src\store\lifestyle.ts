import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createLifestyleData,
  updateLifestyleData,
  getLifestyleQuestions,
  getPatientLifestyle,
} from '@/query/lifestyle';

import {
  lifestyleFormMode,
  LifestyleFormsType,
  NutritionAndDietForm,
} from '@/constants/lifestyle';

import { LifestyleQuestion, LifestyleSource } from '@/emr/types/lifestyle';

import { DateType } from '@/core/components/date-range-picker/types';

import { useLifestyleUtilStore } from './lifestyle-utils-store';

const { DIETARY_ASSESSMENT } = NutritionAndDietForm;

type LifestyleStore = {
  questions: LifestyleQuestion | null;
  patientLifestyle: LifestyleQuestion[];
  loading: boolean;
  updating: boolean;
  loadingTimeLine: boolean;
  selectedForm: LifestyleFormsType;
  getLifestyleQuestions: (_source: LifestyleSource) => any;
  createLifestyleData: <T>(_data: T, _patientId: string) => any;
  updateLifestyleData: <T>(_data: T, _id: string) => any;
  finaliseRecord: (_data: LifestyleQuestion | null) => any;
  getPatientLifestyle: (
    _patientId: string,
    _source: LifestyleSource,
    _fromDate?: DateType,
    _toDate?: DateType
  ) => any;
};

/**
 * @deprecated This store is deprecated and will be removed in future versions.
 * Please use the store/emr/lifestyle instead.
 */
export const useLifestyleStore = create<LifestyleStore>((set, get) => ({
  questions: null,
  loading: false,
  updating: false,
  loadingTimeLine: false,
  patientLifestyle: [],
  selectedForm: DIETARY_ASSESSMENT,

  getLifestyleQuestions: async (source: LifestyleSource) => {
    try {
      set({ loading: true, questions: null });
      const { data } = await getLifestyleQuestions(source);
      set({
        questions: {
          sections: [...data],
          source: data[data?.length - 1]?.source,
          updated_on: data[data?.length - 1]?.updated_on,
          id: data[data?.length - 1]?.id,
        },
        loading: false,
      });
      return data;
    } catch (error) {
      console.error(error);
      set({ loading: false, questions: null });
    }
  },

  createLifestyleData: async <T>(data: T, patientId: string) => {
    const isSubmittedToday = useLifestyleUtilStore.getState().isSubmittedToday;
    const selectedForm = useLifestyleUtilStore.getState().currentTab;
    if (isSubmittedToday) {
      toast.error('You have already submitted today');
      return;
    }
    set({ selectedForm });
    try {
      set({ updating: true });
      await createLifestyleData(data, patientId);
      set({ updating: false });
      toast.success(`${get().selectedForm} created successfully`);
    } catch {
      set({ updating: false });
      toast.error(`Failed to create ${get().selectedForm}`);
    }
  },

  updateLifestyleData: async <T>(data: T, id: string) => {
    const selectedForm = useLifestyleUtilStore.getState().currentTab;
    set({ selectedForm });
    try {
      set({ updating: true });
      await updateLifestyleData(data, id);
      set({ updating: false });
      toast.success(`${get().selectedForm} updated successfully`);
    } catch {
      set({ updating: false });
      toast.error(`Failed to update ${get().selectedForm}`);
    }
  },

  finaliseRecord: async (data) => {
    const selectedForm = useLifestyleUtilStore.getState().currentTab;
    set({ selectedForm });
    try {
      set({ updating: true });
      if (!data?.id) return;
      await updateLifestyleData(
        { status: lifestyleFormMode.FINALIZED },
        data.id
      );
      set({ updating: false });
      toast.success(`${get().selectedForm} finalized successfully`);
    } catch {
      set({ updating: false });
      toast.error(`Failed to finalize ${get().selectedForm}`);
    }
  },

  getPatientLifestyle: async (
    patientId: string,
    source: LifestyleSource,
    fromDate?: DateType,
    toDate?: DateType
  ) => {
    try {
      set({ loadingTimeLine: true });
      const { data } = await getPatientLifestyle(
        patientId,
        source,
        fromDate,
        toDate
      );
      useLifestyleUtilStore.getState().hasSubmissionToday(data, source);
      const reversedData = data?.reverse();
      set({ patientLifestyle: reversedData, loadingTimeLine: false });
      return data;
    } catch (error) {
      console.error(error);
      set({ loadingTimeLine: false });
    }
  },
}));
