import React, { useState, useEffect } from 'react';

import { Control, Controller, FieldPath } from 'react-hook-form';

import { Box, TextareaAutosize } from '@mui/material';

import PencilIcon from '@/assets/svg/PencilIcon';

import CustomModal from '@/core/components/modal';
import PrimaryButton from '@/core/components/primary-button';

import TruncatedTextarea from './TruncatedTextarea';

import { LabTest } from '.';

interface InstructionsSelectorProps {
  name: `labTest.${number}.${string}`;
  control: Control<LabTest>;
  rules?: { [key: string]: any };
  clearErrors: (name?: FieldPath<LabTest> | FieldPath<LabTest>[]) => void;
  isNotValid?: boolean;
}

const InstructionsSelector: React.FC<InstructionsSelectorProps> = ({
  name,
  control,
  rules,
  clearErrors,
  isNotValid,
}) => {
  const [open, setOpen] = useState(false);
  const [instructionValue, setInstructionValue] = useState('');
  const [tempValue, setTempValue] = useState('');

  useEffect(() => {
    const parts = name.split('.');
    const value = control._formValues?.labTest?.[parts[1]]?.[parts[2]] || '';
    setInstructionValue(value);
  }, [control._formValues, name]);

  const handleOpen = () => {
    setOpen(true);
    setTempValue(instructionValue);
  };

  const handleClose = () => {
    setOpen(false);
    setTempValue('');
  };

  const handleSave = () => {
    const parts = name.split('.');
    const index = parseInt(parts[1]);
    const field = parts[2];

    const currentValues = { ...control._formValues };

    if (currentValues.labTest?.[index]) {
      currentValues.labTest[index][field] = tempValue;

      control._subjects.state.next({
        ...currentValues,
      });

      clearErrors?.(name);
    }

    setInstructionValue(tempValue);
    setOpen(false);
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={() => (
        <>
          <TruncatedTextarea
            value={instructionValue}
            onClick={handleOpen}
            endAdornmentIcon={
              <PencilIcon
                className={`w-auto h-3 ${isNotValid ? 'text-[#E4626F]' : 'text-black'}`}
              />
            }
            placeholder="Instructions"
            isNotValid={isNotValid}
          />

          <CustomModal
            open={open}
            onClose={handleClose}
            title="Instructions"
            width="450px"
            minHeight="85px"
            titleBoxSx={{ mb: 0 }}
            titleTypographySx={{ fontSize: '18px', fontWeight: 600 }}
            showDivider={false}
            contentSx={{ mb: 0 }}
            actionsSx={{ justifyContent: 'center' }}
            content={
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  height: '100%',
                }}
              >
                <Box sx={{ flex: 1, backgroundColor: '#f5f5f5', mb: 2 }}>
                  <TextareaAutosize
                    value={tempValue}
                    onChange={(e) => setTempValue(e.target.value)}
                    style={{
                      width: '100%',
                      height: '100px',
                      padding: '8px',
                      border: 'none',
                      backgroundColor: '#f5f5f5',
                      resize: 'none',
                      outline: 'none',
                    }}
                  />
                </Box>
              </Box>
            }
            actions={
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  gap: 1,
                  width: '100%',
                  px: 1,
                }}
              >
                <PrimaryButton
                  type="button"
                  onClick={handleClose}
                  className="capitalize text-black bg-[#C2CDD6] text-md h-7 w-full"
                >
                  Cancel
                </PrimaryButton>
                <PrimaryButton
                  type="button"
                  onClick={handleSave}
                  className="capitalize text-md h-7 w-full"
                >
                  Save
                </PrimaryButton>
              </Box>
            }
          />
        </>
      )}
    />
  );
};

export default InstructionsSelector;
