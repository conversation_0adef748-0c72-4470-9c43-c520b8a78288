import React, { memo, useCallback } from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import FileInput, { FileInputProps } from '@core/components/file-input';

type Props<T extends FieldValues> = FileInputProps & {
  control: Control<T>;
  name: Path<T>;
};

const ControlledFileUpload = <T extends FieldValues>({
  control,
  name,
  ...rest
}: Props<T>) => {
  const getValue = React.useCallback((value?: File | string) => {
    if (value instanceof File) {
      const fileList = new DataTransfer();
      fileList.items.add(value);
      return fileList.files;
    }
    return value;
  }, []);

  const handleOnChange = useCallback((file?: FileList | string | null) => {
    if (file instanceof FileList && file.length > 0) {
      return file[0];
    }
    return file;
  }, []);

  return (
    <Controller<T>
      name={name}
      control={control}
      render={({ field: { value, onChange, ...field } }) => (
        <FileInput
          {...rest}
          {...field}
          value={getValue(value)}
          onChange={(files) => onChange(handleOnChange(files))}
        />
      )}
    />
  );
};

export default memo(ControlledFileUpload) as typeof ControlledFileUpload;
