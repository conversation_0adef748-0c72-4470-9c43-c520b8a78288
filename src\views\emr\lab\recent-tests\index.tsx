import React, { memo, useCallback, useEffect } from 'react';

import Loading from '@/lib/common/loading';

import { useTestStore } from '@/store/emr/lab/reports-store';

import { getRecentTestsFiltered } from '@/utils/emr/lab/result';

import RecentTestTable from './RecentTestTable';

const RecentTests = () => {
  const { fetchRecentTest, recentTest, recentTestLoading } = useTestStore();

  useEffect(() => {
    fetchRecentTest();
  }, [fetchRecentTest]);

  const renderTestResults = useCallback(() => {
    const filteredTests = getRecentTestsFiltered(recentTest);

    if (recentTestLoading) {
      return (
        <div className="h-full overflow-y-auto">
          <div className="flex justify-center items-center h-full">
            <Loading />
          </div>
        </div>
      );
    }

    if (!filteredTests?.length) {
      return (
        <span className="text-center pt-20 text-sm text-gray-600">
          No recent tests available.
        </span>
      );
    }

    return (
      <div className="h-full overflow-y-auto">
        {filteredTests?.map((test) => (
          <RecentTestTable key={test.id} test={test} />
        ))}
      </div>
    );
  }, [recentTest, recentTestLoading]);

  return (
    <div className="flex flex-col w-full h-full gap-2 p-1">
      <h2 className="font-semibold">Recent Tests</h2>
      {renderTestResults()}
    </div>
  );
};

export default memo(RecentTests);
