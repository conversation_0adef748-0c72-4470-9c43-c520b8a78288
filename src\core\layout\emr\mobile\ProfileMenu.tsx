import React, { memo, useCallback, useState, MouseEvent } from 'react';

import { Theme } from '@mui/material';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { BiLogOut } from 'react-icons/bi';
import { IoMenu } from 'react-icons/io5';

import { useRouter } from 'next/navigation';

import { logout } from '@core/lib/auth/services';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import colors from '@/utils/colors';
import { capitalizeFirstLetter, getInitials } from '@/utils/string';

import { routes } from '@/constants/routes';

const getUserProfileMenuStyles = (theme: Theme) => ({
  padding: theme.spacing(0.5, 2),
  minWidth: 200,
  display: 'flex',
  gap: 2,
  maxWidth: 200,
});

const ProfileMenu = () => {
  const { replace } = useRouter();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const { name, data: userData } = useUserStore();
  const { doctorProfile } = useDoctorStore();

  const open = Boolean(anchorEl);

  const handleClick = useCallback((event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  }, []);

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const renderUserProfile = useCallback(() => {
    const username = doctorProfile?.general?.fullName || name;
    const role = capitalizeFirstLetter(userData?.userRole) || 'Normal User';

    return (
      <MenuItem
        onClick={() => {
          replace(routes.EMR_PROFILE_PERSONAL_INFO);
          handleClose();
        }}
        sx={getUserProfileMenuStyles}
      >
        <Avatar sx={{ width: 40, height: 40 }}>{getInitials(username)}</Avatar>
        <div className="flex flex-col">
          <div className="font-semibold text-wrap">{username}</div>
          <div className={`text-[${colors.common.ashGray}] text-sm`}>
            {role}
          </div>
        </div>
      </MenuItem>
    );
  }, [
    doctorProfile?.general?.fullName,
    name,
    userData?.userRole,
    replace,
    handleClose,
  ]);

  return (
    <div>
      <IconButton
        onClick={handleClick}
        aria-controls={open ? 'account-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
      >
        <IoMenu />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {renderUserProfile()}
        <Divider />
        <MenuItem
          sx={{ padding: (t) => t.spacing(0.5, 2) }}
          onClick={() => {
            handleClose();
            logout();
          }}
        >
          <ListItemIcon sx={{ fontSize: '1rem' }}>
            <BiLogOut />
          </ListItemIcon>
          Logout
        </MenuItem>
      </Menu>
    </div>
  );
};

export default memo(ProfileMenu);
