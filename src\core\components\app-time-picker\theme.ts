import { Position } from './types';

const borderRadius: Record<Position, string> = {
  first: '6px 0 0 6px',
  middle: '0',
  last: '0 6px 6px 0',
};

const minWidth: Record<Position, number> = {
  first: 50,
  middle: 50,
  last: 60,
};

export const getCustomStyles = (position: Position) => ({
  control: (base: any, state: any) => ({
    ...base,
    borderRadius: borderRadius[position],
    borderRight: position === 'last' ? base.borderRight : 'none',
    minWidth: minWidth[position],
    boxShadow: state.isFocused ? '0 0 0 2px #1976d233' : base.boxShadow,
  }),
  menu: (base: any) => ({ ...base, zIndex: 9999 }),
  valueContainer: (base: any) => ({ ...base, paddingRight: 0, paddingLeft: 1 }),
  indicatorsContainer: (base: any) => ({ ...base, paddingRight: 0 }),
  dropdownIndicator: (base: any) => ({
    ...base,
    paddingLeft: 1,
    paddingRight: 2,
  }),
});
