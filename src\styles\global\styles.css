@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 200 96% 11%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --arca-blue-2: 200 97% 25%;
    --arca-blue-2-light: 200 96% 85%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --vh: 100%;
    --spacing: 0.625rem;
    --shadow: 0px 2px 2px 0px #0000001a;
    --letter-spacing: -2.2%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    max-height: 100vh;
    max-width: 100vw;
    overflow: hidden;
  }
}

.custom-checkbox {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid #000;
  width: 1.15em;
  height: 1.15em;
  border-radius: 0.25rem;
  vertical-align: middle;
  position: relative;
  cursor: pointer;
  margin-left: 12px;
}

.custom-checkboxhead {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid #eaedef;
  width: 1.15em;
  height: 1.15em;
  border-radius: 0.25rem;
  vertical-align: middle;
  position: relative;
  cursor: pointer;
}

.custom-checkbox:checked {
  background-color: #000;
  border-color: #000;
}

.custom-checkboxhead:checked {
  background-color: #000;
  border-color: #000;
}

input[type='radio']:disabled {
  opacity: 1 !important;
  accent-color: #000000 !important;
}

input[type='radio']:disabled:checked {
  opacity: 1 !important;
  accent-color: #000000 !important;
}

.MuiRadio-root.Mui-disabled {
  color: #000000 !important;
  opacity: 1 !important;
}

.MuiRadio-root.Mui-disabled.Mui-checked {
  color: #000000 !important;
  opacity: 1 !important;
}

.MuiRadio-root.Mui-disabled .MuiSvgIcon-root {
  color: #000000 !important;
  opacity: 1 !important;
}

.MuiFormControlLabel-root .MuiRadio-root.Mui-disabled {
  color: #000000 !important;
  opacity: 1 !important;
}

.MuiFormControlLabel-root .MuiRadio-root.Mui-disabled.Mui-checked {
  color: #000000 !important;
  opacity: 1 !important;
}

.MuiFormControlLabel-root .MuiRadio-root.Mui-disabled .MuiSvgIcon-root {
  color: #000000 !important;
  opacity: 1 !important;
}

[data-readonly='true'] .MuiRadio-root,
[readonly] .MuiRadio-root,
.pointer-events-none .MuiRadio-root {
  color: #000000 !important;
  opacity: 1 !important;
}

[data-readonly='true'] .MuiRadio-root.Mui-checked,
[readonly] .MuiRadio-root.Mui-checked,
.pointer-events-none .MuiRadio-root.Mui-checked {
  color: #000000 !important;
  opacity: 1 !important;
}

[data-readonly='true'] .MuiRadio-root .MuiSvgIcon-root,
[readonly] .MuiRadio-root .MuiSvgIcon-root,
.pointer-events-none .MuiRadio-root .MuiSvgIcon-root {
  color: #000000 !important;
  opacity: 1 !important;
}

.MuiRadio-root[style*='color: rgb(158, 158, 158)'],
.MuiRadio-root[style*='color: #9e9e9e'],
.MuiRadio-root[style*='color: grey'],
.MuiRadio-root[style*='color: gray'] {
  color: #000000 !important;
}

.MuiRadio-root .MuiSvgIcon-root[style*='color: rgb(158, 158, 158)'],
.MuiRadio-root .MuiSvgIcon-root[style*='color: #9e9e9e'],
.MuiRadio-root .MuiSvgIcon-root[style*='color: grey'],
.MuiRadio-root .MuiSvgIcon-root[style*='color: gray'] {
  color: #000000 !important;
}

.readonly-form .MuiRadio-root,
.readonly-form .MuiRadio-root.Mui-disabled,
.readonly-form .MuiRadio-root.Mui-checked,
.readonly-form .MuiRadio-root.Mui-disabled.Mui-checked {
  color: #000000 !important;
  opacity: 1 !important;
}

.readonly-form .MuiRadio-root .MuiSvgIcon-root,
.readonly-form .MuiRadio-root.Mui-disabled .MuiSvgIcon-root,
.readonly-form .MuiRadio-root.Mui-checked .MuiSvgIcon-root,
.readonly-form .MuiRadio-root.Mui-disabled.Mui-checked .MuiSvgIcon-root {
  color: #000000 !important;
  opacity: 1 !important;
}

.readonly-form .MuiRadio-root.Mui-checked .MuiSvgIcon-root,
.readonly-form .MuiRadio-root.Mui-disabled.Mui-checked .MuiSvgIcon-root,
.MuiRadio-root.Mui-disabled.Mui-checked .MuiSvgIcon-root,
.MuiRadio-root.Mui-checked .MuiSvgIcon-root {
  font-weight: bold !important;
  stroke-width: 2px !important;
  filter: brightness(0.8) !important;
}

.MuiFormControlLabel-root .MuiRadio-root.Mui-disabled.Mui-checked,
.MuiFormControlLabel-root .MuiRadio-root.Mui-checked {
  font-weight: bold !important;
}

.MuiFormControlLabel-root
  .MuiRadio-root.Mui-disabled.Mui-checked
  .MuiSvgIcon-root,
.MuiFormControlLabel-root .MuiRadio-root.Mui-checked .MuiSvgIcon-root {
  font-weight: bold !important;
  stroke-width: 2px !important;
  filter: brightness(0.8) contrast(1.2) !important;
}

.readonly-form .MuiFormControlLabel-root .MuiRadio-root,
.readonly-form .MuiFormControlLabel-root .MuiRadio-root.Mui-disabled,
.readonly-form .MuiFormControlLabel-root .MuiRadio-root.Mui-checked,
.readonly-form
  .MuiFormControlLabel-root
  .MuiRadio-root.Mui-disabled.Mui-checked {
  color: #000000 !important;
  opacity: 1 !important;
}

.readonly-form .MuiFormControlLabel-root .MuiRadio-root .MuiSvgIcon-root,
.readonly-form
  .MuiFormControlLabel-root
  .MuiRadio-root.Mui-disabled
  .MuiSvgIcon-root,
.readonly-form
  .MuiFormControlLabel-root
  .MuiRadio-root.Mui-checked
  .MuiSvgIcon-root,
.readonly-form
  .MuiFormControlLabel-root
  .MuiRadio-root.Mui-disabled.Mui-checked
  .MuiSvgIcon-root {
  color: #000000 !important;
  opacity: 1 !important;
}

.custom-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.custom-checkboxhead:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
