import React, { useCallback, useState, useEffect, useMemo } from 'react';

import { Controller, useFieldArray, useFormContext } from 'react-hook-form';

import { Box, Typography, CircularProgress } from '@mui/material';
import { useQuery } from '@tanstack/react-query';

import { allowNumbersWithDecimals } from '@/utils/validation';

import AppIcon from '@/core/components/app-icon';
import AppIconButton from '@/core/components/app-icon-button';
import AppSelect from '@/core/components/app-select';
import AppTextField from '@/core/components/app-text-field';
import AppTimeRange from '@/core/components/app-time-range';
import DeleteModal from '@/core/components/delete-modal';
import TableV2 from '@/core/components/table-v2';
import { HeaderV2, RowV2 } from '@/core/components/table-v2/types';
import { api } from '@/core/lib/interceptor';
import { GroupedTableField as GroupedTableFieldType } from '@/types/emr/lifestyle/questionnaire';

import { FieldComponentProps } from './types';

export const GroupedTableField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  readonly,
}) => {
  const tableField = field as GroupedTableFieldType;
  const [hasInitialized, setHasInitialized] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [rowToDelete, setRowToDelete] = useState<{
    groupIndex: number;
    rowIndex: number;
    rowData: any;
  } | null>(null);

  const { fields, append, replace } = useFieldArray({
    control,
    name: name,
  });

  const addRow = useCallback(
    (groupIndex: number) => {
      const newRow: Record<string, any> = {};

      tableField.headers.forEach((header) => {
        if (header.type === 'time_range') {
          newRow[header.id] = { from: '', to: '' };
        } else if (header.type === 'select') {
          newRow[header.id] = '';
        } else {
          newRow[header.id] = '';
        }
      });

      const updatedFields = fields.map((group: any, index) => {
        if (index === groupIndex) {
          const originalMealGroup = tableField.mealGroups?.find(
            (mealGroup) => mealGroup.id === group.id
          );

          return {
            id: originalMealGroup?.id || group.id,
            label: originalMealGroup?.label || group.label,
            rows: [...(group.rows || []), newRow],
          };
        }
        return group;
      });

      replace(updatedFields);
    },
    [fields, tableField.headers, tableField.mealGroups, replace]
  );

  const handleDeleteClick = useCallback(
    (groupIndex: number, rowIndex: number) => {
      const group = fields[groupIndex] as any;
      const rowData = group.rows[rowIndex];
      setRowToDelete({ groupIndex, rowIndex, rowData });
      setDeleteModalOpen(true);
    },
    [fields]
  );

  const handleConfirmDelete = useCallback(() => {
    if (rowToDelete !== null) {
      const updatedFields = fields.map((group: any, index) => {
        if (index === rowToDelete.groupIndex) {
          const originalMealGroup = tableField.mealGroups?.find(
            (mealGroup) => mealGroup.id === group.id
          );

          const updatedRows = group.rows.filter(
            (_: any, rowIndex: number) => rowIndex !== rowToDelete.rowIndex
          );

          return {
            id: originalMealGroup?.id || group.id,
            label: originalMealGroup?.label || group.label,
            rows: updatedRows,
          };
        }
        return group;
      });

      replace(updatedFields);
      setDeleteModalOpen(false);
      setRowToDelete(null);
    }
  }, [rowToDelete, fields, tableField.mealGroups, replace]);

  const handleCancelDelete = useCallback(() => {
    setDeleteModalOpen(false);
    setRowToDelete(null);
  }, []);

  const getRowDisplayName = useCallback(() => {
    if (!rowToDelete?.rowData) return 'this entry';
    return rowToDelete.rowData.food_item || 'this entry';
  }, [rowToDelete]);

  const { setValue, watch } = useFormContext();

  // Updated SearchableSelectCell component for GroupedTableField
  const SearchableSelectCell = ({ name, header, control, disabled }: any) => {
    const [searchQuery, setSearchQuery] = React.useState('');
    const [menuIsOpen, setMenuIsOpen] = React.useState(false);
    const [selectedOption, setSelectedOption] = React.useState<any>(null);

    const fetchOptions = async (inputValue: string) => {
      if (!header.fetchOptions) return [];

      const {
        endpoint,
        method = 'GET',
        queryParam = 'input',
      } = header.fetchOptions;

      try {
        const params: Record<string, string> = {};
        if (inputValue.trim()) {
          params[queryParam] = inputValue;
        }

        const response = await api({
          method,
          url: endpoint,
          params,
        });

        // Try different response formats
        const items = response.data?.data || response.data || [];
        const foodItems = Array.isArray(items) ? items : [items];

        return foodItems.map((item: any) => ({
          value: item.food_name || item.value || item.label || '',
          label: item.food_name || item.label || item.value || '',
        }));
      } catch (error) {
        console.error('Error fetching options:', error);
        return [];
      }
    };

    const { data: options = [], isLoading } = useQuery({
      queryKey: ['searchable-select', header.id, searchQuery],
      queryFn: () => fetchOptions(searchQuery),
      enabled: true,
    });

    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { onChange, value } }) => {
          return (
            <Box
              sx={{ width: '100%', backgroundColor: 'white', borderRadius: 1 }}
            >
              <AppSelect
                value={
                  selectedOption ||
                  options.find((opt: any) => opt.value === value) ||
                  null
                }
                onChange={(selectedOption: any) => {
                  setSelectedOption(selectedOption);
                  onChange(selectedOption?.value || '');
                  setMenuIsOpen(false); // Close menu after selection
                }}
                onMenuOpen={() => {
                  setMenuIsOpen(true);
                  // Refresh options when opening menu
                  setSearchQuery('');
                }}
                onMenuClose={() => setMenuIsOpen(false)}
                onInputChange={(newValue) => {
                  setSearchQuery(newValue);
                  // Clear selected option when typing to avoid stale references
                  if (newValue !== selectedOption?.label) {
                    setSelectedOption(null);
                  }
                }}
                options={options}
                placeholder={header.placeholder || 'Search...'}
                isLoading={isLoading}
                isDisabled={disabled}
                isClearable
                isSearchable
                showSearchIcon={true} // Enable search icon
                menuIsOpen={menuIsOpen}
                menuPosition="fixed"
                menuPortalTarget={
                  (document.getElementById('app-modal') as HTMLElement) ||
                  document.body
                }
                styles={{
                  control: (base, state) => ({
                    ...base,
                    width: '100%',
                    minWidth: '180px',
                    minHeight: '36px',
                    backgroundColor: 'white',
                    borderColor: state.isFocused ? 'primary.main' : 'grey.300',
                    boxShadow: state.isFocused
                      ? '0 0 0 1px primary.main'
                      : 'none',
                    '&:hover': {
                      borderColor: 'primary.main',
                    },
                  }),
                  valueContainer: (base) => ({
                    ...base,
                    padding: '2px 8px',
                  }),
                  input: (base) => ({
                    ...base,
                    margin: 0,
                    padding: '8px 2px',
                  }),
                  singleValue: (base) => ({
                    ...base,
                    maxWidth: 'calc(100% - 32px)', // Adjusted to account for search icon
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }),
                  menu: (base) => ({
                    ...base,
                    zIndex: 9999,
                    marginTop: '4px',
                    boxShadow:
                      '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                  }),
                  option: (base, state) => ({
                    ...base,
                    padding: '8px 12px',
                    backgroundColor: state.isSelected
                      ? 'primary.light'
                      : 'white',
                    color: state.isSelected ? 'white' : 'text.primary',
                    '&:hover': {
                      backgroundColor: 'grey.100',
                    },
                  }),
                }}
                // Remove DropdownIndicator and IndicatorSeparator from components
                // since we're using showSearchIcon
                components={{
                  IndicatorSeparator: null,
                }}
              />
            </Box>
          );
        }}
      />
    );
  };

  // Helper component for dependent autofill
  const DependentAutofillCell = ({
    name,
    header,
    control,
    rowIndex,
    groupIndex,
    disabled,
  }: any) => {
    const dependsOnField = tableField.headers.find(
      (h) => h.id === header.dependsOn
    );
    const dependsOnValue = watch(
      `${name}.${groupIndex}.rows.${rowIndex}.${header.dependsOn}`
    );
    const [isLoading, setIsLoading] = React.useState(false);
    const [fieldValue, setFieldValue] = React.useState('');
    const { setValue } = useFormContext();

    useEffect(() => {
      const fetchDependentData = async () => {
        if (!dependsOnValue || !header.fetchDependentData) return;

        const {
          endpoint,
          method = 'GET',
          paramName = 'foodId',
          fieldMapping,
        } = header.fetchDependentData;

        try {
          setIsLoading(true);
          const response = await api({
            method,
            url: endpoint,
            params: {
              [paramName]: dependsOnValue,
            },
          });

          if (response.data) {
            const data = response.data.data || response.data;

            // Set servings_unit if it exists in the response
            if (data.servings_unit) {
              const unitFieldName = `${name}.${groupIndex}.rows.${rowIndex}.servings_unit`;
              setValue(unitFieldName, data.servings_unit, {
                shouldValidate: true,
              });
              setFieldValue(data.servings_unit);
            }

            // Set the current field value if it exists in the response
            const fieldName = `${name}.${groupIndex}.rows.${rowIndex}.${header.id}`;

            // If there's a direct mapping for this field
            if (fieldMapping?.serving_type && data[fieldMapping.serving_type]) {
              const value = data[fieldMapping.serving_type];
              setValue(fieldName, value, { shouldValidate: true });
              setFieldValue(value);
            }
            // Fallback to first available value if no direct mapping
            else if (Object.values(data).length > 0) {
              const value = Object.values(data)[0] as string;
              if (typeof value === 'string') {
                setValue(fieldName, value, { shouldValidate: true });
                setFieldValue(value);
              }
            }
          }
        } catch (error) {
          console.error('Error fetching dependent data:', error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchDependentData();
    }, [
      dependsOnValue,
      header.fetchDependentData,
      groupIndex,
      rowIndex,
      header.id,
      name,
      setValue,
    ]);

    return (
      <Box sx={{ width: '100%' }}>
        <AppTextField
          value={fieldValue || ''}
          fullWidth
          size="small"
          // variant="outlined"
          disabled={true}
          placeholder={isLoading ? 'Loading...' : ''}
          InputProps={{
            readOnly: true,
            endAdornment: isLoading ? <CircularProgress size={20} /> : null,
            sx: {
              '& .MuiOutlinedInput-input': {
                backgroundColor: 'white',
                color: 'text.primary',
                cursor: 'default',
                opacity: 1,
              },
            },
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              '& fieldset': {
                borderColor: 'divider',
              },
              '&:hover fieldset': {
                borderColor: 'divider',
              },
              '&.Mui-focused fieldset': {
                borderColor: 'divider',
              },
              backgroundColor: 'white',
              '&.Mui-disabled': {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'divider !important',
                },
                '& .MuiInputBase-input': {
                  color: 'text.primary',
                  WebkitTextFillColor: 'text.primary',
                },
              },
            },
          }}
        />
      </Box>
    );
  };

  const { headers, rows } = useMemo(() => {
    // Filter out icon column in readonly mode and adjust headers
    const filteredHeaders = readonly
      ? tableField.headers.filter((header) => header.type !== 'icon')
      : tableField.headers;

    const tableHeaders: HeaderV2[] = readonly
      ? [
          {
            key: 'meal',
            header: 'Meal',
            cellProps: { align: 'center' as const },
          },
          ...filteredHeaders.map((header) => ({
            key: header.id,
            header: header.label,
            cellProps: { align: 'center' as const },
          })),
        ]
      : tableField.headers.map((header) => ({
          key: header.id,
          header: header.label,
          cellProps: { align: 'center' as const },
        }));

    const tableRows: RowV2[] = [];

    if (readonly) {
      fields.forEach((group: any, groupIndex) => {
        if (group.rows && group.rows.length > 0) {
          group.rows.forEach((_: any, rowIndex: number) => {
            const tableRow: RowV2 = {
              key: `${group.id}-${rowIndex}`,
            };

            tableRow['meal'] = {
              value: <Typography variant="body2">{group.label}</Typography>,
              cellProps: { align: 'center' as const },
            };

            filteredHeaders.forEach((header) => {
              tableRow[header.id] = {
                value: (
                  <Controller
                    name={`${name}.${groupIndex}.rows.${rowIndex}.${header.id}`}
                    control={control}
                    defaultValue={
                      header.type === 'time_range' ? { from: '', to: '' } : ''
                    }
                    render={({ field: controllerField }) => {
                      // In readonly mode, just show the value
                      if (readonly) {
                        if (header.type === 'time_range') {
                          return (
                            <Typography variant="body2">
                              {controllerField.value?.from &&
                              controllerField.value?.to
                                ? `${controllerField.value.from} - ${controllerField.value.to}`
                                : '-'}
                            </Typography>
                          );
                        }
                        if (
                          header.type === 'select' ||
                          header.type === 'searchable_select'
                        ) {
                          type OptionType =
                            | string
                            | { value: string; label: string };
                          const value = controllerField.value as string;
                          const options = (header.options ||
                            []) as OptionType[];

                          if (!options.length) {
                            return (
                              <Typography variant="body2">
                                {value || '-'}
                              </Typography>
                            );
                          }

                          // Find the matching option
                          const option = options.find((opt) =>
                            typeof opt === 'string'
                              ? opt === value
                              : opt.value === value
                          );

                          // Get display value based on option type
                          const displayValue =
                            option === undefined
                              ? value
                              : typeof option === 'string'
                                ? option
                                : option.label;

                          return (
                            <Typography variant="body2">
                              {displayValue || value || '-'}
                            </Typography>
                          );
                        }
                        return (
                          <Typography variant="body2">
                            {controllerField.value || '-'}
                          </Typography>
                        );
                      }

                      // Editable mode - show form controls
                      if (header.type === 'time_range') {
                        return (
                          <AppTimeRange
                            value={controllerField.value}
                            onChange={controllerField.onChange}
                            disabled={readonly}
                            slotProps={{
                              select: {
                                menuPosition: 'fixed',
                                menuPortalTarget:
                                  (document.getElementById(
                                    'app-modal'
                                  ) as HTMLElement) || document.body,
                              },
                            }}
                          />
                        );
                      } else if (header.type === 'searchable_select') {
                        return (
                          <SearchableSelectCell
                            name={`${name}.${groupIndex}.rows.${rowIndex}.${header.id}`}
                            header={header}
                            control={control}
                            disabled={readonly}
                          />
                        );
                      } else if (header.type === 'dependent_autofill') {
                        return (
                          <DependentAutofillCell
                            name={name}
                            header={header}
                            control={control}
                            rowIndex={rowIndex}
                            groupIndex={groupIndex}
                            disabled={readonly}
                          />
                        );
                      } else if (header.type === 'select' && header.options) {
                        return (
                          <AppSelect
                            {...controllerField}
                            value={controllerField.value || ''}
                            options={
                              Array.isArray(header.options)
                                ? header.options.map((option) => ({
                                    label: option,
                                    value: option,
                                  }))
                                : []
                            }
                            isDisabled={readonly}
                            formControlProps={{ sx: { minWidth: 200 } }}
                            menuPosition="fixed"
                            menuPortalTarget={
                              (document.getElementById(
                                'app-modal'
                              ) as HTMLElement) || document.body
                            }
                            id={`group-${group.id}-row-${rowIndex}-field-${header.id}`}
                          />
                        );
                      } else if (header.type === 'number') {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            type="number"
                            size="small"
                            fullWidth
                            disabled={readonly}
                            slotProps={{
                              htmlInput: { min: header.min || 0 },
                            }}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'white !important',
                              },
                            }}
                          />
                        );
                      } else {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            size="small"
                            fullWidth
                            disabled={readonly}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: readonly
                                  ? '#f9fafb !important'
                                  : 'white !important',
                              },
                            }}
                          />
                        );
                      }
                    }}
                  />
                ),
                cellProps: { align: 'center' as const },
              };
            });

            tableRows.push(tableRow);
          });
        }
      });
    } else {
      // Edit mode: Show group headers with add buttons
      fields.forEach((group: any, groupIndex) => {
        const groupHeaderRow: RowV2 = {
          key: `group-${group.id}`,
        };

        tableField.headers.forEach((header, index) => {
          if (index === 0) {
            groupHeaderRow[header.id] = {
              value: (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '100%',
                  }}
                >
                  <Typography variant="h6" fontWeight="medium">
                    {group.label}
                  </Typography>
                  <AppIconButton
                    onClick={() => addRow(groupIndex)}
                    variant="outlined"
                    color="info"
                    sx={{ borderRadius: 1, px: 2 }}
                  >
                    <AppIcon icon="humbleicons:plus" />
                  </AppIconButton>
                </Box>
              ),
              cellProps: {
                colSpan: tableField.headers.length,
                sx: {
                  backgroundColor: '#f9fafb',
                  borderBottom: '2px solid #e5e7eb',
                  py: '5px !important',
                },
              },
            };
          } else {
            groupHeaderRow[header.id] = {
              value: null,
              returnNullForEmpty: true,
            };
          }
        });

        tableRows.push(groupHeaderRow);

        if (group.rows && group.rows.length > 0) {
          group.rows.forEach((_: any, rowIndex: number) => {
            const tableRow: RowV2 = {
              key: `${group.id}-${rowIndex}`,
            };

            tableField.headers.forEach((header) => {
              tableRow[header.id] = {
                value: (
                  <Controller
                    name={`${name}.${groupIndex}.rows.${rowIndex}.${header.id}`}
                    control={control}
                    defaultValue={
                      header.type === 'time_range' ? { from: '', to: '' } : ''
                    }
                    render={({ field: controllerField }) => {
                      if (header.type === 'time_range') {
                        return (
                          <AppTimeRange
                            value={controllerField.value}
                            onChange={controllerField.onChange}
                            disabled={readonly}
                            slotProps={{
                              select: {
                                menuPosition: 'fixed',
                                menuPortalTarget:
                                  (document.getElementById(
                                    'app-modal'
                                  ) as HTMLElement) || document.body,
                              },
                            }}
                          />
                        );
                      } else if (header.type === 'searchable_select') {
                        return (
                          <SearchableSelectCell
                            name={`${name}.${groupIndex}.rows.${rowIndex}.${header.id}`}
                            header={header}
                            control={control}
                            disabled={readonly}
                          />
                        );
                      } else if (header.type === 'dependent_autofill') {
                        return (
                          <DependentAutofillCell
                            name={name}
                            header={header}
                            control={control}
                            rowIndex={rowIndex}
                            groupIndex={groupIndex}
                            disabled={readonly}
                          />
                        );
                      } else if (header.type === 'select' && header.options) {
                        return (
                          <AppSelect
                            {...controllerField}
                            value={controllerField.value || ''}
                            options={
                              Array.isArray(header.options)
                                ? header.options.map((option: string) => ({
                                    label: option,
                                    value: option,
                                  }))
                                : []
                            }
                            isDisabled={readonly}
                            formControlProps={{ sx: { minWidth: 200 } }}
                            menuPosition="fixed"
                            menuPortalTarget={
                              (document.getElementById(
                                'app-modal'
                              ) as HTMLElement) || document.body
                            }
                            id={`group-${group.id}-row-${rowIndex}-field-${header.id}`}
                          />
                        );
                      } else if (header.type === 'number') {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            size="small"
                            fullWidth
                            disabled={readonly}
                            slotProps={{
                              htmlInput: {
                                min: header.min || 0,
                                onKeyDown: allowNumbersWithDecimals,
                              },
                            }}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: readonly
                                  ? '#f9fafb !important'
                                  : 'white !important',
                              },
                            }}
                          />
                        );
                      } else if (header.type === 'icon') {
                        return (
                          <div>
                            <AppIconButton
                              onClick={() =>
                                handleDeleteClick(groupIndex, rowIndex)
                              }
                              variant="outlined"
                              color="error"
                            >
                              <AppIcon icon="ic:round-close" />
                            </AppIconButton>
                          </div>
                        );
                      } else {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            size="small"
                            fullWidth
                            disabled={readonly}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: readonly
                                  ? '#f9fafb !important'
                                  : 'white !important',
                              },
                            }}
                          />
                        );
                      }
                    }}
                  />
                ),
                cellProps: { align: 'center' as const },
              };
            });

            tableRows.push(tableRow);
          });
        } else {
          tableRows.push({
            key: `${group.id}-empty`,
            ...tableField.headers.reduce(
              (acc, header, index) => {
                if (index === 0) {
                  acc[header.id] = {
                    value: (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        fontStyle="italic"
                      >
                        No entries for {group.label}
                      </Typography>
                    ),
                    cellProps: {
                      colSpan: tableField.headers.length,
                      align: 'center' as const,
                      sx: { py: 2 },
                    },
                  };
                } else {
                  acc[header.id] = {
                    value: null,
                    returnNullForEmpty: true,
                  };
                }
                return acc;
              },
              {} as Record<string, any>
            ),
          });
        }
      });
    }

    return { headers: tableHeaders, rows: tableRows };
  }, [fields, tableField, name, control, readonly, addRow, handleDeleteClick]);

  useEffect(() => {
    if (!hasInitialized) {
      if (fields.length === 0 && tableField.mealGroups) {
        tableField.mealGroups.forEach((group) => {
          append({
            id: group.id,
            label: group.label,
            rows: group.defaultRows || [],
          });
        });
      }
      setHasInitialized(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasInitialized, tableField.mealGroups, append]);

  return (
    <div className="space-y-6">
      <TableV2
        headers={headers}
        rows={rows}
        tableContainerProps={{
          sx: {
            '& .MuiTableCell-root': {
              padding: '12px 16px',
            },
            '& thead th': {
              fontSize: '14px',
              fontWeight: 600, // semi-bold
            },
            '& tbody tr': {
              backgroundColor: 'white !important',
            },
            ...(readonly && {
              '& .MuiTableHead-root .MuiTableCell-root': {
                backgroundColor: '#64707D',
              },
            }),
          },
        }}
        noDataMessage={
          <Typography variant="body2" color="text.secondary">
            No data available
          </Typography>
        }
      />

      <DeleteModal
        open={deleteModalOpen}
        onClose={handleCancelDelete}
        onDelete={handleConfirmDelete}
        confirmationMessage={`Are you sure you want to delete ${getRowDisplayName()}?`}
      />
    </div>
  );
};
