import React from 'react';

import { Box } from '@mui/material';
import { LuX } from 'react-icons/lu';

import { DiagnosisRecord } from '@/store/extraNoteDiagnosisStore';

import OutLinedIconButton from '../lifestyle/lifestyle-forms/shared/OutlinedIconButton';

import ActivityStatusSelector from './ActivityStatusSelector';
import DiagnosisStatusSelector from './DiagnosisStatusSelector';

interface DiagnosisEntryProps {
  diagnosis: DiagnosisRecord;
  onStatusChange: (
    recordId: string,
    field: 'diagnosisStatus' | 'activityStatus',
    value: 'provisional' | 'confirmed' | 'active' | 'inactive'
  ) => void;
  onRemove?: (recordId: string) => void;
  showRemoveButton?: boolean;
  isEditable?: boolean;
  isSavedRecord?: boolean;
}

const extractTextFromHtml = (htmlContent: string): string => {
  if (!htmlContent) return '';

  try {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';
    const cleanText = textContent.trim();

    if (!cleanText || cleanText.length === 0) {
      return 'Empty diagnosis entry';
    }

    return cleanText;
  } catch (error) {
    console.error('Error extracting text from HTML:', error);
    return 'Error parsing diagnosis content';
  }
};

const DiagnosisEntry: React.FC<DiagnosisEntryProps> = ({
  diagnosis,
  onStatusChange,
  onRemove,
  showRemoveButton = false,
  isEditable = true,
  isSavedRecord = false,
}) => {
  const displayText = extractTextFromHtml(diagnosis.content);
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '12px 0px',
        mb: 1,
        ml: 2,
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1 }}>
        <Box sx={{ display: 'flex', gap: '10px' }}>
          <DiagnosisStatusSelector
            value={diagnosis.diagnosisStatus}
            onChange={(value) =>
              onStatusChange(diagnosis.record_id, 'diagnosisStatus', value)
            }
            disabled={!isEditable}
            className="h-6 text-sm min-w-[70px]"
            isSavedRecord={isSavedRecord}
          />
          <ActivityStatusSelector
            value={diagnosis.activityStatus}
            onChange={(value) =>
              onStatusChange(diagnosis.record_id, 'activityStatus', value)
            }
            disabled={!isEditable}
            className="h-6 text-sm min-w-[55px]"
          />
        </Box>

        <Box sx={{ flex: 1, ml: 2 }}>
          <span
            className="text-black"
            style={{
              fontSize: '14px',
              fontWeight: 'normal',
              fontStyle: 'normal',
            }}
          >
            {displayText}
          </span>
        </Box>

        <Box sx={{ minWidth: '80px', textAlign: 'right', mr: 2 }}>
          <span className="text-xs text-gray-500">
            {diagnosis.timestamp
              ? new Date(diagnosis.timestamp).toLocaleDateString('en-US', {
                  day: 'numeric',
                  month: 'short',
                  year: 'numeric',
                })
              : ''}
          </span>
        </Box>
      </Box>

      {showRemoveButton && onRemove && (
        <OutLinedIconButton
          onClick={() => onRemove(diagnosis.record_id)}
          sx={{ width: 24, height: 24, ml: 2 }}
        >
          <LuX size={12} />
        </OutLinedIconButton>
      )}
    </Box>
  );
};

export default DiagnosisEntry;
