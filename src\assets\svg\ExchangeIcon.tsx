import { IconProps } from '@/types/icon';

export default function ExchangeIcon(props: IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_40002223_3026)">
        <path
          d="M5.82499 16L7.69999 17.875C7.88332 18.0584 7.97499 18.2875 7.97499 18.5625C7.97499 18.8375 7.88332 19.075 7.69999 19.275C7.49999 19.475 7.26249 19.575 6.98749 19.575C6.71249 19.575 6.47499 19.475 6.27499 19.275L2.69999 15.7C2.59999 15.6 2.52915 15.4917 2.48749 15.375C2.44582 15.2584 2.42499 15.1334 2.42499 15C2.42499 14.8667 2.44582 14.7417 2.48749 14.625C2.52915 14.5084 2.59999 14.4 2.69999 14.3L6.29999 10.7C6.49999 10.5 6.73332 10.4042 6.99999 10.4125C7.26665 10.4209 7.49999 10.525 7.69999 10.725C7.88332 10.925 7.97915 11.1584 7.98749 11.425C7.99582 11.6917 7.89999 11.925 7.69999 12.125L5.82499 14H12C12.2833 14 12.5208 14.0959 12.7125 14.2875C12.9042 14.4792 13 14.7167 13 15C13 15.2834 12.9042 15.5209 12.7125 15.7125C12.5208 15.9042 12.2833 16 12 16H5.82499ZM18.175 10H12C11.7167 10 11.4792 9.90422 11.2875 9.71255C11.0958 9.52088 11 9.28338 11 9.00005C11 8.71672 11.0958 8.47922 11.2875 8.28755C11.4792 8.09588 11.7167 8.00005 12 8.00005H18.175L16.3 6.12505C16.1167 5.94172 16.025 5.71255 16.025 5.43755C16.025 5.16255 16.1167 4.92505 16.3 4.72505C16.5 4.52505 16.7375 4.42505 17.0125 4.42505C17.2875 4.42505 17.525 4.52505 17.725 4.72505L21.3 8.30005C21.4 8.40005 21.4708 8.50838 21.5125 8.62505C21.5542 8.74172 21.575 8.86672 21.575 9.00005C21.575 9.13338 21.5542 9.25838 21.5125 9.37505C21.4708 9.49172 21.4 9.60005 21.3 9.70005L17.7 13.3C17.5 13.5 17.2667 13.5959 17 13.5875C16.7333 13.5792 16.5 13.475 16.3 13.275C16.1167 13.075 16.0208 12.8417 16.0125 12.575C16.0042 12.3084 16.1 12.075 16.3 11.875L18.175 10Z"
          fill="#012436"
        />
      </g>
      <defs>
        <clipPath id="clip0_40002223_3026">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
