import React, { memo } from 'react';

import { SvgIcon, SvgIconProps } from '@mui/material';
import { styled, keyframes } from '@mui/system';

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const Spinner = styled(SvgIcon)({
  width: '40px',
  height: '40px',
  animation: `${spin} 1s linear infinite`,
  borderRadius: '50%',
  overflow: 'hidden',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
});

const AppSpinner = (props: SvgIconProps) => {
  return (
    <Spinner viewBox="0 0 32 32" fontSize="inherit" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.70759 13.2166C4.21239 13.1509 5.48553 14.3175 5.55123 15.8223L5.59274 16.773L5.7014 17.5984L5.88159 18.4112L6.13193 19.2051L6.45051 19.9743L6.83492 20.7127L7.28223 21.4148L7.78903 22.0753L8.35146 22.6891L8.96525 23.2515L9.62572 23.7583L10.3278 24.2056L11.0663 24.59L11.8354 24.9086L12.6294 25.159L13.4422 25.3392L14.2676 25.4478L15.0993 25.4841L15.931 25.4478L16.7564 25.3392L17.5691 25.159L18.3631 24.9086L19.1323 24.59L19.8707 24.2056L20.6733 23.6944C21.9436 22.8851 23.6295 23.2588 24.4388 24.5291C25.2481 25.7995 24.8743 27.4854 23.604 28.2947L22.6006 28.9339L21.4396 29.5383L20.2305 30.0391L18.9822 30.4327L17.7044 30.716L16.4068 30.8868L15.0993 30.9439L13.7917 30.8868L12.4941 30.716L11.2163 30.4327L9.96809 30.0391L8.75891 29.5383L7.59798 28.9339L6.49414 28.2307L5.45579 27.4339L4.49084 26.5497L3.60662 25.5848L2.80987 24.5464L2.10665 23.4426L1.50231 22.2817L1.00145 21.0725L0.607881 19.8242L0.324603 18.5465L0.15377 17.2488L0.101874 16.0602C0.0361735 14.5554 1.20279 13.2823 2.70759 13.2166Z"
        fill="#B4E5FE"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.85646 10.0631C2.74595 9.04548 2.67062 7.32031 3.68822 6.2098L4.49199 5.33263L5.45695 4.44842L6.49529 3.65167L7.59913 2.94844L8.76006 2.3441L9.96924 1.84324L11.2175 1.44968L12.4953 1.1664L13.7929 0.995565L15.1004 0.938477L16.408 0.995567L17.7056 1.1664L18.9834 1.44968L20.2316 1.84324L21.4408 2.3441L22.6017 2.94844L23.7056 3.65167L24.7439 4.44842L25.7089 5.33264L26.5931 6.29759L27.3898 7.33594L28.0931 8.43977L28.6974 9.6007L29.1983 10.8099L29.5918 12.0581L29.8751 13.3359L30.0459 14.6335L30.103 15.9411L30.0459 17.2486L29.8751 18.5462L29.6176 19.7078C29.2916 21.1783 27.8352 22.1061 26.3647 21.7801C24.8942 21.4541 23.9663 19.9977 24.2924 18.5272L24.4983 17.5982L24.607 16.7728L24.6433 15.9411L24.607 15.1094L24.4983 14.284L24.3181 13.4712L24.0678 12.6772L23.7492 11.9081L23.3648 11.1696L22.9175 10.4675L22.4107 9.80704L21.8482 9.19325L21.2345 8.63083L20.574 8.12402L19.8719 7.67672L19.1334 7.29231L18.3643 6.97372L17.5703 6.72338L16.7575 6.5432L15.9321 6.43453L15.1004 6.39822L14.2687 6.43453L13.4433 6.5432L12.6306 6.72338L11.8366 6.97372L11.0674 7.29231L10.329 7.67672L9.62687 8.12402L8.9664 8.63082L8.35261 9.19325L7.70973 9.89484C6.69214 11.0053 4.96697 11.0807 3.85646 10.0631Z"
        fill="#012436"
      />
    </Spinner>
  );
};

export default memo(AppSpinner);
