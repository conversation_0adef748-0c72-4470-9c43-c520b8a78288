'use client';

import { useEffect } from 'react';

import { usePathname, useRouter } from 'next/navigation';

import { useHasPermission } from '@/utils/permissions';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  redirectPath?: string;
}

/**
 * Component that protects routes based on user permissions
 * @param children - The content to render if the user has the required permissions
 * @param requiredPermissions - Array of permission keys required to access the route
 * @param redirectPath - Path to redirect to if the user doesn't have the required permissions (default: '/')
 * @example
 * // Protect a route with required permissions
 * <ProtectedRoute requiredPermissions={['mrd.manage-patient.view']}>
 *   <ManagePatients />
 * </ProtectedRoute>
 */
export function ProtectedRoute({
  children,
  requiredPermissions = [],
  redirectPath = '/',
}: ProtectedRouteProps) {
  const hasPermission = useHasPermission(requiredPermissions);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // If no permissions are required, allow access
    if (requiredPermissions.length === 0) {
      return;
    }

    // If user doesn't have permission and is not already on the redirect path, redirect
    if (!hasPermission && pathname !== redirectPath) {
      router.replace(redirectPath);
    }
  }, [
    hasPermission,
    pathname,
    redirectPath,
    requiredPermissions.length,
    router,
  ]);

  // If no permissions are required or user has permission, render children
  if (requiredPermissions.length === 0 || hasPermission) {
    return <>{children}</>;
  }

  // Otherwise, render nothing (will redirect)
  return null;
}
