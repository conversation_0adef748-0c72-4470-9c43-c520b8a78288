'use client';
import React, { FC, ReactNode, useEffect } from 'react';

import { useIsAuthenticated, useMsal } from '@azure/msal-react';

import { useRouter } from 'next/navigation';

const RequireAuth: FC<{ children: ReactNode }> = ({ children }) => {
  const isAuthenticated = useIsAuthenticated();
  const router = useRouter();
  const { instance } = useMsal();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, instance, router]);

  return <div>{children}</div>;
};

export default RequireAuth;
