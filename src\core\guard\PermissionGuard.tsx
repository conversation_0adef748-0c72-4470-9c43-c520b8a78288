'use client';

import { ReactNode } from 'react';

import { useUserStore } from '@/store/userStore';

type PermissionGuardProps = {
  children: ReactNode;
  requiredPermissions: string[];
  fallback?: ReactNode;
};

export default function PermissionGuard({
  children,
  requiredPermissions,
  fallback = null,
}: PermissionGuardProps) {
  const { permissions = [] } = useUserStore();

  const hasPermission = requiredPermissions.some((permission) =>
    permissions.includes(permission)
  );

  if (!hasPermission) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
