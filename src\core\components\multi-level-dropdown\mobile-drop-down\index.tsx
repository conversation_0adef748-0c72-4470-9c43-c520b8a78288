import React, { FC, memo, useEffect, useState } from 'react';
import { useCallback } from 'react';

import Modal, { ModalProps } from '@mui/material/Modal';

import { DropdownMenuOption, DropDownProps } from '../types';

import DropDownItems from './DropDownItems';
import TopNavigation from './TopNavigation';

type MultiDropDownSelectProps = Omit<ModalProps, 'children'> & DropDownProps;

const MobileDropDown: FC<MultiDropDownSelectProps> = ({
  onClose,
  sx,
  options,
  value,
  isMultiLevel,
  isLoading,
  loading,
  maxHeight,
  handleSelect,
  ...rest
}) => {
  const [currentOptions, setCurrentOptions] = useState<
    (DropdownMenuOption[] | string[])[]
  >([options]);

  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [lastSelectedValue, setLastSelectedValue] = useState<string | null>(
    null
  );

  useEffect(() => {
    setCurrentOptions([options]);
  }, [options]);

  useEffect(() => {
    if (!loading && selectedOption) {
      const lastLevel = currentOptions[currentOptions.length - 1];
      const lastSelected = lastLevel.find(
        (opt) => typeof opt === 'object' && opt.value === selectedOption
      ) as DropdownMenuOption | undefined;
      if (
        lastSelected &&
        Array.isArray(lastSelected.subOptions) &&
        lastSelected.subOptions.length > 0
      ) {
        setCurrentOptions((prev) => {
          const alreadyAdded = prev.some(
            (level) => level === lastSelected.subOptions
          );
          if (!alreadyAdded) {
            return [...prev, lastSelected.subOptions!];
          }
          return prev;
        });
        setSelectedOption(null);
      } else if (lastSelected) {
        setLastSelectedValue(lastSelected.value);
      }
    }
  }, [loading, currentOptions, selectedOption]);

  const handleOnClose = useCallback(
    (
      e: React.MouseEvent<HTMLButtonElement> | {} = {},
      reason: 'backdropClick' | 'escapeKeyDown' = 'backdropClick'
    ) => {
      if (lastSelectedValue) {
        let key: string | undefined;

        const findKey = (options: DropdownMenuOption[]): boolean => {
          for (const option of options) {
            if (option.value === lastSelectedValue) {
              key = option.key as string;
              return true;
            }
            if (
              Array.isArray(option.subOptions) &&
              option.subOptions.length > 0 &&
              typeof option.subOptions[0] === 'object'
            ) {
              const found = findKey(option.subOptions as DropdownMenuOption[]);
              if (found) return true;
            }
          }
          return false;
        };

        if (
          Array.isArray(options) &&
          options.length > 0 &&
          typeof options[0] === 'object'
        ) {
          findKey(options as DropdownMenuOption[]);
        }

        handleSelect(lastSelectedValue, key);
      }

      setCurrentOptions([options]);
      setSelectedOption(null);
      setLastSelectedValue(null);
      onClose?.(e, reason);
    },
    [options, onClose, handleSelect, lastSelectedValue]
  );

  const latestOptions = currentOptions[currentOptions.length - 1];

  const isSelected = useCallback(
    (option: DropdownMenuOption | string): boolean => {
      if (typeof option === 'object' && option?.subOptions?.length) {
        return (
          option?.subOptions?.some((subOption) =>
            typeof subOption === 'object' && subOption !== null
              ? isSelected(subOption)
              : false
          ) || false
        );
      }
      return (
        typeof option === 'object' &&
        'value' in option &&
        option.value === value
      );
    },
    [value]
  );

  const handleOnSelect = useCallback(
    (option: DropdownMenuOption | string, key?: string) => {
      if (typeof option === 'string') {
        handleSelect(option, key);
        handleOnClose();
        return;
      }

      const isMainLevel = currentOptions.length === 1;

      if (option.subOptions && option.subOptions.length > 0) {
        setCurrentOptions((prev) => [...prev, option.subOptions!]);
      } else if (isMainLevel) {
        setSelectedOption(option.value);
        handleSelect(option.value, option.key as string);
      } else {
        setLastSelectedValue(option.value);
        handleSelect(option.value, option.key as string, true);
        handleOnClose();
      }
    },
    [handleSelect, handleOnClose, currentOptions]
  );

  const handleOnBack = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      setCurrentOptions((prev) => {
        if (prev.length === 1) {
          handleOnClose(e);
          return prev;
        }
        return prev.slice(0, prev.length - 1);
      });
      setSelectedOption(null);
    },
    [handleOnClose]
  );

  const shouldShowArrow = useCallback(
    (option: DropdownMenuOption | string): boolean => {
      if (typeof option === 'string') {
        return false;
      }

      const isMainLevel = currentOptions.length === 1;
      if (isMainLevel) {
        return true;
      }

      return !!(option.subOptions && option.subOptions.length > 0);
    },
    [currentOptions]
  );

  return (
    <Modal
      {...rest}
      onClose={handleOnClose}
      sx={{
        ...sx,
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <div className="w-[90vw] h-[80vh]">
        <TopNavigation
          onClickBack={handleOnBack}
          onClose={(e) => onClose?.(e, 'backdropClick')}
        />
        <div className="bg-white w-full h-[calc(100%-2.5rem)] max-h-[calc(100%-2.5rem)] overflow-y-auto p-2 rounded drop-down-items">
          {latestOptions?.map((option, index) => {
            const optionValue =
              typeof option === 'object' && option !== null
                ? option.value
                : option;
            const showLoading = loading && selectedOption === optionValue;

            const hasSubOption =
              typeof option === 'object' ? shouldShowArrow(option) : false;

            if (typeof option === 'object') {
              return (
                <DropDownItems
                  key={index}
                  value={optionValue}
                  selected={isSelected(option)}
                  lastItem={index === latestOptions.length - 1}
                  onClick={() => handleOnSelect(option, option?.key)}
                  hasSubOption={hasSubOption && isMultiLevel}
                  isLoading={showLoading}
                />
              );
            }

            return (
              <DropDownItems
                key={index}
                value={optionValue}
                selected={isSelected(option)}
                lastItem={index === latestOptions.length - 1}
                onClick={() => handleOnSelect(option)}
                hasSubOption={false}
                isLoading={false}
              />
            );
          })}
        </div>
      </div>
    </Modal>
  );
};

export default memo(MobileDropDown);
