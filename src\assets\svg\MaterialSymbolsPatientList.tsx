import React from 'react';
import type { SVGProps } from 'react';

const MaterialSymbolsPatientList = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    viewBox="0 0 24 24"
    {...props}
  >
    <path
      fill="#000"
      d="M16 14q-1.25 0-2.125-.875T13 11t.875-2.125T16 8t2.125.875T19 11t-.875 2.125T16 14m-6 6v-1.9q0-.525.25-1t.7-.75q1.125-.675 2.388-1.012T16 15t2.663.338t2.387 1.012q.45.275.7.75t.25 1V20zm-7-6v-2h8v2zm0-8V4h12v2zm8.1 4H3V8h9q-.35.425-.562.925T11.1 10"
    />
  </svg>
);

export default MaterialSymbolsPatientList;
