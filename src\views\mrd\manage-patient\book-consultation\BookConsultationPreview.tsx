import React, { memo } from 'react';

import { useFormContext } from 'react-hook-form';

import { ConsultationForm } from '@/types/mrd/manage-patient/consultation';

import ConsultationPreviewItem from './ConsultationPreviewItem';

const BookConsultationPreview = () => {
  const { getValues } = useFormContext<{ consultation: ConsultationForm[] }>();

  const consultation = getValues('consultation');

  return (
    <div className="flex flex-col gap-base w-3/4">
      {consultation.map((item, index) => (
        <ConsultationPreviewItem key={index} consultation={item} />
      ))}
    </div>
  );
};

export default memo(BookConsultationPreview);
