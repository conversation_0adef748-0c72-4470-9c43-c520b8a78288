import React, { memo } from 'react';

import { cn } from '@/lib/utils';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import PrimaryButton from '@/core/components/primary-button';

const SaveButton = ({ className = '' }) => {
  const isLoading = useDoctorStore((state) => state.isLoading);

  return (
    <div
      className={cn(
        'flex justify-end ',
        'w-[calc(100%-2.7rem)] md:w-full',
        'fixed bottom-17  md:static',
        className
      )}
    >
      <PrimaryButton
        type="submit"
        className="capitalize text-xl !mt-0 md:mt-12 ml-auto w-full md:w-fit"
        isLoading={isLoading}
      >
        Save Changes
      </PrimaryButton>
    </div>
  );
};

export default memo(SaveButton);
