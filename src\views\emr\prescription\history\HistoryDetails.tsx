import React, { memo, useCallback, useMemo, useState } from 'react';

import { Box, IconButton, Stack, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { GoPencil } from 'react-icons/go';
import { IoIosArrowBack } from 'react-icons/io';
import { MdOutlinePrint } from 'react-icons/md';

import { usePrescriptionStore } from '@/store/emr/prescription';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { prescriptionMode } from '@/types/emr/prescription';

import NewPrescription from '../NewPrescription';
import PrintPreview from '../shared/Print/PrintPreview';

import OutlinedButton from './OutLinedButton';

const { EDIT } = prescriptionMode;

const HistoryDetails = () => {
  const {
    refillPrescription,
    onBack,
    selectedHistory,
    prescriptionMode,
    editPrescription,
  } = usePrescriptionStore();

  const [printModalOpen, setPrintModalOpen] = useState(false);

  const isOldRecord = useMemo(() => {
    if (!selectedHistory?.created_on) return false;
    return dayjs().diff(dayjs(selectedHistory.created_on), 'hour') >= 24;
  }, [selectedHistory?.created_on]);

  const disableEditButton = useMemo(() => {
    const isPaidStatus = selectedHistory?.status === 'Paid';
    return prescriptionMode === EDIT || isOldRecord || isPaidStatus;
  }, [prescriptionMode, isOldRecord, selectedHistory?.status]);

  const handlePrint = useCallback(() => {
    setPrintModalOpen(true);
  }, []);

  const handlePrintModal = useCallback(() => {
    setPrintModalOpen(true);
  }, []);

  const handleEdit = useCallback(() => {
    editPrescription();
  }, [editPrescription]);

  const handleRefill = useCallback(() => {
    refillPrescription();
  }, [refillPrescription]);

  return (
    <Stack spacing={2} className="w-full h-full">
      <Box className="flex justify-between items-center">
        <IconButton size="small" onClick={onBack}>
          <IoIosArrowBack />
        </IconButton>
        {isOldRecord && (
          <Typography>
            {formatDate(
              selectedHistory?.created_on,
              DateFormats.DATE_DD_MM_YYYY_SLASH
            )}
          </Typography>
        )}
        <Box className="flex gap-2">
          <OutlinedButton onClick={handlePrintModal}>
            <MdOutlinePrint fontSize={18} />
            &nbsp;Print
          </OutlinedButton>
          <OutlinedButton onClick={handleEdit} disabled={disableEditButton}>
            <GoPencil fontSize={18} />
            &nbsp;Edit Prescription
          </OutlinedButton>
          <OutlinedButton onClick={handleRefill}>Refill</OutlinedButton>
        </Box>
      </Box>
      <div className=" w-full">
        <div className="w-full overflow-x-hidden">
          <NewPrescription tableHeight="calc(100vh - 21.5rem)" />
        </div>
      </div>
      <PrintPreview
        open={printModalOpen}
        onConfirm={handlePrint}
        onCancel={() => setPrintModalOpen(false)}
      />
    </Stack>
  );
};

export default memo(HistoryDetails);
