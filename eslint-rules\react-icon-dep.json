{"rules": {"no-restricted-imports": ["warn", {"paths": [{"name": "react-icons/**", "message": "'react-icons' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/fa", "message": "'react-icons/fa' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/md", "message": "'react-icons/md' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/ai", "message": "'react-icons/ai' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/bi", "message": "'react-icons/bi' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/io", "message": "'react-icons/io' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/fa6", "message": "'react-icons/fa6' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/pi", "message": "'react-icons/pi' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/io5", "message": "'react-icons/io5' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/tb", "message": "'react-icons/tb' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/ri", "message": "'react-icons/ri' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/lu", "message": "'react-icons/lu' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/cg", "message": "'react-icons/cg' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/hi", "message": "'react-icons/hi' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/fi", "message": "'react-icons/fi' is deprecated. Use AppIcon with Iconify instead."}, {"name": "react-icons/go", "message": "'react-icons/go' is deprecated. Use AppIcon with Iconify instead."}, {"name": "lucide-react", "message": "'lucide-react' is deprecated. Use AppIcon with Iconify instead."}]}]}}