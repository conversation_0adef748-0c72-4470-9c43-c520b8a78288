'use client';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useUserStore } from '@/store/userStore';

import { PERMISSION_KEYS } from '@/constants/permission-keys';

import ProfileSidebar from '@/views/emr/doctor-profile/profile-sidebar';

import NoAccessMessage from '@/components/permission/no-access-message';

import TopNavigation from '@/core/layout/emr/mobile/TopNavigation';

function hasProfilePermission(permissions: string[]): boolean {
  return permissions.includes(PERMISSION_KEYS.EMR_DOCTOR_PROFILE_VIEW);
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const isMobile = useIsMobile();
  const { permissions } = useUserStore();
  const hasPermission = hasProfilePermission(permissions);

  if (!hasPermission) {
    return (
      <div className="h-full bg-white rounded-lg w-full flex items-center justify-center">
        <NoAccessMessage />
      </div>
    );
  }

  if (isMobile) {
    return (
      <>
        <TopNavigation />
        {children}
      </>
    );
  }

  return (
    <div className="grid grid-cols-9 gap-base w-full h-full overflow-hidden">
      <ProfileSidebar />
      {children}
    </div>
  );
}
