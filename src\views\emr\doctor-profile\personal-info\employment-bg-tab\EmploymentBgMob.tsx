import React, {
  Dispatch,
  FC,
  memo,
  SetStateAction,
  useCallback,
  useEffect,
  useRef,
} from 'react';

import {
  Control,
  FieldArrayWithId,
  Path,
  UseFormGetValues,
  useWatch,
} from 'react-hook-form';

import Stack from '@mui/material/Stack';
import { isEqual } from 'lodash';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import { getNestedValue } from '@/utils/emr/doctor-profile/personal-info';
import {
  allowNumbersTextAndDot,
  preventNonAlphabeticInput,
} from '@/utils/validation';

import PencilIcon from '@/assets/svg/PencilIcon';

import {
  defaultExperience,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import AutoResizeTextArea from '../shared/AutoResizeTextArea';
import ControlledDateRange from '../shared/ControlledDateRange';
import ControlledFileUpload from '../shared/ControlledFileUpload';
import ControlledTextField from '../shared/ControlledTextField';
import MobileAddDeleteButtons from '../shared/MobileAddDeleteButtons';

import { FormData } from '.';

type Props = {
  fields: FieldArrayWithId<FormData, 'experience', 'id'>[];
  control: Control<FormData>;
  editableField: Set<string>;
  getValues: UseFormGetValues<FormData>;
  isSubmitted: boolean;
  setEditableField: Dispatch<SetStateAction<Set<string>>>;
  handleOnDelete: (_itemToDelete: ItemToDelete) => void;
};

const EmploymentBgMob: FC<Props> = ({
  fields,
  handleOnDelete,
  setEditableField,
  editableField,
  getValues,
  isSubmitted,
  control,
}) => {
  const { doctorProfile } = useDoctorStore();
  const watchedEmployment = useWatch({
    control,
    name: 'experience',
  });

  const lastItemRef = useRef<HTMLDivElement | null>(null);

  const handleEditClick = useCallback(
    (fieldName: Path<FormData>) => {
      setEditableField((prev) => new Set(prev.add(fieldName)));
    },
    [setEditableField]
  );

  const isFieldDisabled = useCallback(
    (fieldName: Path<FormData>) => {
      if (editableField.has(fieldName)) {
        return false;
      }

      const formValues = getValues();
      const fieldValue = formValues[fieldName as keyof FormData];
      const doctorFieldValue = getNestedValue(
        doctorProfile?.professionalDetails,
        fieldName
      );

      return (
        !!doctorFieldValue ||
        (isSubmitted && !!fieldValue && fieldValue === doctorFieldValue)
      );
    },
    [editableField, getValues, doctorProfile?.professionalDetails, isSubmitted]
  );

  const renderEditIcon = useCallback(
    (fieldName: Path<FormData>) => {
      return isFieldDisabled(fieldName) ? (
        <button type="button" onClick={() => handleEditClick(fieldName)}>
          <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
        </button>
      ) : null;
    },
    [isFieldDisabled, handleEditClick]
  );

  useEffect(() => {
    lastItemRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
    });
  }, [fields.length]);

  return (
    <>
      {fields?.map((field, index) => (
        <Stack
          spacing={1}
          key={field.id}
          ref={index === fields.length - 1 ? lastItemRef : undefined}
        >
          <AutoResizeTextArea
            label="Previous Employer"
            autoFocus={index === fields.length - 1}
            control={control}
            name={`experience.${index}.hospitalName`}
            endDecoration={renderEditIcon(`experience.${index}.hospitalName`)}
            disabled={isFieldDisabled(`experience.${index}.hospitalName`)}
            placeholder="ABC Pvt Ltd"
            onKeyDown={preventNonAlphabeticInput}
            className="w-full"
          />
          <AutoResizeTextArea
            label="Designation"
            control={control}
            name={`experience.${index}.designation`}
            endDecoration={renderEditIcon(`experience.${index}.designation`)}
            disabled={isFieldDisabled(`experience.${index}.designation`)}
            placeholder="Critical Care Technologist"
            onKeyDown={preventNonAlphabeticInput}
            className="w-full"
          />
          <ControlledDateRange
            control={control}
            name={`experience.${index}.duration`}
            disabled={isFieldDisabled(`experience.${index}.duration`)}
            inputProps={{ label: 'Duration' }}
            endAdornment={renderEditIcon(`experience.${index}.duration`)}
            separator="To"
          />
          <ControlledTextField
            label="Salary"
            control={control}
            name={`experience.${index}.salary`}
            endDecoration={renderEditIcon(`experience.${index}.salary`)}
            disabled={isFieldDisabled(`experience.${index}.salary`)}
            placeholder=""
            onKeyDown={allowNumbersTextAndDot}
            className="w-full"
          />
          <ControlledFileUpload
            name={`experience.${index}.doc1`}
            control={control}
            label="Document 1"
            className="w-full"
            allowedFileTypes={['image/jpeg', 'image/png', 'application/pdf']}
            fileTypeErrorMessage="*Only JPG, PNG, and PDF files are allowed."
            showPreview
            maxFileSize={6}
            isBoxStyle
          />
          <ControlledFileUpload
            name={`experience.${index}.doc2`}
            control={control}
            label="Document 2"
            className="w-full"
            allowedFileTypes={['image/jpeg', 'image/png', 'application/pdf']}
            fileTypeErrorMessage="*Only JPG, PNG, and PDF files are allowed."
            showPreview
            maxFileSize={6}
            isBoxStyle
          />
          <ControlledTextField
            label="Status"
            control={control}
            name={`experience.${index}.status`}
            endDecoration={renderEditIcon(`experience.${index}.status`)}
            disabled={isFieldDisabled(`experience.${index}.status`)}
            placeholder=""
            onKeyDown={preventNonAlphabeticInput}
            className="w-full"
          />
          <MobileAddDeleteButtons
            onDelete={
              isEqual(watchedEmployment[index], defaultExperience)
                ? undefined
                : () => handleOnDelete({ index, uuId: field?.uuId })
            }
          />
        </Stack>
      ))}
    </>
  );
};

export default memo(EmploymentBgMob);
