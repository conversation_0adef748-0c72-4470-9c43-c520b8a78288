import dayjs, { Dayjs } from 'dayjs';
import { create } from 'zustand';

import { FilterByType, filterBy } from '@/utils/filter-by-util';

import { SortOrder, sortOrder } from '@/core/components/table/types';

interface OrderHistoryFilterState {
  searchText: string;
  department: string | null;
  dateFilter: FilterByType;
  customStartDate: Dayjs;
  customEndDate: Dayjs;
  sortField: string;
  sortOrder: SortOrder;
  onSearchTextChange: (text: string) => void;
  onDepartmentChange: (_value: string | null) => void;
  onDateFilterChange: (filter: FilterByType, customDate?: Dayjs[]) => void;
  onSort: (field: string, order: SortOrder | null) => void;
  clearFilters: () => void;
}

const initialState = {
  searchText: '',
  department: null,
  dateFilter: filterBy.ALL,
  customStartDate: dayjs(),
  customEndDate: dayjs().endOf('day'),
  sortField: 'date',
  sortOrder: sortOrder.DESC,
};

export const useOrderHistoryFilterStore = create<OrderHistoryFilterState>(
  (set) => ({
    ...initialState,
    onSearchTextChange: (searchText) => set({ searchText }),
    onDepartmentChange: (department) => set({ department }),
    onDateFilterChange: (dateFilter, customDate) => {
      if (dateFilter === filterBy.CUSTOM_DATE && customDate) {
        set({
          dateFilter,
          customStartDate: customDate[0],
          customEndDate: customDate[1],
        });
      } else {
        set({ dateFilter });
      }
    },
    onSort: (field, order) => {
      if (order) {
        set({ sortField: field, sortOrder: order });
      }
    },
    clearFilters: () => set(initialState),
  })
);
