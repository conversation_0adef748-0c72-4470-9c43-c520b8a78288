import { FC, memo, useState, useCallback } from 'react';

import { capitalize } from '@mui/material';
import { IoCloseSharp } from 'react-icons/io5';

import { useBookConsultationStore } from '@/store/mrd/manage-patient/book-consultation';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { addDateAndTime, formatDate } from '@/utils/dateUtils/dayUtils';

import EditIcon from '@/assets/svg/EditIcon';

import AppIconButton from '@/core/components/app-icon-button';
import AppModal from '@/core/components/app-modal'; // Import AppModal
import DeleteModal from '@/core/components/delete-modal';
import { Consultation } from '@/types/mrd/manage-patient/consultation';

import RescheduleAppointmentForm from './RescheduleAppointmentForm'; // Import the new form component

type Props = {
  appointment: Consultation;
};

const AppointmentCard: FC<Props> = ({ appointment }) => {
  const doctor = useBookConsultationStore((state) =>
    state.doctors.find((doc) => doc.id === appointment.doctorId)
  );
  const {
    updateAppointment,
    updatingAppointment,
    deleteAppointment,
    deleting,
    getFutureAppointments,
  } = useBookConsultationStore();

  const [isRescheduleModalOpen, setIsRescheduleModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const handleOpenRescheduleModal = useCallback(() => {
    setIsRescheduleModalOpen(true);
  }, []);

  const handleCloseRescheduleModal = useCallback(() => {
    setIsRescheduleModalOpen(false);
  }, []);

  const handleSaveReschedule = useCallback(
    async (newDate: string, newTime: string) => {
      try {
        await updateAppointment({
          queueId: appointment.queueId ?? '',
          date: addDateAndTime(newDate, newTime),
          time: newTime,
        });
        handleCloseRescheduleModal();
      } catch (error) {
        console.error('Failed to reschedule appointment:', error);
      } finally {
        getFutureAppointments(appointment.patientId ?? '');
      }
    },
    [
      updateAppointment,
      appointment,
      handleCloseRescheduleModal,
      getFutureAppointments,
    ]
  );

  const handleDeleteClick = useCallback(() => {
    setIsDeleteModalOpen(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    try {
      await deleteAppointment(appointment.queueId ?? '');
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Failed to delete appointment:', error);
    } finally {
      setTimeout(() => {
        getFutureAppointments(appointment.patientId ?? '');
      }, 1000);
    }
  }, [
    deleteAppointment,
    appointment.queueId,
    appointment.patientId,
    getFutureAppointments,
  ]);

  return (
    <div className="flex border-2 rounded-base p-base items-center">
      <span className="text-base flex-1">
        {formatDate(appointment.date, DateFormats.DATE_DD_MM_YYYY_SLASH)}
      </span>
      <span className="text-base flex-1">{appointment.time}</span>
      <span className="text-base flex-1">{doctor?.name}</span>
      <span className="text-base flex-1">
        {capitalize(appointment.type ?? 'new')}
      </span>
      <div className="flex gap-base flex-1 justify-end">
        <AppIconButton
          variant="outlined"
          onClick={handleOpenRescheduleModal}
          title="edit"
        >
          <EditIcon />
        </AppIconButton>
        <AppIconButton
          variant="outlined"
          title="delete"
          onClick={handleDeleteClick}
        >
          <IoCloseSharp />
        </AppIconButton>
      </div>

      <AppModal
        open={isRescheduleModalOpen}
        onClose={handleCloseRescheduleModal}
        title="Reschedule Appointment"
        classes={{ root: 'w-100', header: 'pb-2', closeButton: '!hidden' }}
      >
        <RescheduleAppointmentForm
          appointment={appointment}
          onSave={handleSaveReschedule}
          onCancel={handleCloseRescheduleModal}
          isLoading={updatingAppointment}
        />
      </AppModal>
      <DeleteModal
        open={isDeleteModalOpen}
        onClose={() => !deleting && setIsDeleteModalOpen(false)}
        onDelete={handleConfirmDelete}
        isLoading={deleting}
        confirmationMessage="Are you sure you want to delete this appointment?"
        bodyContent={
          <div className="flex gap-base items-center">
            <span className="text-sm font-semibold text-blue-500">
              {doctor?.name ?? ''}
            </span>
            <div className="text-sm">
              <span>
                {`${formatDate(
                  appointment.date,
                  DateFormats.DATE_DD_MM_YYYY_SLASH
                )} | ${appointment.time}`}
              </span>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default memo(AppointmentCard);
