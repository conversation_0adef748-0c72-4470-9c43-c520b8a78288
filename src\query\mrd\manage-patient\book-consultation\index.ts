import { api, arcaAxios } from '@/core/lib/interceptor';
import { Consultation, Doctors } from '@/types/mrd/manage-patient/consultation';

export async function getAllDoctors(): Promise<Doctors[]> {
  const { data } = await arcaAxios.get<Doctors[]>('/user?userType=doctor');
  return data.sort((a, b) => a?.name?.localeCompare(b?.name ?? ''));
}

export async function createAppointment({ doctorId, ...rest }: Consultation) {
  const { data } = await arcaAxios.post('/appointment', rest, {
    params: { doctorId },
  });

  return data;
}

export const updateAppointment = async (
  data: { queueId: string } & Partial<Consultation>
) => {
  const payload = {
    date: data?.date,
    time: data?.time,
    doctorId: data?.doctorId,
    department: data?.department,
    paymentStatus: data?.paymentStatus,
    consultationFee: data?.consultationFee,
  };
  const { data: updatedAppointment } = await api.patch(
    `consultation/v0.1/book-consultation/queue`,
    payload,
    { params: { queueId: data.queueId } }
  );
  return updatedAppointment;
};

export const deleteAppointment = async (appointmentId: string) => {
  await api.delete(`appointment/v0.1/appointment`, {
    params: { queueId: appointmentId },
  });
};

export const getFutureAppointments = async (patientId: string) => {
  const { data } = await api.get<Consultation[]>(
    'consultation/v0.1/book-consultation/future',
    {
      params: { patientId },
    }
  );
  return data;
};

export const getAppointmentType = async (
  patientId: string,
  doctorId: string,
  appointmentId: string
) => {
  const { data } = await api.get<{ visitType: string }>(
    'consultation/v0.1/book-consultation/type',
    {
      params: { patientId, doctorId, appointmentId },
    }
  );
  return data;
};
