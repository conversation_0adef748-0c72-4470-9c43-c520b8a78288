import { FC } from 'react';

import { IconButton } from '@mui/material';
import { FaAngleRight } from 'react-icons/fa6';

import { cn } from '@/lib/utils';

import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import colors from '@/utils/colors';

import { LifestyleFormsType, mobileViews } from '@/constants/lifestyle';

type Props = {
  items: Record<string, string>;
  activeForm: LifestyleFormsType;
  onSelectTab: (tab: LifestyleFormsType) => void;
};

const { TIMELINE } = mobileViews;

const TabPanels: FC<Props> = ({ items, activeForm, onSelectTab }) => {
  return (
    <div className="flex flex-col gap-2">
      {Object.values(items)?.map((key) => (
        <PanelItem
          key={key}
          item={key}
          isActive={key === activeForm}
          onClick={() => onSelectTab(key as LifestyleFormsType)}
        />
      ))}
    </div>
  );
};

export default TabPanels;

type PanelItemProps = {
  item: string;
  isActive: boolean;
  onClick?: () => void;
};

const PanelItem: FC<PanelItemProps> = ({ item, isActive = false, onClick }) => {
  const { setMobilePage, closeModal } = useLifestyleUtilStore();

  const handleClick = () => {
    setMobilePage(TIMELINE);
    closeModal();
  };

  return (
    <div
      style={{
        backgroundColor: isActive ? colors.common.navyBlue : 'transparent',
      }}
      className={cn(
        'flex items-center justify-between p-2 pl-3 rounded-lg cursor-pointer select-none border shadow-custom-xs',
        {
          [`bg-[${colors.common.navyBlue}] text-white`]: isActive,
        }
      )}
      onClick={onClick}
    >
      <span className="text-[14px]">{item}</span>
      <IconButton onClick={handleClick} size="small">
        <FaAngleRight
          className={cn('text-white opacity-0', {
            'translate-x-[-2rem]': !isActive,
            'opacity-100 translate-x-0 transition-all duration-300': isActive,
          })}
        />
      </IconButton>
    </div>
  );
};
