import React from 'react';

import { IoClose } from 'react-icons/io5';

import { usePrescriptionPackageStore } from '@/store/emr/prescription/package';

import OutLinedIconButton from '@/emr/components/lifestyle/lifestyle-forms/shared/OutlinedIconButton';

import { packageTypes } from '@/types/emr/lab';

interface PackageModalHeaderProps {
  onClose: () => void;
}

export const PackageModalHeader: React.FC<PackageModalHeaderProps> = ({
  onClose,
}) => {
  const { activePackageType } = usePrescriptionPackageStore();

  return (
    <div className="flex items-center justify-between p-4 py-3 border-b">
      <span className="font-bold text-lg">
        {activePackageType === packageTypes.DEPARTMENT
          ? 'Department Packages'
          : 'User Packages'}
      </span>
      <div className="flex items-center gap-2">
        <OutLinedIconButton
          onClick={onClose}
          sx={{
            width: 25,
            height: 25,
            p: 0.3,
          }}
        >
          <IoClose />
        </OutLinedIconButton>
      </div>
    </div>
  );
};
