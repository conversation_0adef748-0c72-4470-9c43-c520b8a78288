import { api } from '@/core/lib/interceptor';

export type FilterType = 'last_7_days' | 'last_15_days' | 'last_month';

export type MetricType =
  | 'calories'
  | 'carbs'
  | 'protein'
  | 'fat'
  | 'fiber'
  | 'micro'
  | 'additional';

export type NutritionAverageResponse = {
  filter: FilterType;
  fromDate: string;
  toDate: string;
  daysWithData: number;
  averages: {
    calories: number;
    carbs: number;
    protein: number;
    fat: number;
    fiber: number;
    sugar: number;
    salt: number;
    oil: number;
  };
};

export type ChartDataPoint = {
  date: string;
  [key: string]: number | string;
};

export type NutritionChartResponse = {
  filter: FilterType;
  metrics: string[];
  fromDate: string;
  toDate: string;
  chartData: ChartDataPoint[];
};

export type PercentageChartDataPoint = {
  date: string;
  fat_percentage: number;
  carbs_percentage: number;
  protein_percentage: number;
};

export type NutritionPercentageChartResponse = {
  filter: FilterType;
  fromDate: string;
  toDate: string;
  chartData: PercentageChartDataPoint[];
};

export type NutritionSummaryDataPoint = {
  date: string;
  // Macronutrients
  calories: number;
  carbs: number;
  protein: number;
  fat: number;
  sugar: number;
  salt: number;
  fiber: number;
  cholesterol: number;

  // Macronutrient percentages
  fat_percentage: number;
  carbs_percentage: number;
  protein_percentage: number;

  // Fatty acids
  sfa: number;
  mufa: number;
  pufa: number;

  // Micronutrients
  calcium?: number;
  iron?: number;
  magnesium?: number;
  phosphorus?: number;
  potassium?: number;
  sodium?: number;

  // Make all fields optional for the API response
  [key: string]: string | number | undefined;
};

export type NutritionSummaryResponse = NutritionSummaryDataPoint[];

// API Functions
export const getNutritionAverage = async (
  patientId: string,
  filter: FilterType
): Promise<NutritionAverageResponse> => {
  const { data } = await api.get<NutritionAverageResponse>(
    '/nutrition/v0.1/patient/nutrition/average',
    {
      params: { patientId, filter },
    }
  );
  return data;
};

export const getNutritionChart = async (
  patientId: string,
  filter: FilterType,
  metric: MetricType
): Promise<NutritionChartResponse> => {
  const { data } = await api.get<NutritionChartResponse>(
    '/nutrition/v0.1/patient/nutrition/chart',
    {
      params: { patientId, filter, metric },
    }
  );
  return data;
};

export const getNutritionPercentageChart = async (
  patientId: string,
  filter: FilterType
): Promise<NutritionPercentageChartResponse> => {
  const { data } = await api.get<NutritionPercentageChartResponse>(
    '/nutrition/v0.1/patient/nutrition/percentage-chart',
    {
      params: { patientId, filter },
    }
  );
  return data;
};

export const getNutritionSummary = async (
  patientId: string,
  metrics: 'macro' | 'micro',
  filter: FilterType,
  sortField: string = 'date',
  sortDirection: 'asc' | 'desc' = 'desc'
): Promise<NutritionSummaryResponse> => {
  const { data } = await api.get<NutritionSummaryResponse>(
    '/nutrition/v0.1/patient/nutrition/summary',
    {
      params: {
        patientId,
        metrics,
        filter,
        sortField,
        sortDirection,
      },
    }
  );
  return data;
};
