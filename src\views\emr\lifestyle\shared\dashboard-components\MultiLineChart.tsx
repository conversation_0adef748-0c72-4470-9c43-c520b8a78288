import React, { useState } from 'react';

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
  Legend,
  Tooltip,
  Area,
  ComposedChart,
} from 'recharts';

import { ChartDataPoint } from '@/query/emr/lifestyle/nutrition-dashboard';

interface LineConfig {
  dataKey: string;
  color: string;
  name: string;
}

interface MultiLineChartProps {
  data: ChartDataPoint[];
  lines: LineConfig[];
  title: string;
  unit?: string;
  height?: number;
  showClearFilter?: boolean;
}

const MultiLineChart: React.FC<MultiLineChartProps> = ({
  data,
  lines,
  title,
  unit = '',
  height = 286,
  showClearFilter = false,
}) => {
  const [activeLine, setActiveLine] = useState<string | null>(null);

  const handleLegendClick = (dataKey: string) => {
    setActiveLine(activeLine === dataKey ? null : dataKey);
  };

  const handleClearFilter = () => {
    setActiveLine(null);
  };

  // Calculate max value for Y-axis domain
  const calculateMaxYValue = () => {
    if (!data.length) return 100;
    let max = 0;
    data.forEach((item) => {
      lines.forEach((line) => {
        const value = Number(item[line.dataKey]) || 0;
        if (value > max) max = value;
      });
    });
    // Round up to nearest 20 for better Y-axis ticks
    return Math.ceil(max / 20) * 20 || 100;
  };

  const yDomainMax = calculateMaxYValue();

  // Process data for display (no clipping to preserve actual values)
  const processedData = data.map((item) => {
    const newItem = { ...item };
    lines.forEach((line) => {
      const value = newItem[line.dataKey];
      // No longer clipping values to preserve actual values
    });
    return newItem;
  });

  // Custom tick component for X-axis
  const CustomizedAxisTick = (props: any) => {
    const { x, y, payload } = props;
    const date = new Date(payload.value);
    const day = date.getDate();
    const month = date.toLocaleString('en-US', { month: 'short' });

    return (
      <g transform={`translate(${x},${y})`}>
        <text
          x={0}
          y={0}
          dy={12}
          textAnchor="middle"
          fill="#6b7280"
          fontSize={11}
        >
          {day}
        </text>
        <text
          x={0}
          y={12}
          dy={12}
          textAnchor="middle"
          fill="#6b7280"
          fontSize={9}
        >
          {month}
        </text>
      </g>
    );
  };

  const formatTooltipLabel = (label: string) => {
    const date = new Date(label);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const date = new Date(label);
      const formattedDate = date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });

      // Get the actual data item for this point
      const dataItem: Record<string, any> =
        data.find(
          (item) =>
            item.date && new Date(item.date).getTime() === date.getTime()
        ) || {};

      // Get all unique data keys from the lines
      const allDataKeys = Array.from(
        new Set(lines.map((line) => line.dataKey))
      );

      // Create tooltip items for all lines
      const tooltipItems = allDataKeys
        .map((dataKey) => {
          const line = lines.find((l) => l.dataKey === dataKey);
          if (!line) return null;

          const value = dataItem[dataKey];
          const isSelected = activeLine === dataKey;
          const isHighlighted = !activeLine || isSelected;

          return {
            ...line,
            value,
            isHighlighted,
            isSelected,
          };
        })
        .filter(Boolean);

      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg min-w-[180px]">
          <p className="text-sm font-medium text-gray-900 mb-2">
            {formattedDate}
          </p>
          <div className="space-y-1.5">
            {tooltipItems.map((item: any) => {
              if (item.value === null || item.value === undefined) return null;

              const isClipped = item.value > yDomainMax;
              const displayValue =
                typeof item.value === 'number'
                  ? item.value.toFixed(1)
                  : item.value;

              return (
                <div
                  key={item.dataKey}
                  className={`flex items-center justify-between ${!item.isHighlighted ? 'opacity-50' : ''}`}
                >
                  <div className="flex items-center">
                    <div
                      className="w-2 h-2 rounded-full mr-2 flex-shrink-0"
                      style={{
                        backgroundColor: item.color,
                        opacity: item.isHighlighted ? 1 : 0.5,
                      }}
                    />
                    <span
                      className={`text-sm ${item.isSelected ? 'font-semibold' : 'font-medium'} text-gray-800`}
                    >
                      {item.name}:
                    </span>
                  </div>
                  <span
                    className={`text-sm ml-2 ${item.isSelected ? 'font-semibold text-gray-900' : 'text-gray-700'}`}
                  >
                    {displayValue} {unit}
                    {isClipped && ' (clipped)'}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      );
    }
    return null;
  };

  // Calculate Y-axis ticks based on max value
  const yTicks = () => {
    const ticks = [];
    const step = yDomainMax / 5;
    for (let i = 0; i <= 5; i++) {
      ticks.push(Math.round(step * i));
    }
    return ticks;
  };

  // Custom dot component
  const renderDot = (props: any) => {
    const { cx, cy, stroke, payload, value, dataKey } = props;
    if (value === null || value === undefined) return <></>;

    const showLabel = activeLine === dataKey;

    return (
      <g>
        <circle
          cx={cx}
          cy={cy}
          r={4}
          fill={stroke}
          stroke="#fff"
          strokeWidth={1.5}
        />
        {showLabel && (
          <text
            x={cx}
            y={cy - 10}
            textAnchor="middle"
            fill={stroke}
            fontSize={10}
            fontWeight="600"
            style={{
              textShadow: '0 0 3px white, 0 0 3px white, 0 0 3px white',
            }}
          >
            {value?.toFixed?.(1) ?? value}
          </text>
        )}
      </g>
    );
  };

  return (
    <div
      className="w-full flex flex-col gap-2"
      style={{ height: `${height}px` }}
    >
      {title && (
        <div className="px-4 pt-2 pb-3 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-base font-medium text-[#001926]">
              {title} {unit && `(${unit})`}
            </h3>
            <div className="flex items-center space-x-4">
              {showClearFilter && activeLine && (
                <button
                  onClick={handleClearFilter}
                  className="text-xs text-blue-600 hover:text-blue-800 mr-2 transition-colors"
                >
                  Clear Filter
                </button>
              )}
              <div className="flex items-center space-x-4">
                {lines.map((line) => {
                  const isClickable = showClearFilter;
                  const isActive =
                    activeLine === null || activeLine === line.dataKey;
                  return (
                    <div
                      key={line.dataKey}
                      className={`flex items-center ${isClickable ? 'cursor-pointer' : 'cursor-default'}`}
                      onClick={
                        isClickable
                          ? () => handleLegendClick(line.dataKey)
                          : undefined
                      }
                    >
                      <div
                        className="w-2 h-2 rounded-full mr-1.5 flex-shrink-0"
                        style={{
                          backgroundColor: isActive
                            ? line.color
                            : `${line.color}80`,
                          boxShadow:
                            activeLine === line.dataKey
                              ? `0 0 0 2px ${line.color}40`
                              : 'none',
                        }}
                      />
                      <span
                        className={`text-xs whitespace-nowrap ${isActive ? 'text-gray-900' : 'text-gray-400'}`}
                      >
                        {line.name}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}
      <div className="flex-1">
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart
            data={processedData}
            margin={{
              top: 40,
              right: 25,
              left: 35,
              bottom: 2,
            }}
            barGap={0}
            barCategoryGap="10%"
          >
            <defs>
              {lines.map((line) => (
                <linearGradient
                  key={`gradient-${line.dataKey}`}
                  id={`gradient-${line.dataKey}`}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor={line.color} stopOpacity={0.2} />
                  <stop
                    offset="95%"
                    stopColor={line.color}
                    stopOpacity={0.02}
                  />
                </linearGradient>
              ))}
            </defs>

            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#e5e7eb"
              horizontal={true}
              vertical={true}
            />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              interval={0}
              minTickGap={1}
              height={50}
              tick={<CustomizedAxisTick />}
              tickMargin={10}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              domain={[0, yDomainMax]}
              ticks={yTicks()}
              tickFormatter={(value) => value.toString()}
              width={40}
              tickMargin={8}
            />
            <Tooltip content={<CustomTooltip />} />
            {/* Hide the default legend since we're using a custom one in the title */}
            <Legend wrapperStyle={{ display: 'none' }} />

            {/* Render Area component only for the active line */}
            {activeLine &&
              lines.map((line) => {
                if (line.dataKey === activeLine) {
                  return (
                    <Area
                      key={`area-${line.dataKey}`}
                      type="linear"
                      dataKey={line.dataKey}
                      stroke="none"
                      fill={`url(#gradient-${line.dataKey})`}
                      fillOpacity={1}
                      connectNulls={true}
                      isAnimationActive={false}
                    />
                  );
                }
                return null;
              })}

            {/* Render all lines */}
            {lines.map((line) => {
              const isActive =
                activeLine === null || activeLine === line.dataKey;
              const opacity = isActive ? 1 : 0.3;
              const strokeWidth = activeLine === line.dataKey ? 1.8 : 1.2;

              return (
                <Line
                  key={line.dataKey}
                  type="linear"
                  dataKey={line.dataKey}
                  stroke={line.color}
                  strokeWidth={strokeWidth}
                  strokeOpacity={opacity}
                  dot={renderDot}
                  activeDot={{
                    r: 4,
                    stroke: line.color,
                    strokeWidth: 1.5,
                    fill: line.color,
                  }}
                  name={line.name}
                  connectNulls={true}
                  isAnimationActive={false}
                  style={{
                    filter:
                      activeLine === line.dataKey
                        ? `drop-shadow(0 1px 4px ${line.color}20)`
                        : 'none',
                  }}
                />
              );
            })}
          </ComposedChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default MultiLineChart;
