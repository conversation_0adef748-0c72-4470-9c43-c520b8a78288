import React, { memo, useMemo, useRef, useState } from 'react';

import { useReactToPrint } from 'react-to-print';

import { formatDate } from '@/utils/dateUtils/dayUtils';

import AppButton from '@/core/components/app-button';
import '@/styles/emr/lab/print.css';
import { OrderHistoryItem } from '@/types/emr/lab';

import AppSign from '../../print-button/AppSign';
import PatientDetails from '../../print-button/PatientDetails';
import PrintHeader from '../../print-button/PrintHeader';
import TableFooter from '../../print-button/TableFooter';

interface OrderHistoryPrintContentProps {
  selectedOrders: OrderHistoryItem[];
  onClose: () => void;
}
interface SimpleTableRow {
  id: string;
  date: string;
  department: string;
  testName: string;
  amount: string;
  status: string;
}

const OrderHistoryPrintContent: React.FC<OrderHistoryPrintContentProps> = ({
  selectedOrders,
  onClose: _onClose,
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [printOpen, setPrintOpen] = useState(false);
  const handlePrint = useReactToPrint({
    contentRef,
    documentTitle: 'order-history',
    onBeforePrint: async () => setPrintOpen(true),
    onAfterPrint: () => setPrintOpen(false),
    onPrintError: () => setPrintOpen(false),
  });

  const simpleRows = useMemo<SimpleTableRow[]>(() => {
    return selectedOrders.map((order) => ({
      id: order.id,
      date: formatDate(order.date),
      department: order.department,
      testName: order.testName,
      amount: order.amount,
      status: order.status,
    }));
  }, [selectedOrders]);

  const totalCost = useMemo(() => {
    return selectedOrders.reduce((sum, order) => {
      const cost =
        typeof order.amount === 'string'
          ? parseFloat(order.amount)
          : order.amount;
      return sum + (isNaN(cost) ? 0 : cost || 0);
    }, 0);
  }, [selectedOrders]);

  return (
    <div
      ref={contentRef}
      className="w-full h-full max-h-full flex flex-col print-content-print order-history-print"
    >
      <div className="flex-1 p-base overflow-y-auto">
        <PrintHeader />

        <div
          className="w-full border-t mb-2 -mx-base"
          style={{ borderColor: '#E5E7EB' }}
        ></div>
        <div className="w-full flex justify-center p-base font-bold text-base">
          Lab Tests
        </div>

        <div
          className="w-full border-t mb-2 -mx-base"
          style={{ borderColor: '#E5E7EB' }}
        ></div>
        <PatientDetails />

        <div className="flex-1 flex flex-col gap-base pb-base w-full justify-between">
          <div className="w-full">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr style={{ backgroundColor: '#64707D', color: 'white' }}>
                  <th className="border border-gray-300 px-4 py-2 text-left">
                    Date
                  </th>
                  <th className="border border-gray-300 px-4 py-2 text-left">
                    Department
                  </th>
                  <th className="border border-gray-300 px-4 py-2 text-left">
                    Test Name
                  </th>
                  <th className="border border-gray-300 px-4 py-2 text-left">
                    Amount
                  </th>
                  <th className="border border-gray-300 px-4 py-2 text-left">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody>
                {simpleRows.length === 0 ? (
                  <tr>
                    <td
                      colSpan={5}
                      className="border border-gray-300 px-4 py-2 text-center"
                    >
                      No orders found
                    </td>
                  </tr>
                ) : (
                  simpleRows.map((row, index) => (
                    <tr key={row.id || index}>
                      <td
                        className="border border-gray-300 px-4 py-2 text-sm"
                        style={{ fontSize: '14px' }}
                      >
                        {row.date}
                      </td>
                      <td
                        className="border border-gray-300 px-4 py-2 text-sm"
                        style={{ fontSize: '14px' }}
                      >
                        {row.department}
                      </td>
                      <td
                        className="border border-gray-300 px-4 py-2 text-sm"
                        style={{ fontSize: '14px' }}
                      >
                        {row.testName}
                      </td>
                      <td
                        className="border border-gray-300 px-4 py-2 text-sm"
                        style={{ fontSize: '14px' }}
                      >
                        ₹{row.amount}
                      </td>
                      <td
                        className="border border-gray-300 px-4 py-2 text-sm"
                        style={{ fontSize: '14px' }}
                      >
                        {row.status === 'Paid' ? 'Paid' : 'Unpaid'}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Print-only footer HTML - similar to table structure */}
          <div
            className="print-only-footer"
            style={{
              pageBreakBefore: 'auto',
              pageBreakInside: 'avoid',
              marginTop: '30px',
            }}
          >
            {/* Table Footer as HTML */}
            <div
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'end',
                marginBottom: '8px',
                paddingTop: '8px',
              }}
            >
              <TableFooter />
            </div>

            {/* Divider above total cost */}
            <div
              style={{ width: '100%', borderTop: '1px solid #E5E7EB' }}
            ></div>

            {/* Total Cost Display as HTML */}
            <div
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'end',
                alignItems: 'center',
                marginTop: '16px',
                marginBottom: '4px',
              }}
            >
              <span
                style={{
                  fontSize: '14px',
                  fontWeight: '600',
                  marginRight: '16px',
                }}
              >
                Total Amount :
              </span>
              <span
                style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#012436',
                }}
              >
                ₹ {totalCost.toFixed(2)}
              </span>
            </div>

            {/* Divider below total cost */}
            <div
              style={{
                width: '100%',
                borderTop: '1px solid #E5E7EB',
                marginBottom: '8px',
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* AppSign for all print pages */}
      <div className="print-app-sign">
        <div className="w-full flex justify-start mb-2 app-sign-container">
          <AppSign />
        </div>
      </div>

      {/* Screen preview section - unchanged */}
      <div className="flex-shrink-0 w-full flex flex-col p-base no-print">
        <div className="w-full flex justify-between items-end mb-2 pt-2">
          <TableFooter />
        </div>

        <div
          className="w-full border-t -mx-base"
          style={{ borderColor: '#E5E7EB' }}
        ></div>

        <div className="w-full flex justify-end items-center mt-4 mb-1">
          <span className="text-sm font-semibold mr-4">Total Amount :</span>
          <span className="text-sm font-bold text-[#012436]">
            ₹ {totalCost.toFixed(2)}
          </span>
        </div>
        <div
          className="w-full border-t mb-2 -mx-base"
          style={{ borderColor: '#E5E7EB' }}
        ></div>
        {!printOpen && (
          <div className="w-full flex justify-end mb-2">
            <AppButton onClick={handlePrint} className="w-32">
              Print
            </AppButton>
          </div>
        )}

        <div className="w-full flex justify-start mb-2 app-sign-container">
          <AppSign />
        </div>
      </div>
    </div>
  );
};

export default memo(OrderHistoryPrintContent);
