'use client';

import React, { useCallback } from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import TestReports from '@/views/emr/lab';

import { ReportsPage } from '@/components/permission/protected-page';

import Chat from '@/emr/components/chat';
import NoPatientView from '@/emr/components/shared/NoPatientView';

const ReportsContent = () => {
  const { patient, isActivePatient } = useCurrentPatientStore();
  const renderReports = useCallback(() => {
    if (!patient || !isActivePatient) {
      return <NoPatientView />;
    } else {
      return <TestReports />;
    }
  }, [isActivePatient, patient]);

  return (
    <>
      <div className="flex-1 flex gap-base w-full h-full max-h-full">
        {renderReports()}
      </div>
      {patient && <Chat />}
    </>
  );
};

const Reports = () => (
  <ReportsPage className="h-full flex flex-col w-full">
    <ReportsContent />
  </ReportsPage>
);

export default Reports;
