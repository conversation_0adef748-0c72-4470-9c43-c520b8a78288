import React from 'react';

import { Controller, Path, Control, FieldValues } from 'react-hook-form';

import Textarea, { TextareaProps } from './Textarea';

type CustomTextareaProps<T extends FieldValues> = TextareaProps & {
  name: Path<T>;
  control: Control<T>;
  rows?: number;
};

const ControlledTextarea = <T extends FieldValues>({
  name,
  control,
  ...props
}: CustomTextareaProps<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => (
        <>
          <Textarea {...props} value={value} onChange={onChange} />
        </>
      )}
    />
  );
};

export default ControlledTextarea;
