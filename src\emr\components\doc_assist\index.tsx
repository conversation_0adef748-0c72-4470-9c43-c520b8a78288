import { ChangeEvent, FormEvent, useEffect, useState } from 'react';

import Markdown from 'react-markdown';

import { IconButton } from '@mui/material';
import { BiSearch } from 'react-icons/bi';
import { toast } from 'sonner';

import Loading from '@/lib/common/loading';

import { useDocAssistStore } from '@/store/doc-assist-store';

import { searchKnowledge } from '@/query/search';

interface DocAssistProps {
  pageId: string;
}

export default function DocAssist({ pageId }: DocAssistProps) {
  const { searches, setSearch } = useDocAssistStore();
  const [isSearching, setIsSearching] = useState(false);
  const [query, setQuery] = useState('');
  const [knowledge, setKnowledge] = useState<string | null>(null);

  useEffect(() => {
    const savedSearch = searches[pageId];
    if (savedSearch) {
      setQuery(savedSearch.query || '');
      setKnowledge(savedSearch.knowledge || null);
    } else {
      setQuery('');
      setKnowledge(null);
    }
  }, [pageId, searches]);

  const handleSearch = async (e: FormEvent) => {
    try {
      e.stopPropagation();
      e.preventDefault();

      if (!query) {
        return;
      }

      setIsSearching(true);

      const result = await searchKnowledge(query);
      setIsSearching(false);
      setKnowledge(result);

      setSearch(pageId, query, result);
    } catch (err) {
      setIsSearching(false);
      toast.error('Searching knowledge base failed');
      console.error(err);
    }
  };

  const handleInput = (e: ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const input = e.target.value || '';
    setQuery(input);
  };

  return (
    <div className="pb-2.5 bg-white overflow-auto shadow-custom-xs rounded-lg flex-1 h-full w-full overflow-y-hidden">
      <div className="pt-2.5 w-full">
        <div className="px-[2.5px] border-b border-[#C2CDD6]">
          <span className="text-lg text-[#001926] leading-[150%] -tracking-[2.2%]">
            Doc Assist
          </span>
        </div>

        <form
          className="flex items-center mt-2 gap-2.5 w-full"
          onSubmit={handleSearch}
        >
          <input
            type="text"
            className="rounded-full shadow-custom-xs border border-[#DAE1E7] px-2.5 py-1.5 w-full placeholder:text-xs placeholder:italic"
            placeholder="Search"
            value={query}
            onChange={handleInput}
          />
          <IconButton onClick={handleSearch} size="small">
            <BiSearch className="text-xl" />
          </IconButton>
        </form>
      </div>

      <div className="flex flex-col gap-3 mt-2.5 mb-20 w-full">
        <div className="flex flex-col gap-2.5 px-[5px] w-full">
          {isSearching && (
            <div className="mt-8 w-full">
              <Loading />
            </div>
          )}

          {!isSearching && knowledge && (
            <div className="flex flex-col w-full gap-2.5 p-2.5 bg-[#DAE1E7] rounded-lg rounded-thin-scrollbar max-h-[calc(100vh-10.5rem)] overflow-y-auto">
              <Markdown>{knowledge}</Markdown>
            </div>
          )}

          {!isSearching && !knowledge && (
            <div className="flex flex-col w-full items-center justify-center mt-8 text-gray-400">
              Search for something to see results here
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
