'use client';

import { useEffect } from 'react';

import { FaHome } from 'react-icons/fa';
import { MdErrorOutline } from 'react-icons/md';

import Link from 'next/link';

type ErrorPageProps = {
  readonly error: Error & { digest?: string };
  readonly reset: () => void;
};

export default function ErrorPage({ error, reset }: ErrorPageProps) {
  useEffect(() => {
    const errorMessage = error?.message || 'An unknown error occurred';
    const errorStack = error?.stack ?? '';
    console.error('Error:', errorMessage, '\nStack:', errorStack);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-white">
      <div className="text-center space-y-6 p-8 max-w-lg mx-auto">
        <div className="flex justify-center">
          <MdErrorOutline className="text-[#D01010] text-8xl animate-pulse" />
        </div>
        <h1 className="text-4xl font-bold text-[#D01010]">
          Something went wrong!
        </h1>
        <p className="text-[#637D92] max-w-md mx-auto">
          We apologize for the inconvenience. An unexpected error has occurred.
          Our team has been notified and is working to fix the issue.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={reset}
            className="inline-flex items-center gap-2 px-6 py-3 rounded-lg text-white bg-[#012436] hover:bg-[#001926] transition-colors duration-200"
          >
            <span>Try Again</span>
          </button>
          <Link
            href="/"
            className="inline-flex items-center gap-2 px-6 py-3 rounded-lg text-[#012436] border border-[#012436] hover:bg-[#012436] hover:text-white transition-colors duration-200"
          >
            <FaHome />
            <span>Return Home</span>
          </Link>
        </div>
      </div>
    </div>
  );
}
