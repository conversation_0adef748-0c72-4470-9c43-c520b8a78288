import React from 'react';

import { UseFormReturn, useWatch } from 'react-hook-form';

import EditableText from '@/lib/common/editable_text';
import TextInput from '@/lib/text_input';

import { SummarizeConversationRes } from '@/query/speech';

import {
  vitalSignFields,
  anthropometryFields,
  generalPhysicalExaminationFields,
  generalExaminationFields,
  GeneralPhysicalExaminationField,
  AnthropometryField,
  VitalField,
  SystemicExaminationField,
  systemicExaminationFields,
} from '@/utils/constants/consultation';

import { Switch } from '@/components/ui/switch';

type ExaminationTabProps = {
  editable: boolean;
  data: any;
  expanded: boolean;
  form: UseFormReturn<SummarizeConversationRes['summary']>;
  handleChangeVitalSignField: (field: VitalField, newValue?: string) => void;
  handleChangeAnthropometryField: (
    field: AnthropometryField,
    newValue?: string
  ) => void;
  handleChangeGeneralPhysicalExaminationField: (
    field: GeneralPhysicalExaminationField,
    newValue: boolean | string
  ) => void;
  handleChangeHEENTField: (newValue?: string) => void;
  handleChangeSystemicExaminationField: (
    field: SystemicExaminationField,
    newValue?: string
  ) => void;
};

// Reusable FieldInput for vital/anthropometry fields
const FieldInput: React.FC<{
  label: string;
  unit?: string;
  value?: string;
  defaultValue?: string;
  readOnly?: boolean;
  editable?: boolean;
  onChange?: (val?: string) => void;
  className?: string;
  inputClassname?: string;
}> = ({
  label,
  unit,
  value,
  defaultValue,
  readOnly,
  editable,
  onChange,
  className = '',
  inputClassname = '',
}) => (
  <div className={`flex flex-col w-[110px] min-w-[110px] ${className}`}>
    <span
      className="text-xs font-medium leading-tight mb-0.5 whitespace-nowrap overflow-visible break-words text-black"
      style={{
        whiteSpace: 'nowrap',
        overflow: 'visible',
        textOverflow: 'unset',
      }}
    >
      {label}
    </span>
    <span className="block text-xs font-normal leading-tight mb-1 min-h-[14px] text-black">
      {unit || ''}
    </span>
    <TextInput
      label={undefined}
      className="w-full"
      inputClassname={`w-4/6 h-8 min-h-8 max-h-8 px-2 py-1 text-xs ${!editable ? 'read-only:bg-[#E8EBED] bg-[#E8EBED]' : 'bg-white'} ${inputClassname}`}
      variant={'bordered'}
      readOnly={readOnly}
      defaultValue={defaultValue}
      value={value}
      onChange={onChange}
    />
  </div>
);

const ExaminationTab: React.FC<ExaminationTabProps> = ({
  editable,
  data,
  expanded,
  form,
  handleChangeVitalSignField,
  handleChangeAnthropometryField,
  handleChangeGeneralPhysicalExaminationField,
  handleChangeHEENTField,
  handleChangeSystemicExaminationField,
}) => {
  const anthropometry = useWatch({
    control: form.control,
    name: 'anthropometry',
  });
  const generalPhysicalExamination = useWatch({
    control: form.control,
    name: 'generalPhysicalExamination',
  });

  return (
    <div className="flex flex-col gap-1.5">
      <section>
        <h3 className="font-medium text-sm text-[#001926] mb-2">Vital Signs</h3>
        <div className="flex flex-wrap gap-3 mt-2 items-center">
          {vitalSignFields.map((field) => (
            <FieldInput
              key={field.key}
              label={field.label}
              unit={field.unit}
              editable={editable}
              readOnly={!editable}
              defaultValue={
                data?.summary?.vitals?.[field.key as VitalField] != null
                  ? data?.summary?.vitals[field.key as VitalField]?.toString()
                  : ''
              }
              onChange={(newValue) =>
                handleChangeVitalSignField(field.key as VitalField, newValue)
              }
            />
          ))}
        </div>
      </section>
      <section>
        <h3 className="font-medium text-sm text-[#001926] mb-2">Anthropometry</h3>
        <div className="flex flex-nowrap gap-3 mt-2">
          {anthropometryFields.map((field) => (
            <FieldInput
              key={field.key}
              label={field.label}
              unit={field.unit}
              editable={editable}
              readOnly={!editable || field.key === 'bmi'}
              defaultValue={
                data?.summary?.anthropometry?.[
                  field.key as AnthropometryField
                ] != null
                  ? data?.summary?.anthropometry[
                      field.key as AnthropometryField
                    ]?.toString()
                  : ''
              }
              value={
                anthropometry &&
                `${anthropometry[field.key as AnthropometryField] || ''}`
              }
              onChange={(newValue) =>
                handleChangeAnthropometryField(
                  field.key as AnthropometryField,
                  newValue
                )
              }
            />
          ))}
        </div>
      </section>
      <section>
        <h3 className="font-medium text-sm text-[#001926] mb-2">General Physical Examination</h3>
        <div className="flex flex-wrap gap-3 mt-2 items-start">
          {/* Simple fields without notes */}
          {generalPhysicalExaminationFields.map((field) => {
            const isEnabled =
              data?.summary?.generalPhysicalExamination &&
              !!data?.summary?.generalPhysicalExamination[
                field.key as GeneralPhysicalExaminationField
              ];
            return (
              <div
                key={field.key}
                className="flex flex-col gap-1 items-start mt-3"
              >
                <div className="flex gap-2 items-center">
                  <label
                    htmlFor={field.key}
                    className="text-sm whitespace-nowrap"
                  >
                    {field.label}
                  </label>
                  {editable || expanded ? (
                    <Switch
                      id={field.key}
                      disabled={!editable}
                      defaultChecked={isEnabled}
                      onCheckedChange={(checked) =>
                        handleChangeGeneralPhysicalExaminationField(
                          field.key as GeneralPhysicalExaminationField,
                          checked
                        )
                      }
                    />
                  ) : (
                    <span className="text-sm gap-2 flex">
                      <span>:</span>
                      <span>{isEnabled ? ' Yes' : ' No'}</span>
                    </span>
                  )}
                </div>
              </div>
            );
          })}

          {generalExaminationFields.map((field) => {
            const isEnabled =
              data?.summary?.generalPhysicalExamination &&
              !!data?.summary?.generalPhysicalExamination[
                field.key as GeneralPhysicalExaminationField
              ];
            return (
              <div
                key={field.key}
                className={`flex flex-col  gap-1  rounded-lg p-3 w-auto ${editable ? 'bg-[#E6F6FF]' : ''}`}
              >
                <div className="flex gap-3 items-center">
                  <label
                    htmlFor={field.key}
                    className="text-sm whitespace-nowrap"
                  >
                    {field.label}
                  </label>
                  {editable || expanded ? (
                    <Switch
                      id={field.key}
                      disabled={!editable}
                      defaultChecked={isEnabled}
                      onCheckedChange={(checked) =>
                        handleChangeGeneralPhysicalExaminationField(
                          field.key as GeneralPhysicalExaminationField,
                          checked
                        )
                      }
                    />
                  ) : (
                    <span className="text-sm gap-2 flex">
                      <span>:</span>
                      <span>{isEnabled ? ' Yes' : ' No'}</span>
                    </span>
                  )}
                </div>
                {field.key === 'pedalEnema' &&
                  generalPhysicalExamination?.pedalEnema === true && (
                    <TextInput
                      className="mt-1 min-w-[120px] max-w-[180px]"
                      placeholder="Notes"
                      readOnly={!editable}
                      inputClassname={`h-8 px-2 py-1 text-xs ${editable ? 'bg-[#B4E5FE]' : 'read-only:bg-[#E8EBED]'}`}
                      defaultValue={
                        generalPhysicalExamination?.pedalEnemaNotes ||
                        data?.summary?.generalPhysicalExamination
                          ?.pedalEnemaNotes ||
                        ''
                      }
                      onChange={(newValue) =>
                        handleChangeGeneralPhysicalExaminationField(
                          'pedalEnemaNotes' as GeneralPhysicalExaminationField,
                          newValue as string
                        )
                      }
                    />
                  )}
                {field.key === 'lymphadenopathy' &&
                  generalPhysicalExamination?.lymphadenopathy === true && (
                    <TextInput
                      className="mt-1 w-[140px]"
                      placeholder="Notes"
                      readOnly={!editable}
                      inputClassname={`h-8 px-2 py-1 text-xs ${editable ? 'bg-[#B4E5FE]' : 'read-only:bg-[#E8EBED]'}`}
                      defaultValue={
                        generalPhysicalExamination?.lymphadenopathyNotes ||
                        data?.summary?.generalPhysicalExamination
                          ?.lymphadenopathyNotes ||
                        ''
                      }
                      onChange={(newValue) =>
                        handleChangeGeneralPhysicalExaminationField(
                          'lymphadenopathyNotes' as GeneralPhysicalExaminationField,
                          newValue as string
                        )
                      }
                    />
                  )}
              </div>
            );
          })}
        </div>
      </section>
      <section>
        <h3 className="font-medium text-base text-[#001926] ">HEENT</h3>
        <EditableText
          editable={editable}
          bg={'white'}
          contentClassName="min-h-[36px] py-2 max-h-[96px] overflow-y-auto border border-[#DAE1E7] bg-white rounded-md px-3 shadow-sm mb-2"
          defaultValue={data?.summary?.heent}
          onChange={handleChangeHEENTField}
          emptyPlaceholder={'Nil'}
        />
      </section>
      <section >
        <h3 className="font-medium text-base text-[#001926] mb-2">Systemic Examination</h3>
        {systemicExaminationFields.map((field) => (
          <EditableText
            key={field.key}
            className="mt-2.5"
            label={field.label}
            labelSize="Small"
            editable={editable}
            bg={'white'}
            emptyPlaceholder={'Nil'}
            contentClassName="min-h-[36px] py-2 max-h-[96px] overflow-y-auto border border-[#DAE1E7] bg-white rounded-md px-3 shadow-sm mb-2"
            defaultValue={
              data?.summary?.systemicExamination
                ? data?.summary?.systemicExamination[
                    field.key as SystemicExaminationField
                  ]
                : ''
            }
            onChange={(newValue) =>
              handleChangeSystemicExaminationField(
                field.key as SystemicExaminationField,
                newValue
              )
            }
          />
        ))}
      </section>
    </div>
  );
};

export default ExaminationTab;
