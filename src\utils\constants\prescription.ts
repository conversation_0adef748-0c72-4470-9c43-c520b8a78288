import { SxProps, Theme } from '@mui/material';
import { TableCellProps } from '@mui/material';

import { Header } from '@/core/components/table/types';
import {
  actionButtonCellProps,
  getFileCellProps,
} from '@/types/emr/doctor-profile/personal-info';

import colors from '../colors';

export type DurationType = (typeof DURATION_TYPES)[number];

export type DurationCodeType = 'D' | 'W' | 'M';

const baseStyle = (minWidth: number, sxProps?: SxProps<Theme>) => ({
  sx: {
    minWidth,
    p: '5px !important',
    ...sxProps,
  },
});

export const prescriptionHeaders: Header[] = [
  { key: 'drugForm', header: 'Drug Form', cellProps: baseStyle(60) },
  {
    key: 'genericName',
    header: 'Generic Name',
    cellProps: baseStyle(80, { whiteSpace: 'normal' }),
  },
  {
    key: 'brandName',
    header: 'Brand Name',
    cellProps: baseStyle(80, { whiteSpace: 'normal' }),
  },
  { key: 'strength', header: 'Strength', cellProps: baseStyle(50) },
  { key: 'measure', header: 'Measure', cellProps: baseStyle(50) },
  { key: 'uom', header: 'UOM', cellProps: baseStyle(20) },

  { key: 'frequency', header: 'Frequency', cellProps: baseStyle(60) },
  { key: 'duration', header: 'Duration', cellProps: baseStyle(40) },
  { key: 'quantity', header: 'QTY', cellProps: baseStyle(40) },
  { key: 'route', header: 'Route', cellProps: baseStyle(50) },
  {
    key: 'instructions',
    header: 'Instructions',
    cellProps: baseStyle(90, { whiteSpace: 'normal' }),
  },
  { key: 'cost', header: 'Cost', cellProps: baseStyle(30) },
  {
    key: 'canSubstitute',
    header: 'Do not Substitute',
    cellProps: baseStyle(60, { whiteSpace: 'normal' }),
  },
  { key: 'close', header: 'Close', cellProps: baseStyle(40) },
];

export const departmentPackageHeaders: Header[] = [
  { key: 'drugForm', header: 'Drug Form' },
  {
    key: 'genericName',
    header: 'Generic Name',
    cellProps: { sx: { minWidth: 120 } },
  },
  {
    key: 'brandName',
    header: 'Brand Name',
    cellProps: { sx: { minWidth: 120 } },
  },
  { key: 'strength', header: 'Strength' },
  { key: 'select', header: 'Select' },
];

export const frequencyOptions = [
  { code: 'q8h', label: 'every 8 hours' },
  { code: 'q12h', label: 'every 12 hours' },
  { code: 'qd', label: 'every day, once a day' },
  { code: 'bid', label: 'twice a day' },
  { code: 'tid', label: 'three times a day' },
  { code: 'qid', label: 'four times a day' },
  { code: 'hs', label: 'at bedtime' },
  { code: 'pc', label: 'after meals' },
  { code: 'ac', label: 'before meals' },
  { code: 'prn', label: 'as needed' },
];

export const routeOptions = [
  { code: 'PO', label: 'Oral' },
  { code: 'SL', label: 'Sublingual' },
  { code: 'BUCC', label: 'Buccal' },
  { code: 'PR', label: 'Rectal' },
  { code: 'PV', label: 'Vaginal' },
  { code: 'TOP', label: 'Topical' },
  { code: 'TD', label: 'Transdermal' },
  { code: 'NAS', label: 'Nasal' },
  { code: 'INH', label: 'Inhalation' },
  { code: 'IV', label: 'Intravenous' },
  { code: 'IM', label: 'Intramuscular' },
  { code: 'SC', label: 'Subcutaneous' },
  { code: 'ID', label: 'Intradermal' },
  { code: 'IO', label: 'Intraocular' },
  { code: 'OPH', label: 'Ophthalmic (eye drops)	' },
  { code: 'OT', label: 'Otic (ear drops)' },
  { code: 'ENT', label: 'Enteral' },
  { code: 'PAR', label: 'Parenteral' },
];

export const DURATION_TYPES = ['Days', 'Week', 'Months'] as const;

export const DURATION_TYPE_MAPPING: Record<DurationCodeType, DurationType> = {
  D: 'Days',
  W: 'Week',
  M: 'Months',
};

export const DURATION_CODE_MAPPING: Record<DurationType, DurationCodeType> = {
  Days: 'D',
  Week: 'W',
  Months: 'M',
};

export const editableKeys = [
  'frequency',
  'duration',
  'route',
  'instructions',
  'quantity',
  'canSubstitute',
  'close',
];

export const tableSxStyles: SxProps<Theme> = {
  maxHeight: 'calc(100vh - 17rem)',
  '& tbody td': {
    height: 30,
    border: '0.5px solid black !important',
    borderTop: 'none !important',
  },
  '& tbody tr td:first-of-type': {
    borderLeft: '1.5px solid black !important',
  },
  '& tbody tr td:last-of-type': {
    borderRight: '1.5px solid black !important',
  },
};

export const getCellStyling = ({
  isReadOnly = false,
  isNonEmpty = false,
  isValid = true,
}): TableCellProps => ({
  ...getFileCellProps(isReadOnly),
  sx: {
    ...(getFileCellProps(isReadOnly).sx || {}),
    ...(isNonEmpty && !isValid
      ? {
          border: `1px solid ${colors.common.redRose} !important`,
          background: ` ${colors.common.paleBlush}`,
        }
      : {}),
    ...(isReadOnly ? { pointerEvents: 'none' } : {}),
  },
});

export const getInstructionsStyling = ({
  isReadOnly = false,
  isNonEmpty = false,
  isValid = true,
}): TableCellProps => ({
  ...getCellStyling({ isReadOnly, isNonEmpty, isValid }),
  sx: {
    ...getCellStyling({ isReadOnly, isNonEmpty, isValid }).sx,
    minWidth: 150,
  },
});

export const getActionButtonCellProps = (
  isReadOnly = false
): TableCellProps => ({
  ...actionButtonCellProps,
  sx: {
    ...(actionButtonCellProps?.sx || {}),
    paddingTop: '4px !important',
    ...(isReadOnly && {
      backgroundColor: 'rgb(229 231 235 / var(--tw-bg-opacity))',
      pointerEvents: 'none',
    }),
  },
});
