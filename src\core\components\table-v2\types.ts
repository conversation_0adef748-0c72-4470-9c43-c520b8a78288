import { ReactNode } from 'react';

import { TableCellProps } from '@mui/material';
import { TableContainerProps } from '@mui/material/TableContainer';

export const sortOrder = {
  ASC: 'asc',
  DESC: 'desc',
} as const;

export type SortOrder = (typeof sortOrder)[keyof typeof sortOrder];

export type SortOptionsV2 = {
  fieldName: string | null;
  order: SortOrder | null;
  onSort: (fieldName: string, order: SortOrder | null) => void;
};

export type SortButtonV2Props = {
  sortOrder: SortOrder | null;
};

export type HeaderV2Property = {
  key: string;
  header: ReactNode;
  cellProps?: TableCellProps;
  sortFieldName?: string | null;
  sortOptions?: SortOptionsV2;
};

export type HeaderV2 = HeaderV2Property & {
  subHeaders?: HeaderV2[];
};

export type CellV2 = {
  value: React.ReactNode;
  cellProps?: TableCellProps;
  returnNullForEmpty?: boolean;
};

export type SubCellV2 = {
  [subKey: string]: CellV2;
};

export type RowV2 = {
  [key: string]: CellV2 | SubCellV2 | string;
};

export type TableV2Props = {
  headers: HeaderV2[];
  rows: RowV2[];
  loading?: boolean;
  stickyHeader?: boolean;
  tableContainerProps?: TableContainerProps;
  noDataMessage?: ReactNode;
  sortOptions?: SortOptionsV2;
};

export type TableHeaderV2Props = {
  headers: HeaderV2[];
  sortOptions?: SortOptionsV2;
};

export type TableBodyContentV2Props = {
  loading?: boolean;
  headers: HeaderV2[];
  rows: RowV2[];
  noDataMessage?: ReactNode;
};
