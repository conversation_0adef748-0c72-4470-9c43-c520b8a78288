{
  "recommendations": [
    // Essential for Next.js/React development
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",

    // React/JSX specific
    "dsznajder.es7-react-js-snippets",
    "burkeholland.simple-react-snippets",

    // TypeScript enhancements
    "usernamehw.errorlens",
    "yoavbls.pretty-ts-errors",
    "ms-vscode.typescript-importer",

    // Code quality and formatting
    "streetsidesoftware.code-spell-checker",
    "editorconfig.editorconfig",
    "aaron-bond.better-comments",

    // Git integration
    "eamodio.gitlens",

    // Documentation and markdown
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    "bierner.markdown-mermaid"
  ],
  "unwantedRecommendations": [
    // Avoid conflicting formatters
    "hookyqr.beautify",
    "ms-vscode.vscode-typescript",
    "vscode.typescript-language-features"
  ]
}
