import { useState } from 'react';

import ExpandNoteIcon from '@/assets/svg/ExpandNoteIcon';
import StatusInfoIcon from '@/assets/svg/StatusInfoIcon';

import {
  infoHealthMetrics,
  InfoPatient,
  noteInfoSections,
} from '@/constants/emr/doctor-profile/customise-emr';

const tabs = ['Today', '12/02/24', '12/12/23'];

export function PatientVitalsPopover() {
  const [activeTab, _setActiveTab] = useState('Today');

  return (
    <div className=" max-w-sm bg-white">
      <div className="p-1">
        <p className=" text-xs mb-2">
          Customize the patient vitals grid to suit your needs. Click on these
          options to select your preferred layout and choose which vitals to
          display on each tile in the consultation screen.
        </p>
        <div className="bg-white rounded-lg shadow-sm  mt-2">
          <div className="border rounded-lg">
            <div className=" px-2  py-0.5  border-b">
              <span className=" font-medium text-[9.5px]">Now Consulting</span>
            </div>
            <div className="border-b p-2 flex items-center gap-2">
              <div className=" rounded-full h-7 w-7 flex items-center p-1 justify-center text-[9.5px] border border-red-300">
                {InfoPatient.initials}
              </div>
              <div>
                <p className="font-medium text-[9.5px]">{InfoPatient.name}</p>
                <p className="text-[8px] text-gray-600">
                  {InfoPatient.age} | {InfoPatient.gender} |{' '}
                  {InfoPatient.location}
                </p>
              </div>
            </div>
          </div>
          <div className="flex border-b justify-evenly">
            {tabs.map((tab) => (
              <div
                key={tab}
                className={`py-1 px-2 text-[9.5px] cursor-pointer ${
                  activeTab === tab
                    ? 'border-b-2 border-blue-500 text-blue-500'
                    : 'text-black-500'
                }`}
              >
                {tab}
              </div>
            ))}
          </div>

          <div className="flex justify-around p-1">
            {infoHealthMetrics.map((metric, index) => (
              <div
                key={index}
                className="bg-white rounded border border-gray-200 shadow-sm p-1 flex-1 mx-0.5"
              >
                <div className="flex flex-col items-start">
                  <div className="flex items-baseline">
                    <span className="text-[15px] ">{metric.value}</span>
                    {metric.unit && (
                      <span className="text-[11px]  ml-0.5">{metric.unit}</span>
                    )}
                  </div>
                  <div className="flex items-center justify-between w-full">
                    <span className="text-[8px] font-medium">
                      {metric.label}
                    </span>
                    <div
                      className="flex items-center text-xxs"
                      style={{ color: metric.color }}
                    >
                      <StatusInfoIcon color={metric.color} />
                      <span className="text-[8px]">{metric.change}%</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export function NotesPopover() {
  return (
    <div className="max-w-xs bg-white">
      <div className="p-2">
        <p className="text-xs mb-2 pb-2 border-b leading-tight">
          Customize the notes tab displayed on the patient info bar in the
          consultation screen. Select the options based on your preferences.
        </p>

        {noteInfoSections.map((section, idx) => (
          <div
            key={idx}
            className="border border-gray-200 rounded-md shadow-sm p-2 flex flex-col gap-1 mt-1"
          >
            <div className="flex items-center justify-between">
              <span className="font-medium text-[9.5px]">{section.title}</span>
              <ExpandNoteIcon className="w-2 h-2" />
            </div>
            <ul className="list-disc list-inside text-[7px] mt-1">
              {section.items.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
}
