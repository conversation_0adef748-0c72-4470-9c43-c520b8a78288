import React, { memo } from 'react';

import { FaPlus } from 'react-icons/fa';

import Link from 'next/link';

import { useMrdPatientSearch } from '@/store/mrd/manage-patient/patient-search';

import PenIcon from '@/assets/svg/PenIcon';

import { routes } from '@/constants/routes';

import VitalsCard from '@/components/vitals-card';

import AppButton from '@/core/components/app-button';

import UpdateVitals from './UpdateVitals';

const { MRD_MANAGE_PATIENTS_BOOK_CONSULTATION, MRD_MANAGE_PATIENTS_MANAGE } =
  routes;

const PatientVitals = () => {
  const { vitalsLoading, calculatedVital, patient } = useMrdPatientSearch();

  return (
    <div className="flex gap-base justify-between py-1.5 h-24 w-full">
      <div className="flex gap-base py-1 min-h-[76px] h-full w-[calc(100%-17.5rem)] overflow-x-auto overflow-y-hidden scrollbar-minimal">
        {calculatedVital.map((vitalCard, i) => (
          <VitalsCard key={i} loading={vitalsLoading} {...vitalCard} />
        ))}
      </div>
      <div className="flex flex-nowrap justify-between h-full w-70">
        <UpdateVitals />
        <div className="flex flex-col justify-between">
          <AppButton
            size="small"
            variant="outlined"
            startIcon={<PenIcon className="w-3.5 h-3.5" />}
            component={Link}
            href={`${MRD_MANAGE_PATIENTS_MANAGE}/${patient?.id}`}
          >
            Edit Patient Details
          </AppButton>
          <AppButton
            size="small"
            startIcon={<FaPlus className="w-3.5 h-3.5" fontSize={16} />}
            component={Link}
            href={`${MRD_MANAGE_PATIENTS_BOOK_CONSULTATION}/${patient?.id}`}
          >
            Book Consultation
          </AppButton>
        </div>
      </div>
    </div>
  );
};

export default memo(PatientVitals);
