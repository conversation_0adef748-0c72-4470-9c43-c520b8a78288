import React, { memo, useCallback, useEffect, useRef, useState } from 'react';

import { useFieldArray, useForm } from 'react-hook-form';

import Stack from '@mui/material/Stack';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import {
  discardDefaultValues,
  getUniqueId,
} from '@/utils/emr/doctor-profile/personal-info';

import { profileTabs } from '@/constants/emr/doctor-profile/personal-info';

import AddButton from '@/emr/components/lifestyle/lifestyle-forms/shared/AddButton';
import { useFileUpload } from '@/emr/hooks/use-file-upload';

import {
  defaultQualification,
  ItemToDelete,
  type Qualification,
} from '@/types/emr/doctor-profile/personal-info';

import DeleteModal from '../shared/DeleteModal';
import SaveButton from '../shared/SaveButton';
import Title from '../shared/Title';

import QualificationMob from './QualificationMob';
import QualificationTable from './QualificationTable';

export type FormData = {
  qualifications: Qualification[];
};

const QualificationTab = () => {
  const { data: userData } = useUserStore();
  const isMobile = useIsMobile();
  const lastFieldRef = useRef<HTMLDivElement | null>(null);

  const [itemToEdit, setItemToEdit] = useState<number | null>(null);
  const [itemToDelete, setItemToDelete] = useState<ItemToDelete>(null);
  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    updateDoctorProfile,
    doctorProfile,
    isDeleting,
    createDoctorProfile,
    setTabName,
    deleteDoctorProfileTableItem,
  } = useDoctorStore();

  const { fileUpload } = useFileUpload({
    userId: doctorProfile?.id || userData?.id,
    type: 'qualification',
  });

  const { handleSubmit, control, reset, register, getValues } =
    useForm<FormData>({
      defaultValues: {
        qualifications: [isMobile ? defaultQualification : undefined],
      },
    });

  const { append, fields, remove } = useFieldArray<FormData>({
    control,
    name: 'qualifications',
  });

  const onSubmit = useCallback(
    async (data: FormData) => {
      const qualifications = discardDefaultValues(
        data?.qualifications,
        defaultQualification
      );

      const finalData = await Promise.all(
        qualifications.map(async (item) => {
          const doc1 = await fileUpload(item?.doc1);
          const doc2 = await fileUpload(item?.doc2);

          return {
            ...item,
            uuId: getUniqueId(item?.uuId, 'QLF'),
            doc1,
            doc2,
          };
        })
      );

      if (doctorProfile?.id) {
        updateDoctorProfile(doctorProfile.id, {
          professionalDetails: {
            ...doctorProfile?.professionalDetails,
            qualifications: finalData,
          },
        });
      } else {
        createDoctorProfile({
          professionalDetails: {
            ...doctorProfile?.professionalDetails,
            qualifications: finalData,
          },
          username: userData?.email,
        });
      }
      setItemToEdit(null);
      setIsSubmitted(true);
      setEditableField(new Set());
    },
    [
      doctorProfile?.id,
      doctorProfile?.professionalDetails,
      fileUpload,
      updateDoctorProfile,
      createDoctorProfile,
      userData?.email,
    ]
  );

  const handleItemEdit = useCallback(
    (index: number) => () => {
      setItemToEdit(index);
    },
    []
  );

  const handleOnAdd = useCallback(() => {
    setItemToEdit(fields.length);
    append(defaultQualification);
  }, [append, fields?.length]);

  const handleOnDelete = useCallback(
    (item: ItemToDelete) => setItemToDelete(item),
    []
  );

  const handleDelete = useCallback(async () => {
    if (!itemToDelete) return;

    const { uuId, index } = itemToDelete;

    if (uuId && doctorProfile?.id) {
      const qualifications = doctorProfile?.professionalDetails?.qualifications;

      const filteredQualification = qualifications?.filter(
        (item) => item?.uuId !== itemToDelete?.uuId
      );

      await deleteDoctorProfileTableItem(doctorProfile.id, {
        professionalDetails: {
          ...doctorProfile?.professionalDetails,
          qualifications: filteredQualification,
        },
      });
    } else {
      if (index === 0 && isMobile) {
        reset({
          qualifications: [defaultQualification] as Qualification[],
        });
      } else {
        remove(index);
      }
    }

    setItemToDelete(null);
  }, [
    itemToDelete,
    doctorProfile?.id,
    deleteDoctorProfileTableItem,
    remove,
    reset,
    isMobile,
    doctorProfile?.professionalDetails,
  ]);

  const handleCancel = useCallback(() => {
    setItemToDelete(null);
  }, []);

  useEffect(() => {
    if (doctorProfile?.professionalDetails?.qualifications?.length) {
      reset({
        qualifications: doctorProfile?.professionalDetails?.qualifications,
      });
    } else if (isMobile) {
      reset({ qualifications: [defaultQualification] });
    }
  }, [doctorProfile?.professionalDetails?.qualifications, reset, isMobile]);

  useEffect(() => {
    setTabName(profileTabs.QUALIFICATION);
  }, [setTabName]);

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <Stack
      component="form"
      spacing={2}
      pb={{ xs: 8, md: 0 }}
      onSubmit={handleSubmit(onSubmit)}
    >
      <Title
        title="Qualification"
        onAdd={handleOnAdd}
        showEndButton={!isMobile}
      />
      {isMobile ? (
        <QualificationMob
          fields={fields}
          control={control}
          register={register}
          editableField={editableField}
          getValues={getValues}
          isSubmitted={isSubmitted}
          setEditableField={setEditableField}
          handleOnDelete={handleOnDelete}
          handleOnAdd={handleOnAdd}
          lastFieldRef={lastFieldRef}
        />
      ) : (
        <QualificationTable
          fields={fields}
          control={control}
          itemToEdit={itemToEdit}
          handleItemEdit={handleItemEdit}
          handleOnDelete={handleOnDelete}
        />
      )}

      {isMobile && (
        <div className="fixed bottom-34 md:static right-6 bg-transparent">
          <AddButton size="medium" onClick={handleOnAdd} />
        </div>
      )}
      <SaveButton />
      <DeleteModal
        open={!!itemToDelete}
        onClose={handleCancel}
        onDelete={handleDelete}
        isLoading={isDeleting}
      />
    </Stack>
  );
};

export default memo(QualificationTab);
