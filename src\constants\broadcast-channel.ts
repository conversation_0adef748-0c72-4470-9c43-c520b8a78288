export const BROAD_CAST_CHANNEL = 'emr-mrd-app-channel';

export const broadCastActions = {
  CREATE_CONSULTATION: 'create-consultation',
  UPDATE_VITALS: 'update-vitals',
  PATIENT_UPDATED: 'patient-updated', // <-- Add this action
} as const;

export type BroadCastActions =
  (typeof broadCastActions)[keyof typeof broadCastActions];

export type BaseMessage = {
  type: BroadCastActions;
};

export type PatientUpdatedMessage = BaseMessage & {
  type: typeof broadCastActions.PATIENT_UPDATED;
  patientId: string;
};
