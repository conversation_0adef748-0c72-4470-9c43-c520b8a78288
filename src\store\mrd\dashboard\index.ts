import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { fetchAllDoctors, useDoctorStore } from '@/store/mrd/queue/doctor';

import {
  getUpcomingAppointments,
  getDashboardWidgetData,
} from '@/query/mrd/dashboard';

import { Doctor } from '@/types/emr/lifestyle/questionnaire';

// Helper function to format date for API
const formatDateForAPI = (date: Date | undefined): string | null => {
  if (!date) return null;
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export interface UpcomingAppointment {
  id: string;
  patientId: string;
  patientName: string;
  doctorId: string;
  doctorName: string;
  appointmentDate: string;
  appointmentTime: string;
  status: 'scheduled' | 'confirmed' | 'cancelled' | 'completed';
  department: string;
}

export interface DashboardWidgetData {
  totalPatients: number;
  todaysAppointments: number;
  patientQueue: number;
  averageWaitingTime: number;
  averageWaitingTimeDetails: {
    averageWaitingTime: number;
    totalCompletedConsultations: number;
    validCalculations: number;
    unit: string;
    date: string;
  };
  organizationId: string;
  date: string;
}

export interface DashboardFilters {
  selectedDoctorId: string | null;
  selectedDate: string | null;
  searchQuery: string;
  page: number;
  pageSize: number;
}

export interface DashboardState {
  // Data
  upcomingAppointments: UpcomingAppointment[];
  doctors: Doctor[];
  widgetData: DashboardWidgetData | null;

  // Loading states
  isLoadingAppointments: boolean;
  isLoadingDoctors: boolean;
  isLoadingWidgetData: boolean;

  // Filters
  filters: DashboardFilters;

  // Pagination
  totalItems: number;
  totalPages: number;

  // Actions
  setFilters: (filters: Partial<DashboardFilters>) => void;
  setSelectedDoctor: (doctorId: string | null) => void;
  setSelectedDate: (date: string | null) => void;
  setSearchQuery: (query: string) => void;
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;

  // API Actions
  fetchUpcomingAppointments: () => Promise<void>;
  fetchDoctors: () => Promise<void>;
  fetchWidgetData: () => Promise<void>;

  // Reset
  resetFilters: () => void;
}

const initialFilters: DashboardFilters = {
  selectedDoctorId: null,
  selectedDate: null,
  searchQuery: '',
  page: 1,
  pageSize: 4, // Set default page size to 4 as requested
};

export const useMrdDashboardStore = create<DashboardState>()(
  persist(
    (set, get) => ({
      // Initial state
      upcomingAppointments: [],
      doctors: [],
      widgetData: null,
      isLoadingAppointments: false,
      isLoadingDoctors: false,
      isLoadingWidgetData: false,
      filters: initialFilters,
      totalItems: 0,
      totalPages: 0,

      // Filter actions
      setFilters: (newFilters) => {
        set((state) => ({
          filters: { ...state.filters, ...newFilters },
        }));
        // Fetch appointments when filters change
        get().fetchUpcomingAppointments();
      },

      setSelectedDoctor: (doctorId) => {
        set((state) => ({
          filters: { ...state.filters, selectedDoctorId: doctorId, page: 1 },
        }));
        get().fetchUpcomingAppointments();
      },

      setSelectedDate: (date) => {
        set((state) => ({
          filters: { ...state.filters, selectedDate: date, page: 1 },
        }));
        get().fetchUpcomingAppointments();
      },

      setSearchQuery: (query) => {
        set((state) => ({
          filters: { ...state.filters, searchQuery: query, page: 1 },
        }));
        // Debounce search - you might want to implement debouncing
        get().fetchUpcomingAppointments();
      },

      setPage: (page) => {
        set((state) => ({
          filters: { ...state.filters, page },
        }));
        get().fetchUpcomingAppointments();
      },

      setPageSize: (pageSize) => {
        set((state) => ({
          filters: { ...state.filters, pageSize, page: 1 },
        }));
        get().fetchUpcomingAppointments();
      },

      // API actions
      fetchUpcomingAppointments: async () => {
        const { filters } = get();
        set({ isLoadingAppointments: true });

        try {
          // Get the selected doctor and date from the doctor store
          const doctorStore = useDoctorStore.getState();
          const selectedDoctor = doctorStore.selectedDoctor;
          const selectedDate = doctorStore.date;

          const queryParams: Record<string, any> = {
            page: filters.page,
            pageSize: filters.pageSize,
          };

          if (selectedDoctor?.id) {
            queryParams.doctorId = selectedDoctor.id;
          }

          const formattedDate = formatDateForAPI(selectedDate);
          if (formattedDate) {
            queryParams.date = formattedDate;
          }

          if (filters.searchQuery) {
            queryParams.patientName = filters.searchQuery;
          }

          const response = await getUpcomingAppointments(queryParams);

          // Map response data to match UpcomingAppointment interface
          const rawAppointments =
            response.upcomingAppointments || response.data || [];
          const mappedAppointments = rawAppointments.map((appt: any) => ({
            id: appt.appointmentId,
            patientId: appt.patientId,
            patientName: appt.patientName,
            doctorId: appt.doctorId,
            doctorName: appt.doctorName || 'N/A',
            appointmentDate: appt.date, // ISO string
            appointmentTime: appt.time,
            status: appt.status.replace('-Booked', '').toLowerCase() as
              | 'scheduled'
              | 'confirmed'
              | 'cancelled'
              | 'completed', // Clean status
            department: appt.department || '',
          }));

          set({
            upcomingAppointments: mappedAppointments,
            totalItems: response.totalItems,
            totalPages: response.totalPages,
            isLoadingAppointments: false,
          });
        } catch (error) {
          console.error('Error fetching upcoming appointments:', error);
          set({
            upcomingAppointments: [],
            totalItems: 0,
            totalPages: 0,
            isLoadingAppointments: false,
          });
        }
      },

      fetchDoctors: async () => {
        set({ isLoadingDoctors: true });

        try {
          // Use the same API as MRD queue
          await fetchAllDoctors();

          // Get doctors from the MRD queue store
          const { useDoctorStore } = await import('@/store/mrd/queue/doctor');
          const doctors = useDoctorStore.getState().doctors;

          set({
            doctors: doctors,
            isLoadingDoctors: false,
          });
        } catch (error) {
          console.error('Error fetching doctors:', error);
          set({
            doctors: [],
            isLoadingDoctors: false,
          });
        }
      },

      fetchWidgetData: async () => {
        set({ isLoadingWidgetData: true });

        try {
          const widgetData = await getDashboardWidgetData();
          set({
            widgetData,
            isLoadingWidgetData: false,
          });
        } catch (error) {
          console.error('Error fetching widget data:', error);
          set({
            widgetData: null,
            isLoadingWidgetData: false,
          });
        }
      },

      // Reset
      resetFilters: () => {
        set({ filters: initialFilters });
        get().fetchUpcomingAppointments();
      },
    }),
    {
      name: 'mrd-dashboard-store',
      partialize: (state) => ({
        filters: state.filters,
      }),
    }
  )
);
