'use client';

import useIsMobile from '@/hooks/use-mobile-layout';

import { ProfileForm } from '@/views/emr/doctor-profile/personal-info';
import { ProfileFormMob } from '@/views/emr/doctor-profile/personal-info/mobile-view/ProfileFormMob';

export default function PersonalInfoPage() {
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <div className="bg-white overflow-hidden">
        <ProfileFormMob />
      </div>
    );
  }
  
  return (
    <div className="col-span-7 h-full bg-white px-2 rounded-base shadow-base overflow-hidden">
      <ProfileForm />
    </div>
  );
}
