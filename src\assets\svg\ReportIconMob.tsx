import React from 'react';
import type { SVGProps } from 'react';

const ReportIconMob = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M10 19v-2q-2.075 0-3.537-1.463T5 12q0-1.525.838-2.775T8.1 7.4q.2-.85.888-1.375T10.55 5.5l-.225-.625q-.125-.4.038-.763t.562-.512h.025q-.15-.375.025-.737t.6-.513q.375-.125.738.038t.487.562q.4-.15.775.025t.525.575l2.05 5.625q.15.4-.012.763t-.563.512h-.025q.15.4-.025.775t-.6.525q-.375.125-.737-.037t-.488-.563q-.4.15-.775-.025t-.525-.575l-.25-.7q-.375.35-.862.525t-.988.125q-.55-.05-1.025-.337T8.45 9.45q-.675.4-1.062 1.075T7 12q0 1.25.875 2.125T10 15h7q.425 0 .713.288T18 16t-.288.713T17 17h-4v2h5q.425 0 .713.288T19 20t-.288.713T18 21H6q-.425 0-.712-.288T5 20t.288-.712T6 19zm3.65-9.45l.9-.35l-1.7-4.7l-.95.35zM10.5 9q.425 0 .713-.288T11.5 8t-.288-.712T10.5 7t-.712.288T9.5 8t.288.713T10.5 9m0-1"
      ></path>
    </svg>
  );
};

export default ReportIconMob;
