import React, { memo, useCallback, useEffect, useRef } from 'react';

import {
  Controller,
  FieldValues,
  UseControllerProps,
  useWatch,
} from 'react-hook-form';

type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>;

type Props<T extends FieldValues> = UseControllerProps<T> &
  TextareaProps & {
    showInputError?: boolean;
  };
const TableTextarea = <T extends FieldValues>({
  name,
  control,
  defaultValue,
  style,
  showInputError = true,
  ...inputProps
}: Props<T>) => {
  const textareaRef = useRef<HTMLTextAreaElement | null>(
    null
  ) as React.MutableRefObject<HTMLTextAreaElement | null>;

  const value = useWatch({ control, name });

  const autoResize = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = textarea.scrollHeight + 'px';
    }
  }, []);

  useEffect(() => {
    autoResize();
  }, [autoResize, value]);

  useEffect(() => {
    textareaRef.current?.addEventListener('resize', autoResize);
    return () => {
      textareaRef.current?.removeEventListener('resize', autoResize);
    };
  }, [autoResize]);

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}
      rules={inputProps.rules}
      render={({ field, fieldState }) => (
        <textarea
          {...inputProps}
          {...field}
          ref={(el) => {
            if (el) {
              textareaRef.current = el;
              field.ref(el);
            }
          }}
          rows={1}
          onInput={(e) => {
            autoResize();
            inputProps.onInput?.(e);
          }}
          style={{
            outline: 'none',
            border: '1px solid',
            borderColor:
              fieldState.error && showInputError ? '#E4626F' : 'transparent',
            resize: 'none',
            overflow: 'hidden',
            ...style,
          }}
          className={`p-2 text-sm bg-transparent w-full m-0 min-h-[40px] h-fit disabled:bg-gray-200 disabled:cursor-not-allowed align-middle`}
        />
      )}
    />
  );
};

export default memo(TableTextarea) as typeof TableTextarea;
