export type LabTestItem = {
  id?: string;
  no?: string;
  testName?: string;
  quantity?: string;
  instructions?: string;
  cost?: string;
  toBeDoneBy?: string;
  close?: string;
  results?: string | null;
  reference?: string | null;
  [key: string]: string | boolean | undefined | null;
};

export const defaultLabTestRow: LabTestItem = {
  id: '',
  no: '',
  testName: '',
  quantity: '',
  instructions: '',
  cost: '',
  toBeDoneBy: '',
  close: '',
};

export const testReportTabs = {
  NEW_TEST: 'new_test',
  TEST_RESULTS: 'test_results',
  ORDER_HISTORY: 'order_history',
} as const;

export type LabTestTabType =
  | (typeof testReportTabs)[keyof typeof testReportTabs]
  | string;

export const labTestModalTypes = {
  SAVE: 'save',
  CLEAR: 'clear',
} as const;

export type LabTestModalType =
  | (typeof labTestModalTypes)[keyof typeof labTestModalTypes]
  | string;

export const modalModes = {
  VIEW: 'view',
  CREATE: 'create',
  DETAIL: 'detail',
  ADD: 'add',
} as const;

export const packageTypes = {
  DEPARTMENT: 'department',
  USER: 'user',
  DEFAULT: 'default',
  NEW_TEST: 'new_test',
} as const;

export type ModalMode = (typeof modalModes)[keyof typeof modalModes];
export type PackageType =
  | (typeof packageTypes)[keyof typeof packageTypes]
  | null;

export type LabTest = {
  id?: string;
  testName: string;
  reference: string | null;
  results: string | null;
  qty: number;
  instructions: string;
  cost: number;
  toBeDoneBy: string;
  date: string;
  testId: string;
  department: string;
  fileMetadata: FileMetaData[];
  status: StatusType;
};

export type StatusType =
  | 'Ready'
  | 'Awaited'
  | 'Not Paid'
  | 'Upload'
  | 'Uploaded';

export type FileMetaData = {
  id: string;
  fileName: string;
  fileSize: number;
  blobPath: string;
  encryptionKey: string;
  iv: string;
  patientId: string;
  labTestId: string;
  ocrStatus: string;
  detectedLanguage: string | null;
  ocrData: string | null;
  fileType: string;
  uploadedAt: string;
};

export type TestResultItem = {
  id: string;
  labTests: Record<string, LabTest[]>;
  status: StatusType;
  patientId: string;
  created_on: string;
  updated_on: string;
};

export type FileUploadParams = {
  file: File;
  patientId: string;
  labTestId: string;
};

export type OrderStatusType =
  | 'Ready'
  | 'Awaited'
  | 'Not Paid'
  | 'Upload'
  | 'Uploaded'
  | string;

export type OrderHistoryItem = {
  id: string;
  testId: string;
  date: string;
  department: string;
  testName: string;
  amount: string;
  status: OrderStatusType;
  [key: string]: string | number;
};
