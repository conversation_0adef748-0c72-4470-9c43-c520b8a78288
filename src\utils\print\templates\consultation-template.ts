import {
  commonPrintStyles,
  generatePrintHeader,
  generatePatientInfo,
  generatePrintFooter,
  generate<PERSON>ageTitle,
  CommonPrintData,
} from './common-template';

interface ConsultationData {
  id: string;
  date: string;
  doctor: string;
  notes?: string;
  diagnosis?: string;
  treatment?: string;
  summary?: any;
}

interface ConsultationTemplateData extends CommonPrintData {
  consultations: ConsultationData[];
  digitalSignature?: string;
  letterHeadDetails?: string;
  organizationLogo?: string;
}

// Consultation-specific styles
export const consultationSpecificStyles = `
  .section-container {
    margin-bottom: 25px;
  }
  
  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 5px;
   
    padding: 8px 5px;
    
  }
  
  .field-container {
    margin-bottom: 15px;
  }
  
  .field-label {
    font-size: 12px;
    font-weight: 600;
    color: #555;
    margin-bottom: 5px;
  }
  
  .field-value {
    font-size: 12px;
    color: #333;
    line-height: 1.4;
    min-height: 16px;
  }
  
  .field-value p {
    margin: 0;
  }
  
  .vitals-container {
    margin-bottom: 20px;
  }
  
  .vitals-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
    margin-top: 10px;
  }
  
  .vital-field {
    display: flex;
    flex-direction: column;
    min-width: 80px;
    width: 80px;
    margin-right: 20px;
    margin-bottom: 10px;
  }
  
  .vital-label {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: visible;
  }
  
  .vital-unit {
    font-size: 10px;
    color: #666;
    margin-bottom: 3px;
    line-height: 1.2;
    min-height: 12px;
  }
  
  .vital-value {
    font-size: 12px;
    color: #333;
    background-color: #fff;
    border: none;
    padding: 4px 0px;
    min-height: 24px;
    display: flex;
    align-items: center;
  }
  
  .anthropometry-container {
    margin-bottom: 20px;
  }
  
  .anthropometry-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
  }
  
  .general-exam-container {
    margin-bottom: 20px;
  }
  
  .general-exam-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    align-items: center;
    margin-top: 10px;
  }
  
  .general-exam-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .general-exam-item span {
    font-size: 12px;
    color: #333;
  }
  
  .special-exam-container {
    margin-bottom: 20px;
  }
  
  .special-exam-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 10px;
  }
  
  .special-exam-item {
    width: 100%;
    max-width: 300px;
  }
  
  .special-exam-main {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 3px;
  }
  
  .special-exam-main span {
    font-size: 12px;
    color: #333;
  }
  
  .special-exam-notes {
    font-size: 12px;
    color: #333;
    margin-left: 20px;
    font-style: italic;
  }
  
  .systemic-exam-container {
    margin-bottom: 20px;
  }
  
  .subsection-title {
    font-size: 12px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
    margin-top: 15px;
  }
`;

// Combined styles for consultation template
export const consultationStyles = `
  ${commonPrintStyles}
  ${consultationSpecificStyles}
`;

export const getConsultationHtml = (
  data: ConsultationTemplateData,
  currentPage: number = 1,
  totalPages: number = 1
): string => {
  const { consultations } = data;

  if (consultations.length === 0) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>${consultationStyles}</style>
      </head>
      <body>
        <div class="print-container">
          ${generatePrintHeader(data, data.organizationLogo, data.letterHeadDetails)}
          ${generatePageTitle('Consultation Summary')}
          ${generatePatientInfo(data, 'DD MMM YYYY')}
          
          <div style="text-align: center; padding: 40px;">
            <p>No consultation data available</p>
          </div>
          
          ${generatePrintFooter(currentPage, totalPages, data.digitalSignature)}
        </div>
      </body>
      </html>
    `;
  }

  const consultation = consultations[0];
  const summary = consultation?.summary || {};

  // Helper function to render HTML content safely
  const renderHtmlContent = (htmlString?: string) => {
    if (!htmlString) return 'Not Recorded';
    return htmlString;
  };

  // Get vitals data
  const vitalsData = [
    {
      label: 'Heart Rate',
      value: summary?.vitals?.heartRate || summary?.vitals?.pulse || '',
      unit: '(bpm)',
    },
    {
      label: 'Systolic',
      value: summary?.vitals?.systolicPressure || '',
      unit: '(mmHg)',
    },
    {
      label: 'Diastolic',
      value: summary?.vitals?.diastolicPressure || '',
      unit: '(mmHg)',
    },
    {
      label: 'Respiratory Rate',
      value: summary?.vitals?.respiratoryRate || '',
      unit: '(/min)',
    },
    {
      label: 'SpO2',
      value: summary?.vitals?.spO2 || summary?.vitals?.spo2 || '',
      unit: '(%)',
    },
    {
      label: 'Temperature',
      value: summary?.vitals?.temperature || '',
      unit: '(°F)',
    },
  ];

  // Get anthropometry data
  const anthropometryData = [
    {
      label: 'Height',
      value: summary?.anthropometry?.height || '',
      unit: '(cm)',
    },
    {
      label: 'Weight',
      value: summary?.anthropometry?.weight || '',
      unit: '(Kg)',
    },
    {
      label: 'Body Mass Index',
      value: summary?.anthropometry?.bmi || '',
      unit: '',
    },
    {
      label: 'Waist Circumference',
      value: summary?.anthropometry?.waistCircumference || '',
      unit: '(cm)',
    },
  ];

  // Get general physical examination data
  const generalPhysicalExamData = [
    {
      label: 'Pallor',
      value:
        summary?.generalPhysicalExamination?.pallor !== undefined
          ? summary.generalPhysicalExamination.pallor
            ? 'Yes'
            : 'No'
          : 'No',
    },
    {
      label: 'Icterus',
      value:
        summary?.generalPhysicalExamination?.icterus !== undefined
          ? summary.generalPhysicalExamination.icterus
            ? 'Yes'
            : 'No'
          : 'No',
    },
    {
      label: 'Cyanosis',
      value:
        summary?.generalPhysicalExamination?.cyanosis !== undefined
          ? summary.generalPhysicalExamination.cyanosis
            ? 'Yes'
            : 'No'
          : 'No',
    },
    {
      label: 'Clubbing',
      value:
        summary?.generalPhysicalExamination?.clubbing !== undefined
          ? summary.generalPhysicalExamination.clubbing
            ? 'Yes'
            : 'No'
          : 'No',
    },
  ];

  // Get systemic examination data
  const systemicExamData = [
    {
      key: 'neurological',
      label: 'Neurological Examination',
      value: summary?.systemicExamination?.neurologicalExamination || 'Nil',
    },
    {
      key: 'cardiovascular',
      label: 'Cardiovascular Examination',
      value: summary?.systemicExamination?.cardiovascularExamination || 'Nil',
    },
    {
      key: 'respiratory',
      label: 'Respiratory Examination',
      value: summary?.systemicExamination?.respiratoryExamination || 'Nil',
    },
    {
      key: 'gastrointestinal',
      label: 'Abdomen Examination',
      value: summary?.systemicExamination?.abdomenExamination || 'Nil',
    },
    {
      key: 'genitourinary',
      label: 'Rheumatological Examination',
      value: summary?.systemicExamination?.rheumatologicalExamination || 'Nil',
    },
  ];

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>${consultationStyles}</style>
    </head>
    <body>
      <div class="print-container">
        ${generatePrintHeader(data, data.organizationLogo, data.letterHeadDetails)}
        ${generatePageTitle('Consultation Summary')}
        ${generatePatientInfo(data, 'DD MMM YYYY')}

        <!-- HISTORY Section -->
        <div class="section-container">
          <div class="section-title">History</div>
          
          <div class="field-container">
            <div class="field-label">Presenting Complaints</div>
            <div class="field-value">${renderHtmlContent(summary?.presentingComplaints)}</div>
          </div>
          
          <div class="field-container">
            <div class="field-label">History of Presenting Illness</div>
            <div class="field-value">${renderHtmlContent(summary?.historyOfPresenting)}</div>
          </div>
          
          <div class="field-container">
            <div class="field-label">Past Medical History</div>
            <div class="field-value">${renderHtmlContent(summary?.pastMedicalHistory)}</div>
          </div>
          
          <div class="field-container">
            <div class="field-label">Past Surgical History</div>
            <div class="field-value">${renderHtmlContent(summary?.pastSurgicalHistory)}</div>
          </div>
          
          <div class="field-container">
            <div class="field-label">Family History</div>
            <div class="field-value">${renderHtmlContent(summary?.familyHistory)}</div>
          </div>
          
          <div class="field-container">
            <div class="field-label">Addiction History</div>
            <div class="field-value">${renderHtmlContent(summary?.addictionHistory)}</div>
          </div>
          
          <div class="field-container">
            <div class="field-label">Diet History</div>
            <div class="field-value">${renderHtmlContent(summary?.dietHistory)}</div>
          </div>
          
          <div class="field-container">
            <div class="field-label">Physical Activity History</div>
            <div class="field-value">${renderHtmlContent(summary?.physicalActivityHistory)}</div>
          </div>
          
          <div class="field-container">
            <div class="field-label">Stress History</div>
            <div class="field-value">${renderHtmlContent(summary?.stressHistory)}</div>
          </div>
          
          <div class="field-container">
            <div class="field-label">Sleep History</div>
            <div class="field-value">${renderHtmlContent(summary?.sleepHistory)}</div>
          </div>
          
          <div class="field-container">
            <div class="field-label">Current Medication History</div>
            <div class="field-value">${renderHtmlContent(summary?.currentMedicationHistory)}</div>
          </div>
        </div>

        <!-- EXAMINATION Section -->
        <div class="section-container">
          <div class="section-title">Examination</div>
          
          <!-- Vital Signs -->
          <div class="vitals-container">
            <div class="subsection-title">Vital signs</div>
            <div class="vitals-grid">
              ${vitalsData
                .map(
                  (vital) => `
                <div class="vital-field">
                  <div class="vital-label">${vital.label}</div>
                  <div class="vital-unit">${vital.unit}</div>
                  <div class="vital-value">${vital.value || ''}</div>
                </div>
              `
                )
                .join('')}
            </div>
          </div>
          
          <!-- Anthropometry -->
          <div class="anthropometry-container">
            <div class="subsection-title">Anthropometry</div>
            <div class="anthropometry-grid">
              ${anthropometryData
                .map(
                  (measurement) => `
                <div class="vital-field">
                  <div class="vital-label">${measurement.label}</div>
                  <div class="vital-unit">${measurement.unit}</div>
                  <div class="vital-value">${measurement.value || ''}</div>
                </div>
              `
                )
                .join('')}
            </div>
          </div>
          
          <!-- General Physical Examination -->
          <div class="general-exam-container">
            <div class="subsection-title">General physical examination</div>
            <div class="general-exam-grid">
              ${generalPhysicalExamData
                .map(
                  (item) => `
                <div class="general-exam-item">
                  <span>${item.label} : ${item.value}</span>
                </div>
              `
                )
                .join('')}
            </div>
          </div>
          
          <!-- Special Fields -->
          <div class="special-exam-container">
            <div class="special-exam-grid">
              <div class="special-exam-item">
                <div class="special-exam-main">
                  <span>Pedal Edema: ${summary?.generalPhysicalExamination?.pedalEdema ? 'Yes' : 'No'}</span>
                </div>
                ${
                  summary?.generalPhysicalExamination?.pedalEdemaNotes
                    ? `
                  <div class="special-exam-notes">${summary.generalPhysicalExamination.pedalEdemaNotes}</div>
                `
                    : ''
                }
              </div>
              
              <div class="special-exam-item">
                <div class="special-exam-main">
                  <span>Lymphadenopathy: ${summary?.generalPhysicalExamination?.lymphadenopathy ? 'Yes' : 'No'}</span>
                </div>
                ${
                  summary?.generalPhysicalExamination?.lymphadenopathyNotes
                    ? `
                  <div class="special-exam-notes">${summary.generalPhysicalExamination.lymphadenopathyNotes}</div>
                `
                    : ''
                }
              </div>
            </div>
          </div>
          
          <!-- HEENT -->
          <div class="field-container">
            <div class="subsection-title">HEENT</div>
            <div class="field-value">${renderHtmlContent(summary?.heent)}</div>
          </div>
          
          <!-- Systemic Examination -->
          <div class="systemic-exam-container">
            <div class="subsection-title">Systemic Examination</div>
            ${systemicExamData
              .map(
                (system) => `
              <div class="field-container">
                <div class="field-label">${system.label}</div>
                <div class="field-value">${renderHtmlContent(system.value)}</div>
              </div>
            `
              )
              .join('')}
          </div>
        </div>
        
        ${generatePrintFooter(currentPage, totalPages, data.digitalSignature)}
      </div>
    </body>
    </html>
  `;
};
