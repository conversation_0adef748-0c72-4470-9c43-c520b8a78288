import React, { memo, useMemo } from 'react';

import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import colors from '@/utils/colors';

import {
  NutritionAndDietForm,
  NutritionAndDietFormType,
} from '@/constants/lifestyle';

import AwarenessAdaptationModal from '../lifestyle-forms/awareness-adaptation/AwarenessAdaptationModal';
import DietaryAssessmentModal from '../lifestyle-forms/dietary-assessment/DietaryAssessmentModal';
import NutritionMonitoringModal from '../lifestyle-forms/nutrition-monitoring/NutritionMonitoringModal';

import DetailsTimelineButtons from './shared/DetailsTimelineButtons';
import MobilePageTitle from './shared/MobilePageTitle';

const {
  DIETARY_ASSESSMENT,
  LIFESTYLE_AWARENESS_ADAPTATION,
  NUTRITION_MONITORING_SHEET,
} = NutritionAndDietForm;

const AddRecordManuallyMob = () => {
  const { currentTab } = useLifestyleUtilStore();

  const lifestyleForms: Record<NutritionAndDietFormType, JSX.Element> = useMemo(
    () => ({
      [DIETARY_ASSESSMENT]: <DietaryAssessmentModal />,
      [LIFESTYLE_AWARENESS_ADAPTATION]: <AwarenessAdaptationModal />,
      [NUTRITION_MONITORING_SHEET]: <NutritionMonitoringModal />,
    }),
    []
  );
  return (
    <div className="h-fit pb-20">
      <div className="h-fit bg-white px-2 pb-2 rounded-b-lg">
        <MobilePageTitle
          title={
            currentTab === LIFESTYLE_AWARENESS_ADAPTATION
              ? 'Lifestyle Awareness & Adaptation'
              : currentTab
          }
          variant="small"
        />
        <DetailsTimelineButtons />
        <div
          style={{
            maxHeight: 'calc(100dvh - 20.2rem)',
            background: colors.common.lightBlue,
          }}
          className=" relative overflow-y-auto overflow-x-hidden p-2 pb-2"
        >
          {lifestyleForms[currentTab as NutritionAndDietFormType]}
        </div>
      </div>
    </div>
  );
};

export default memo(AddRecordManuallyMob);
