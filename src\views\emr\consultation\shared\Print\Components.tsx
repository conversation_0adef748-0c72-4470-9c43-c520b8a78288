import { Box, Typography } from '@mui/material';

import { VitalSign, ExaminationField, SystemicExamination } from './Constants';
import { renderHtmlContent } from './PrintPreview';
import { styles } from './PrintStyles';

// Component for rendering field containers (vitals/anthropometry)
export const FieldContainer: React.FC<VitalSign> = ({ label, value, unit }) => (
  <Box sx={styles.fieldContainer}>
    <Typography sx={styles.fieldTitle}>{label}</Typography>
    <Typography sx={styles.fieldUnit}>{unit}</Typography>
    <Box sx={styles.fieldInput}>{value || ''}</Box>
  </Box>
);

// Component for rendering examination fields
export const ExaminationItem: React.FC<ExaminationField> = ({
  label,
  value,
}) => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
    <Typography sx={styles.inlineText}>
      {label}: {value}
    </Typography>
  </Box>
);

// Component for rendering special fields with notes
export const SpecialField: React.FC<{
  label: string;
  value: boolean;
  notes?: string;
}> = ({ label, value, notes }) => (
  <Box sx={{ width: '100%', maxWidth: '300px' }}>
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
      <Typography sx={styles.inlineText}>
        {label}: {value ? 'Yes' : 'No'}
      </Typography>
    </Box>
    {notes && (
      <Typography sx={{ ...styles.inlineText, ml: 4, fontStyle: 'italic' }}>
        {notes}
      </Typography>
    )}
  </Box>
);

// Component for rendering history fields
export const HistoryField: React.FC<{
  label: string;
  content?: string;
}> = ({ label, content }) => (
  <Box sx={{ mb: 2 }}>
    <Typography sx={styles.fieldLabel}>{label}</Typography>
    {renderHtmlContent(content)}
  </Box>
);

// Component for rendering systemic examination items
export const SystemicExaminationItem: React.FC<SystemicExamination> = ({
  label,
  value,
}) => (
  <Box sx={{ mb: 2 }}>
    <Typography sx={styles.fieldLabel}>{label}</Typography>
    {renderHtmlContent(value)}
  </Box>
);
