import React, { FC, memo } from 'react';

import { IconButton, IconButtonProps } from '@mui/material';
import { MdOutlineAdd } from 'react-icons/md';

import { cn } from '@/lib/utils';

type Props = {
  title: string;
  onAdd?: () => void;
  showEndButton?: boolean;
  iconButtonProps?: IconButtonProps;
  className?: string;
};

const Title: FC<Props> = ({
  title,
  onAdd,
  showEndButton = false,
  iconButtonProps,
  className,
}) => {
  return (
    <div className="flex items-center w-full justify-between ">
      <h3 className={cn('capitalize text-lg font-medium', className)}>
        {title}
      </h3>
      {showEndButton && (
        <IconButton
          onClick={onAdd}
          className="*:font-black *:text-black *:text-2xl"
          {...iconButtonProps}
        >
          {!iconButtonProps?.loading && (
            <MdOutlineAdd className="*:font-black *:text-black *:text-2xl" />
          )}
        </IconButton>
      )}
    </div>
  );
};

export default memo(Title);
