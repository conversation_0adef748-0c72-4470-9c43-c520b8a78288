import {
  ChangeEvent<PERSON><PERSON><PERSON>,
  ReactNode,
  useCallback,
  useRef,
  useState,
} from 'react';

import { Icon<PERSON>utton } from '@mui/material';
import { truncate } from 'lodash';
import { XIcon } from 'lucide-react';
import { MdOutlineRemoveRedEye } from 'react-icons/md';
import { RiAttachment2 } from 'react-icons/ri';

import { cn } from '@/lib/utils';

import usePdf from '@/hooks/use-pdf';

import { regex } from '@/utils/constants/regex';

import InputLabel from '../input-label';

import PreviewModal from './PreviewModal';

export interface FileInputProps {
  label?: string;
  className?: string;
  value?: FileList | null | string;
  helperText?: ReactNode | string;
  onChange?: (files: FileList | null) => void;
  isBoxStyle?: boolean;
  allowedFileTypes?: string[];
  maxFileSize?: number;
  fileTypeErrorMessage?: string;
  showPreview?: boolean;
  maxNameLength?: number;
  errorClassName?: string;
}

export default function FileInput({
  className,
  value,
  onChange,
  isBoxStyle,
  allowedFileTypes,
  maxFileSize,
  fileTypeErrorMessage,
  showPreview = false,
  maxNameLength = 40,
  errorClassName,
  ...rest
}: FileInputProps) {
  const { previewPdf } = usePdf();

  const inputRef = useRef<HTMLInputElement | null>(null);

  const [error, setError] = useState<string | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleChange: ChangeEventHandler<HTMLInputElement> = (e) => {
    const files = e.target.files;
    if (!files) return;

    if (allowedFileTypes && files.length > 0) {
      const fileType = files[0].type;
      if (!allowedFileTypes.includes(fileType)) {
        setError(`${fileTypeErrorMessage}`);
        return;
      }
    }

    if (maxFileSize && files.length > 0) {
      const fileSize = files[0].size / (1024 * 1024);
      if (fileSize > maxFileSize) {
        setError(`File size must be less than ${maxFileSize} MB.`);
        return;
      }
    }

    setError(null);
    if (onChange) {
      onChange(files);
    }
  };

  const clearFile = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    event.stopPropagation();

    if (onChange) {
      onChange(null);
    }

    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  const handleFocus = () => {
    window.scrollTo(0, 0);
  };
  const getSelectedFileName = (): string => {
    if (typeof value === 'string') {
      return decodeURIComponent(value.split('/').pop() || '');
    } else if (value instanceof FileList && value.length > 0) {
      return value[0].name;
    }
    return '';
  };

  const displayFileName = getSelectedFileName();

  const openFilePicker = () => {
    if (!value || (value instanceof FileList && value.length === 0)) {
      inputRef.current?.click();
    }
  };

  const openFilePreview = useCallback(() => {
    if (!value) return;

    if (typeof value === 'string') {
      if (value.endsWith('.pdf')) {
        previewPdf(value);
      } else if (value.match(regex.IMAGE_FILE_EXTENSION_REGEX)) {
        setImagePreview(value);
      }
    } else if (value instanceof FileList) {
      const file = value[0];
      const fileUrl = URL.createObjectURL(file);

      if (file.type === 'application/pdf') {
        previewPdf(fileUrl);
      } else if (file.type.startsWith('image/')) {
        setImagePreview(fileUrl);
      }
      setTimeout(() => URL.revokeObjectURL(fileUrl), 5000);
    }
  }, [previewPdf, value]);

  return (
    <label className={cn('hover:cursor-pointer flex flex-col', className)}>
      {rest.label && <InputLabel label={rest.label} />}

      <div className="flex w-full max-w-full">
        {!value || value.length === 0 ? (
          isBoxStyle ? (
            <div className="flex-1 w-full flex justify-between gap-2 border border-[#637D92] rounded-md px-4 py-2">
              <div className="truncate">Choose file</div>
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  openFilePicker();
                }}
                className="flex-shrink-0"
              >
                <RiAttachment2 size={20} color="gray" />
              </button>
            </div>
          ) : (
            <div className="flex-1 w-full flex items-center gap-2 text-xs">
              <div className="border border-[#637D92] rounded-md px-4 py-1 truncate">
                Choose file
              </div>
              <div className="truncate flex-shrink-0">No File Chosen</div>
            </div>
          )
        ) : (
          <div
            className="flex-1 w-[calc(100%-40px)] flex justify-between border border-[#637D92] rounded-md px-4 py-2"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            <div className="truncate mr-2">
              {truncate(displayFileName, { length: maxNameLength })}
            </div>
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                clearFile(e);
              }}
              className="flex-shrink-0"
            >
              <XIcon color="#D01010" />
            </button>
          </div>
        )}
        {Boolean(value && value.length > 0 && showPreview) && (
          <IconButton
            onClick={openFilePreview}
            sx={{ justifySelf: 'flex-end' }}
            className="flex-shrink-0"
          >
            <MdOutlineRemoveRedEye />
          </IconButton>
        )}
      </div>

      {(error || rest.helperText) && (
        <div
          className={`flex gap-7 text-[10px] sm:text-[12px] md:text-[13px] mt-2 ${
            error ? 'text-[#D01010]' : 'text-[#637d92]'
          } ${errorClassName}`}
        >
          {error || rest.helperText}
        </div>
      )}

      <input
        ref={inputRef}
        className="sr-only"
        type="file"
        {...rest}
        multiple={false}
        onChange={handleChange}
        onFocus={handleFocus}
        accept={allowedFileTypes?.join(', ')}
      />
      <PreviewModal
        imagePreview={imagePreview}
        onClose={() => setImagePreview(null)}
      />
    </label>
  );
}
