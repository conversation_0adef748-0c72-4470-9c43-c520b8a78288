import React from 'react';

import { Box, styled } from '@mui/material';

interface TruncatedTextareaProps {
  value: string;
  onClick: () => void;
  placeholder?: string;
  endAdornmentIcon?: React.ReactNode;
  isNotValid?: boolean;
  color?: string;
  maxLines?: number;
  truncateLength?: number;
}

const Container = styled(Box)<{ textColor: string }>(({ textColor }) => ({
  position: 'relative',
  cursor: 'pointer',
  minHeight: '32px',
  padding: '8px 40px 8px 10px',
  border: '1px solid transparent',
  borderRadius: '4px',
  backgroundColor: 'transparent',
  fontSize: '14px',
  lineHeight: 1.43,
  color: textColor,
  display: 'flex',
  alignItems: 'flex-start',
  '&:focus': {
    outline: 'none',
    backgroundColor: 'rgba(0, 0, 0, 0.04)',
  },
}));

const StyledPre = styled(Box)<{ maxLines: number }>(({ maxLines }) => ({
  margin: 0,
  padding: 0,
  fontFamily: 'inherit',
  fontSize: 'inherit',
  lineHeight: 'inherit',
  color: 'inherit',
  whiteSpace: 'pre-wrap',
  wordWrap: 'break-word',
  overflow: 'hidden',
  display: '-webkit-box',
  WebkitLineClamp: maxLines,
  WebkitBoxOrient: 'vertical',
}));

const EndAdornment = styled(Box)({
  position: 'absolute',
  right: '10px',
  top: '50%',
  transform: 'translateY(-50%)',
  display: 'flex',
  alignItems: 'center',
  pointerEvents: 'none',
});

const TruncatedTextarea: React.FC<TruncatedTextareaProps> = ({
  value,
  onClick,
  placeholder = 'Instructions',
  endAdornmentIcon,
  isNotValid = false,
  color = 'inherit',
  maxLines = 3,
  truncateLength = 30,
}) => {
  const truncateText = (text: string): string => {
    if (!text) return '';
    const lines = text.split('\n');
    if (lines.length <= maxLines) return text;

    const visibleLines = lines.slice(0, maxLines - 1);
    const lastLine = lines[maxLines - 1] || '';
    const truncatedLast =
      lastLine.length > truncateLength
        ? lastLine.slice(0, truncateLength).trim() + '...'
        : lastLine + '...';

    return [...visibleLines, truncatedLast].join('\n');
  };

  const displayText = value ? truncateText(value) : placeholder;
  const textColor = value ? color : isNotValid ? '#E4626F' : 'gray';

  return (
    <Container onClick={onClick} textColor={textColor}>
      <StyledPre as="pre" maxLines={maxLines}>
        {displayText}
      </StyledPre>

      {endAdornmentIcon && <EndAdornment>{endAdornmentIcon}</EndAdornment>}
    </Container>
  );
};

export default TruncatedTextarea;
