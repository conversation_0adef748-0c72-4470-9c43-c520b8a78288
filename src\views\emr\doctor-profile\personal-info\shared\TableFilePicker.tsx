import { useCallback, useState } from 'react';

import { Controller, FieldValues, UseControllerProps } from 'react-hook-form';

import { IconButton } from '@mui/material';
import { FaFilePdf } from 'react-icons/fa';
import { MdOutlineRemoveRedEye } from 'react-icons/md';
import { toast } from 'sonner';

import PreviewModal from '@core/components/file-input/PreviewModal';

import { cn } from '@/lib/utils';

import usePdf from '@/hooks/use-pdf';

import { regex } from '@/utils/constants/regex';

type Props<T extends FieldValues> = UseControllerProps<T> & {
  imageUrl?: string | File;
};

const getPreviewLink = (value?: string | File) => {
  if (!value) return null;

  if (typeof value === 'string') {
    if (value.endsWith('.pdf')) {
      return null;
    }

    return value;
  } else if (value instanceof File && value.type.includes('image')) {
    return URL.createObjectURL(value);
  }
  return null;
};

const TableFilePicker = <T extends FieldValues>({
  name,
  control,
  disabled,
  imageUrl,
}: Props<T>) => {
  const { previewPdf } = usePdf();

  const [preview, setPreview] = useState<string | null>(
    getPreviewLink(imageUrl) ?? null
  );
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const openFilePreview = useCallback(
    (value?: string | File) => {
      if (!value) return;

      if (typeof value === 'string') {
        if (value.endsWith('.pdf')) {
          previewPdf(value);
        } else if (value.match(regex.IMAGE_FILE_EXTENSION_REGEX)) {
          setImagePreview(value);
        }
      } else if (value instanceof File) {
        const fileUrl = URL.createObjectURL(value);

        if (value.type === 'application/pdf') {
          previewPdf(fileUrl);
        } else if (value.type.startsWith('image/')) {
          setImagePreview(fileUrl);
        }
        setTimeout(() => URL.revokeObjectURL(fileUrl), 5000);
      }
    },
    [previewPdf]
  );

  const validateFile = (file: File) => {
    if (!file.type.match('image.*') && file.type !== 'application/pdf') {
      toast.error('Only image and PDF files are allowed.');
      return false;
    }

    if (file.size > 6 * 1024 * 1024) {
      toast.error('File size must be less than 6MB.');
      return false;
    }

    return true;
  };

  return (
    <>
      <Controller
        name={name}
        control={control}
        render={({ field: { onChange, value, ref } }) => (
          <div
            className={cn(
              'flex items-center gap-2 w-full h-full min-w-10 min-h-10',
              { 'cursor-not-allowed bg-gray-200': disabled }
            )}
          >
            <input
              type="file"
              id={`file-input-${name}`}
              className={cn('hidden', { 'cursor-not-allowed': disabled })}
              ref={ref}
              disabled={disabled}
              accept={'image/jpeg, image/png, application/pdf'}
              onChange={(e) => {
                const file = e.target.files?.[0] || null;
                if (file && !disabled && validateFile(file)) {
                  onChange(file);
                  setPreview(getPreviewLink(file));
                }
              }}
            />
            <label
              htmlFor={`file-input-${name}`}
              className={cn('cursor-pointer w-10 h-10', {
                'cursor-not-allowed': disabled,
              })}
            >
              {value ? (
                <div className="w-full h-full flex items-center justify-center overflow-hidden">
                  {preview ? (
                    <img
                      src={preview}
                      alt="Preview"
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <span className="text-lg">
                      <FaFilePdf />
                    </span>
                  )}
                </div>
              ) : (
                <div className="w-full h-full border flex items-center justify-center min-w-10 min-h-10"></div>
              )}
            </label>
            {Boolean(value) && (
              <IconButton
                onClick={() => openFilePreview(value)}
                sx={{ justifySelf: 'flex-end' }}
                size="small"
              >
                <MdOutlineRemoveRedEye />
              </IconButton>
            )}
          </div>
        )}
      />
      <PreviewModal
        imagePreview={imagePreview}
        onClose={() => setImagePreview(null)}
      />
    </>
  );
};

export default TableFilePicker;
