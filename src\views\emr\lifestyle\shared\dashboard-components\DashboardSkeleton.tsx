// DashboardSkeleton.tsx - Updated with reusable skeleton components
import React from 'react';

// Reusable skeleton components
export const AverageCardSkeleton = () => (
  <div className="rounded-lg p-4 bg-gray-200 animate-pulse">
    <div className="h-4 w-3/4 bg-gray-300 rounded mb-2"></div>
    <div className="h-6 w-1/2 bg-gray-300 rounded mb-1"></div>
    <div className="h-4 w-1/4 bg-gray-300 rounded"></div>
  </div>
);

export const ChartSkeleton = ({
  title,
  height = 'h-48',
}: {
  title?: string;
  height?: string;
}) => (
  <div className="rounded-lg p-4 bg-white">
    {title && (
      <div className="h-5 w-1/3 bg-gray-200 rounded mb-3 animate-pulse"></div>
    )}
    <div className={`${height} bg-gray-100 rounded animate-pulse`}></div>
  </div>
);

export const TableSkeleton = () => (
  <div className="rounded-lg  bg-white">
    <div className="h-10 w-full bg-gray-200 rounded animate-pulse"></div>
  </div>
);

export const HeaderSkeleton = () => (
  <div className="flex items-center justify-between mb-6">
    <div className="h-8 w-64 bg-gray-200 rounded animate-pulse"></div>
  </div>
);

const DashboardSkeleton: React.FC = () => {
  return (
    <div className="h-full flex flex-col p-4 bg-gray-50 overflow-y-auto">
      {/* Header with Filter Tabs Skeleton */}
      <HeaderSkeleton />

      {/* Average Cards Skeleton */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        {[...Array(8)].map((_, index) => (
          <AverageCardSkeleton key={index} />
        ))}
      </div>

      {/* Summary Tables Skeleton */}
      <div className="grid grid-cols-1 gap-1 ">
        {[...Array(2)].map((_, index) => (
          <TableSkeleton key={index} />
        ))}
      </div>

      {/* Charts Grid Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {[...Array(6)].map((_, index) => (
          <ChartSkeleton key={index} />
        ))}
      </div>
    </div>
  );
};

export default DashboardSkeleton;
