export const AppointmentStatus = {
  Awaiting: 'Awaiting',
  Booked: 'Booked',
  Priority: 'Priority',
  Investigations: 'Investigations',
  Consultation: 'Consultation',
  Done: 'Done',
  Cancelled: 'Cancelled',
} as const;

export type AppointmentStatus =
  (typeof AppointmentStatus)[keyof typeof AppointmentStatus];

export const PatientStatus = {
  Booked: 'Booked',
  Arrived: 'Arrived',
  ProxyVisit: 'ProxyVisit',
  NoShow: 'NoShow',
} as const;

export type PatientStatus = (typeof PatientStatus)[keyof typeof PatientStatus];

export const Department = {
  InPatient: 'InPatient',
  OutPatient: 'OutPatient',
} as const;

export type Department = (typeof Department)[keyof typeof Department];

export const ConsultationView = {
  form: 'form',
  preview: 'preview',
} as const;

export const PatientQueueLabStatus = {
  REGISTERED_NOT_PAID: 'Registered/Not paid',
  LAB_REPORT_AWAITING: 'Lab reports awaiting',
  LAB_REPORT_READY: 'Lab reports ready',
  NO_TEST_ORDERED: 'No tests ordered',
} as const;

export type PatientQueueLabStatus =
  (typeof PatientQueueLabStatus)[keyof typeof PatientQueueLabStatus];

export type ConsultationView =
  (typeof ConsultationView)[keyof typeof ConsultationView];
