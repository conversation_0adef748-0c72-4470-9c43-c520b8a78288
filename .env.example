# ============================================
# ARCA EMR - Environment Configuration Example
# ============================================
# Copy this file to `.env` and update the values accordingly
# ============================================


# ================================
# API CONFIGURATION
# ================================

# Frontend application URL
NEXT_PUBLIC_URL=http://localhost:3001

# Backend API URL
NEXT_PUBLIC_API_URL=your-api-url

# API subscription key for Azure API Management
NEXT_PUBLIC_SUBSCRIPTION_KEY=your-subscription-key


# ================================
# SPEECH ENGINE CONFIGURATION
# ================================

# Speech token subscription key
NEXT_PUBLIC_SPEECH_ENGINE_SUBSCRIPTION_KEY=your-subscription-key


# ================================
# AZURE AD B2C CONFIGURATION
# ================================

# Azure AD B2C Client ID
NEXT_PUBLIC_CLIENT_ID=your-client-id

# Azure AD B2C Tenant Name (e.g., yourtenant)
NEXT_PUBLIC_TENANT_NAME=your-tenant-name

# Azure AD B2C Tenant ID (GUID)
NEXT_PUBLIC_TENANT_ID=your-tenant-id

# Azure AD B2C Sign-in Policy (e.g., B2C_1_signin)
NEXT_PUBLIC_SIGNIN_POLICY=your-signin-policy


# ================================
# DEVELOPMENT CONFIGURATION
# ================================

# Enable debug logging or dev-related settings
NEXT_PUBLIC_NODE_ENV=development
