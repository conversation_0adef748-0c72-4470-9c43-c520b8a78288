import { ButtonHTMLAttributes, ReactNode } from 'react';

import { AiOutlineLoading } from 'react-icons/ai';

import { cn } from '@/lib/utils';

/**
 * @deprecated This type is deprecated and will be removed in future versions.
 * Please use the app button component instead.
 */
export type PrimaryButtonProps = ButtonHTMLAttributes<HTMLButtonElement> & {
  className?: string;
  children?: ReactNode;
  isLoading?: boolean;
  variant?: 'contained' | 'outlined';
  isDisabled?: boolean;
};

/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the app button field component instead.
 */
export default function PrimaryButton({
  className,
  isLoading,
  children,
  isDisabled,
  variant = 'contained',
  ...props
}: PrimaryButtonProps) {
  const baseClasses =
    'rounded-[5px] h-10 px-4 flex items-center gap-2 justify-center';
  const containedClasses = 'text-[#FCFCFC] bg-[#001926]';
  const outlinedClasses =
    'text-[#001926] border border-[#001926] bg-transparent';
  const isButtonDisabled = isLoading || isDisabled;

  return (
    <button
      className={cn(
        baseClasses,
        variant === 'outlined' ? outlinedClasses : containedClasses,
        { 'bg-gray-700 cursor-not-allowed': isButtonDisabled },
        className
      )}
      type="button"
      disabled={isButtonDisabled}
      {...props}
    >
      {children}
      {isLoading && <AiOutlineLoading className="animate-spin" />}
    </button>
  );
}
