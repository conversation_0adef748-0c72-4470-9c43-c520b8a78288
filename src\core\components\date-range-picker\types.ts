import { ReactNode } from 'react';

import { DayPickerRangeProps } from 'react-day-picker';

import { IconButtonProps } from '@mui/material';

import { TextInputProps } from '../text-input';

export type DateType = Date | null | string | undefined;

export type DateRange = {
  from: DateType;
  to: DateType;
};

type DateRangeProps = Omit<DayPickerRangeProps, 'mode'>;

export type DateRangePickerProps = DateRangeProps & {
  inputProps?: Omit<TextInputProps, 'value' | 'onChange'>;
  value?: DateRange;
  onChange?: (value: DateRange) => void;
  renderInput?: (props: TextInputProps) => JSX.Element;
  format?: string;
  disabled?: boolean;
  inputWrapperClassName?: string;
  endAdornment?: ReactNode | null;
  onClickEndAdornment?: (e: React.MouseEvent) => void;
  endAdornmentProps?: Omit<IconButtonProps, 'onClick'>;
  separator?: string;
  calendarIcon?: React.ReactNode;
  setIsCalendarOpen?: (isOpen: boolean) => void;
  renderFooter?: () => React.ReactNode;
  isCalendarOpen?: boolean;
  maxDate?: Date;
};
