# ARCA EMR Authentication System

## Overview

ARCA EMR uses Azure AD B2C with MSAL for authentication. All authentication and token management is frontend-based using MSAL library.

## Key Components

### Authentication Files

- `src/core/lib/auth/msal.ts` - MSAL configuration
- `src/core/lib/auth/services.ts` - Login/logout functions
- `src/core/providers/AuthProviders.tsx` - MSAL context provider
- `src/core/guard/AuthGuard.tsx` - Route protection

### Token Management

- Tokens stored in browser localStorage via MSAL
- Automatic token attachment to API requests via Axios interceptor
- 401 errors trigger automatic logout and redirect

## Authentication Flow

1. User accesses protected route
2. AuthGuard checks authentication status
3. If not authenticated → redirect to Azure AD B2C login
4. On successful login → MSAL stores token in localStorage
5. API requests automatically include Bearer token
6. 401 errors → automatic logout and redirect

## Environment Variables

```env
NEXT_PUBLIC_CLIENT_ID=your-client-id
NEXT_PUBLIC_TENANT_NAME=your-tenant-name
NEXT_PUBLIC_TENANT_ID=your-tenant-id
NEXT_PUBLIC_SIGNIN_POLICY=your-signin-policy
```

## Troubleshooting

- Verify Azure AD B2C configuration in `.env`
- Check browser console for MSAL errors
- Ensure user flows/policies are active in Azure AD B2C

For setup instructions, see `AUTHENTICATION_SETUP.md`.
