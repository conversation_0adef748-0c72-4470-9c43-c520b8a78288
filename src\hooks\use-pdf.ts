import { useCallback } from 'react';

import axios from 'axios';
import { toast } from 'sonner';

const openPdfFromBase64 = (base64String: string) => {
  const byteCharacters = atob(base64String.split(',')[1]);
  const byteNumbers = new Array(byteCharacters.length)
    .fill(0)
    .map((_, i) => byteCharacters.charCodeAt(i));
  const byteArray = new Uint8Array(byteNumbers);
  const fileBlob = new Blob([byteArray], { type: 'application/pdf' });

  const fileUrl = URL.createObjectURL(fileBlob);
  window.open(fileUrl, '_blank');
};

const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    reader.onloadend = () => resolve(reader.result as string);
    reader.onerror = reject;
  });
};

const usePdf = () => {
  const previewPdf = useCallback(async (pdf?: string | File) => {
    if (pdf && typeof pdf === 'string') {
      try {
        const response = await axios.get(pdf, {
          responseType: 'blob',
        });

        const base64String = await blobToBase64(response.data);

        openPdfFromBase64(base64String);
      } catch (error) {
        toast.error('Error getting PDF');
        console.error('Error fetching PDF:', error);
      }
    } else if (pdf && pdf instanceof File) {
      const fileUrl = URL.createObjectURL(pdf);
      open(fileUrl, '_blank');
    }
  }, []);

  return { previewPdf };
};

export default usePdf;
