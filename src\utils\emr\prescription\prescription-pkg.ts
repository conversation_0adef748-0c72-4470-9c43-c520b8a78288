import { HeaderV2 } from '@/core/components/table-v2/types';
import {
  PrescriptionModalMode,
  prescriptionModalModes,
} from '@/types/emr/prescription';

const { DETAIL, CREATE, ADD } = prescriptionModalModes;

export const getPackageTableHeaders = (
  modalMode: PrescriptionModalMode
): HeaderV2[] => {
  const baseHeaders: HeaderV2[] = [
    { header: 'Drug Form', key: 'drugForm' },
    { header: 'Generic Name', key: 'medicineName' },
    { header: 'Brand Name', key: 'brandName' },
    { header: 'Strength', key: 'strength' },
  ];

  if (modalMode === DETAIL) {
    return [
      ...baseHeaders,
      {
        header: '',
        key: 'select',
        cellProps: { sx: { width: 10, textAlign: 'center' } },
      },
    ];
  }

  if (modalMode === CREATE || modalMode === ADD) {
    return [
      ...baseHeaders,
      {
        header: '',
        key: 'action',
        cellProps: { sx: { width: 10, textAlign: 'center' } },
      },
    ];
  }

  return baseHeaders;
};

export const getMedicineTableHeaders = (useTwoColumns: boolean) => {
  if (useTwoColumns) {
    return [
      { header: 'Medicine Name', key: 'medicineName1' },
      { header: 'Brand Name', key: 'brandName1' },
      { header: 'Strength', key: 'strength1' },
      { header: 'Drug Form', key: 'drugForm1' },
      { header: 'Action', key: 'action1' },
      { header: 'Medicine Name', key: 'medicineName2' },
      { header: 'Brand Name', key: 'brandName2' },
      { header: 'Strength', key: 'strength2' },
      { header: 'Drug Form', key: 'drugForm2' },
      { header: 'Action', key: 'action2' },
    ];
  }

  return [
    { label: 'Medicine Name', key: 'medicineName' },
    { label: 'Brand Name', key: 'brandName' },
    { label: 'Strength', key: 'strength' },
    { label: 'Drug Form', key: 'drugForm' },
    { label: 'Action', key: 'action' },
  ];
};
