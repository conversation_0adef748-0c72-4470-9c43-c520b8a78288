import React, { ReactNode } from 'react';

interface ChartTitleProps {
  title: string;
  children?: ReactNode;
  showBorder?: boolean;
  className?: string;
  rightContent?: ReactNode;
}

export const ChartTitle: React.FC<ChartTitleProps> = ({
  title,
  children,
  showBorder = true,
  className = '',
  rightContent,
}) => {
  return (
    <div
      className={`flex items-center justify-between px-4 py-3 ${showBorder ? 'border-b border-gray-200' : ''} ${className}`}
    >
      <h3 className="text-base font-medium text-[#001926]">{title}</h3>
      <div className="flex items-center gap-4">
        {rightContent}
        {children}
      </div>
    </div>
  );
};

interface LegendItemProps {
  color: string;
  label: string;
  className?: string;
  onClick?: () => void;
  active?: boolean;
}

export const LegendItem: React.FC<LegendItemProps> = ({
  color,
  label,
  className = '',
  onClick,
  active = true,
}) => {
  return (
    <div
      className={`flex items-center cursor-pointer ${className}`}
      onClick={onClick}
    >
      <div
        className="w-2.5 h-2.5 mr-2 rounded-full flex-shrink-0"
        style={{
          backgroundColor: active ? color : `${color}4D`,
          opacity: active ? 1 : 0.6,
        }}
      />
      <span className="text-sm text-[#64707D]">{label}</span>
    </div>
  );
};

interface ChartContainerProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
  headerActions?: React.ReactNode;
  showTitleInChart?: boolean;
  chartTitleNode?: React.ReactNode;
  showShadow?: boolean;
  legendItems?: Array<{
    color: string;
    label: string;
    active?: boolean;
    onClick?: () => void;
  }>;
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  children,
  className = '',
  headerActions,
  showTitleInChart = false,
  chartTitleNode,
  showShadow = true,
  legendItems,
}) => {
  const shadowClass = showShadow ? 'shadow-md' : '';

  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 ${shadowClass} ${className}`}
    >
      {/* Title with optional legend items */}
      {title && (
        <ChartTitle
          title={title}
          rightContent={
            <div className="flex items-center gap-4">
              {legendItems && (
                <div className="flex items-center gap-4">
                  {legendItems.map((item, index) => (
                    <LegendItem
                      key={index}
                      color={item.color}
                      label={item.label}
                      active={item.active}
                      onClick={item.onClick}
                    />
                  ))}
                </div>
              )}
              {headerActions}
            </div>
          }
        />
      )}

      {/* Custom title node */}
      {showTitleInChart && chartTitleNode}

      {/* Chart content */}
      <div className="w-full">{children}</div>
    </div>
  );
};

export default ChartContainer;
