import { Box, styled } from '@mui/material';
import Slider from '@mui/material/Slider';

export const Mu<PERSON><PERSON>lide<PERSON> = styled(Slider)({
  color: '#1976d2',
  height: 8,

  ['& .MuiSlider-track']: {
    border: 'none',
    background: 'linear-gradient(to right, #50C1FC, #0496E1)',
    height: 8,
    zIndex: 2,
  },

  ['& .MuiSlider-rail']: {
    backgroundColor: '#0496E1',
    opacity: 0.2,
    height: 8,
    zIndex: 2,
  },

  ['& .MuiSlider-thumb']: {
    height: 16,
    width: 16,
    backgroundColor: '#1976d2',
    zIndex: 2,
    border: '2px solid currentColor',
    borderRadius: '40px',

    ['&:focus, &:hover, &.Mui-active, &.Mui-focusVisible']: {
      boxShadow: 'inherit',
    },

    ['&:before']: {
      display: 'none',
    },
  },

  ['& .MuiSlider-mark']: {
    backgroundColor: '#1976d2',
    height: 8,
    width: 2,

    ['&.MuiSlider-markActive']: {
      opacity: 1,
      backgroundColor: '#1976d2',
    },
  },

  ['& .MuiSlider-markLabel']: {
    fontSize: '14px',
    color: '#333',
    fontWeight: 500,
  },

  ['&.Mui-disabled']: {
    color: '#9e9e9e',

    ['& .MuiSlider-track']: {
      background: '#bdbdbd',
    },

    ['& .MuiSlider-rail']: {
      backgroundColor: '#e0e0e0',
      opacity: 0.5,
    },

    ['& .MuiSlider-thumb']: {
      backgroundColor: '#9e9e9e',
      borderColor: '#9e9e9e',
    },

    ['& .MuiSlider-mark']: {
      backgroundColor: '#9e9e9e',
      '&.MuiSlider-markActive': {
        backgroundColor: '#9e9e9e',
      },
    },

    ['& .MuiSlider-markLabel']: {
      color: '#9e9e9e',
    },
  },
});

export const Indicator = styled(Box)(({}) => ({
  position: 'absolute',
  top: 8,
  left: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  width: '100%',
}));
