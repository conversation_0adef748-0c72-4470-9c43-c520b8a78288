'use client';

import { ReactNode } from 'react';
import { useEffect } from 'react';

import { useIsAuthenticated } from '@azure/msal-react';

import { useRouter } from 'next/navigation';

import SessionIdleMonitor from '@core/providers/SessionIdleMonitor';

import Loading from '@/lib/common/loading';

export default function AuthGuard({ children }: { children: ReactNode }) {
  const isAuthenticated = useIsAuthenticated();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      if (typeof window !== 'undefined') {
        localStorage.setItem('redirectTo', window.location.pathname + window.location.search);
      }
      router.push(`/login`);
    }
  }, [isAuthenticated, router]);

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-screen w-screen">
        <Loading />
      </div>
    );
  }

  return (
    <>
      <SessionIdleMonitor />
      {children}
    </>
  );
}
