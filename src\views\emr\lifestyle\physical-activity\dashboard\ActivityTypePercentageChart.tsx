import React from 'react';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';

import {
  ChartTitle,
  LegendItem,
} from '@/views/emr/lifestyle/shared/dashboard-components/ChartTitle';
import { CustomBarCursor } from '@/views/emr/lifestyle/shared/dashboard-components/CustomBarCursor';
import { calculateBarWidth } from '@/views/emr/lifestyle/shared/dashboard-components/utils/chartUtils';

interface DataPoint {
  [key: string]: any;
  date: string;
}

interface ChartDataItem {
  key: string;
  name: string;
  color: string;
  radius?: [number, number, number, number];
}

interface ActivityTypePercentageChartProps<T extends DataPoint> {
  data: T[];
  dataKeys: ChartDataItem[];
  title?: string;
  height?: number;
  showLegend?: boolean;
  maxBarWidth?: number;
  xAxisKey?: string;
}

const ActivityTypePercentageChart = <T extends DataPoint>({
  data,
  dataKeys,
  title = '',
  height = 286,
  showLegend = false,
  maxBarWidth = 35,
  xAxisKey = 'date',
}: ActivityTypePercentageChartProps<T>) => {
  const CustomizedAxisTick = (props: any) => {
    const { x, y, payload } = props;
    const date = new Date(payload.value);
    const day = date.getDate();
    const month = date.toLocaleString('en-US', { month: 'short' });

    return (
      <g transform={`translate(${x},${y})`}>
        <text
          x={0}
          y={0}
          dy={12}
          textAnchor="middle"
          fill="#6b7280"
          fontSize={11}
        >
          {day}
        </text>
        <text
          x={0}
          y={12}
          dy={12}
          textAnchor="middle"
          fill="#6b7280"
          fontSize={9}
        >
          {month}
        </text>
      </g>
    );
  };

  interface TooltipProps {
    active?: boolean;
    payload?: Array<{
      dataKey: string;
      value: number;
      name: string;
      color: string;
      payload: {
        [key: string]: any;
        fullDate?: string;
      };
    }>;
    label?: string | number | Date;
    dataKeys: ChartDataItem[];
    xAxisKey: string;
  }

  const CustomTooltip = ({
    active,
    payload,
    label,
    dataKeys,
  }: TooltipProps) => {
    if (active && payload && payload.length > 0) {
      // Get the full date from the payload
      const dataItem = payload[0]?.payload;
      const displayDate = dataItem?.fullDate || label;

      // Format the date for display
      let dateDisplay = '';
      if (displayDate) {
        try {
          const date = new Date(displayDate);
          if (!isNaN(date.getTime())) {
            dateDisplay = date.toLocaleDateString('en-US', {
              day: 'numeric',
              month: 'short',
              year: 'numeric',
            });
          } else {
            dateDisplay = String(displayDate);
          }
        } catch (e) {
          dateDisplay = String(displayDate);
        }
      }

      return (
        <div className="bg-white p-2 border border-gray-200 rounded shadow-md mx-2">
          {dateDisplay && <p className="text-sm font-medium">{dateDisplay}</p>}
          {/* Reverse the order to match the chart's stacking order */}
          {[...(payload || [])].reverse().map((entry, index) => {
            const dataItem = dataKeys.find((d) => d.key === entry.dataKey);
            const color = dataItem?.color || entry.color;
            const name = dataItem?.name || entry.name;

            return (
              <p
                key={`${entry.dataKey}-${index}`}
                className="text-sm"
                style={{ color }}
              >
                {name}: {entry.value.toFixed(1)}%
              </p>
            );
          })}
        </div>
      );
    }
    return null;
  };

  const CustomCursor = (props: any) => (
    <CustomBarCursor
      {...props}
      barWidth={calculateBarWidth({
        dataLength: data.length,
        maxBarWidth,
        margin: 50,
      })}
    />
  );

  const legendItems = showLegend
    ? dataKeys.map((item) => (
        <LegendItem key={item.key} color={item.color} label={item.name} />
      ))
    : null;

  return (
    <div className="w-full flex flex-col" style={{ height: `${height}px` }}>
      <ChartTitle
        title={title}
        className="px-4 pt-3 pb-3"
        rightContent={
          showLegend ? (
            <div className="flex items-center gap-3">{legendItems}</div>
          ) : null
        }
      />
      <div className="flex-1">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{
              top: 20,
              right: 25,
              left: 25,
              bottom: 2,
            }}
            barGap={0}
            barCategoryGap="10%"
          >
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#e5e7eb"
              horizontal={true}
              vertical={true}
            />
            <XAxis
              dataKey={xAxisKey}
              axisLine={false}
              tickLine={false}
              interval={0}
              minTickGap={1}
              height={50}
              tick={<CustomizedAxisTick />}
              tickMargin={10}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              domain={[0, 100]}
              tickFormatter={(value) => `${value}%`}
              width={30}
            />
            <Tooltip
              content={
                <CustomTooltip dataKeys={dataKeys} xAxisKey={xAxisKey} />
              }
              cursor={<CustomCursor />}
            />

            {dataKeys.map((item) => (
              <Bar
                key={item.key}
                dataKey={item.key}
                name={item.name}
                fill={item.color}
                radius={item.radius || [0, 0, 0, 0]}
                stackId="a"
              />
            ))}
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ActivityTypePercentageChart;
