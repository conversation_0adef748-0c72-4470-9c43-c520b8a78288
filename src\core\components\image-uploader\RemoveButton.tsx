import React, { FC, memo } from 'react';

import { cn } from '@/lib/utils';

import { BinIcon } from '@/assets/svg/BinIcon';

type Props = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  showPreviewOption: boolean;
};

const RemoveButton: FC<Props> = ({
  showPreviewOption,
  className,
  onClick,
  ...props
}) => {
  return (
    <button
      type="button"
      className={cn(
        'absolute -right-1 -top-2',
        'bg-black',
        'p-1 rounded-full z-10',
        'transition-opacity opacity-100',
        'focus:outline-none focus:ring-0 focus:ring-offset-0 ',
        { 'opacity-0': !showPreviewOption },
        className
      )}
      onClick={(e) => {
        e.stopPropagation();
        onClick && onClick(e);
      }}
      {...props}
    >
      <BinIcon />
    </button>
  );
};

export default memo(RemoveButton);
