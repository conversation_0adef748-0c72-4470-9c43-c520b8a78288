import React, { memo } from 'react';

import { SuccessIcon } from '@/core/components/status-modal/modal-icons';

interface LabTestPaymentSuccessModalProps {
  open: boolean;
  onClose: () => void;
}

const LabTestPaymentSuccessModal: React.FC<LabTestPaymentSuccessModalProps> = ({
  open,
  onClose,
}) => {
  console.log('🔍 LabTestPaymentSuccessModal render - open:', open);

  if (!open) {
    return null;
  }

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-3xl p-6 w-[250px] text-center"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-center mb-4">
          <div className="scale-75">
            <SuccessIcon />
          </div>
        </div>
        <h3 className="text-lg font-semibold text-[#001926] mb-2">
          Payment Processed Successfully!
        </h3>
      </div>
    </div>
  );
};

export default memo(LabTestPaymentSuccessModal);
