import React, { useState, useRef, useEffect } from 'react';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';
import { useUserStore } from '@/store/userStore';

import { getDoctorName } from '@/utils/constants/lifeStyle';

import {
  lifestyleModes,
  renderFormModes,
  RenderFormModesTypes,
} from '@/constants/lifestyle';

import Accordion from '../shared/Accordion';
import FinalizeModal from '../shared/FinalizeModal';
import TimeLine from '../shared/TimeLine';
import TimelineTitle from '../shared/TimelineTitle';

import AwarenessAdaptationModal from './AwarenessAdaptationModal';
import AwarenessAdaptationRecordModal from './AwarenessAdaptationRecordModal';

type Props = {
  isShowTitle?: boolean;
};

const AwarenessAdaptationTimelineMobileExpand = ({
  isShowTitle = true,
}: Props) => {
  const { name } = useUserStore();
  const { closeModal, setFormMode } = useLifestyleUtilStore();
  const doctor = useDoctorStore((state) => state.doctorProfile);

  const [expanded, setExpanded] = useState(false);
  const [expand, setExpand] = useState(true);

  const [open, setOpen] = useState(false);

  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  const toggleAccordion = () => {
    setExpanded((prev) => !prev);
  };
  const toggleExpand = () => {
    setExpand((prev) => !prev);
    closeModal();
  };
  useEffect(() => {
    if (expanded && scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [expanded]);

  useEffect(() => {
    if (expand) setFormMode?.(lifestyleModes.VIEW);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [expand]);

  const isMobile = useIsMobile();
  const [renderForm, setRenderForm] = useState<RenderFormModesTypes>(
    renderFormModes.TRANSCRIPT_MODE
  );

  return (
    <div className="max-h-full min-h-0 mt-3  w-full flex flex-col overflow-hidden">
      {isShowTitle && (
        <TimelineTitle
          title={
            isMobile
              ? 'Lifestyle Awareness & Adaptation'
              : 'Lifestyle Awareness & Adaptation Questionnaire'
          }
        />
      )}

      <div
        ref={scrollContainerRef}
        className="h-full w-full overflow-y-hidden rounded-thin-scrollbar pr-1"
      >
        <div className="flex flex-col h-full min-h-0 w-full">
          <div className="overflow-auto  h-full ">
            <TimeLine
              items={[
                {
                  content: (
                    <div className="accordion-item ">
                      <Accordion
                        open={expand}
                        onToggle={toggleAccordion}
                        designation={doctor?.general?.department}
                        onFinalise={() => setOpen(true)}
                        doctorName={getDoctorName(
                          doctor?.general?.fullName,
                          name
                        )}
                        onExpand={toggleExpand}
                        expand={expand}
                        width="100%"
                      >
                        <div
                          style={{ maxHeight: 'calc(100dvh - 25rem)' }}
                          className=" relative overflow-y-auto overflow-x-hidden p-2 pb-3"
                        >
                          {renderForm === renderFormModes.RECORD_MODE && (
                            <AwarenessAdaptationRecordModal
                              setRenderForm={setRenderForm}
                              renderForm={renderForm}
                            />
                          )}
                          {renderForm === renderFormModes.TRANSCRIPT_MODE && (
                            <>
                              <AwarenessAdaptationModal
                                setRenderForm={setRenderForm}
                                renderForm={renderForm}
                              />
                            </>
                          )}
                        </div>
                      </Accordion>
                    </div>
                  ),
                },
              ]}
            />
          </div>
        </div>
      </div>

      <FinalizeModal
        open={open}
        onClose={() => setOpen(false)}
        onFinalize={() => setOpen(false)}
      />
    </div>
  );
};

export default AwarenessAdaptationTimelineMobileExpand;
