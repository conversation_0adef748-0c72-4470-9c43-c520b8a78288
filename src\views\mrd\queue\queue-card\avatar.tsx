import { useCallback, useMemo } from 'react';

import { cn } from '@/lib/utils';

import {
  AppointmentStatus,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';

export type AvatarProps = {
  name: string;
  status?: AppointmentStatus;
  patientStatus?: PatientStatus;
  queuePosition?: number;
};
export default function Avatar({
  name,
  status,
  patientStatus,
  queuePosition,
}: AvatarProps) {
  const displayText = useMemo(() => {
    if (queuePosition !== undefined) {
      return queuePosition.toString();
    }
    return name
      .split(' ')
      .slice(0, 2)
      .map((item) => item[0]?.toUpperCase())
      .join('');
  }, [name, queuePosition]);

  const getAvatarColor = useCallback(() => {
    const red = 'bg-[#e4626f]';
    const green = 'bg-[#06c6a7]';
    const yellow = 'bg-[#ff9f2a]';

    if (status === AppointmentStatus.Consultation) {
      return green;
    }

    if (status === AppointmentStatus.Booked) {
      if (patientStatus === PatientStatus.Arrived) {
        return green;
      }

      if (patientStatus === PatientStatus.ProxyVisit) {
        return yellow;
      }
    }

    return red;
  }, [patientStatus, status]);

  return (
    <div
      className={cn(getAvatarColor(), [
        'rounded-full w-8 h-8 2xl:h-10 2xl:w-10 flex items-center justify-center col-span-2',
      ])}
    >
      <div className="h-7 w-7 2xl:h-9 2xl:w-9 rounded-full bg-[#C2CDD6] flex items-center justify-center">
        <span className="text-white text-[10px] 2xl:text-xs font-medium">
          {displayText}
        </span>
      </div>
    </div>
  );
}
