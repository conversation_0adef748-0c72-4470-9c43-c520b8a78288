import { ReactNode } from 'react';

import { Popover, PopoverProps } from '@mui/material';

interface CustomPopoverProps extends Omit<PopoverProps, 'children'> {
  children: ReactNode;
  containerClassName?: string;
}

const CustomPopover = ({
  open,
  anchorEl,
  onClose,
  anchorOrigin,
  transformOrigin,
  children,
  containerClassName = 'p-3 max-w-70',
  ...props
}: CustomPopoverProps) => {
  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={anchorOrigin}
      transformOrigin={transformOrigin}
      {...props}
    >
      <div className={containerClassName}>{children}</div>
    </Popover>
  );
};

export default CustomPopover;
