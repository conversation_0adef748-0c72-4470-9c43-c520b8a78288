import React, { useEffect } from 'react';

import { Box, Typography } from '@mui/material';

import { useCustomiseEmrStore } from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import { ArcaLogo } from '@/assets/svg/ArcaLogo';

import CustomModal from '@/core/components/modal';
import PrimaryButton from '@/core/components/primary-button';

import {
  CONTAINER_STYLES,
  MODAL_CONFIG,
  TYPOGRAPHY_STYLES,
} from './PrintModalStyles';

// Types
interface PatientData {
  id?: string;
  name?: string;
  age?: string | number;
  mobile?: string;
  gender?: string;
}

interface OrganizationData {
  name?: string;
  address?: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
}

interface PrintModalProps {
  open: boolean;
  onClose: () => void;
  patient?: PatientData;
  doctor?: string;
  date?: string;
  children: React.ReactNode;
  title?: string;
  prescriptionTitle?: string;
  onPrint?: () => void;
  organization?: OrganizationData | null;
  width?: string;
}

const formatDate = (dateString?: string): string => {
  const date = dateString ? new Date(dateString) : new Date();
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  });
};

const formatDoctorName = (doctor?: string): string => {
  if (!doctor) return 'Dr. [Name]';
  return `Dr. ${doctor}`;
};

// Sub-components
const OrganizationLogo: React.FC = () => {
  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();
  const { doctorProfile } = useDoctorStore();

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile.id);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  const mostRecentData = customiseEmrData?.length
    ? [...customiseEmrData].sort(
        (a, b) =>
          new Date(b.updated_on || '').getTime() -
          new Date(a.updated_on || '').getTime()
      )[0]
    : null;

  const organizationLogoUrl = mostRecentData?.organizationLogo;

  if (organizationLogoUrl) {
    return (
      <Box width={90} height={90} flexShrink={0}>
        <img
          src={organizationLogoUrl}
          alt="Organization Logo"
          style={{ width: '100%', height: '100%', objectFit: 'contain' }}
        />
      </Box>
    );
  }

  return <Box bgcolor="grey.300" width={60} height={30} flexShrink={0} />;
};

const OrganizationInfo: React.FC<{
  organization?: OrganizationData | null;
}> = ({ organization }) => (
  <Box>
    <Typography sx={TYPOGRAPHY_STYLES.organization}>
      {organization?.name || 'Hospital Name'}
    </Typography>
    {organization?.address && (
      <Typography sx={TYPOGRAPHY_STYLES.organization}>
        {organization.address}
      </Typography>
    )}
    {organization?.phone && (
      <Typography sx={TYPOGRAPHY_STYLES.organization}>
        {organization.phone}
      </Typography>
    )}
    {organization?.email && (
      <Typography sx={TYPOGRAPHY_STYLES.organization}>
        {organization.email}
      </Typography>
    )}
  </Box>
);

const DoctorInfo: React.FC<{ doctor?: string }> = ({ doctor }) => {
  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();
  const { doctorProfile } = useDoctorStore();

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile.id);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  const mostRecentData = customiseEmrData?.length
    ? customiseEmrData.sort(
        (a, b) =>
          new Date(b.updated_on || '').getTime() -
          new Date(a.updated_on || '').getTime()
      )[0]
    : null;

  const letterHeadDetails = mostRecentData?.letterHeadDetails || '';
  const letterHeadLines = letterHeadDetails
    .split('\n')
    .filter((line: string) => line.trim());

  return (
    <Box>
      <Typography sx={TYPOGRAPHY_STYLES.doctor}>
        {formatDoctorName(doctor)}
      </Typography>
      {letterHeadLines.map((letterHeadLine: string, index: number) => (
        <Typography key={index} sx={{ ...TYPOGRAPHY_STYLES.doctor }}>
          {letterHeadLine.trim()}
        </Typography>
      ))}
    </Box>
  );
};

const HeaderSection: React.FC<{
  doctor?: string;
  organization?: OrganizationData | null;
}> = ({ doctor, organization }) => (
  <Box
    display="flex"
    justifyContent="space-between"
    mb={3}
    sx={CONTAINER_STYLES.header}
  >
    <DoctorInfo doctor={doctor} />
    <Box textAlign="right">
      <Box display="flex" alignItems="flex-start" gap={1}>
        <OrganizationLogo />
        <OrganizationInfo organization={organization} />
      </Box>
    </Box>
  </Box>
);

const PrescriptionTitle: React.FC<{ title?: string }> = ({ title }) => (
  <Typography
    variant="h6"
    fontWeight="bold"
    textAlign="center"
    mb={2}
    sx={TYPOGRAPHY_STYLES.prescriptionTitle}
  >
    {title}
  </Typography>
);

const PatientInfoField: React.FC<{ label: string; value: string | number }> = ({
  label,
  value,
}) => (
  <Typography>
    <span>{label}: </span>
    <span style={{ fontWeight: 'bold' }}>{value || 'N/A'}</span>
  </Typography>
);

const PatientInfoSection: React.FC<{
  patient?: PatientData;
  date?: string;
}> = ({ patient, date }) => (
  <Box
    display="flex"
    justifyContent="space-between"
    mb={2}
    sx={CONTAINER_STYLES.patientInfo}
  >
    <PatientInfoField label="Patient Name" value={patient?.name || ''} />
    <PatientInfoField label="Patient ID" value={patient?.id || ''} />
    <PatientInfoField label="Age" value={patient?.age || ''} />
    <PatientInfoField label="Mobile" value={patient?.mobile || ''} />
    <PatientInfoField label="Gender" value={patient?.gender || ''} />
    <PatientInfoField label="Date" value={formatDate(date)} />
  </Box>
);

const PrintButton: React.FC<{ onPrint: () => void }> = ({ onPrint }) => (
  <Box sx={{ position: 'absolute', top: 8, right: 16 }}>
    <PrimaryButton
      type="button"
      onClick={onPrint}
      className="capitalize text-md h-8 w-full px-10"
    >
      Print
    </PrimaryButton>
  </Box>
);

const PoweredBySection: React.FC = () => (
  <Box
    sx={{
      position: 'absolute',
      bottom: 0,
      left: '50%',
      transform: 'translateX(-50%)',
      display: 'flex',
      alignItems: 'center',
      gap: 1,
    }}
  >
    <Typography sx={TYPOGRAPHY_STYLES.footer}>Powered by</Typography>
    <ArcaLogo width={40} height={16} />
  </Box>
);

const FooterSection: React.FC<{ onPrint: () => void }> = ({ onPrint }) => (
  <Box sx={CONTAINER_STYLES.footer}>
    <PrintButton onPrint={onPrint} />
    <PoweredBySection />
  </Box>
);

// Main Component
const PrintModal: React.FC<PrintModalProps> = ({
  open,
  onClose,
  patient,
  doctor,
  date,
  children,
  title = 'Print Preview',
  prescriptionTitle,
  onPrint,
  organization,
  width,
}) => {
  const handlePrint = () => {
    if (onPrint) {
      onPrint();
    } else {
      window.print();
    }
  };
  return (
    <CustomModal
      open={open}
      onClose={onClose}
      width={width || MODAL_CONFIG.width}
      maxHeight={MODAL_CONFIG.maxHeight}
      minHeight={MODAL_CONFIG.minHeight}
      showDivider
      contentSx={CONTAINER_STYLES.modal}
      title={
        <Typography variant="subtitle1" sx={{ color: '#012436' }}>
          {title}
        </Typography>
      }
      content={
        <div style={CONTAINER_STYLES.main}>
          <HeaderSection doctor={doctor} organization={organization} />
          <PrescriptionTitle title={prescriptionTitle} />
          <PatientInfoSection patient={patient} date={date} />

          <Box sx={CONTAINER_STYLES.content}>{children}</Box>

          <FooterSection onPrint={handlePrint} />
        </div>
      }
      actionsSx={{ display: 'none' }}
      actions={null}
    />
  );
};

export default PrintModal;
