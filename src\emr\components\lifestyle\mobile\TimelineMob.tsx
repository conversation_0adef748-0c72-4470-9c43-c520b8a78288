import React, { memo, ReactNode, useMemo } from 'react';

import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import {
  LifestyleFormsType,
  NutritionAndDietForm,
} from '@/constants/lifestyle';

import AwarenessAdaptationTimeline from '../lifestyle-forms/awareness-adaptation';
import AwarenessAdaptationTimelineMobileExpand from '../lifestyle-forms/awareness-adaptation/ExpandMobileView';
import DietaryAssessmentTimeline from '../lifestyle-forms/dietary-assessment';
import DietaryAssessmentExpandMobileView from '../lifestyle-forms/dietary-assessment/DietaryAssessmentExpandMobileView';
import NutritionMonitoringTimeline from '../lifestyle-forms/nutrition-monitoring';
import NutritionMonitoringTimelineMobileExpand from '../lifestyle-forms/nutrition-monitoring/ExpandMobileView';

import DetailsTimelineButtons from './shared/DetailsTimelineButtons';
import MobilePageTitle from './shared/MobilePageTitle';

const {
  DIETARY_ASSESSMENT,
  LIFESTYLE_AWARENESS_ADAPTATION,
  NUTRITION_MONITORING_SHEET,
} = NutritionAndDietForm;

const TimelineMob = () => {
  const { currentTab, isModalOpen } = useLifestyleUtilStore();

  const lifestyleTimeline = useMemo<Record<LifestyleFormsType, ReactNode>>(
    () => ({
      [DIETARY_ASSESSMENT]: <DietaryAssessmentTimeline showTitle={false} />,
      [LIFESTYLE_AWARENESS_ADAPTATION]: (
        <AwarenessAdaptationTimeline isShowTitle={false} />
      ),
      [NUTRITION_MONITORING_SHEET]: (
        <NutritionMonitoringTimeline isShowTitle={false} />
      ),
    }),
    []
  );

  const formModals = useMemo<Record<LifestyleFormsType, ReactNode>>(
    () => ({
      [DIETARY_ASSESSMENT]: <DietaryAssessmentExpandMobileView />,
      [LIFESTYLE_AWARENESS_ADAPTATION]: (
        <AwarenessAdaptationTimelineMobileExpand isShowTitle={false} />
      ),
      [NUTRITION_MONITORING_SHEET]: (
        <NutritionMonitoringTimelineMobileExpand isShowTitle={false} />
      ),
    }),
    []
  );

  return (
    <div className="h-full  overflow-y-hidden ">
      <div className="h-full flex flex-col px-2 pb-2 rounded-b-lg">
        <div>
          <MobilePageTitle
            title={
              currentTab === LIFESTYLE_AWARENESS_ADAPTATION
                ? 'Lifestyle Awareness & Adaptation'
                : currentTab
            }
            variant="small"
            showCalender
            currentTab={currentTab}
          />
        </div>
        <div className="h-[10%]">
          <DetailsTimelineButtons />
        </div>
        <div className="h-full overflow-y-hidden">
          {isModalOpen ? formModals[currentTab] : lifestyleTimeline[currentTab]}
        </div>
      </div>
    </div>
  );
};

export default memo(TimelineMob);
