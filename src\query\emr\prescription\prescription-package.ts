import { useUserStore } from '@/store/userStore';

import { api } from '@/core/lib/interceptor';
import {
  PrescriptionPackageType,
  prescriptionPackageTypes,
} from '@/types/emr/prescription';
import {
  MedicineItem,
  PrescriptionPackageData,
} from '@/types/emr/prescription/package';

const baseUrl = `/prescription-package/v0.1/prescription-package`;

const { USER, DEPARTMENT } = prescriptionPackageTypes;

export const fetchMedicinePackages = async (
  packageType: PrescriptionPackageType
) => {
  const userId = useUserStore.getState().data.id;
  const organizationId = useUserStore.getState().data.organizationId;
  const params: any = { type: packageType };
  if (packageType === USER || packageType === DEPARTMENT) {
    params.organizationId = organizationId;
  }
  if (packageType === USER) {
    params.userId = userId;
  }
  return await api.get<PrescriptionPackageData[]>(baseUrl, {
    params,
  });
};

export const fetchMedicinesByPackageId = async (
  id: string,
  organizationId?: string
): Promise<{
  data: {
    package: PrescriptionPackageData;
    medicines: MedicineItem[];
  };
}> => {
  const params: any = { id };
  if (organizationId) {
    params.organizationId = organizationId;
  }
  const response = await api.get(`${baseUrl}/details`, {
    params,
  });

  return response.data;
};

export const createMedicinePackage = async (data: {
  name: string;
  type: string;
  medicineIds: (string | number)[];
}) => {
  const userId = useUserStore.getState().data.id;
  return await api.post(baseUrl, { ...data, userId });
};

export const updateMedicinePackage = async (
  packageId: string,
  data: {
    medicineIds: (string | number)[];
    name?: string;
  }
) => {
  const userId = useUserStore.getState().data.id;
  return await api.put(`${baseUrl}?id=${packageId}`, {
    ...data,
    userId,
  });
};

export const deleteMedicinePackage = async (packageId: string) => {
  return await api.delete(baseUrl, {
    params: { id: packageId },
  });
};
