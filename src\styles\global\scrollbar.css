/* Global Scrollbar Styles with Reduced Opacity */

/* Hide scrollbar completely */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Default scrollbar with reduced opacity */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  cursor: grab;
}

::-webkit-scrollbar:hover {
  height: 13px;
  width: 13px;
}

::-webkit-scrollbar-track {
  background: rgba(8, 42, 66, 0.1); /* Reduced opacity */
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(8, 42, 66, 0.2); /* Reduced opacity */
  border-radius: 10px;
  transition: background-color 0.2s ease;
  cursor: grab;
}

::-webkit-scrollbar-thumb:active {
  cursor: grabbing;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(8, 42, 66, 0.6); /* Slightly more visible on hover */
}

/* Thin scrollbar with reduced opacity */
.rounded-thin-scrollbar::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  border-radius: 8px;
}

.rounded-thin-scrollbar::-webkit-scrollbar-track {
  background: rgba(211, 217, 221, 0.2); /* Very low opacity */
  border-radius: 10px;
}

.rounded-thin-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(94, 122, 142, 0.3); /* Reduced opacity */
  border-radius: 10px;
  transition: background-color 0.2s ease;
}

.rounded-thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(74, 102, 122, 0.5); /* Slightly more visible on hover */
}

/* Extra thin scrollbar with minimal opacity */
.scrollbar-minimal::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbar-minimal::-webkit-scrollbar-track {
  background: rgba(241, 241, 241, 0.367); /* Very minimal opacity */
  border-radius: 8px;
}

.scrollbar-minimal::-webkit-scrollbar-thumb {
  background: rgba(193, 193, 193, 0.667); /* Very low opacity */
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.scrollbar-minimal::-webkit-scrollbar-thumb:hover {
  background: rgba(160, 160, 160, 0.4); /* Slightly more visible on hover */
}

/* Invisible scrollbar that only appears on hover */
.scrollbar-hover-only::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-hover-only::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}

.scrollbar-hover-only::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.scrollbar-hover-only:hover::-webkit-scrollbar-thumb {
  background: rgba(193, 193, 193, 0.3);
}

.scrollbar-hover-only::-webkit-scrollbar-thumb:hover {
  background: rgba(160, 160, 160, 0.5);
}
