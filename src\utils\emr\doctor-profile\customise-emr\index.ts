import { GRID_LAYOUTS } from '@/constants/emr/doctor-profile/customise-emr';

export const getDisplayedItems = (selectedButton: string): string[][] => {
  const displayedItems: string[][] = [];

  if (selectedButton === GRID_LAYOUTS.SMALL) {
    displayedItems.push(Array(3).fill(''));
  } else if (selectedButton === GRID_LAYOUTS.MEDIUM) {
    displayedItems.push(Array(3).fill(''), Array(3).fill(''));
  } else if (selectedButton === GRID_LAYOUTS.LARGE) {
    displayedItems.push(
      Array(3).fill(''),
      Array(3).fill(''),
      Array(3).fill('')
    );
  }
  return displayedItems;
};
