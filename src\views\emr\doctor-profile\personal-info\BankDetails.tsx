'use client';

import { useEffect, useState } from 'react';

import { Controller, FormProvider, useForm } from 'react-hook-form';

import FileInput from '@core/components/file-input';
import TextInput from '@core/components/text-input';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { getNestedValue } from '@/utils/emr/doctor-profile/personal-info';
import { isFileList } from '@/utils/fileTypeChecks';
import {
  allowOnlyNumbers,
  enforceAlphanumericInput,
  enforceNumericInput,
  restrictToNumbersAndAlphabetsAndDeletes,
} from '@/utils/validation';

import PencilIcon from '@/assets/svg/PencilIcon';

import {
  bankBranchOptions,
  bankOptions,
  profileTabs,
} from '@/constants/emr/doctor-profile/personal-info';

import SectionTitle from '@/views/emr/doctor-profile/personal-info/SectionTitle';

import { useFileUpload } from '@/emr/hooks/use-file-upload';

import { BankDetails } from '@/types/emr/doctor-profile/personal-info';

import { FormRow, SelectField, TextAreaInput } from './Components';

import SaveButton from './shared/SaveButton';

export default function BankInfo() {
  const { data: userData } = useUserStore();
  const isMobile = useIsMobile();
  const userId = userData?.id;

  const { fileUpload } = useFileUpload({ userId, type: 'idProof' });

  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);

  const methods = useForm({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      bank: '',
      ifsc: '',
      document: '',
      branch: '',
      accountNumber: '',
      description: '',
    },
  });

  const {
    register,
    setValue,
    watch,
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = methods;

  const {
    doctorProfile,
    fetchDoctorProfileByEmail,
    createDoctorProfile,
    updateDoctorProfile,
    setTabName,
  } = useDoctorStore();

  useEffect(() => {
    if (doctorProfile) {
      const { bankDetails } = doctorProfile || {};
      reset({
        ...bankDetails,
      });
    }
  }, [doctorProfile, reset]);

  useEffect(() => {
    setTabName(profileTabs.BANK_DETAILS);
    if (userData?.email) {
      fetchDoctorProfileByEmail(userData?.email);
    }
  }, [userData?.email, fetchDoctorProfileByEmail, setTabName]);

  const onSubmit = async (data: BankDetails) => {
    try {
      let uploadedDocument = data?.document;
      if (isFileList(uploadedDocument) && uploadedDocument.length) {
        uploadedDocument = await fileUpload(uploadedDocument[0]);
      }
      const bankDetails = {
        ...data,
        document: uploadedDocument,
      };

      const payload = { bankDetails: bankDetails };
      if (doctorProfile && doctorProfile.id) {
        await updateDoctorProfile(doctorProfile.id, payload);
        setEditableField(new Set());
        setIsSubmitted(true);
      } else {
        await createDoctorProfile({ ...payload, username: userData?.email });
        setEditableField(new Set());
        setIsSubmitted(true);
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const handleEditClick = (fieldName: string) => {
    setEditableField((prev) => new Set(prev.add(fieldName)));
  };

  const isFieldDisabled = (fieldName: string) => {
    if (editableField.has(fieldName)) {
      return false;
    }

    const formValues = methods.getValues() as BankDetails;
    const fieldValue = formValues[fieldName as keyof BankDetails];
    const doctorFieldValue = getNestedValue(
      doctorProfile?.bankDetails,
      fieldName
    );

    return (
      !!doctorFieldValue ||
      (isSubmitted && !!fieldValue && fieldValue === doctorFieldValue)
    );
  };

  const renderEditIcon = (fieldName: keyof BankDetails) => {
    return isFieldDisabled(fieldName) ? (
      <button
        className="mt-2"
        type="button"
        onClick={() => handleEditClick(fieldName)}
      >
        <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
      </button>
    ) : null;
  };

  return (
    <div className="w-full px-1 pb-5">
      <SectionTitle className="mb-5 mt-1" title="Bank Details" />
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            <FormRow className="w-full flex flex-col gap-4 col-span-1">
              <SelectField
                id="bank"
                label="Bank"
                options={bankOptions}
                value={watch('bank')}
                onChange={(value) => {
                  setValue('bank', value);
                }}
                wrapperClassName="flex-2 flex-grow"
                placeholder={'Bank Name'}
                disabledInput={isFieldDisabled('bank')}
                endDecoration={renderEditIcon('bank')}
              />
              {isMobile && (
                <SelectField
                  id="branch"
                  label="Branch"
                  options={bankBranchOptions}
                  value={watch('branch')}
                  onChange={(value) => {
                    setValue('branch', value);
                  }}
                  wrapperClassName="w-full flex-grow"
                  placeholder="--- Select Branch ---"
                  disabledInput={isFieldDisabled('branch')}
                  endDecoration={renderEditIcon('branch')}
                />
              )}
              <TextInput
                key={'ifsc'}
                label={'IFSC'}
                placeholder={'ABCD552066338821'}
                color="white"
                {...register('ifsc')}
                disabled={isFieldDisabled('ifsc')}
                endDecoration={renderEditIcon('ifsc')}
                onInput={enforceAlphanumericInput()}
                onKeyDown={restrictToNumbersAndAlphabetsAndDeletes}
                onChange={(e) => {
                  const transformedValue = e.target.value.toUpperCase();
                  setValue('ifsc', transformedValue);
                }}
              />
              <Controller
                name={`document`}
                control={control}
                render={({ field }) => (
                  <FileInput
                    label={'Document'}
                    value={field.value}
                    onChange={(files) => field.onChange(files)}
                    isBoxStyle
                    showPreview
                    allowedFileTypes={[
                      'image/jpeg',
                      'image/png',
                      'application/pdf',
                    ]}
                    fileTypeErrorMessage="*Only JPG, PNG, and PDF files are allowed."
                    maxFileSize={6}
                    className="relative"
                    errorClassName="absolute top-18"
                  />
                )}
              />
            </FormRow>
            <FormRow className="w-full gap-4 flex-col col-span-1">
              {!isMobile && (
                <SelectField
                  id="branch"
                  label="Branch"
                  options={bankBranchOptions}
                  value={watch('branch')}
                  onChange={(value) => {
                    setValue('branch', value);
                  }}
                  wrapperClassName="w-full xs:w-full md:w-2/3 flex-grow flex-2"
                  placeholder="--- Select Branch ---"
                  disabledInput={isFieldDisabled('branch')}
                  endDecoration={renderEditIcon('branch')}
                />
              )}
              <TextInput
                key={'accountNumber'}
                label={'Account Number'}
                placeholder={'251454866331486333'}
                color="white"
                {...register('accountNumber')}
                disabled={isFieldDisabled('accountNumber')}
                endDecoration={renderEditIcon('accountNumber')}
                onKeyDown={allowOnlyNumbers}
                onInput={enforceNumericInput()}
                inputMode="numeric"
                pattern="[0-9]*"
                errors={errors?.accountNumber}
              />
              {isMobile ? (
                <TextAreaInput
                  key="description"
                  label="Description"
                  color="white"
                  {...register('description')}
                  disabled={isFieldDisabled('description')}
                  endDecoration={renderEditIcon('description')}
                  rows={3}
                />
              ) : (
                <TextInput
                  key="description"
                  label="Description"
                  color="white"
                  {...register('description')}
                  disabled={isFieldDisabled('description')}
                  endDecoration={renderEditIcon('description')}
                />
              )}
            </FormRow>
          </div>

          <SaveButton className="mt-15" />
        </form>
      </FormProvider>
    </div>
  );
}
