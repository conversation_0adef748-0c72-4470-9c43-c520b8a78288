import React, { FC } from 'react';

import { Controller, Path, useFormContext } from 'react-hook-form';

import {
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Typography,
} from '@mui/material';

import { renderItalicText } from '@/utils/textUtil';

import { LifestyleQuestion, Questions } from '@/emr/types/lifestyle';

import ControlledTextarea from './ControlledTextarea';

type Props = {
  name: Path<LifestyleQuestion>;
  isReadOnly?: boolean;
  question: Questions;
};

const RenderCheckbox: FC<Props> = ({ name, isReadOnly, question }) => {
  const { control, watch } = useFormContext();

  const value = watch(`${name}.value`);

  return (
    <div>
      <Typography
        sx={{
          fontSize: { xs: '14px', sm: '16px', mt: 1 },
        }}
      >
        {renderItalicText(question?.question)}
      </Typography>
      <Controller
        control={control}
        name={`${name}.value`}
        render={({ field: { onChange, ...field } }) => (
          <FormGroup row>
            {question?.options?.map((option) => {
              return (
                <Box key={option} display="flex" alignItems="center">
                  <FormControlLabel
                    value={option}
                    control={
                      <Checkbox
                        disabled={isReadOnly}
                        sx={{ color: '#001926 !important' }}
                        size="small"
                        {...field}
                        checked={Array.isArray(value) && value.includes(option)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            onChange([...(value || []), option]);
                          } else {
                            onChange(
                              value?.filter((v: string) => v !== option)
                            );
                          }
                        }}
                      />
                    }
                    label={
                      <Typography
                        sx={{ fontSize: '13px', whiteSpace: 'nowrap' }}
                      >
                        {option}
                      </Typography>
                    }
                  />
                  {value?.includes('Others (please specify)') &&
                    option === 'Others (please specify)' && (
                      <ControlledTextarea
                        control={control}
                        name={`${name}.others`}
                        placeholder="Please specify"
                        rows={1}
                        disabled={isReadOnly}
                      />
                    )}
                </Box>
              );
            })}
          </FormGroup>
        )}
      />
    </div>
  );
};

export default RenderCheckbox;
