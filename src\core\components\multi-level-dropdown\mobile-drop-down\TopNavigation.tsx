import { memo } from 'react';

import IconButton from '@mui/material/IconButton';
import { AiOutlineClose } from 'react-icons/ai';
import { IoIosArrowBack } from 'react-icons/io';

import { TopNavigationProps } from '../types';

const TopNavigation = ({ onClickBack, onClose }: TopNavigationProps) => {
  return (
    <div className="flex justify-between items-center h-10">
      <div className="bg-white h-fit w-fit rounded-full">
        <IconButton size="small" onClick={onClickBack}>
          <IoIosArrowBack />
        </IconButton>
      </div>
      <div className="bg-white h-fit w-fit rounded-full">
        <IconButton size="small" onClick={onClose}>
          <AiOutlineClose size={18} />
        </IconButton>
      </div>
    </div>
  );
};

export default memo(TopNavigation);
