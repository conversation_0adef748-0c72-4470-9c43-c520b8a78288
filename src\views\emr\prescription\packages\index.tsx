import React, { useEffect } from 'react';

import { Box, Modal } from '@mui/material';

import { usePrescriptionStore } from '@/store/emr/prescription';
import { usePrescriptionPackageStore } from '@/store/emr/prescription/package';

import { getPackageTableHeaders } from '@/utils/emr/prescription/prescription-pkg';

import { modalStyle } from '@/constants/emr/lab';

import {
  prescriptionModalModes,
  PrescriptionPackageType,
} from '@/types/emr/prescription';

import { EmptyView } from './Components';
import { CreatePackageForm } from './CreatePackageForm';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import { DetailPackageView } from './DetailPackageView';
import {
  getMedicineRowsForCreateMode,
  getMedicineRowsForDetailMode,
} from './MedicineTableRows';
import { PackageList } from './PackageList';
import { PackageModalFooter } from './PackageModalFooter';
import { PackageModalHeader } from './PackageModalHeader';
import { PackageTypeButtons } from './PackageTypeButtons';
import { UnsavedChangesModal } from './UnsavedChangesModal';

const { DETAIL, CREATE, ADD } = prescriptionModalModes;

const PackageSelector = () => {
  const { selectedMedicines: prescriptionMedicines = [] } =
    usePrescriptionStore();

  const {
    isModalOpen,
    modalMode,
    packages,
    activePackageType,
    medicineItems,
    openModal,
    closeModal,
    removeMedicine,
    toggleMedicineSelection,
    addMultipleMedicines,
    fetchPackages,
    handleCancelWithCheck,
    checkUnsavedChanges,
  } = usePrescriptionPackageStore();

  useEffect(() => {
    if (isModalOpen && activePackageType) {
      fetchPackages();
    }
  }, [isModalOpen, activePackageType, fetchPackages]);

  useEffect(() => {
    if (
      isModalOpen &&
      (modalMode === CREATE || modalMode === ADD) &&
      prescriptionMedicines &&
      prescriptionMedicines.length > 0
    ) {
      const packageMedicines = prescriptionMedicines
        .filter(
          (medicine) =>
            medicine.id !== undefined &&
            typeof medicine.GenericName === 'string' &&
            medicine.packageType === activePackageType
        )
        .map((medicine) => ({
          id: medicine.id as string,
          medicineName: medicine.GenericName as string,
          brandName: medicine.BrandName || '',
          strength: medicine.Strength || '',
          drugForm: medicine.DrugFormulation || '',
          unitOfMeasure: medicine.UnitOfMeasure || '',
          measure: medicine.Measure || '',
          cost: parseFloat(medicine.Cost as string) || 0,
        }));

      if (packageMedicines.length > 0) {
        addMultipleMedicines(packageMedicines);
      }
    }
  }, [
    prescriptionMedicines,
    modalMode,
    isModalOpen,
    addMultipleMedicines,
    activePackageType,
  ]);

  const handlePackageTypeSelect = (type: PrescriptionPackageType) => {
    openModal(type);
  };

  const handleCloseModal = () => {
    if (checkUnsavedChanges()) {
      handleCancelWithCheck();
    } else {
      closeModal();
    }
  };

  const medicineRows = React.useMemo(() => {
    if (!medicineItems?.length) return [];

    if (modalMode === DETAIL) {
      return getMedicineRowsForDetailMode(
        medicineItems,
        toggleMedicineSelection
      );
    } else {
      return getMedicineRowsForCreateMode(medicineItems, removeMedicine);
    }
  }, [medicineItems, modalMode, removeMedicine, toggleMedicineSelection]);

  const tableHeaders = React.useMemo(() => {
    return getPackageTableHeaders(modalMode);
  }, [modalMode]);

  return (
    <>
      <PackageTypeButtons
        activePackageType={activePackageType}
        onSelectType={handlePackageTypeSelect}
        disabled={false}
      />
      <Modal open={isModalOpen} onClose={handleCloseModal} closeAfterTransition>
        <Box sx={modalStyle}>
          <PackageModalHeader onClose={handleCloseModal} />
          <div className="flex flex-grow min-h-0">
            <PackageList />
            <div className="w-3/4 flex flex-col overflow-auto">
              {modalMode === CREATE || modalMode === ADD ? (
                <CreatePackageForm
                  tableHeaders={tableHeaders}
                  medicineRows={medicineRows}
                />
              ) : modalMode === DETAIL ? (
                <DetailPackageView
                  tableHeaders={tableHeaders}
                  medicineRows={medicineRows}
                />
              ) : (
                <EmptyView packages={packages} />
              )}
            </div>
          </div>

          <PackageModalFooter />
        </Box>
      </Modal>

      <UnsavedChangesModal />
      <DeleteConfirmationModal />
    </>
  );
};

export default PackageSelector;
