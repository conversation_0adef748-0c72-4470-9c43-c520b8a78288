import { FC } from 'react';

import { FaMinus } from 'react-icons/fa6';
import { MdKeyboardArrowDown, MdKeyboardArrowUp } from 'react-icons/md';

import colors from '@/utils/colors';
import { archivo } from '@/utils/fonts';

import { VitalAnalyticsValue } from '@/helpers/vitals';

import { cn } from './utils';

export type UserStatCardInfo = VitalAnalyticsValue;

/**
 * @deprecated This type is deprecated and will be removed in future versions.
 * Please use the vital card component instead.
 */
export interface UserStatCardProps {
  className?: string;
  data?: string | number;
  unit?: string;
  topic?: string;
  info?: UserStatCardInfo;
  size?: 'xs' | 'base';
}

/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the vital card component instead.
 */
const UserStatCard: FC<UserStatCardProps> = ({
  className,
  data,
  unit,
  topic,
  info,
}) => {
  const trendIcon = {
    inc: <MdKeyboardArrowUp className="text-xl" />,
    dec: <MdKeyboardArrowDown className="text-xl" />,
    flat: <FaMinus className="text-xs" />,
  };

  const trendColor = {
    inc: colors.common.aquaGreen,
    dec: colors.common.redRose,
    flat: colors.common.mistGray,
  };

  const trend = info?.type === 'trend' ? info.trend : undefined;
  const bgColor = trend ? trendColor[trend] : colors.common.mistGray;

  return (
    <div
      style={{
        backgroundImage: `linear-gradient(to bottom, white calc(100% - 1.5rem), ${bgColor} calc(100% - 1.5rem), ${bgColor} 100%)`,
      }}
      className={cn(
        `rounded-lg overflow-hidden shadow-custom-xs border border-[#DAE1E7]
        flex flex-col max-w-30 min-w-12 h-full`,
        archivo.className,
        className
      )}
    >
      <div className="flex flex-col justify-between px-2 pt-1 flex-grow">
        <div className="text-[10px bg-red-100] xl:text-[11px] 2xl:text-[12px] font-medium truncate">
          {topic}
        </div>
        <div className="flex items-baseline space-x-1">
          <span className="text-[15px] xl:text-[18px] 2xl:text-[22px] font-light">
            {data || '--'}
          </span>
          {unit && (
            <span title={unit} className="text-sm font-light truncate">
              {unit}
            </span>
          )}
        </div>
      </div>

      <div className="flex items-center  justify-between px-2 py-1 mt-auto min-h-6  text-black">
        <div className="flex items-center gap-1">
          {trend && (
            <div className="w-4 h-4 flex items-center justify-center rounded-full bg-opacity-80">
              {trendIcon[trend]}
            </div>
          )}
        </div>
        <div className="text-[8px] xl:text-[9px] 2xl:text-[11px] truncate">
          {info?.value ? `${info.value}` : '--'}
        </div>
      </div>
    </div>
  );
};

export default UserStatCard;
