import { useMemo } from 'react';

import { styled } from '@mui/material/styles';
import Typography from '@mui/material/Typography';

import { DiagnosisRecord } from '@/store/extraNoteDiagnosisStore';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

export const StyledTypography = styled(Typography)(({ theme }) => ({
  fontFamily: 'Inter',
  fontWeight: 400,
  fontStyle: 'italic',
  fontSize: 12,
  lineHeight: 1.5,
  letterSpacing: '-0.022em',
  verticalAlign: 'middle',
  color: theme.palette.text.secondary,
}));

export interface GroupedRecords {
  [date: string]: DiagnosisRecord[];
}
export interface UseGroupedRecordsProps {
  records?: { records?: DiagnosisRecord[] } | null;
  title: string;
}
export function groupByFormattedDate(
  records: DiagnosisRecord[]
): GroupedRecords {
  const grouped: GroupedRecords = {};
  records.forEach((record) => {
    const date = formatDate(record.timestamp, DateFormats.READABLE_DATE);
    if (!grouped[date]) {
      grouped[date] = [];
    }
    grouped[date].push(record);
  });
  return grouped;
}

const isHtmlContentEmpty = (content: string) =>
  !content || content.trim() === '';

export function useGroupedRecords({ records, title }: UseGroupedRecordsProps) {
  return useMemo(() => {
    const matchingRecords = records?.records
      ?.filter(
        (record) =>
          record?.field?.toLowerCase() === title.toLowerCase() &&
          !isHtmlContentEmpty(record.content)
      )
      .sort((recordA, recordB) => {
        const dateA = new Date(recordA.timestamp).toISOString().split('T')[0];
        const dateB = new Date(recordB.timestamp).toISOString().split('T')[0];
        return dateA.localeCompare(dateB);
      });

    return groupByFormattedDate(matchingRecords ?? []);
  }, [records?.records, title]);
}
