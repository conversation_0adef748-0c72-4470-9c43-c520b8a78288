import React, { FC, memo } from 'react';

import colors from '@/utils/colors';

import Header from './app-bar';
import Sidebar from './side-bar';

import { SidebarItem } from './side-bar/types';

type Props = {
  children: React.ReactNode;
  navItem?: SidebarItem[];
  highlightColor?: string;
  renderBottom?: () => React.ReactNode;
};

const AppLayout: FC<Props> = ({
  children,
  navItem,
  highlightColor,
  renderBottom,
}) => {
  return (
    <div
      className="h-screen max-h-screen flex flex-col overflow-hidden"
      style={{ backgroundColor: colors.common.paleBlue }}
    >
      <Header />
      <div className="p-base flex gap-base h-full overflow-hidden">
        <Sidebar
          items={navItem}
          highlightColor={highlightColor}
          renderBottom={renderBottom}
        />
        <main className="flex-1 flex w-full h-full" id="content">
          {children}
        </main>
      </div>
    </div>
  );
};

export default memo(AppLayout);
