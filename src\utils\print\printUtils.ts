export interface PrintOptions {
  title?: string;
  styles?: string;
  onAfterPrint?: () => void;
  onError?: (error: Error) => void;
}

export const printHtmlContent = (
  content: string,
  options: PrintOptions = {}
): void => {
  // Create a hidden iframe for printing
  const iframe = document.createElement('iframe');
  iframe.style.position = 'fixed';
  iframe.style.right = '0';
  iframe.style.bottom = '0';
  iframe.style.width = '0';
  iframe.style.height = '0';
  iframe.style.border = '0';
  
  // Add iframe to document
  document.body.appendChild(iframe);
  
  // Get the iframe's document
  const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
  if (!iframeDoc) {
    options.onError?.(new Error('Failed to create print iframe'));
    return;
  }
  
  // Write the HTML content to the iframe
  iframeDoc.open();
  iframeDoc.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${options.title || 'Document'}</title>
      <style>
        ${options.styles || ''}
        @page { 
          size: A4;
          margin: 0;
        }
        body { 
          margin: 0;
          padding: 15px;
        }
        @media print {
          body { 
            padding: 0;
          }
        }
      </style>
    </head>
    <body>
      ${content}
      <script>
        // Auto-print when content loads
        window.onload = function() {
          setTimeout(() => {
            window.print();
            // Close the iframe after a delay to ensure printing starts
            setTimeout(() => {
              window.parent.postMessage('removePrintIframe', '*');
            }, 1000);
          }, 100);
        };
      </script>
    </body>
    </html>
  `);
  iframeDoc.close();
  
  // Listen for message to remove the iframe
  const messageHandler = (event: MessageEvent) => {
    if (event.data === 'removePrintIframe' && iframe.parentNode) {
      document.body.removeChild(iframe);
      window.removeEventListener('message', messageHandler);
      options.onAfterPrint?.();
    }
  };
  
  window.addEventListener('message', messageHandler);
  
  // Fallback: remove iframe if it's still there after a while
  setTimeout(() => {
    if (iframe.parentNode) {
      document.body.removeChild(iframe);
      window.removeEventListener('message', messageHandler);
    }
  }, 30000); // 30 seconds timeout
};

export const printFromElement = (
  elementId: string,
  options: PrintOptions = {}
): void => {
  const element = document.getElementById(elementId);
  if (!element) {
    options.onError?.(new Error(`Element with id '${elementId}' not found`));
    return;
  }
  
  const content = element.outerHTML;
  printHtmlContent(content, options);
};
