import { BaseOption } from '@/types';

export enum LifestyleFilter {
  ONE_MONTH = 'ONE_MONTH',
  SEVEN_DAYS = 'SEVEN_DAYS',
  FIFTEEN_DAYS = 'FIFTEEN_DAYS',
}

export const lifestyleFilterOptions: BaseOption[] = [
  {
    value: LifestyleFilter.ONE_MONTH,
    label: 'Last 1 Month',
  },
  {
    value: LifestyleFilter.SEVEN_DAYS,
    label: 'Last 07 Days',
  },
  {
    value: LifestyleFilter.FIFTEEN_DAYS,
    label: 'Last 15 Days',
  },
];
