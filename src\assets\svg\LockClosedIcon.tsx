import * as React from 'react';
import { SVGProps } from 'react';

const LockClosedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="14"
    viewBox="0 0 12 14"
    fill="none"
  >
    <path
      d="M3.08333 6.41663V4.08329C3.08333 3.30974 3.39062 2.56788 3.9376 2.0209C4.48459 1.47392 5.22645 1.16663 6 1.16663C6.77355 1.16663 7.51541 1.47392 8.06239 2.0209C8.60938 2.56788 8.91667 3.30974 8.91667 4.08329V6.41663M1.91667 6.41663H10.0833C10.7277 6.41663 11.25 6.93896 11.25 7.58329V11.6666C11.25 12.311 10.7277 12.8333 10.0833 12.8333H1.91667C1.27233 12.8333 0.75 12.311 0.75 11.6666V7.58329C0.75 6.93896 1.27233 6.41663 1.91667 6.41663Z"
      stroke="#8E8E93"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export default LockClosedIcon;
