import React from 'react';

interface PreviewButtonProps {
  disabled: boolean;
  onClick: () => void;
}

const PreviewButton: React.FC<PreviewButtonProps> = ({ disabled, onClick }) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`px-10 py-1 font-bold text-black bg-white border border-black rounded hover:bg-gray-100 disabled:opacity-50 font-archivo`}
    >
      Print
    </button>
  );
};

export default PreviewButton;
