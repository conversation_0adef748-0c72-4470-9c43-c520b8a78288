import { TextField, InputAdornment } from '@mui/material';
import { ChevronDown } from 'lucide-react';

interface CustomTextFieldProps {
  value: string;
  onClick: () => void;
  readOnly?: boolean;
  endAdornmentIcon?: React.ReactNode;
  placeholder?: string;
  color?: string;
  inputProps?: React.ComponentProps<typeof InputAdornment>;
  error?: boolean;
  helperText?: string;
  options?: { code: string; label: string }[];
  multiline?: boolean;
  minRows?: number;
  maxRows?: number;
  hideEndAdornmentIcon?: boolean;
}

const CustomTextField: React.FC<CustomTextFieldProps> = ({
  value,
  onClick,
  readOnly = true,
  endAdornmentIcon,
  placeholder = 'Instructions',
  color = 'inherit',
  inputProps,
  error = false,
  helperText,
  multiline = false,
  maxRows = 6,
  hideEndAdornmentIcon = false,
}) => (
  <TextField
    fullWidth
    variant="standard"
    value={value || ''}
    onClick={onClick}
    placeholder={placeholder}
    multiline={multiline}
    minRows={1}
    maxRows={multiline ? maxRows : undefined}
    InputProps={{
      readOnly,
      disableUnderline: true,
      endAdornment: hideEndAdornmentIcon ? null : (
        <InputAdornment position="end" {...inputProps}>
          {endAdornmentIcon || <ChevronDown size={16} />}
        </InputAdornment>
      ),
      sx: {
        cursor: 'pointer',
        px: '10px',
        fontSize: '14px',
        minHeight: multiline ? '32px' : '40px',
        alignItems: 'center',
      },
    }}
    sx={{
      '& .MuiInputBase-root': {
        paddingTop: 0,
        paddingBottom: 0,
      },
      '& textarea': {
        lineHeight: '1.43',
        padding: 0,
        cursor: 'pointer',
        color: value ? color : 'gray',
        overflow: 'hidden',
      },
    }}
    error={error}
    helperText={helperText}
  />
);

export default CustomTextField;
