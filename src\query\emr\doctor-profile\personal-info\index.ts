import { getActiveAccount } from '@core/lib/auth/services';

import { api, arcaAxios } from '@/core/lib/interceptor';
import {
  DoctorInfo,
  ProfilePictureResponse,
} from '@/types/emr/doctor-profile/personal-info';

export interface DoctorProfileResponse {
  data: {
    permissionKeys?: string[];
    // Add other profile fields as needed
    [key: string]: any;
  };
  permissionKeys?: string[]; // To match the response structure
}

export interface UserProfile {
  email: string;
  id: string;
  isActive: boolean;
  name: string;
  organizationId: string;
  organizationName: string;
  permissionKeys: string[];
}

export const getDoctorProfile = async (): Promise<{ data: any[] }> => {
  try {
    const account = getActiveAccount();

    if (!account) {
      console.error('No active account found');
      throw new Error('No active account found');
    }

    const response = await arcaAxios.get<any>(
      `/user?email=${account.username}`
    );

    // Handle both array and object response formats
    if (Array.isArray(response.data)) {
      // Old format: Array of user objects
      return { data: response.data };
    } else if (response.data) {
      // New format: Single user object with permissionKeys
      return {
        data: [
          {
            ...response.data,
            // Ensure backward compatibility with existing code
            permissions: response.data.permissionKeys || [],
          },
        ],
      };
    }

    // If we get here, the response format is unexpected
    console.error('Unexpected response format:', response);
    throw new Error('Unexpected response format from server');
  } catch (error) {
    console.error('Error fetching doctor profile:', error);
    throw error;
  }
};

export const createDoctorProfileInfo = async (data: Partial<DoctorInfo>) => {
  return await arcaAxios.post('/doctor', data);
};

export const getDoctorProfileInfo = async (id: string) => {
  return await arcaAxios.get(`/doctor?id=${id}`);
};

export const getDoctorProfileByEmail = async (email: string) => {
  return await arcaAxios.get(`/doctor?email=${email}`);
};

export const updateDoctorProfileInfo = async (
  id: string,
  data: Partial<DoctorInfo>
) => {
  return await arcaAxios.patch(`/doctor?id=${id}`, data);
};

export const uploadDocument = async (data: FormData) => {
  return await arcaAxios.post('/user/document/upload', data);
};

export const uploadProfilePicture = async (
  data: FormData
): Promise<ProfilePictureResponse> => {
  const response = await arcaAxios.post('/doctor/profile-picture/upload', data);
  return response.data;
};

export const getProfilePicture = async (
  doctorId: string
): Promise<ProfilePictureResponse> => {
  const response = await api.get('doctor/v0.1/doctor/profile-picture/url', {
    params: { doctorId },
  });
  return response.data;
};

export const deleteProfilePicture = async (doctorId: string): Promise<void> => {
  return await api.delete('doctor/v0.1/doctor/profile-picture/url', {
    params: { doctorId },
  });
};
