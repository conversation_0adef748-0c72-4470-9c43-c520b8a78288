export const genderOptions = [
  { label: 'Male', value: 'Male' },
  { label: 'Female', value: 'Female' },
  { label: 'Other', value: 'Other' },
];

export const maritalStatusOptions = [
  { label: 'Single', value: 'Single' },
  { label: 'Married', value: 'Married' },
  { label: 'Separated', value: 'Separated' },
  { label: 'Widowed', value: 'Widowed' },
  { label: 'Divorced', value: 'Divorced' },
];

export const proofOptions = [
  { label: 'Aadhar card', value: 'Aadhar card' },
  { label: 'Pan card', value: 'Pan card' },
  { label: 'Passport', value: 'Passport' },
];

export const insuranceProvidersOptions = [
  {
    label: 'Star Health and Allied Insurance',
    value: 'Star Health and Allied Insurance',
  },
  {
    label: 'Care Health Insurance (formerly Religare)',
    value: 'Care Health Insurance (formerly Religare)',
  },
  {
    label: 'Niva Bupa Health Insurance',
    value: 'Niva Bupa Health Insurance',
  },
  {
    label: 'HDFC ERGO Health Insurance',
    value: 'HDFC ERGO Health Insurance',
  },
  {
    label: 'ICICI Lombard Health Insurance',
    value: 'ICICI Lombard Health Insurance',
  },
  {
    label: 'Aditya Birla Health Insurance',
    value: 'Aditya Birla Health Insurance',
  },
  {
    label: 'ManipalCigna Health Insurance',
    value: 'ManipalCigna Health Insurance',
  },
  {
    label: 'TATA AIG Health Insurance',
    value: 'TATA AIG Health Insurance',
  },
  {
    label: 'SBI Health Insurance',
    value: 'SBI Health Insurance',
  },
  {
    label: 'Reliance Health Insurance',
    value: 'Reliance Health Insurance',
  },
];
