import React, { Dispatch, FC, memo, SetStateAction, useCallback } from 'react';

import {
  Control,
  FieldArrayWithId,
  Path,
  UseFormGetValues,
  UseFormRegister,
  useWatch,
} from 'react-hook-form';

import { Stack } from '@mui/material';
import { isEqual } from 'lodash';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import {
  getMinMaxDate,
  getNestedValue,
} from '@/utils/emr/doctor-profile/personal-info';
import {
  allowAlphanumericInput,
  enforceAlphabeticInputWithSpace,
  preventNonAlphabeticInput,
} from '@/utils/validation';

import PencilIcon from '@/assets/svg/PencilIcon';

import DatePicker from '@/core/components/date-picker';
import {
  defaultCertification,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import AutoResizeTextArea from '../shared/AutoResizeTextArea';
import ControlledTextField from '../shared/ControlledTextField';
import MobileAddDeleteButtons from '../shared/MobileAddDeleteButtons';

import { FormData } from '.';

type Props = {
  fields: FieldArrayWithId<FormData, 'certifications', 'id'>[];
  control: Control<FormData>;
  register: UseFormRegister<FormData>;
  editableField: Set<string>;
  getValues: UseFormGetValues<FormData>;
  isSubmitted: boolean;
  setEditableField: Dispatch<SetStateAction<Set<string>>>;
  handleOnDelete: (_itemToDelete: ItemToDelete) => void;
};

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const CertificationMob: FC<Props> = ({
  fields,
  editableField,
  getValues,
  isSubmitted,
  setEditableField,
  control,
  handleOnDelete,
}) => {
  const { doctorProfile } = useDoctorStore();
  const watchedCertifications = useWatch({
    control,
    name: 'certifications',
  });

  const handleEditClick = useCallback(
    (fieldName: Path<FormData>) => {
      setEditableField((prev) => new Set(prev.add(fieldName)));
    },
    [setEditableField]
  );

  const isFieldDisabled = useCallback(
    (fieldName: Path<FormData>) => {
      if (editableField.has(fieldName)) {
        return false;
      }

      const formValues = getValues();
      const fieldValue = formValues[fieldName as keyof FormData];
      const doctorFieldValue = getNestedValue(
        doctorProfile?.professionalDetails,
        fieldName
      );

      return (
        !!doctorFieldValue ||
        (isSubmitted && !!fieldValue && fieldValue === doctorFieldValue)
      );
    },
    [editableField, getValues, doctorProfile?.professionalDetails, isSubmitted]
  );

  const renderEditIcon = useCallback(
    (fieldName: Path<FormData>) => {
      return isFieldDisabled(fieldName) ? (
        <button type="button" onClick={() => handleEditClick(fieldName)}>
          <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
        </button>
      ) : null;
    },
    [isFieldDisabled, handleEditClick]
  );

  return (
    <>
      {fields?.map((field, index) => (
        <Stack key={field.id} spacing={1}>
          <AutoResizeTextArea
            label="Name"
            name={`certifications.${index}.name`}
            control={control}
            endDecoration={renderEditIcon(`certifications.${index}.name`)}
            disabled={isFieldDisabled(`certifications.${index}.name`)}
            placeholder=""
            onKeyDown={preventNonAlphabeticInput}
            className="w-full"
            onInput={enforceAlphabeticInputWithSpace}
            autoFocus={index === fields.length - 1}
          />
          <ControlledTextField
            label="Reg No"
            name={`certifications.${index}.regNumber`}
            control={control}
            endDecoration={renderEditIcon(`certifications.${index}.regNumber`)}
            disabled={isFieldDisabled(`certifications.${index}.regNumber`)}
            placeholder=""
            onKeyDown={allowAlphanumericInput}
            className="w-full"
          />
          <DatePicker
            label="Valid From"
            format={DATE_DD_MM_YYYY_SLASH}
            name={`certifications.${index}.validFrom`}
            control={control}
            disableInput={isFieldDisabled(`certifications.${index}.validFrom`)}
            renderEditIcon={() =>
              renderEditIcon(`certifications.${index}.validFrom`)
            }
            maxDate={getMinMaxDate(watchedCertifications[index]?.validTo)}
          />
          <DatePicker
            label="Valid To"
            format={DATE_DD_MM_YYYY_SLASH}
            name={`certifications.${index}.validTo`}
            control={control}
            disableInput={isFieldDisabled(`certifications.${index}.validTo`)}
            renderEditIcon={() =>
              renderEditIcon(`certifications.${index}.validTo`)
            }
            minDate={getMinMaxDate(watchedCertifications[index]?.validFrom)}
          />
          <DatePicker
            label="Date of Updation"
            format={DATE_DD_MM_YYYY_SLASH}
            name={`certifications.${index}.dateOfUpdation`}
            control={control}
            disableInput={isFieldDisabled(
              `certifications.${index}.dateOfUpdation`
            )}
            renderEditIcon={() =>
              renderEditIcon(`certifications.${index}.dateOfUpdation`)
            }
          />
          <ControlledTextField
            label="Status"
            name={`certifications.${index}.status`}
            control={control}
            endDecoration={renderEditIcon(`certifications.${index}.status`)}
            disabled={isFieldDisabled(`certifications.${index}.status`)}
            placeholder=""
            className="w-full"
            onKeyDown={preventNonAlphabeticInput}
            onInput={enforceAlphabeticInputWithSpace}
          />
          <MobileAddDeleteButtons
            onDelete={
              isEqual(watchedCertifications[index], defaultCertification)
                ? undefined
                : () => handleOnDelete({ index, uuId: field?.uuId })
            }
          />
        </Stack>
      ))}
    </>
  );
};

export default memo(CertificationMob);
