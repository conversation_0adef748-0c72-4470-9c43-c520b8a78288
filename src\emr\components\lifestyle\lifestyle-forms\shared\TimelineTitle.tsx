import React, { FC, memo, useCallback } from 'react';

import { LuCalendar } from 'react-icons/lu';

import { DateFormats } from '@/utils/dateUtils/dateFormats';

import DateRangePicker from '@/core/components/date-range-picker';
import { DateRange } from '@/core/components/date-range-picker/types';

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

type Props = {
  title: string;
  className?: string;
  dateRange?: DateRange;
  onDateChange?: (range: DateRange) => void;
  setIsCalendarOpen?: (isOpen: boolean) => void;
  isCalendarOpen?: boolean;
  maxDate?: Date;
};

const TimelineTitle: FC<Props> = ({
  title,
  className,
  dateRange,
  onDateChange,
  setIsCalendarOpen,
  isCalendarOpen,
  maxDate,
}) => {
  const hasSelectedDates = Boolean(dateRange?.from && dateRange?.to);

  const handleClearDates = useCallback(() => {
    if (onDateChange) {
      onDateChange({ from: undefined, to: undefined });
    }
    setIsCalendarOpen?.(false);
  }, [onDateChange, setIsCalendarOpen]);

  const renderFooter = useCallback(() => {
    if (hasSelectedDates) {
      return (
        <div className="w-full py-2 px-4 flex justify-end">
          <button
            className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded"
            onClick={handleClearDates}
          >
            Clear
          </button>
        </div>
      );
    }
    return null;
  }, [hasSelectedDates, handleClearDates]);

  return (
    <div className="flex items-center justify-between  min-h-8 px-2">
      <span className={`font-semibold text-[19px] ${className}`}>{title}</span>
      <div>
        <DateRangePicker
          value={dateRange}
          onChange={onDateChange}
          setIsCalendarOpen={setIsCalendarOpen}
          calendarIcon={<LuCalendar size={17} color="black" />}
          format={DATE_DD_MM_YYYY_SLASH}
          separator="to"
          inputWrapperClassName="min-w-37"
          inputProps={{
            iconClassName: '!right-0 bg-white top-0',
            inputClassName: 'h-full input:bg-transparent m-0',
            fieldClassName: '!text-[10px] rounded-none m-0 !px-1 h-[10px]',
            style: { outline: 'none', border: 'none' },
          }}
          renderFooter={renderFooter}
          isCalendarOpen={isCalendarOpen}
          maxDate={maxDate}
        />
      </div>
    </div>
  );
};

export default memo(TimelineTitle);
