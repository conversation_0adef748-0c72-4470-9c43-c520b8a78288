import React, { FC } from 'react';

import { Control, Controller } from 'react-hook-form';

import Checkbox from '@mui/material/Checkbox';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';

import {
  fluencyLevels,
  languages,
} from '@/constants/emr/doctor-profile/personal-info';

import { cellStyles, checkboxStyles, headerCellStyles } from '../Components';

import { FormData } from '.';

type Props = {
  control: Control<FormData>;
};

const LanguagesKnownTable: FC<Props> = ({ control }) => {
  return (
    <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell sx={headerCellStyles}>Languages</TableCell>
            {fluencyLevels?.map((level) => (
              <TableCell key={level} sx={headerCellStyles}>
                {level.charAt(0).toUpperCase() + level.slice(1)}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {languages.map((language) => (
            <TableRow key={language}>
              <TableCell sx={cellStyles}>{language}</TableCell>
              {fluencyLevels.map((level) => (
                <TableCell key={level} sx={cellStyles}>
                  <Controller
                    name={`languages.${language}.${level}`}
                    control={control}
                    render={({ field }) => (
                      <Checkbox
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                        sx={checkboxStyles}
                      />
                    )}
                  />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default LanguagesKnownTable;
