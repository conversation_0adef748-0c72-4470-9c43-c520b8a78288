import React from 'react';

import {
  Controller,
  useFieldArray,
  useWatch,
  Control,
  useFormContext,
} from 'react-hook-form';

import {
  Button,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Chip,
  Box,
} from '@mui/material';
import { IoIosArrowDown } from 'react-icons/io';
import { MdOutlineAdd, MdClose, MdEdit } from 'react-icons/md';

import { LifestyleMode } from '@/constants/emr/lifestyle';

import DeleteModal from '@/core/components/delete-modal';
import { accentColors } from '@/core/theme/colors';
import { lightColors } from '@/core/theme/colors/light-colors';
import { TableField as TableFieldType } from '@/types/emr/lifestyle/questionnaire';

import { FieldComponentProps } from './types';

const borderlessTheme = (baseTheme: any) => ({
  ...baseTheme,
  components: {
    ...baseTheme.components,
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-notchedOutline': {
            border: 'none !important',
            borderColor: 'transparent !important',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            border: 'none !important',
            borderColor: 'transparent !important',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            border: 'none !important',
            borderColor: 'transparent !important',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              border: 'none !important',
              borderColor: 'transparent !important',
            },
            '&:hover fieldset': {
              border: 'none !important',
              borderColor: 'transparent !important',
            },
            '&.Mui-focused fieldset': {
              border: 'none !important',
              borderColor: 'transparent !important',
            },
          },
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-notchedOutline': {
            border: 'none !important',
            borderColor: 'transparent !important',
          },
        },
      },
    },
  },
});

interface ConditionalSelectProps {
  controllerField: any;
  header: any;
  name: string;
  rowIndex: number;
  control: Control<any>;
  readonly?: boolean;
  mode?: string;
}

const DaysSelect: React.FC<ConditionalSelectProps> = ({
  controllerField,
  header,
  name,
  rowIndex,
  control,
  readonly,
  mode,
}) => {
  const [selectOpen, setSelectOpen] = React.useState(false);
  const { setValue } = useFormContext();

  const dependentValue = useWatch({
    control,
    name: header.dependsOn
      ? `${name}.value.${rowIndex}.${header.dependsOn}`
      : `${name}.value.${rowIndex}.nonexistent`,
  });

  const allDays = React.useMemo(
    () => [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    []
  );
  const shortDays = React.useMemo(
    () => ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    []
  );

  const handleRemoveDay = React.useCallback(
    (dayToRemove: string) => {
      const currentSelected = Array.isArray(controllerField.value)
        ? controllerField.value
        : [];
      const updatedDays = currentSelected.filter(
        (day: string) => day !== dayToRemove
      );

      if (updatedDays.length !== currentSelected.length) {
        controllerField.onChange(updatedDays);
      }
    },
    [controllerField]
  );

  const getMaxDaysFromFrequency = (frequency: string): number => {
    switch (frequency) {
      case 'Daily':
        return 7;
      case 'Two days a week':
        return 2;
      case 'Three days a week':
        return 3;

      case 'Four days a week':
        return 4;
      case 'Five days a week':
        return 5;
      case 'Six days a week':
        return 6;
      case 'Weekly':
        return 1;
      default:
        return 7;
    }
  };

  React.useEffect(() => {
    const currentSelected = Array.isArray(controllerField.value)
      ? controllerField.value
      : [];

    if (dependentValue === 'Daily') {
      controllerField.onChange(allDays);
    } else if (currentSelected.length === 7 && dependentValue !== 'Daily') {
      controllerField.onChange([]);
    }
  }, [dependentValue, controllerField, allDays]);

  if (dependentValue === 'Daily') {
    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, padding: '4px' }}>
        {shortDays.map((day) => (
          <Chip
            key={day}
            label={day}
            size="small"
            sx={{
              fontSize: '10px',
              height: '20px',
              backgroundColor:
                mode === LifestyleMode.VIEW ? '#F0F4F7' : '#E6F6FF',
              color: mode === LifestyleMode.VIEW ? '#000000' : '#02537D',
              paddingX: '4px',
            }}
          />
        ))}
      </Box>
    );
  }

  const selectedDays = Array.isArray(controllerField.value)
    ? controllerField.value
    : [];
  const maxAllowedDays = getMaxDaysFromFrequency(dependentValue || '');
  const canAddMoreDays = selectedDays.length < maxAllowedDays;
  const availableDays = allDays.filter((day) => !selectedDays.includes(day));

  const handleChange = (event: any) => {
    const value = event.target.value;
    const newSelectedDays =
      typeof value === 'string' ? value.split(',') : value;
    if (newSelectedDays.length <= maxAllowedDays) {
      controllerField.onChange(newSelectedDays);
    }
  };

  return (
    <div style={{ position: 'relative' }}>
      {selectedDays.length > 0 && (
        <Box
          sx={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: 0.5,
            padding: '2px',
            margin: 0,
            paddingRight: mode !== LifestyleMode.VIEW ? '24px' : '2px',
          }}
        >
          {selectedDays.map((day: string) => (
            <Chip
              key={day}
              label={shortDays[allDays.indexOf(day)] || day}
              size="small"
              className={
                mode === LifestyleMode.VIEW
                  ? 'view-mode-chip'
                  : 'edit-mode-chip'
              }
              onDelete={
                mode !== LifestyleMode.VIEW && !readonly
                  ? () => handleRemoveDay(day)
                  : undefined
              }
              deleteIcon={<MdClose />}
              sx={{
                fontSize: '10px',
                height: '20px',
                paddingX: '4px',
                margin: 0,
                '& .MuiChip-deleteIcon': {
                  fontSize: '11px',
                  color: '#02537D',
                  backgroundColor: 'transparent',
                  borderRadius: 0,
                  cursor: 'pointer',
                  '&:hover': {
                    color: '#FF0000',
                    backgroundColor: 'transparent',
                  },
                },
              }}
            />
          ))}
        </Box>
      )}

      {mode !== LifestyleMode.VIEW &&
        canAddMoreDays &&
        (selectedDays.length === 0 || selectOpen) && (
          <Select
            {...controllerField}
            value={selectedDays}
            onChange={handleChange}
            multiple
            size="small"
            fullWidth
            variant="outlined"
            displayEmpty={mode !== LifestyleMode.VIEW}
            disabled={readonly || !dependentValue || dependentValue === 'Daily'}
            open={selectOpen}
            onOpen={() => setSelectOpen(true)}
            onClose={() => setSelectOpen(false)}
            IconComponent={() => null}
            renderValue={(selected: string[]) => {
              if (!selected || selected.length === 0) {
                if (mode !== LifestyleMode.VIEW) {
                  return (
                    <span style={{ color: '#9ca3af' }}>
                      {!dependentValue ? 'Add Days' : 'Add Days'}
                    </span>
                  );
                }
                return '';
              }

              return '';
            }}
            sx={{
              width: '100%',
              minWidth: '200px',
              paddingRight: mode !== LifestyleMode.VIEW ? '24px' : '0px',
              '&&& .MuiInputBase-root': {
                backgroundColor: 'transparent !important',
                color:
                  mode === LifestyleMode.VIEW
                    ? '#000000 !important'
                    : 'inherit',
                border: 'none !important',
                outline: 'none !important',
                borderColor: 'transparent !important',
                boxShadow: 'none !important',
                borderWidth: '0px !important',
                borderStyle: 'none !important',
              },
              '&&& .MuiOutlinedInput-root': {
                backgroundColor: 'transparent !important',
                border: 'none !important',
                outline: 'none !important',
                borderColor: 'transparent !important',
                '& fieldset': {
                  border: 'none !important',
                  outline: 'none !important',
                  borderColor: 'transparent !important',
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none !important',
                  outline: 'none !important',
                  display: 'none !important',
                  borderColor: 'transparent !important',
                },
              },
              '& .MuiSelect-select': {
                backgroundColor: 'transparent !important',
                color:
                  mode === LifestyleMode.VIEW
                    ? '#000000 !important'
                    : 'inherit',
              },
            }}
          >
            {availableDays.map((day) => (
              <MenuItem key={day} value={day}>
                {day}
              </MenuItem>
            ))}
          </Select>
        )}

      {mode !== LifestyleMode.VIEW &&
        selectedDays.length > 0 &&
        !selectOpen &&
        canAddMoreDays && (
          <div
            style={{
              minHeight: '20px',
              cursor:
                readonly || !dependentValue || dependentValue === 'Daily'
                  ? 'default'
                  : 'pointer',
              opacity:
                readonly || !dependentValue || dependentValue === 'Daily'
                  ? 0.5
                  : 1,
            }}
            onClick={() => {
              if (!readonly && dependentValue && dependentValue !== 'Daily') {
                setSelectOpen(true);
              }
            }}
          />
        )}

      {mode !== LifestyleMode.VIEW && canAddMoreDays && (
        <div
          style={{
            position: 'absolute',
            right: '4px',
            top: selectedDays.length > 0 ? '12px' : '8px',
            zIndex: 1,
            pointerEvents:
              readonly || !dependentValue || dependentValue === 'Daily'
                ? 'none'
                : 'auto',
          }}
        >
          <IoIosArrowDown
            className="h-4 w-auto text-[#637D92] cursor-pointer"
            style={{
              opacity:
                readonly || !dependentValue || dependentValue === 'Daily'
                  ? 0.3
                  : 1,
            }}
            onClick={(e) => {
              e.stopPropagation();
              if (!readonly && dependentValue && dependentValue !== 'Daily') {
                setSelectOpen(!selectOpen);
              }
            }}
          />
        </div>
      )}
    </div>
  );
};

const ConditionalSelect: React.FC<ConditionalSelectProps> = ({
  controllerField,
  header,
  name,
  rowIndex,
  control,
  readonly,
  mode,
}) => {
  const [selectOpen, setSelectOpen] = React.useState(false);

  const dependentValue = useWatch({
    control,
    name: header.dependsOn
      ? `${name}.value.${rowIndex}.${header.dependsOn}`
      : `${name}.value.${rowIndex}.nonexistent`,
  });

  const conditionalOptions = React.useMemo(() => {
    if (!header.dependsOn) return [];
    if (typeof header.options === 'object' && !Array.isArray(header.options)) {
      return header.options[dependentValue] || [];
    }
    return [];
  }, [header.dependsOn, header.options, dependentValue]);

  React.useEffect(() => {
    if (
      header.dependsOn &&
      dependentValue &&
      controllerField.value &&
      !conditionalOptions.includes(controllerField.value)
    ) {
      controllerField.onChange('');
    }
  }, [dependentValue, conditionalOptions, controllerField, header.dependsOn]);

  const actualDependentValue = header.dependsOn ? dependentValue : null;

  return (
    <div
      style={{
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        width: '100%',
      }}
    >
      <Select
        {...controllerField}
        value={controllerField.value || ''}
        size="small"
        fullWidth
        variant="outlined"
        displayEmpty={mode !== LifestyleMode.VIEW}
        disabled={
          readonly ||
          (header.id === 'days'
            ? dependentValue === 'Daily' || !dependentValue
            : !actualDependentValue)
        }
        open={selectOpen}
        onOpen={() => setSelectOpen(true)}
        onClose={() => setSelectOpen(false)}
        IconComponent={() => null}
        MenuProps={{
          PaperProps: {
            style: {
              maxHeight: 5 * 48,
              overflowY: 'auto',
            },
          },
        }}
        sx={{
          width: '100%',
          minWidth: '200px',
          '&&& .MuiInputBase-root': {
            backgroundColor: 'transparent !important',
            color:
              mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
            border: 'none !important',
            outline: 'none !important',
            borderColor: 'transparent !important',
            boxShadow: 'none !important',
            borderWidth: '0px !important',
            borderStyle: 'none !important',
            '&:before': {
              border: 'none !important',
              borderBottom: 'none !important',
              display: 'none !important',
            },
            '&:after': {
              border: 'none !important',
              borderBottom: 'none !important',
              display: 'none !important',
            },
            '&:hover': {
              backgroundColor: 'transparent !important',
              border: 'none !important',
              borderColor: 'transparent !important',
              '&:before': {
                border: 'none !important',
                borderBottom: 'none !important',
                display: 'none !important',
              },
            },
            '&.Mui-focused': {
              backgroundColor: 'transparent !important',
              border: 'none !important',
              borderColor: 'transparent !important',
              boxShadow: 'none !important',
              '&:after': {
                border: 'none !important',
                borderBottom: 'none !important',
                display: 'none !important',
              },
            },
            '&.Mui-disabled': {
              backgroundColor: 'transparent !important',
              color:
                mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
              border: 'none !important',
              borderColor: 'transparent !important',
            },
          },
          '&&& .MuiOutlinedInput-root': {
            backgroundColor: 'transparent !important',
            color:
              mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
            border: 'none !important',
            outline: 'none !important',
            borderColor: 'transparent !important',
            borderWidth: '0px !important',
            borderStyle: 'none !important',
            boxShadow: 'none !important',
            '&:hover': {
              backgroundColor: 'transparent !important',
              border: 'none !important',
              borderColor: 'transparent !important',
            },
            '&.Mui-focused': {
              backgroundColor: 'transparent !important',
              border: 'none !important',
              borderColor: 'transparent !important',
            },
            '&.Mui-disabled': {
              backgroundColor: 'transparent !important',
              color:
                mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
              border: 'none !important',
              borderColor: 'transparent !important',
            },
            '& fieldset': {
              border: 'none !important',
              outline: 'none !important',
              borderColor: 'transparent !important',
            },
            '&:hover fieldset': {
              border: 'none !important',
              outline: 'none !important',
              borderColor: 'transparent !important',
            },
            '&.Mui-focused fieldset': {
              border: 'none !important',
              outline: 'none !important',
              borderColor: 'transparent !important',
            },
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none !important',
              outline: 'none !important',
              display: 'none !important',
              borderColor: 'transparent !important',
            },
          },
          '& .MuiSelect-select': {
            backgroundColor: 'transparent !important',
            color:
              mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
            '&:focus': {
              backgroundColor: 'transparent !important',
              color:
                mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
            },
            '&.Mui-disabled': {
              backgroundColor: 'transparent !important',
              color:
                mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
              WebkitTextFillColor:
                mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
            },
          },
        }}
        renderValue={(selected) => {
          if (!selected && mode !== LifestyleMode.VIEW) {
            return (
              <span style={{ color: '#9ca3af' }}>
                {header.id === 'days'
                  ? dependentValue === 'Daily' || !dependentValue
                    ? 'N/A for Daily'
                    : 'Select days'
                  : actualDependentValue
                    ? 'Select'
                    : 'Select'}
              </span>
            );
          }
          return selected || '';
        }}
      >
        {conditionalOptions.map((option: string) => (
          <MenuItem key={option} value={option}>
            {option}
          </MenuItem>
        ))}
      </Select>

      {mode !== LifestyleMode.VIEW && (
        <div
          style={{
            position: 'absolute',
            right: '4px',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 1,
            pointerEvents: readonly || !actualDependentValue ? 'none' : 'auto',
          }}
        >
          <IoIosArrowDown
            className="h-4 w-auto text-[#637D92] cursor-pointer"
            style={{
              opacity: readonly || !actualDependentValue ? 0.3 : 1,
            }}
            onClick={(e) => {
              e.stopPropagation();
              if (!readonly && actualDependentValue) {
                setSelectOpen(!selectOpen);
              }
            }}
          />
        </div>
      )}
    </div>
  );
};

export const TableField: React.FC<
  FieldComponentProps & {
    patientData?: any[];
  }
> = ({ name, field, control, readonly, mode, patientData = [], variant }) => {
  const tableField = field as TableFieldType;
  const [hasInitialized, setHasInitialized] = React.useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = React.useState(false);
  const [rowToDelete, setRowToDelete] = React.useState<{
    index: number;
    rowData: any;
  } | null>(null);
  const [editableFields, setEditableFields] = React.useState<
    Record<string, boolean>
  >({});
  const [selectOpenStates, setSelectOpenStates] = React.useState<
    Record<string, boolean>
  >({});

  const { fields, append, remove } = useFieldArray({
    control,
    name: `${name}.value`,
  });

  const { getValues, watch } = useFormContext();

  const watchedValue = watch(`${name}.value`);

  const appendRef = React.useRef(append);
  appendRef.current = append;

  const toggleFieldEdit = (rowIndex: number, fieldId: string) => {
    const fieldKey = `${rowIndex}-${fieldId}`;
    setEditableFields((prev) => ({
      ...prev,
      [fieldKey]: !prev[fieldKey],
    }));
  };

  const isFieldEditable = (rowIndex: number, fieldId: string) => {
    const fieldKey = `${rowIndex}-${fieldId}`;
    return editableFields[fieldKey] || false;
  };

  const toggleSelectOpen = (rowIndex: number, fieldId: string) => {
    const selectKey = `${rowIndex}-${fieldId}`;
    setSelectOpenStates((prev) => ({
      ...prev,
      [selectKey]: !prev[selectKey],
    }));
  };

  const isSelectOpen = (rowIndex: number, fieldId: string) => {
    const selectKey = `${rowIndex}-${fieldId}`;
    return selectOpenStates[selectKey] || false;
  };

  const setSelectOpen = (rowIndex: number, fieldId: string, open: boolean) => {
    const selectKey = `${rowIndex}-${fieldId}`;
    setSelectOpenStates((prev) => ({
      ...prev,
      [selectKey]: open,
    }));
  };

  React.useEffect(() => {
    if (
      hasInitialized &&
      fields.length === 0 &&
      tableField.defaultRows &&
      tableField.defaultRows.length > 0
    ) {
      const currentFormValues = getValues();
      tableField.defaultRows.forEach((row, index) => {
        console.warn(`Re-adding default row ${index}:`, row);
        appendRef.current({ ...row });
      });
    }
  }, [
    fields.length,
    hasInitialized,
    tableField.defaultRows,
    name,
    getValues,
    watchedValue,
  ]);

  const addRow = () => {
    try {
      const newRow: Record<string, any> = {};
      tableField.headers.forEach((header) => {
        newRow[header.id] = header.id === 'days' ? [] : '';
      });

      append(newRow);

      setTimeout(() => {
        const currentFormValues = getValues();
        const currentFieldValue = watch(`${name}.value`);
      }, 0);
      setTimeout(() => {
        const currentFieldValue = watch(`${name}.value`);
      }, 100);
    } catch (error) {
      console.error('Error during add row:', error);
    }
  };

  const handleDeleteClick = (index: number) => {
    const rowData = fields[index];
    setRowToDelete({ index, rowData });
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (rowToDelete !== null) {
      if (rowToDelete.index >= 0 && rowToDelete.index < fields.length) {
        try {
          remove(rowToDelete.index);
        } catch (error) {
          console.error('Error during deletion:', error);
        }
      } else {
        console.error(
          'Invalid row index for deletion:',
          rowToDelete.index,
          'fields.length:',
          fields.length
        );
      }

      setDeleteModalOpen(false);
      setRowToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setRowToDelete(null);
  };

  const getActivityType = () => {
    if (!rowToDelete?.rowData) return '';

    const rowData = rowToDelete.rowData;

    if (rowData.activity_type) return rowData.activity_type;

    return '';
  };

  const getActivity = () => {
    if (!rowToDelete?.rowData) return '';

    const rowData = rowToDelete.rowData;

    if (rowData.activity) return rowData.activity;

    return '';
  };

  React.useEffect(() => {
    if (!hasInitialized) {
      if (
        fields.length === 0 &&
        tableField.defaultRows &&
        tableField.defaultRows.length > 0
      ) {
        const defaultRowsCopy = [...tableField.defaultRows];
        defaultRowsCopy.forEach((row) => {
          appendRef.current({ ...row });
        });
      }
      setHasInitialized(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once on mount

  return (
    <>
      <style jsx global>{`
        .exercise-patterns-table .MuiInputBase-root,
        .exercise-patterns-table .MuiOutlinedInput-root,
        .exercise-patterns-table .MuiSelect-root,
        .exercise-patterns-table .MuiFormControl-root,
        .exercise-patterns-table .MuiTextField-root {
          border: none !important;
          outline: none !important;
          border-color: transparent !important;
          box-shadow: none !important;
          border-width: 0 !important;
          border-style: none !important;
        }
        .exercise-patterns-table .MuiInputBase-root:hover,
        .exercise-patterns-table .MuiOutlinedInput-root:hover,
        .exercise-patterns-table .MuiSelect-root:hover,
        .exercise-patterns-table .MuiFormControl-root:hover,
        .exercise-patterns-table .MuiTextField-root:hover {
          border: none !important;
          border-color: transparent !important;
          border-width: 0 !important;
          border-style: none !important;
        }
        .exercise-patterns-table .MuiInputBase-root.Mui-focused,
        .exercise-patterns-table .MuiOutlinedInput-root.Mui-focused,
        .exercise-patterns-table .MuiSelect-root.Mui-focused,
        .exercise-patterns-table .MuiFormControl-root.Mui-focused,
        .exercise-patterns-table .MuiTextField-root.Mui-focused {
          border: none !important;
          border-color: transparent !important;
          box-shadow: none !important;
          border-width: 0 !important;
          border-style: none !important;
        }
        .exercise-patterns-table .MuiOutlinedInput-notchedOutline,
        .exercise-patterns-table fieldset,
        .exercise-patterns-table .MuiOutlinedInput-root fieldset,
        .exercise-patterns-table .MuiInputBase-root fieldset {
          border: none !important;
          border-color: transparent !important;
          display: none !important;
          opacity: 0 !important;
          border-width: 0 !important;
          border-style: none !important;
          visibility: hidden !important;
        }
        .exercise-patterns-table
          .MuiInputBase-root:hover
          .MuiOutlinedInput-notchedOutline,
        .exercise-patterns-table
          .MuiInputBase-root.Mui-focused
          .MuiOutlinedInput-notchedOutline,
        .exercise-patterns-table
          .MuiOutlinedInput-root:hover
          .MuiOutlinedInput-notchedOutline,
        .exercise-patterns-table
          .MuiOutlinedInput-root.Mui-focused
          .MuiOutlinedInput-notchedOutline {
          border: none !important;
          border-color: transparent !important;
          display: none !important;
          opacity: 0 !important;
          border-width: 0 !important;
          border-style: none !important;
          visibility: hidden !important;
        }
        .exercise-patterns-table .MuiSelect-select,
        .exercise-patterns-table .MuiInputBase-input {
          border: none !important;
          outline: none !important;
          border-width: 0 !important;
          border-style: none !important;
        }
        /* Additional specific targeting for stubborn borders - only input elements */
        .exercise-patterns-table [class*='MuiOutlinedInput'],
        .exercise-patterns-table [class*='MuiInputBase'],
        .exercise-patterns-table [class*='MuiSelect'] {
          border: none !important;
          border-width: 0 !important;
          border-style: none !important;
          border-color: transparent !important;
        }
        /* Ensure table styling works properly */
        .exercise-patterns-table table {
          border-collapse: collapse !important;
        }
        /* Remove only header top and left borders by default */
        .exercise-patterns-table thead th {
          border-top: none !important;
          border-left: none !important;
          border-right: none !important;
        }
        /* Add column dividers only in non-readonly mode (edit/create modes) */
        .exercise-patterns-table:not(.readonly-mode) thead th {
          border-right: 1px solid #d1d5db !important;
        }
        .exercise-patterns-table:not(.readonly-mode) thead th:last-child {
          border-right: none !important;
        }
        /* Remove the top border of the table that creates sharp edges on header */
        .exercise-patterns-table table {
          border-top: none !important;
        }
        /* Add horizontal borders between rows with higher specificity */
        .exercise-patterns-table table tbody tr {
          border-bottom: 1px solid #d1d5db !important;
        }
        .exercise-patterns-table table tbody tr.border-b {
          border-bottom: 1px solid #d1d5db !important;
        }
        .exercise-patterns-table table tbody td {
          border-right: 1px solid #d1d5db !important;
        }
        .exercise-patterns-table table tbody td:last-child {
          border-right: none !important;
        }
        /* Timeline variant specific styles */
        .exercise-patterns-table.timeline-variant {
          max-width: 100%;
          overflow-x: hidden;
        }
        .exercise-patterns-table.timeline-variant table {
          width: 100% !important;
          max-width: 100% !important;
          table-layout: fixed !important;
        }
        .exercise-patterns-table.timeline-variant .intensity-column {
          width: 60px !important;
          max-width: 60px !important;
          min-width: 60px !important;
        }
        .exercise-patterns-table.timeline-variant .activity-type-column {
          width: 80px !important;
          max-width: 80px !important;
          min-width: 80px !important;
        }
        .exercise-patterns-table.timeline-variant .activity-column {
          width: 120px !important;
          max-width: 120px !important;
          min-width: 120px !important;
        }
        .exercise-patterns-table.timeline-variant .duration-column {
          width: 70px !important;
          max-width: 70px !important;
          min-width: 70px !important;
        }
        .exercise-patterns-table.timeline-variant .frequency-column {
          width: 80px !important;
          max-width: 80px !important;
          min-width: 80px !important;
        }
        .exercise-patterns-table.timeline-variant .days-column {
          width: 140px !important;
          max-width: 140px !important;
          min-width: 90px !important;
        }
        .exercise-patterns-table.timeline-variant td,
        .exercise-patterns-table.timeline-variant th {
          white-space: normal !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
          line-height: 1.3 !important;
          padding: 4px 2px !important;
          vertical-align: top !important;
          text-overflow: unset !important;
          overflow: visible !important;
          max-width: none !important;
        }
        .exercise-patterns-table.timeline-variant .MuiInputBase-root,
        .exercise-patterns-table.timeline-variant .MuiSelect-root {
          min-height: auto !important;
          white-space: normal !important;
          text-overflow: unset !important;
          overflow: visible !important;
        }
        .exercise-patterns-table.timeline-variant .MuiInputBase-input {
          padding: 2px 4px !important;
          white-space: normal !important;
          text-overflow: unset !important;
          overflow: visible !important;
        }
        .exercise-patterns-table.timeline-variant .MuiSelect-select {
          white-space: normal !important;
          text-overflow: unset !important;
          overflow: visible !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
        }
        /* Font styling for readonly timeline variant */
        .exercise-patterns-table.timeline-variant.readonly-mode th,
        .exercise-patterns-table.timeline-variant.readonly-mode td,
        .exercise-patterns-table.timeline-variant.readonly-mode
          .MuiInputBase-input,
        .exercise-patterns-table.timeline-variant.readonly-mode
          .MuiSelect-select {
          font-family: var(--font-archivo), 'Archivo', sans-serif !important;
          font-size: 14px !important;
          font-weight: 400 !important;
        }
        /* Center headers for readonly timeline variant */
        .exercise-patterns-table.timeline-variant.readonly-mode thead th {
          text-align: center !important;
        }
        /* Force day chip colors in view mode */
        .exercise-patterns-table .view-mode-chip.MuiChip-root {
          background-color: #f0f4f7 !important;
          color: #000000 !important;
        }
        .exercise-patterns-table .view-mode-chip.MuiChip-root .MuiChip-label {
          color: #000000 !important;
        }
        .exercise-patterns-table
          .view-mode-chip.MuiChip-root
          .MuiChip-labelSmall {
          color: #000000 !important;
        }
        /* Force day chip colors in non-view mode */
        .exercise-patterns-table .edit-mode-chip.MuiChip-root {
          background-color: #e6f6ff !important;
          color: #02537d !important;
        }
        .exercise-patterns-table .edit-mode-chip.MuiChip-root .MuiChip-label {
          color: #02537d !important;
        }
        .exercise-patterns-table
          .edit-mode-chip.MuiChip-root
          .MuiChip-labelSmall {
          color: #02537d !important;
        }
        /* Ensure days column maintains width during editing */
        .exercise-patterns-table td.w-50,
        .exercise-patterns-table th.w-50 {
          min-width: 200px !important;
          width: 200px !important;
          max-width: 200px !important;
        }
        /* Ensure intensity column maintains reduced width in modal variants */
        .exercise-patterns-table:not(.timeline-variant) td.w-15,
        .exercise-patterns-table:not(.timeline-variant) th.w-15 {
          min-width: 60px !important;
          width: 60px !important;
          max-width: 60px !important;
        }
        /* Additional specificity for intensity columns in all modes */
        .exercise-patterns-table td[class*='w-15'],
        .exercise-patterns-table th[class*='w-15'] {
          min-width: 60px !important;
          width: 60px !important;
          max-width: 60px !important;
        }
        /* Force intensity column width in modal variants with highest specificity */
        .exercise-patterns-table:not(.timeline-variant) table td.w-15,
        .exercise-patterns-table:not(.timeline-variant) table th.w-15 {
          min-width: 60px !important;
          width: 60px !important;
          max-width: 60px !important;
          flex: 0 0 60px !important;
        }
        /* Override any conflicting styles for intensity columns */
        .exercise-patterns-table table td.w-15,
        .exercise-patterns-table table th.w-15,
        .exercise-patterns-table .w-15 {
          min-width: 60px !important;
          width: 60px !important;
          max-width: 60px !important;
        }
      `}</style>
      <div
        className={`exercise-patterns-table mr-2 ${variant === 'timeline' ? 'timeline-variant' : ''} ${readonly ? 'readonly-mode' : ''}`}
      >
        <div
          className={
            mode === LifestyleMode.VIEW
              ? ''
              : variant === 'timeline'
                ? ''
                : 'overflow-x-auto'
          }
        >
          <table
            className={`${variant === 'timeline' ? 'w-full' : 'min-w-full'} border border-gray-300`}
            style={{
              borderCollapse: 'collapse',
            }}
          >
            <thead
              className={`text-white ${
                mode === LifestyleMode.VIEW ? 'bg-gray-400' : ''
              }`}
              style={{
                backgroundColor:
                  mode === LifestyleMode.VIEW ? undefined : 'hsl(200 97% 25%)',
                color: '#ffffff',
              }}
            >
              <tr>
                {tableField.headers.map((header) => {
                  const getColumnWidth = () => {
                    if (header.type === 'number') return 'w-16';
                    if (
                      header.type === 'conditional_select' ||
                      header.id === 'activity'
                    )
                      return 'w-48';
                    if (header.id === 'days') return 'w-50';
                    if (header.id === 'intensity') return 'w-15';
                    return 'w-24';
                  };

                  const getTimelineColumnClass = () => {
                    if (variant !== 'timeline') return '';
                    switch (header.id) {
                      case 'intensity':
                        return 'intensity-column';
                      case 'activity_type':
                        return 'activity-type-column';
                      case 'activity':
                        return 'activity-column';
                      case 'duration':
                        return 'duration-column';
                      case 'frequency':
                        return 'frequency-column';
                      case 'days':
                        return 'days-column';
                      default:
                        return '';
                    }
                  };

                  return (
                    <th
                      key={header.id}
                      className={`px-2 py-2 text-left text-sm font-medium ${getColumnWidth()} ${getTimelineColumnClass()}`}
                      style={
                        header.id === 'intensity' && variant !== 'timeline'
                          ? {
                              minWidth: '90px',
                              width: '130px',
                              maxWidth: '140px',
                            }
                          : undefined
                      }
                    >
                      {header.label}
                    </th>
                  );
                })}
                {!readonly && (
                  <th className="px-2 py-2 text-center text-sm font-medium w-12">
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody>
              {!readonly && (
                <tr>
                  <td
                    colSpan={tableField.headers.length + 1}
                    className="py-4 text-center border-b border-gray-200"
                  >
                    <div className="flex justify-center items-center gap-2">
                      <span className="font-medium text-lg">Add Entry</span>
                      <Button
                        variant="outlined"
                        onClick={addRow}
                        size="small"
                        sx={{
                          minWidth: 0,
                          padding: '2px 4px',
                          borderColor: accentColors.blue,
                          '&:hover': {
                            borderColor: accentColors.blue,
                            backgroundColor: '#e0f2fe',
                          },
                        }}
                      >
                        <MdOutlineAdd
                          size={24}
                          style={{
                            fontWeight: 'bold',
                            color: lightColors.secondary,
                          }}
                        />
                      </Button>
                    </div>
                  </td>
                </tr>
              )}
              {fields.map((row, rowIndex) => (
                <tr
                  key={row.id}
                  className={`border-b border-gray-200 ${
                    mode === LifestyleMode.VIEW ? 'bg-transparent' : ''
                  }`}
                >
                  {tableField.headers.map((header) => {
                    const getColumnWidth = () => {
                      if (header.type === 'number') return 'w-16';
                      if (
                        header.type === 'conditional_select' ||
                        header.id === 'activity'
                      )
                        return 'w-48';
                      if (header.id === 'days') return 'w-50';
                      if (header.id === 'intensity') return 'w-15';
                      return 'w-24';
                    };

                    const getTimelineColumnClass = () => {
                      if (variant !== 'timeline') return '';
                      switch (header.id) {
                        case 'intensity':
                          return 'intensity-column';
                        case 'activity_type':
                          return 'activity-type-column';
                        case 'activity':
                          return 'activity-column';
                        case 'duration':
                          return 'duration-column';
                        case 'frequency':
                          return 'frequency-column';
                        case 'days':
                          return 'days-column';
                        default:
                          return '';
                      }
                    };

                    return (
                      <td
                        key={header.id}
                        className={`${mode === LifestyleMode.VIEW ? 'px-2' : 'px-4'} py-2 border-r border-gray-200 last:border-r-0 ${getColumnWidth()} ${getTimelineColumnClass()} ${
                          mode === LifestyleMode.VIEW
                            ? 'bg-transparent text-black'
                            : ''
                        }`}
                        style={
                          header.id === 'intensity' && variant !== 'timeline'
                            ? {
                                minWidth: '60px',
                                width: '60px',
                                maxWidth: '60px',
                              }
                            : undefined
                        }
                      >
                        <Controller
                          name={`${name}.value.${rowIndex}.${header.id}`}
                          control={control}
                          defaultValue=""
                          render={({ field: controllerField }) => {
                            if (
                              header.type === 'conditional_select' &&
                              header.options &&
                              header.dependsOn
                            ) {
                              if (header.id === 'days') {
                                return (
                                  <DaysSelect
                                    controllerField={controllerField}
                                    header={header}
                                    name={name}
                                    rowIndex={rowIndex}
                                    control={control}
                                    readonly={readonly}
                                    mode={mode}
                                  />
                                );
                              }
                              return (
                                <ConditionalSelect
                                  controllerField={controllerField}
                                  header={header}
                                  name={name}
                                  rowIndex={rowIndex}
                                  control={control}
                                  readonly={readonly}
                                  mode={mode}
                                />
                              );
                            } else if (
                              header.type === 'select' &&
                              header.options
                            ) {
                              const selectOptions = Array.isArray(
                                header.options
                              )
                                ? header.options
                                : [];
                              return (
                                <div
                                  style={{
                                    position: 'relative',
                                    display: 'flex',
                                    alignItems: 'center',
                                    width: '100%',
                                  }}
                                >
                                  <Select
                                    {...controllerField}
                                    value={controllerField.value || ''}
                                    size="small"
                                    fullWidth
                                    variant="outlined"
                                    displayEmpty={mode !== LifestyleMode.VIEW}
                                    disabled={readonly}
                                    open={isSelectOpen(rowIndex, header.id)}
                                    onOpen={() =>
                                      setSelectOpen(rowIndex, header.id, true)
                                    }
                                    onClose={() =>
                                      setSelectOpen(rowIndex, header.id, false)
                                    }
                                    IconComponent={() => null}
                                    sx={{
                                      '&&& .MuiInputBase-root': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                        boxShadow: 'none !important',
                                        '&:before': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                        '&:after': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                        '&:hover': {
                                          backgroundColor:
                                            'transparent !important',
                                          border: 'none !important',
                                          borderColor: 'transparent !important',
                                          '&:before': {
                                            border: 'none !important',
                                            borderBottom: 'none !important',
                                            display: 'none !important',
                                          },
                                        },
                                        '&.Mui-focused': {
                                          backgroundColor:
                                            'transparent !important',
                                          border: 'none !important',
                                          borderColor: 'transparent !important',
                                          boxShadow: 'none !important',
                                          '&:after': {
                                            border: 'none !important',
                                            borderBottom: 'none !important',
                                            display: 'none !important',
                                          },
                                        },
                                        '&.Mui-disabled': {
                                          backgroundColor:
                                            'transparent !important',
                                          color:
                                            mode === LifestyleMode.VIEW
                                              ? '#000000 !important'
                                              : 'inherit',
                                          border: 'none !important',
                                          borderColor: 'transparent !important',
                                        },
                                      },
                                      '&&& .MuiOutlinedInput-root': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                        '&:hover': {
                                          backgroundColor:
                                            'transparent !important',
                                          border: 'none !important',
                                          borderColor: 'transparent !important',
                                        },
                                        '&.Mui-focused': {
                                          backgroundColor:
                                            'transparent !important',
                                          border: 'none !important',
                                          borderColor: 'transparent !important',
                                        },
                                        '&.Mui-disabled': {
                                          backgroundColor:
                                            'transparent !important',
                                          color:
                                            mode === LifestyleMode.VIEW
                                              ? '#000000 !important'
                                              : 'inherit',
                                          border: 'none !important',
                                          borderColor: 'transparent !important',
                                        },
                                        '& fieldset': {
                                          border: 'none !important',
                                          outline: 'none !important',
                                          borderColor: 'transparent !important',
                                        },
                                        '&:hover fieldset': {
                                          border: 'none !important',
                                          outline: 'none !important',
                                          borderColor: 'transparent !important',
                                        },
                                        '&.Mui-focused fieldset': {
                                          border: 'none !important',
                                          outline: 'none !important',
                                          borderColor: 'transparent !important',
                                        },
                                        '& .MuiOutlinedInput-notchedOutline': {
                                          border: 'none !important',
                                          outline: 'none !important',
                                          display: 'none !important',
                                          borderColor: 'transparent !important',
                                        },
                                      },
                                      '& .MuiInput-underline': {
                                        '&:before': {
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                        '&:after': {
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                        '&:hover:not(.Mui-disabled):before': {
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '& .MuiSelect-select': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        '&:focus': {
                                          backgroundColor:
                                            'transparent !important',
                                          color:
                                            mode === LifestyleMode.VIEW
                                              ? '#000000 !important'
                                              : 'inherit',
                                        },
                                        '&.Mui-disabled': {
                                          backgroundColor:
                                            'transparent !important',
                                          color:
                                            mode === LifestyleMode.VIEW
                                              ? '#000000 !important'
                                              : 'inherit',
                                          WebkitTextFillColor:
                                            mode === LifestyleMode.VIEW
                                              ? '#000000 !important'
                                              : 'inherit',
                                        },
                                      },
                                    }}
                                    renderValue={(selected) => {
                                      if (
                                        !selected &&
                                        mode !== LifestyleMode.VIEW
                                      ) {
                                        return (
                                          <span style={{ color: '#9ca3af' }}>
                                            Select
                                          </span>
                                        );
                                      }
                                      return selected || '';
                                    }}
                                  >
                                    {selectOptions.map((option) => (
                                      <MenuItem key={option} value={option}>
                                        {option}
                                      </MenuItem>
                                    ))}
                                  </Select>

                                  {mode !== LifestyleMode.VIEW && (
                                    <div
                                      style={{
                                        position: 'absolute',
                                        right: '4px',
                                        top: '50%',
                                        transform: 'translateY(-50%)',
                                        zIndex: 1,
                                        pointerEvents: readonly
                                          ? 'none'
                                          : 'auto',
                                      }}
                                    >
                                      <IoIosArrowDown
                                        className="h-4 w-auto text-[#637D92] cursor-pointer"
                                        style={{
                                          opacity: readonly ? 0.3 : 1,
                                        }}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          if (!readonly) {
                                            toggleSelectOpen(
                                              rowIndex,
                                              header.id
                                            );
                                          }
                                        }}
                                      />
                                    </div>
                                  )}
                                </div>
                              );
                            } else if (header.type === 'number') {
                              const isEditable = isFieldEditable(
                                rowIndex,
                                header.id
                              );

                              const fieldDisabled =
                                readonly ||
                                (mode === LifestyleMode.EDIT && !isEditable);

                              return (
                                <TextField
                                  {...controllerField}
                                  value={controllerField.value || ''}
                                  type="text"
                                  size="small"
                                  variant="outlined"
                                  disabled={fieldDisabled}
                                  onChange={(e) => {
                                    const value = e.target.value;

                                    if (
                                      value === '' ||
                                      /^\d*\.?\d*$/.test(value)
                                    ) {
                                      controllerField.onChange(value);
                                    }
                                  }}
                                  onKeyPress={(e) => {
                                    if (
                                      !/[\d.]/.test(e.key) &&
                                      e.key !== 'Backspace' &&
                                      e.key !== 'Delete' &&
                                      e.key !== 'Tab' &&
                                      e.key !== 'Enter'
                                    ) {
                                      e.preventDefault();
                                    }
                                  }}
                                  InputProps={{
                                    endAdornment: !readonly &&
                                      mode === LifestyleMode.EDIT && (
                                        <IconButton
                                          size="small"
                                          onClick={() =>
                                            toggleFieldEdit(rowIndex, header.id)
                                          }
                                          sx={{
                                            padding: '2px',
                                            marginRight: '-16px',
                                            color: isEditable
                                              ? '#2563eb'
                                              : '#6b7280',
                                          }}
                                        >
                                          <MdEdit className="h-4 w-4" />
                                        </IconButton>
                                      ),
                                    sx: {
                                      paddingRight:
                                        mode === LifestyleMode.EDIT
                                          ? '4px'
                                          : '16px',
                                    },
                                  }}
                                  sx={{
                                    width: '80px',
                                    minWidth: '80px',
                                    maxWidth: '80px',
                                    '&&& .MuiInputBase-root': {
                                      backgroundColor: 'transparent !important',
                                      border: 'none !important',
                                      outline: 'none !important',
                                      borderColor: 'transparent !important',
                                      boxShadow: 'none !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      '&:before': {
                                        border: 'none !important',
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:after': {
                                        border: 'none !important',
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:hover': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                        '&:before': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '&.Mui-focused': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                        boxShadow: 'none !important',
                                        '&:after': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                    },
                                    '&&& .MuiOutlinedInput-root': {
                                      backgroundColor: 'transparent !important',
                                      border: 'none !important',
                                      outline: 'none !important',
                                      borderColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      '&:hover': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-focused': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '& fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&:hover fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-focused fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '& .MuiOutlinedInput-notchedOutline': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        display: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                    },
                                    '& .MuiInput-underline': {
                                      '&:before': {
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:after': {
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:hover:not(.Mui-disabled):before': {
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                    },
                                    '& .MuiInputBase-input': {
                                      backgroundColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      '&:-webkit-autofill': {
                                        WebkitBoxShadow:
                                          '0 0 0 1000px transparent inset !important',
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&:-webkit-autofill:hover': {
                                        WebkitBoxShadow:
                                          '0 0 0 1000px transparent inset !important',
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&:-webkit-autofill:focus': {
                                        WebkitBoxShadow:
                                          '0 0 0 1000px transparent inset !important',
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&:focus': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&.Mui-disabled': {
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        backgroundColor:
                                          'transparent !important',
                                        WebkitTextFillColor:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                    },
                                  }}
                                />
                              );
                            } else {
                              return (
                                <TextField
                                  {...controllerField}
                                  value={controllerField.value || ''}
                                  size="small"
                                  fullWidth
                                  variant="outlined"
                                  disabled={readonly}
                                  sx={{
                                    '&&& .MuiInputBase-root': {
                                      backgroundColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      border: 'none !important',
                                      outline: 'none !important',
                                      borderColor: 'transparent !important',
                                      boxShadow: 'none !important',
                                      '&:before': {
                                        border: 'none !important',
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:after': {
                                        border: 'none !important',
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:hover': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                        '&:before': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '&.Mui-focused': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                        boxShadow: 'none !important',
                                        '&:after': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                    },
                                    '&&& .MuiOutlinedInput-root': {
                                      backgroundColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      border: 'none !important',
                                      outline: 'none !important',
                                      borderColor: 'transparent !important',
                                      '&:hover': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-focused': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '& fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&:hover fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-focused fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '& .MuiOutlinedInput-notchedOutline': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        display: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                    },
                                    '& .MuiInputBase-input': {
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      backgroundColor: 'transparent !important',
                                      '&:focus': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&.Mui-disabled': {
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        backgroundColor:
                                          'transparent !important',
                                        WebkitTextFillColor:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                    },
                                  }}
                                />
                              );
                            }
                          }}
                        />
                      </td>
                    );
                  })}
                  {!readonly && (
                    <td className="px-4 py-2 text-center">
                      <IconButton
                        onClick={() => handleDeleteClick(rowIndex)}
                        size="small"
                        sx={{
                          color: '#ef4444',
                          border: '1px solid #ef4444',
                          borderRadius: '50%',
                          width: '32px',
                          height: '32px',
                          '&:hover': {
                            backgroundColor: '#fef2f2',
                            color: '#dc2626',
                            borderColor: '#dc2626',
                          },
                        }}
                      >
                        <MdClose size={18} />
                      </IconButton>
                    </td>
                  )}
                </tr>
              ))}
              {fields.length === 0 && (
                <tr>
                  <td
                    colSpan={tableField.headers.length + (!readonly ? 1 : 0)}
                    className="text-center py-4 text-gray-500 border-b border-gray-300"
                  >
                    No entry yet
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        <DeleteModal
          open={deleteModalOpen}
          onClose={handleCancelDelete}
          onDelete={handleConfirmDelete}
          confirmationMessage="Are you sure you want to delete this entry?"
          classes={{ container: '!w-[400px]' }}
          bodyContent={
            getActivityType() || getActivity() ? (
              <div className="mt-2 text-center">
                <div className="text-xl">
                  {getActivity() && (
                    <span className="font-medium" style={{ color: '#0496E1' }}>
                      {getActivity()}
                    </span>
                  )}
                  {getActivity() && getActivityType() && (
                    <>
                      <span className="text-black"> in </span>
                      <span className="text-black font-bold">
                        {getActivityType()}
                      </span>
                    </>
                  )}
                  {!getActivity() && getActivityType() && (
                    <span className="text-black font-bold">
                      {getActivityType()}
                    </span>
                  )}
                </div>
              </div>
            ) : null
          }
        />
      </div>
    </>
  );
};
