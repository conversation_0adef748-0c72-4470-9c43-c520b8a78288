import React, {
  Dispatch,
  FC,
  memo,
  RefObject,
  SetStateAction,
  useCallback,
} from 'react';

import {
  Control,
  Controller,
  FieldArrayWithId,
  Path,
  UseFormGetValues,
  UseFormRegister,
  useWatch,
} from 'react-hook-form';

import { Stack } from '@mui/material';
import dayjs from 'dayjs';
import { isEqual } from 'lodash';

import TextInput from '@core/components/text-input';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { FAMILY_FIELD_REGEX } from '@/utils/emr/doctor-profile/personal-info';
import {
  aadharInput,
  aadharValidation,
  enforceNumericWithHyphen,
  preventNonAlphabeticInput,
} from '@/utils/validation';

import PencilIcon from '@/assets/svg/PencilIcon';

import {
  familyStatusOptions,
  relationships,
  yesNoOptions,
} from '@/constants/emr/doctor-profile/personal-info';

import DatePicker from '@/core/components/date-picker';
import {
  defaultFamily,
  Family,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import { SelectField } from '../Components';
import AutoResizeTextArea from '../shared/AutoResizeTextArea';
import ControlledFileUpload from '../shared/ControlledFileUpload';
import MobileAddDeleteButtons from '../shared/MobileAddDeleteButtons';

import { FormData } from '.';

type Props = {
  fields: FieldArrayWithId<FormData, 'family', 'id'>[];
  control: Control<FormData>;
  register: UseFormRegister<FormData>;
  editableField: Set<string>;
  getValues: UseFormGetValues<FormData>;
  isSubmitted: boolean;
  setEditableField: Dispatch<SetStateAction<Set<string>>>;
  handleOnDelete: (_itemToDelete: ItemToDelete) => void;
  handleOnAdd: () => void;
  lastFieldRef: RefObject<HTMLDivElement>;
};

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const FamilyMob: FC<Props> = ({
  fields,
  editableField,
  getValues,
  isSubmitted,
  setEditableField,
  control,
  handleOnDelete,
}) => {
  const { doctorProfile } = useDoctorStore();
  const watchedEmployment = useWatch({
    control,
    name: 'family',
  });

  const handleEditClick = useCallback(
    (fieldName: Path<FormData>) => {
      setEditableField((prev) => new Set(prev.add(fieldName)));
    },
    [setEditableField]
  );

  const isFieldDisabled = useCallback(
    (fieldName: Path<FormData>) => {
      if (editableField.has(fieldName)) {
        return false;
      }

      const formValues = getValues();
      const fieldValue = formValues[fieldName as keyof FormData];

      const match = fieldName.match(FAMILY_FIELD_REGEX);
      let doctorFieldValue;

      if (match) {
        const index = Number(match[1]);
        const key = match[2];
        doctorFieldValue =
          doctorProfile?.family?.[index]?.[key as keyof Family];
      }

      return (
        !!doctorFieldValue ||
        (isSubmitted &&
          !!fieldValue &&
          JSON.stringify(fieldValue) === JSON.stringify(doctorFieldValue))
      );
    },
    [editableField, getValues, doctorProfile?.family, isSubmitted]
  );

  const renderEditIcon = useCallback(
    (fieldName: Path<FormData>) => {
      return isFieldDisabled(fieldName) ? (
        <button type="button" onClick={() => handleEditClick(fieldName)}>
          <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
        </button>
      ) : null;
    },
    [isFieldDisabled, handleEditClick]
  );

  return (
    <>
      {fields?.map((field, index) => {
        return (
          <Stack key={field.id} spacing={2}>
            <AutoResizeTextArea
              label="Name"
              placeholder=""
              color="white"
              control={control}
              name={`family.${index}.name`}
              onKeyDown={preventNonAlphabeticInput}
              endDecoration={renderEditIcon(`family.${index}.name`)}
              disabled={isFieldDisabled(`family.${index}.name`)}
              className="w-full"
            />
            <Controller
              control={control}
              name={`family.${index}.relation`}
              render={({ field }) => (
                <SelectField
                  label="Relation"
                  options={relationships}
                  endDecoration={renderEditIcon(`family.${index}.relation`)}
                  disabledInput={isFieldDisabled(`family.${index}.relation`)}
                  placeholder="Select"
                  {...field}
                />
              )}
            />
            <Controller
              control={control}
              name={`family.${index}.dependent`}
              render={({ field }) => (
                <SelectField
                  label="Dependent"
                  options={yesNoOptions}
                  endDecoration={renderEditIcon(`family.${index}.dependent`)}
                  disabledInput={isFieldDisabled(`family.${index}.dependent`)}
                  placeholder="Select"
                  {...field}
                />
              )}
            />
            <DatePicker
              label="DOB"
              format={DATE_DD_MM_YYYY_SLASH}
              name={`family.${index}.dob`}
              control={control}
              disableInput={isFieldDisabled(`family.${index}.dob`)}
              renderEditIcon={() => renderEditIcon(`family.${index}.dob`)}
              maxDate={dayjs()}
              rules={{
                validate: (value) => {
                  if (!value) return true;
                  const selectedDate = dayjs(value);
                  const today = dayjs().endOf('day');
                  return selectedDate.isAfter(today)
                    ? 'Please enter a valid date'
                    : true;
                },
              }}
            />
            <Controller
              control={control}
              name={`family.${index}.aadharNumber`}
              rules={{
                validate: (value) => {
                  if (!value) return true;
                  return aadharValidation(value) || 'Invalid Aadhar number';
                },
              }}
              render={({ field, fieldState }) => (
                <TextInput
                  {...field}
                  label="Aadhar Number"
                  endDecoration={renderEditIcon(`family.${index}.aadharNumber`)}
                  disabled={isFieldDisabled(`family.${index}.aadharNumber`)}
                  placeholder=""
                  onKeyDown={aadharInput}
                  className="w-full"
                  errors={fieldState.error}
                  onInput={enforceNumericWithHyphen()}
                  maxLength={14}
                  inputMode="numeric"
                />
              )}
            />
            <Controller
              control={control}
              name={`family.${index}.occupation`}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label="Occupation"
                  endDecoration={renderEditIcon(`family.${index}.occupation`)}
                  disabled={isFieldDisabled(`family.${index}.occupation`)}
                  placeholder="Doctor"
                  className="w-full"
                  onKeyDown={preventNonAlphabeticInput}
                />
              )}
            />
            <ControlledFileUpload
              name={`family.${index}.documents`}
              control={control}
              label="Doc"
              className="w-full"
              allowedFileTypes={['image/jpeg', 'image/png', 'application/pdf']}
              fileTypeErrorMessage="*Only JPG, PNG, and PDF files are allowed."
              showPreview
              maxFileSize={6}
              isBoxStyle
            />
            <Controller
              control={control}
              name={`family.${index}.status`}
              render={({ field }) => (
                <SelectField
                  label="Status"
                  options={familyStatusOptions}
                  endDecoration={renderEditIcon(`family.${index}.status`)}
                  disabledInput={isFieldDisabled(`family.${index}.status`)}
                  placeholder="Select"
                  {...field}
                />
              )}
            />
            <MobileAddDeleteButtons
              onDelete={
                isEqual(watchedEmployment[index], defaultFamily)
                  ? undefined
                  : () => handleOnDelete({ index, uuId: field?.uuId })
              }
            />
          </Stack>
        );
      })}
    </>
  );
};

export default memo(FamilyMob);
