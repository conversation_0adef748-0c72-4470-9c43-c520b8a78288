import { convertToBaseOptions } from '@/utils/common';

import { NicotineDependenceTestQuestion } from '@/types/emr/lifestyle/medical-history-addiction';

export const NICOTINE_DEPENDENCE_TEST_QUESTIONS: NicotineDependenceTestQuestion[] =
  [
    {
      name: 'timeToFirstCigarette',
      questionText: 'How soon after waking do you smoke your first cigarette?',
      options: [
        { label: 'Within 5 minutes', score: 3 },
        { label: '5-30 minutes', score: 2 },
        { label: '31-60 minutes', score: 1 },
      ],
    },
    {
      name: 'findDifficult',
      questionText:
        'Do you find it difficult to refrain from smoking in places where it is forbidden? e.g. Church, Library etc',
      options: [
        { label: 'Yes', score: 1 },
        { label: 'No', score: 0 },
      ],
    },
    {
      name: 'whichCigarette',
      questionText: 'Which cigarette would you hate to give up?',
      options: [
        { label: 'The first in the morning', score: 1 },
        { label: 'Any other', score: 0 },
      ],
    },
    {
      name: 'cigarettesPerDay',
      questionText: 'How many cigarettes do you smoke in a day?',
      options: [
        { label: '31 or more', score: 3 },
        { label: '21-30', score: 2 },
        { label: '11-20', score: 1 },
        { label: '10 or less', score: 0 },
      ],
    },
    {
      name: 'moreFrequentMorning',
      questionText: 'Do you smoke more frequently in morning?',
      options: [
        { label: 'Yes', score: 1 },
        { label: 'No', score: 0 },
      ],
    },
    {
      name: 'smokeWhenIll',
      questionText: 'Do you smoke even if you are sick in bed most of the day',
      options: [
        { label: 'Yes', score: 1 },
        { label: 'No', score: 0 },
      ],
    },
  ];

export const DiagnosisStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
} as const;

export type DiagnosisStatus =
  (typeof DiagnosisStatus)[keyof typeof DiagnosisStatus];

export const statusOption = convertToBaseOptions(DiagnosisStatus);

export const SubstanceHistory = {
  NO: 'no',
  FORMER: 'former',
  CURRENT: 'current',
} as const;

export type SubstanceHistory =
  (typeof SubstanceHistory)[keyof typeof SubstanceHistory];

export const substanceHistoryOption = convertToBaseOptions(SubstanceHistory);

export const SubstanceFrequency = {
  DAILY: 'daily',
  TWO_THREE_TIMES_WEEK: '2-3_times_week',
  FOUR_TIMES_MONTH: '4_times_month',
  OCCASIONALLY: 'occasionally',
} as const;

export type SubstanceFrequency =
  (typeof SubstanceFrequency)[keyof typeof SubstanceFrequency];

export const substanceFrequencyOption =
  convertToBaseOptions(SubstanceFrequency);

export const generateYearOptions = () => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = 0; i < 100; i++) {
    const year = currentYear - i;
    years.push({ value: year.toString(), label: year.toString() });
  }
  return years;
};
