import React, { memo, useCallback, useEffect, useRef, useState } from 'react';

import { useFieldArray, useForm } from 'react-hook-form';

import { Stack } from '@mui/material';
import { toast } from 'sonner';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import {
  discardDefaultValues,
  getUniqueId,
} from '@/utils/emr/doctor-profile/personal-info';

import { profileTabs } from '@/constants/emr/doctor-profile/personal-info';

import AddButton from '@/emr/components/lifestyle/lifestyle-forms/shared/AddButton';
import { useFileUpload } from '@/emr/hooks/use-file-upload';

import {
  defaultFamily,
  Family,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import DeleteModal from '../shared/DeleteModal';
import SaveButton from '../shared/SaveButton';
import Title from '../shared/Title';

import FamilyMob from './FamilyMobTab';
import FamilyTable from './FamilyTable';

export type FormData = {
  family: Family[];
};

const FamilyTab = () => {
  const { data: userData } = useUserStore();
  const isMobile = useIsMobile();
  const lastFieldRef = useRef<HTMLDivElement | null>(null);

  const [itemToEdit, setItemToEdit] = useState<number | null>(null);
  const [itemToDelete, setItemToDelete] = useState<ItemToDelete>(null);
  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    updateDoctorProfile,
    doctorProfile,
    isDeleting,
    createDoctorProfile,
    setTabName,
    deleteDoctorProfileTableItem,
  } = useDoctorStore();

  const { fileUpload } = useFileUpload({
    userId: doctorProfile?.id || userData?.id,
    type: 'family',
  });

  const { handleSubmit, control, reset, register, getValues } =
    useForm<FormData>({
      defaultValues: {
        family: [isMobile ? defaultFamily : {}],
      },
      mode: 'onSubmit',
    });

  const { append, fields, remove } = useFieldArray<FormData>({
    control,
    name: 'family',
    shouldUnregister: true,
  });

  const onError = (errors: any) => {
    if (errors.family) {
      const errorRows: number[] = [];

      errors.family.forEach((itemError: any, index: number) => {
        if (itemError?.aadharNumber) {
          errorRows.push(index + 1);
        }
      });

      if (!isMobile && errorRows.length > 0) {
        toast.error(`Invalid Aadhar number`);
      }
    }
  };

  const onSubmit = useCallback(
    async (data: FormData) => {
      let family = discardDefaultValues(data?.family, defaultFamily);

      if (
        Array.isArray(family) &&
        family.every((item) => !item || Object.keys(item).length === 0)
      ) {
        family = [];
      }
      const finalData = family.length
        ? await Promise.all(
            family.map(async (item) => {
              if (!item?.documents) {
                return { ...item, uuId: getUniqueId(item?.uuId, 'FAM') };
              }
              const file = item.documents;
              return {
                ...item,
                uuId: getUniqueId(item?.uuId, 'FAM'),
                documents: await fileUpload(file),
              };
            })
          )
        : [];

      if (doctorProfile?.id) {
        updateDoctorProfile(doctorProfile.id, { family: finalData });
      } else {
        createDoctorProfile({ family: finalData, username: userData?.email });
      }

      setItemToEdit(null);
      setIsSubmitted(true);
      setEditableField(new Set());
    },
    [
      createDoctorProfile,
      doctorProfile,
      updateDoctorProfile,
      userData?.email,
      fileUpload,
    ]
  );

  const handleItemEdit = useCallback(
    (index: number) => () => {
      setItemToEdit(index);
    },
    []
  );

  const handleOnAdd = useCallback(() => {
    setItemToEdit(fields.length);
    append(defaultFamily);
  }, [append, fields?.length]);

  const handleOnDelete = useCallback(
    (item: ItemToDelete) => setItemToDelete(item),
    []
  );

  const handleDelete = useCallback(async () => {
    if (!itemToDelete) return;

    const { uuId, index } = itemToDelete;

    if (uuId && doctorProfile?.id) {
      const filteredFamily = doctorProfile.family?.filter(
        (item) => item?.uuId !== uuId
      );

      await deleteDoctorProfileTableItem(doctorProfile.id, {
        family: filteredFamily,
      });
    } else {
      if (index === 0 && isMobile) {
        reset({
          family: [defaultFamily] as Family[],
        });
      } else {
        remove(index);
      }
    }

    setItemToDelete(null);
  }, [
    itemToDelete,
    doctorProfile?.id,
    doctorProfile?.family,
    deleteDoctorProfileTableItem,
    remove,
    reset,
    isMobile,
  ]);

  const handleCancel = useCallback(() => {
    setItemToDelete(null);
  }, []);

  useEffect(() => {
    if (doctorProfile?.family) {
      if (isMobile) {
        reset({
          family: doctorProfile.family?.length
            ? doctorProfile.family
            : ([defaultFamily] as Family[]),
        });
      } else {
        reset({ family: doctorProfile.family });
      }
    }
  }, [doctorProfile?.family, isMobile, reset]);

  useEffect(() => {
    setTabName(profileTabs.FAMILY_DETAILS);
  }, [setTabName]);

  return (
    <Stack
      component="form"
      spacing={{ xs: 3, md: 2 }}
      onSubmit={handleSubmit(onSubmit, onError)}
      pb={{ xs: 8, md: 0 }}
    >
      <Title title="Family" onAdd={handleOnAdd} showEndButton={!isMobile} />
      {isMobile ? (
        <FamilyMob
          fields={fields}
          control={control}
          register={register}
          editableField={editableField}
          getValues={getValues}
          isSubmitted={isSubmitted}
          setEditableField={setEditableField}
          handleOnDelete={handleOnDelete}
          handleOnAdd={handleOnAdd}
          lastFieldRef={lastFieldRef}
        />
      ) : (
        <FamilyTable
          fields={fields}
          control={control}
          itemToEdit={itemToEdit}
          handleItemEdit={handleItemEdit}
          handleOnDelete={handleOnDelete}
          register={register}
        />
      )}
      {isMobile && (
        <div className="fixed bottom-34 md:static right-6 bg-transparent">
          <AddButton size="medium" onClick={handleOnAdd} />
        </div>
      )}
      <SaveButton />
      <DeleteModal
        open={!!itemToDelete}
        onClose={handleCancel}
        onDelete={handleDelete}
        isLoading={isDeleting}
      />
    </Stack>
  );
};

export default memo(FamilyTab);
