import {
  DiagnosisStatus,
  SubstanceFrequency,
  SubstanceHistory,
} from '@/constants/emr/lifestyle/medical-history-addiction';

export type NicotineDependenceTestQuestion = {
  name: string;
  questionText: string;
  options: {
    label: string;
    score: number;
  }[];
};

export type NicotineDependenceTestForm = {
  timeToFirstCigarette: string;
  findDifficult: string;
  whichCigarette: string;
  cigarettesPerDay: string;
  moreFrequentMorning: string;
  smokeWhenIll: string;
};

export type NicotineDependenceTestResult = {
  id?: string;
  responses: NicotineDependenceTestForm;
  testDate: string;
  totalScore?: number;
};

export type Diagnosis = {
  diseaseName: string;
  yearOfDiagnosis: string;
  diagnosisDuration: string;
  status: DiagnosisStatus;
  treatmentHistory: string;
};

export type Addiction = {
  history: SubstanceHistory;
  count?: string;
  frequency?: SubstanceFrequency;
};

export type MedicalHistoryAddictionForm = {
  diagnosis: Diagnosis[];
  smoking: Addiction;
  alcohol: Addiction;
  tobacco: Addiction;
  drugs: Addiction;
  nicotineDependenceTest?: NicotineDependenceTestResult;
};

export type MedicalHistoryAddiction = {
  id: string;
  patientId: string;
  diagnosis: Diagnosis[];
  smoking: Addiction;
  alcohol: Addiction;
  tobacco: Addiction;
  drugs: Addiction;
  nicotineDependenceTest?: NicotineDependenceTestResult;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
};

export const defaultNicotineDependenceTestValues: NicotineDependenceTestForm = {
  timeToFirstCigarette: '',
  findDifficult: '',
  whichCigarette: '',
  cigarettesPerDay: '',
  moreFrequentMorning: '',
  smokeWhenIll: '',
};

export const defaultMedicalHistoryAddictionValues: MedicalHistoryAddictionForm =
  {
    diagnosis: [
      {
        diseaseName: '',
        yearOfDiagnosis: '',
        diagnosisDuration: '',
        status: DiagnosisStatus.ACTIVE,
        treatmentHistory: '',
      },
    ],
    smoking: { history: SubstanceHistory.NO },
    alcohol: { history: SubstanceHistory.NO },
    tobacco: { history: SubstanceHistory.NO },
    drugs: { history: SubstanceHistory.NO },
    nicotineDependenceTest: {
      responses: defaultNicotineDependenceTestValues,
      testDate: '',
      totalScore: 0,
    },
  };
