import { toast } from 'sonner';
import { create } from 'zustand';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import {
  createPatientMedicalHistoryAddiction,
  getPatientMedicalHistoryAddiction,
  updatePatientMedicalHistoryAddiction,
} from '@/query/emr/lifestyle/medical-history-addiction';

import { getErrorMessage } from '@/utils/error-message';

import {
  MedicalHistoryAddiction,
  MedicalHistoryAddictionForm,
} from '@/types/emr/lifestyle/medical-history-addiction';

type MedicalHistoryAddictionStoreState = {
  medicalHistory: MedicalHistoryAddiction | null;
  loading: boolean;
  saving: boolean;
};

type MedicalHistoryAddictionStoreActions = {
  getMedicalHistory: () => Promise<void>;
  saveMedicalHistory: (data: MedicalHistoryAddictionForm) => Promise<void>;
  updateMedicalHistory: (data: MedicalHistoryAddictionForm) => Promise<void>;
  clearMedicalHistory: () => void;
};

export type MedicalHistoryAddictionStore = MedicalHistoryAddictionStoreState &
  MedicalHistoryAddictionStoreActions;

const initialState: MedicalHistoryAddictionStoreState = {
  medicalHistory: null,
  loading: false,
  saving: false,
};

export const useMedicalHistoryAddictionStore =
  create<MedicalHistoryAddictionStore>((set, get) => ({
    ...initialState,

    getMedicalHistory: async () => {
      set({ loading: true });
      try {
        const patientId = useCurrentPatientStore.getState().patient?.id;
        if (!patientId) return;

        const data = await getPatientMedicalHistoryAddiction(patientId);

        set({
          medicalHistory: data,
        });
      } catch (error) {
        console.error(getErrorMessage(error, 'Error fetching medical history'));
      } finally {
        set({ loading: false });
      }
    },

    saveMedicalHistory: async (formData) => {
      set({ saving: true });
      try {
        const patientId = useCurrentPatientStore.getState().patient?.id;
        if (!patientId) {
          toast.error('No patient selected');
          return;
        }

        const data = await createPatientMedicalHistoryAddiction(
          patientId,
          formData
        );

        set({ medicalHistory: data });
        toast.success('Medical history saved successfully');
      } catch (error) {
        toast.error(getErrorMessage(error, 'Error saving medical history'));
      } finally {
        set({ saving: false });
      }
    },

    updateMedicalHistory: async (formData) => {
      set({ saving: true });
      try {
        const { medicalHistory } = get();
        if (!medicalHistory?.id) {
          toast.error('No medical history to update');
          return;
        }

        const data = await updatePatientMedicalHistoryAddiction(
          medicalHistory.id,
          formData
        );

        set({ medicalHistory: data });
        toast.success('Medical history updated successfully');
      } catch (error) {
        toast.error(getErrorMessage(error, 'Error updating medical history'));
      } finally {
        set({ saving: false });
      }
    },

    clearMedicalHistory: () => {
      set({
        medicalHistory: null,
      });
    },
  }));
