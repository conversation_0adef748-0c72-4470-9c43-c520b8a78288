/**
 * Metrics Collection API Endpoint
 * Collects frontend metrics and forwards them to configured exporters
 */
import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({ status: 'Metrics endpoint active' });
}

export async function POST(request: NextRequest) {
  try {
    const metrics = await request.json();

    // Log metrics for debugging
    console.log('Received metrics:', metrics);

    // Here you could forward to additional exporters if needed
    // For now, OpenTelemetry handles the export via instrumentation

    return NextResponse.json({
      success: true,
      message: 'Metrics received successfully',
    });
  } catch (error) {
    console.error('Error processing metrics:', error);
    return NextResponse.json(
      { error: 'Failed to process metrics' },
      { status: 500 }
    );
  }
}
