import React from 'react';

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Toolt<PERSON> } from 'recharts';

interface PieChartData {
  name: string;
  value: number;
  color: string;
  count?: number;
  duration?: number;
}

interface ActivityPieChartProps {
  data: Array<{
    name: string;
    value: number;
    percentage: number;
    color: string;
    count?: number;
    duration?: number;
  }>;
  title: string;

  unit: string;
}

const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  percent,
  index,
  name,
  payload,
}: any) => {
  // Position the label directly on the pie slice
  const radius = innerRadius + (outerRadius - innerRadius) * 0.7; // Position inside the slice
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor="middle"
      dominantBaseline="middle"
      style={{
        fontSize: '12px',
        fontWeight: '600',
      }}
    ></text>
  );
};

const CustomTooltip = ({ active, payload, label, unit }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white p-2 border border-gray-200 rounded shadow-md text-xs">
        <p className="font-medium">{data.name}</p>
        <p>
          Duration: {data.duration || data.value} {unit}
        </p>
        {data.count !== undefined && <p>Count: {data.count}</p>}
      </div>
    );
  }
  return null;
};

const ActivityPieChart: React.FC<ActivityPieChartProps> = ({
  data = [],
  title = '',

  unit = '',
}) => {
  const totalValue = data.reduce((acc, item) => acc + item.value, 0);

  // Define legend items based on chart type
  const getLegendItems = () => {
    if (title.toLowerCase().includes('intensity')) {
      return [
        { name: 'Intense', color: '#BA324F' },
        { name: 'Moderate', color: '#FFAA5A' },
        { name: 'Mild', color: '#AB92BF' },
      ];
    } else {
      return [
        { name: 'Flexibility', color: '#0B3948' },
        { name: 'Strength', color: '#A8C256' },
        { name: 'Balance', color: '#1B998B' },
        { name: 'Aerobics', color: '#BA324F' },
      ];
    }
  };

  const legendItems = getLegendItems();
  const chartTitle = title || 'Distribution';

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm mt-4">
      <div
        className={`flex flex-col  border-b border-gray-200 mb-4 pt-1 ${title.toLowerCase().includes('intensity') ? ' pb-5' : 'pb-1'}`}
      >
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h3 className="text-base font-medium text-gray-900">{chartTitle}</h3>

          {title.toLowerCase().includes('activity') ? (
            <div className="grid grid-cols-2 gap-x-6 gap-y-2">
              {legendItems.map((item, index) => (
                <div key={index} className="flex items-center">
                  <div
                    className="w-3 h-3 rounded-full mr-1.5 flex-shrink-0"
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-xs text-gray-600 whitespace-nowrap">
                    {item.name}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-wrap items-center gap-4">
              {legendItems.map((item, index) => (
                <div key={index} className="flex items-center">
                  <div
                    className="w-3 h-3 rounded-full mr-1.5 flex-shrink-0"
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-xs text-gray-600 whitespace-nowrap">
                    {item.name}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="w-full max-w-[400px] h-[300px] relative">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={140}
                innerRadius={80}
                dataKey="percentage"
                nameKey="name"
                animationBegin={0}
                animationDuration={1000}
                animationEasing="ease-out"
              >
                {data.map((entry, index, array) => {
                  let color = entry.color;
                  if (title.toLowerCase().includes('intensity') && entry.name) {
                    const intensityColors: Record<string, string> = {
                      intense: '#BA324F',
                      moderate: '#FFAA5A',
                      mild: '#AB92BF',
                    };
                    color =
                      intensityColors[entry.name.toLowerCase()] || entry.color;
                  }
                  return <Cell key={`cell-${index}`} fill={color} />;
                })}
              </Pie>
              <Tooltip content={<CustomTooltip unit={unit} />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="flex-1 pl-4">
          <div className="space-y-2">
            <div className="space-y-1">
              {data.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between text-xs"
                >
                  <div className="flex items-center">
                    <span className=" font-medium">{item.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-gray-700">{item.percentage}%</div>
                    <div className="text-xs text-gray-500 whitespace-nowrap">
                      {item.duration || item.value} {unit}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivityPieChart;
