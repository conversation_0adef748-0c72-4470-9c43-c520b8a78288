import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createLifestyleData,
  getLifestyleQuestions,
  getPatientLifestyle,
  updateLifestyleData,
} from '@/query/emr/lifestyle';

import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import {
  FieldGroup,
  Questionnaire,
  QuestionnaireResponse,
} from '@/types/emr/lifestyle/questionnaire';

import { useLifestyleFilterStore } from '../../filter-store';

type KnowledgeState = {
  questions: Questionnaire;
  questionLoading: boolean;
  updating: boolean;
  patientData: QuestionnaireResponse[];
  loading: boolean;
  finalizing: boolean;
};

type KnowledgeActions = {
  getLifestyleQuestions: () => Promise<void>;
  createLifestyleData: (data: Record<string, unknown>) => Promise<void>;
  updateLifestyleData: (data: Record<string, unknown>) => Promise<void>;
  finalizeRecord: (id: string) => Promise<void>;
  getPatientData: (
    fromDate?: string,
    toDate?: string,
    silent?: boolean
  ) => Promise<void>;
  refreshData: () => void;
};

export type NutritionKnowledgeStore = KnowledgeState & KnowledgeActions;

const defaultQuestions = {
  source: LifestyleSources.NUTRITION_KNOWLEDGE,
  questions: [],
};

const initialState: KnowledgeState = {
  questions: defaultQuestions,
  questionLoading: false,
  updating: false,
  patientData: [],
  loading: false,
  finalizing: false,
};

export const nutritionKnowledgeStore = create<NutritionKnowledgeStore>(
  (set, get) => ({
    ...initialState,
    getLifestyleQuestions: async () => {
      try {
        set({ questionLoading: true });

        const data = await getLifestyleQuestions(
          LifestyleSources.NUTRITION_KNOWLEDGE
        );

        set({ questions: data });
      } catch (error) {
        console.error('Error fetching knowledge questions:', error);
        toast.error(
          'Failed to load knowledge questionnaire. Please try again.'
        );

        set({ questions: defaultQuestions });
      } finally {
        set({ questionLoading: false });
      }
    },

    createLifestyleData: async (data: Record<string, unknown>) => {
      try {
        set({ updating: true });
        const payload = {
          ...data,
          source: LifestyleSources.NUTRITION_KNOWLEDGE,
        };
        await createLifestyleData(payload);
        toast.success('Knowledge record created successfully');
        get().getPatientData();
      } catch (error) {
        console.error('Error creating knowledge data:', error);
        toast.error('Failed to create knowledge record');
      } finally {
        set({ updating: false });
      }
    },

    updateLifestyleData: async (data: Record<string, unknown>) => {
      try {
        set({ updating: true });

        const {
          questions,
          created_on,
          updated_on,
          create_by,
          update_by,
          _attachments,
          _etag,
          _rid,
          _self,
          _ts,
          ...formData
        } = data;

        const payload = {
          ...formData,
          source: LifestyleSources.NUTRITION_KNOWLEDGE,
          questions: (questions || data.questions) as FieldGroup[],
        };

        await updateLifestyleData(payload, data.id as string);
        toast.success('Knowledge record updated successfully');
        get().getPatientData();
      } catch (error) {
        console.error('Error updating knowledge data:', error);
        toast.error('Failed to update knowledge record');
      } finally {
        set({ updating: false });
      }
    },

    finalizeRecord: async (id: string) => {
      try {
        set({ finalizing: true });
        await updateLifestyleData(
          { status: LifestyleRecordStatus.FINALIZED },
          id
        );
        toast.success('Knowledge record finalized successfully');
        get().getPatientData();
      } catch (error) {
        console.error('Error finalizing knowledge record:', error);
        toast.error('Failed to finalize knowledge record');
      } finally {
        set({ finalizing: false });
      }
    },

    getPatientData: async (fromDate, toDate, silent = false) => {
      try {
        set({ loading: !silent });
        const data = await getPatientLifestyle(
          LifestyleSources.NUTRITION_KNOWLEDGE,
          fromDate,
          toDate
        );
        set({ patientData: data });
      } catch (error) {
        console.error('Error fetching patient knowledge data:', error);
        toast.error('Failed to load patient knowledge data');
        set({ patientData: [] });
      } finally {
        set({ loading: false });
      }
    },

    refreshData: () => {
      const { getPatientData } = get();
      const { fromDate, toDate } = useLifestyleFilterStore.getState();
      getPatientData(fromDate, toDate, true);
    },
  })
);
