import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createPaymentOrder,
  getPaymentStatus,
  verifyPayment,
  CreatePaymentOrderRequest,
  CreatePaymentOrderResponse,
  PaymentStatus,
} from '@/query/payments';

import { getErrorMessage } from '@/utils/error-message';
import {
  openRazorpayCheckout,
  getRazorpayKeyId,
  RazorpayResponse,
} from '@/utils/razorpay';

type PaymentStoreState = {
  currentOrder: CreatePaymentOrderResponse | null;
  paymentStatus: PaymentStatus | null;
  isCreatingOrder: boolean;
  isVerifyingPayment: boolean;
  isCheckingStatus: boolean;
  showPaymentModal: boolean;
  showSuccessModal: boolean;
  showFailureModal: boolean;
  showPatientSuccessModal: boolean;
  paymentError: string | null;
};

type PaymentStoreActions = {
  createOrder: (
    data: CreatePaymentOrderRequest
  ) => Promise<CreatePaymentOrderResponse | null>;
  checkPaymentStatus: (orderId: string) => Promise<PaymentStatus | null>;
  verifyPaymentSignature: (data: {
    orderId: string;
    paymentId: string;
    signature: string;
  }) => Promise<boolean>;
  setShowPaymentModal: (show: boolean) => void;
  setShowSuccessModal: (show: boolean) => void;
  setShowFailureModal: (show: boolean) => void;
  setShowPatientSuccessModal: (show: boolean) => void;
  resetPaymentState: () => void;
  handlePaymentSuccess: () => void;
  handlePaymentFailure: (error: string) => void;
  openRazorpayPayment: (
    orderData: CreatePaymentOrderResponse,
    patientName: string,
    onSuccess: (response: RazorpayResponse) => void,
    onFailure: (error: any) => void
  ) => Promise<void>;
};

type PaymentStore = PaymentStoreState & PaymentStoreActions;

const initialState: PaymentStoreState = {
  currentOrder: null,
  paymentStatus: null,
  isCreatingOrder: false,
  isVerifyingPayment: false,
  isCheckingStatus: false,
  showPaymentModal: false,
  showSuccessModal: false,
  showFailureModal: false,
  showPatientSuccessModal: false,
  paymentError: null,
};

export const usePaymentStore = create<PaymentStore>((set) => ({
  ...initialState,

  createOrder: async (data: CreatePaymentOrderRequest) => {
    try {
      set({ isCreatingOrder: true });
      const response = await createPaymentOrder(data);
      set({ currentOrder: response, isCreatingOrder: false });
      return response;
    } catch (error) {
      console.error('Error creating payment order:', error);
      set({ isCreatingOrder: false });
      toast.error(getErrorMessage(error, 'Failed to create payment order'));
      return null;
    }
  },

  checkPaymentStatus: async (orderId: string) => {
    try {
      set({ isCheckingStatus: true });
      const status = await getPaymentStatus(orderId);
      set({ paymentStatus: status, isCheckingStatus: false });
      return status;
    } catch (error) {
      console.error('Error checking payment status:', error);
      set({ isCheckingStatus: false });
      toast.error(getErrorMessage(error, 'Failed to check payment status'));
      return null;
    }
  },

  verifyPaymentSignature: async (data) => {
    try {
      set({ isVerifyingPayment: true });
      const result = await verifyPayment(data);
      set({ isVerifyingPayment: false });
      return result.verified;
    } catch (error) {
      console.error('Error verifying payment:', error);
      set({ isVerifyingPayment: false });
      toast.error(getErrorMessage(error, 'Failed to verify payment'));
      return false;
    }
  },

  setShowPaymentModal: (show: boolean) => {
    set({ showPaymentModal: show });
  },

  setShowSuccessModal: (show: boolean) => {
    set({ showSuccessModal: show });
  },

  setShowFailureModal: (show: boolean) => {
    set({ showFailureModal: show });
  },

  setShowPatientSuccessModal: (show: boolean) => {
    set({ showPatientSuccessModal: show });
  },

  resetPaymentState: () => {
    set({
      currentOrder: null,
      paymentStatus: null,
      showPaymentModal: false,
      showSuccessModal: false,
      showFailureModal: false,
      showPatientSuccessModal: false,
      paymentError: null,
    });
  },

  handlePaymentSuccess: () => {
    set({ showSuccessModal: true });
  },

  handlePaymentFailure: (error: string) => {
    set({ showFailureModal: true, paymentError: error });
  },

  openRazorpayPayment: async (orderData, patientName, onSuccess, onFailure) => {
    try {
      if (!orderData.success || !orderData.data) {
        throw new Error('Invalid order data');
      }

      const { orderId, keyId, amount, currency, description } = orderData.data;
      const razorpayKeyId = getRazorpayKeyId() || keyId;

      const options = {
        key: razorpayKeyId,
        amount: amount,
        currency: currency,
        name: 'ARCA EHR',
        description: description,
        order_id: orderId,
        handler: (response: RazorpayResponse) => {
          console.log('Payment successful:', response);
          onSuccess(response);
        },
        prefill: {
          name: patientName,
          email: '',
          contact: '',
        },
        theme: {
          color: '#3399cc',
        },
        modal: {
          ondismiss: () => {
            console.log('Payment modal dismissed');
            onFailure(new Error('Payment cancelled by user'));
          },
        },
      };

      await openRazorpayCheckout(options);
    } catch (error) {
      console.error('Error opening Razorpay checkout:', error);
      onFailure(error);
    }
  },
}));
