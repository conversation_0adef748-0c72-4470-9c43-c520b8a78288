import React from 'react';

interface PrintButtonProps {
  disabled: boolean;
  onClick: () => void;
}

const PrintButton: React.FC<PrintButtonProps> = ({ disabled, onClick }) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`px-4 py-2 font-bold text-black bg-white border border-black rounded hover:bg-gray-100 disabled:opacity-50 font-archivo`}
    >
      Print
    </button>
  );
};

export default PrintButton;
