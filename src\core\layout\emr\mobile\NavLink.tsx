import React, { FC, memo, useEffect } from 'react';

import ButtonBase from '@mui/material/ButtonBase';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { cn } from '@/lib/utils';

import usePermission from '@/hooks/use-permission';

import colors from '@/utils/colors';

import { NavItems } from '@/core/configs/emr/navigation-mobile';

type Props = NavItems & {
  hideText: boolean;
  textRefs: React.MutableRefObject<Record<string, HTMLSpanElement | null>>;
  checkOverflow: () => void;
};

const { common, text } = colors;

const NavLink: FC<Props> = ({
  label,
  icon,
  path,
  department,
  hideText,
  textRefs,
  checkOverflow,
}) => {
  const pathName = usePathname();
  const { hasDepartment } = usePermission();
  const isActive = pathName === path;

  useEffect(() => {
    checkOverflow();
  }, [checkOverflow]);

  if (!hasDepartment(department)) {
    return null;
  }

  return (
    <ButtonBase
      component={Link}
      href={path}
      className={cn(
        'flex flex-col items-center justify-center !p-1 !rounded-lg',
        'flex-1 min-w-0'
      )}
      style={{
        color: isActive ? common.azureBlue : text.steelGray,
        backgroundColor: isActive ? common.lightSkyBlue : 'transparent',
      }}
      TouchRippleProps={{
        center: false,
      }}
    >
      <div className={cn('mb-1', hideText ? 'text-lg' : '')}>{icon}</div>
      {!hideText && (
        <span
          ref={(el) => {
            textRefs.current[label as string] = el;
          }}
          className="text-xs w-full text-center truncate"
        >
          {label}
        </span>
      )}
    </ButtonBase>
  );
};

export default memo(NavLink);
