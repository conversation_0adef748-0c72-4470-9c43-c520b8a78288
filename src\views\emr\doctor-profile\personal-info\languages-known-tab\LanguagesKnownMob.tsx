import React, {
  Dispatch,
  FC,
  SetStateAction,
  useCallback,
  useMemo,
} from 'react';

import {
  Control,
  Controller,
  FieldArrayWithId,
  Path,
  UseFormGetValues,
  useWatch,
} from 'react-hook-form';

import Box from '@mui/material/Box';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import Stack from '@mui/material/Stack';
import { capitalize } from 'lodash';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import { getNestedValue } from '@/utils/emr/doctor-profile/personal-info';

import PencilIcon from '@/assets/svg/PencilIcon';

import {
  fluencyLevels,
  languagesOptions,
} from '@/constants/emr/doctor-profile/personal-info';

import AddButton from '@/emr/components/lifestyle/lifestyle-forms/shared/AddButton';

import { ItemToDelete } from '@/types/emr/doctor-profile/personal-info';

import { checkboxStyles, SelectField } from '../Components';
import MobileAddDeleteButtons from '../shared/MobileAddDeleteButtons';

import { FormData, LanguageKnownType } from '.';

type Props = {
  control: Control<FormData>;
  fields: FieldArrayWithId<FormData, 'languagesKnown', 'id'>[];
  handleOnDelete: (_itemToDelete: ItemToDelete) => void;
  editableField: Set<string>;
  getValues: UseFormGetValues<FormData>;
  isSubmitted: boolean;
  setEditableField: Dispatch<SetStateAction<Set<string>>>;
  onAdd: () => void;
  lastFieldRef: React.RefObject<HTMLDivElement>;
};

const LanguagesKnownMob: FC<Props> = ({
  control,
  fields,
  handleOnDelete,
  editableField,
  getValues,
  isSubmitted,
  setEditableField,
  onAdd,
  lastFieldRef,
}) => {
  const { doctorProfile } = useDoctorStore();
  const isMobile = useIsMobile();
  const languagesKnowns = useWatch({
    control,
    name: 'languagesKnown',
  });

  const hideAddButton = useMemo<boolean>(() => {
    if (!isMobile) return true;
    return fields?.length >= languagesOptions?.length;
  }, [fields?.length, isMobile]);

  const getFilteredLanguagesOpt = useCallback(
    (value: string) => {
      const fieldValues = getValues()?.languagesKnown;
      const languages = languagesOptions?.filter((language) =>
        fieldValues?.every(
          (field) =>
            field?.language !== language?.value || field?.language === value
        )
      );
      return languages ?? [];
    },
    [getValues]
  );

  const handleEditClick = useCallback(
    (fieldName: Path<FormData>) => {
      setEditableField((prev) => new Set(prev.add(fieldName)));
    },
    [setEditableField]
  );

  const isFieldDisabled = useCallback(
    (fieldName: Path<FormData>) => {
      if (editableField.has(fieldName)) {
        return false;
      }

      const formValues = getValues();
      const fieldValue = getNestedValue(formValues, fieldName);
      const doctorFieldValue = getNestedValue(doctorProfile, fieldName);

      return (
        !!doctorFieldValue ||
        (isSubmitted && !!fieldValue && fieldValue === doctorFieldValue)
      );
    },
    [editableField, getValues, doctorProfile, isSubmitted]
  );

  const renderEditIcon = useCallback(
    (fieldName: Path<FormData>) => {
      return isFieldDisabled(fieldName) ? (
        <button type="button" onClick={() => handleEditClick(fieldName)}>
          <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
        </button>
      ) : null;
    },
    [isFieldDisabled, handleEditClick]
  );
  return (
    <>
      {fields.map((field, i) => (
        <Stack
          key={field?.id}
          spacing={1}
          ref={i === fields.length - 1 ? lastFieldRef : null}
        >
          <Controller
            control={control}
            name={`languagesKnown.${i}.language`}
            render={({ field }) => (
              <SelectField
                label={`Language ${i + 1}`}
                options={getFilteredLanguagesOpt(field.value)}
                endDecoration={renderEditIcon(`languagesKnown.${i}.language`)}
                disabledInput={isFieldDisabled(`languagesKnown.${i}.language`)}
                {...field}
              />
            )}
          />
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            spacing={1}
          >
            <Box pl={2}>
              {fluencyLevels.map((level) => (
                <Controller
                  key={level}
                  name={`languagesKnown.${i}.fluency.${level as keyof LanguageKnownType['fluency']}`}
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          sx={checkboxStyles}
                          checked={Boolean(field.value)}
                          onChange={(e) => field.onChange(e.target.checked)}
                          disabled={isFieldDisabled(
                            `languagesKnown.${i}.language`
                          )}
                        />
                      }
                      label={capitalize(level)}
                    />
                  )}
                />
              ))}
            </Box>
            {languagesKnowns[i]?.language && (
              <MobileAddDeleteButtons
                onDelete={() =>
                  handleOnDelete({ index: i, uuId: field.language })
                }
              />
            )}
          </Stack>
        </Stack>
      ))}
      {!hideAddButton && (
        <div className="fixed bottom-36 md:static right-6 bg-transparent">
          <AddButton size="medium" onClick={onAdd} />
        </div>
      )}
    </>
  );
};

export default LanguagesKnownMob;
