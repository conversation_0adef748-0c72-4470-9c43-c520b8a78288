import React, { memo } from 'react';

type Variant = 'small' | 'normal' | 'large';

type Props = {
  title: string;
  icon?: React.ReactNode;
  variant?: Variant;
  className?: string;
};

const titleVariants: Record<Variant, string> = {
  small: 'font-semibold text-sm md:text-base',
  normal: 'font-semibold text-lg ',
  large: 'font-semibold text-xl',
};

const TitleWithIcon: React.FC<Props> = ({
  icon,
  title,
  variant = 'normal',
  className,
}) => {
  return (
    <div
      className={`flex items-center gap-4 flex-1 ${className} ${titleVariants[variant]}`}
    >
      {icon && <span>{icon}</span>}
      <span>{title}</span>
    </div>
  );
};

export default memo(TitleWithIcon);
