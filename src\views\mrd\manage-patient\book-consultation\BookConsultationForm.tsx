import React, { memo, useCallback } from 'react';

import { useFieldArray, useFormContext } from 'react-hook-form';

import { getAppointmentId } from '@/utils/mrd/manage-patient/get-appointment-id';

import {
  ConsultationForm,
  defaultConsultation,
} from '@/types/mrd/manage-patient/consultation';

import ConsultationItem from './ConsultationItem';

const BookConsultationForm = () => {
  const { control } = useFormContext<{ consultation: ConsultationForm[] }>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'consultation',
  });

  const onAdd = useCallback(() => {
    append({ ...defaultConsultation, id: getAppointmentId() });
  }, [append]);

  const onDelete = useCallback(
    (index: number) => {
      remove(index);
    },
    [remove]
  );

  return (
    <>
      {fields.map((field, i) => {
        const queueId = (field as any).queueId;
        const isDisabled =
          !!(field as any).queueId || field.paymentStatus === 'PAID';
        return (
          <ConsultationItem
            key={field.id}
            index={i}
            onAdd={onAdd}
            onDelete={() => onDelete(i)}
            queueId={queueId}
            paymentStatus={field.paymentStatus}
            disabled={isDisabled}
          />
        );
      })}
    </>
  );
};

export default memo(BookConsultationForm);
