import React from 'react';

import { FieldError, UseFormRegisterReturn } from 'react-hook-form';

interface RadioOption {
  value: string;
  label: string;
}

interface CustomRadioGroupProps {
  label?: string;
  options: RadioOption[];
  register: UseFormRegisterReturn;
  defaultValue?: string;
  className?: string;
  errors?: FieldError;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
}

/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the app radio group component instead.
 */
const CustomRadioGroup: React.FC<CustomRadioGroupProps> = ({
  label,
  options,
  register,
  defaultValue,
  className = '',
  errors,
  required = false,
  ...rest
}) => {
  return (
    <div className={`flex flex-col gap-2 ${className}`}>
      {label && (
        <span className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-0.5">*</span>}
        </span>
      )}
      <div className="flex items-center gap-4">
        {options.map((option: RadioOption) => (
          <label
            key={option.value}
            className="flex items-center cursor-pointer"
          >
            <input
              type="radio"
              value={option.value}
              className="w-4 h-4 accent-black border-gray-300 focus:ring-black"
              defaultChecked={defaultValue === option.value}
              {...register}
              {...rest}
            />
            <span className="ml-2 text-sm">{option.label}</span>
          </label>
        ))}
      </div>
      {errors?.message && (
        <span className="text-red-500 text-xs">{errors.message}</span>
      )}
    </div>
  );
};

export default CustomRadioGroup;
