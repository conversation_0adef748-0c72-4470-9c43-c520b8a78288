import React, { memo, useCallback, useEffect, useState } from 'react';

import { useFieldArray, useForm } from 'react-hook-form';

import { Stack } from '@mui/material';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import {
  discardDefaultValues,
  getUniqueId,
} from '@/utils/emr/doctor-profile/personal-info';

import { profileTabs } from '@/constants/emr/doctor-profile/personal-info';

import AddButton from '@/emr/components/lifestyle/lifestyle-forms/shared/AddButton';
import { useFileUpload } from '@/emr/hooks/use-file-upload';

import {
  defaultExperience,
  Experience,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import DeleteModal from '../shared/DeleteModal';
import SaveButton from '../shared/SaveButton';
import Title from '../shared/Title';

import EmploymentBgMob from './EmploymentBgMob';
import EmploymentBgTable from './EmploymentBgTable';

export type FormData = {
  experience: Experience[];
};

const EmploymentBackground = () => {
  const isMobile = useIsMobile();

  const [itemToEdit, setItemToEdit] = useState<number | null>(null);
  const [itemToDelete, setItemToDelete] = useState<ItemToDelete>(null);
  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);

  const { data: userData } = useUserStore();

  const {
    updateDoctorProfile,
    doctorProfile,
    isDeleting,
    createDoctorProfile,
    setTabName,
    deleteDoctorProfileTableItem,
  } = useDoctorStore();

  const { fileUpload } = useFileUpload({
    userId: doctorProfile?.id || userData?.id,
    type: 'experience',
  });

  const { handleSubmit, control, reset, getValues } = useForm<FormData>({
    defaultValues: { experience: [isMobile ? defaultExperience : undefined] },
  });

  const { append, fields, remove } = useFieldArray<FormData>({
    control,
    name: 'experience',
    shouldUnregister: true,
  });

  const onSubmit = useCallback(
    async (data: FormData) => {
      const experience = discardDefaultValues(
        data?.experience,
        defaultExperience
      );

      const finalData = await Promise.all(
        experience.map(async (item) => {
          const doc1 = await fileUpload(item?.doc1);
          const doc2 = await fileUpload(item?.doc2);

          return {
            ...item,
            doc1,
            doc2,
            uuId: getUniqueId(item?.uuId, 'EXP'),
          };
        })
      );

      if (doctorProfile?.id) {
        updateDoctorProfile(doctorProfile.id, {
          professionalDetails: {
            ...doctorProfile?.professionalDetails,
            experience: finalData,
          },
        });
      } else {
        createDoctorProfile({
          professionalDetails: {
            ...doctorProfile?.professionalDetails,
            experience: finalData,
          },
          username: userData?.email,
        });
      }
      setItemToEdit(null);
      setIsSubmitted(true);
      setEditableField(new Set());
    },
    [
      createDoctorProfile,
      doctorProfile,
      updateDoctorProfile,
      userData?.email,
      fileUpload,
    ]
  );

  const handleItemEdit = useCallback(
    (index: number) => () => {
      setItemToEdit(index);
    },
    []
  );

  const handleOnAdd = useCallback(() => {
    setItemToEdit(fields.length);
    append(defaultExperience);
  }, [append, fields?.length]);

  const handleOnDelete = useCallback((item: ItemToDelete) => {
    setItemToDelete(item);
  }, []);

  const handleDelete = useCallback(async () => {
    if (itemToDelete) {
      if (itemToDelete?.uuId && doctorProfile?.id) {
        const experiences = doctorProfile?.professionalDetails?.experience;

        const filteredExperience = experiences?.filter(
          (item) => item?.uuId !== itemToDelete?.uuId
        );

        await deleteDoctorProfileTableItem(doctorProfile.id, {
          professionalDetails: {
            ...doctorProfile?.professionalDetails,
            experience: filteredExperience,
          },
        });
      } else {
        remove(itemToDelete?.index);
      }
      setItemToDelete(null);
    }
  }, [
    itemToDelete,
    doctorProfile?.id,
    doctorProfile?.professionalDetails,
    deleteDoctorProfileTableItem,
    remove,
  ]);

  const handleCancel = useCallback(() => {
    setItemToDelete(null);
  }, []);

  useEffect(() => {
    if (doctorProfile?.professionalDetails?.experience?.length) {
      reset({ experience: doctorProfile?.professionalDetails?.experience });
    } else if (isMobile) {
      reset({ experience: [defaultExperience] });
    }
  }, [doctorProfile?.professionalDetails?.experience, reset, isMobile]);

  useEffect(() => {
    setTabName(profileTabs.EMPLOYMENT_BACKGROUND);
  }, [setTabName]);

  return (
    <Stack
      component="form"
      spacing={{ xs: 3, md: 2 }}
      pb={{ xs: 8, md: 0 }}
      onSubmit={handleSubmit(onSubmit)}
    >
      <Title
        title="Employment Background"
        onAdd={handleOnAdd}
        showEndButton={!isMobile}
      />
      {isMobile ? (
        <EmploymentBgMob
          fields={fields}
          control={control}
          editableField={editableField}
          getValues={getValues}
          isSubmitted={isSubmitted}
          setEditableField={setEditableField}
          handleOnDelete={handleOnDelete}
        />
      ) : (
        <EmploymentBgTable
          fields={fields}
          control={control}
          itemToEdit={itemToEdit}
          handleItemEdit={handleItemEdit}
          handleOnDelete={handleOnDelete}
        />
      )}
      {isMobile && (
        <div className="fixed bottom-34 md:static right-6 bg-transparent">
          <AddButton size="medium" onClick={handleOnAdd} />
        </div>
      )}
      <SaveButton />
      <DeleteModal
        open={!!itemToDelete}
        onClose={handleCancel}
        onDelete={handleDelete}
        isLoading={isDeleting}
      />
    </Stack>
  );
};

export default memo(EmploymentBackground);
