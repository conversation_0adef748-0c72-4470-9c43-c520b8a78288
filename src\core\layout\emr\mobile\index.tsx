'use client';

import { memo, useEffect } from 'react';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useMobileMenuStore } from '@/store/mobileMenuStore';

import colors from '@/utils/colors';
import { menu, MobileMenuType } from '@/utils/constants/mobileMenu';

import BottomNavigation from './BottomNavigation';
import TopBar from './TopBar';

const MobileLayout = ({
  children,
}: Readonly<{ children: React.ReactNode }>) => {
  const isMobile = useIsMobile();
  const { anchorEl, mobileMenuPage } = useMobileMenuStore();

  useEffect(() => {
    const setHeight = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    setHeight();
    window.addEventListener('resize', setHeight);

    return () => {
      window.removeEventListener('resize', setHeight);
    };
  }, []);

  if (!isMobile) return <>{children}</>;

  const openMenuList =
    Boolean(anchorEl) ||
    Object.values(menu).includes(mobileMenuPage as MobileMenuType);

  return (
    <div
      className="flex flex-col h-screen w-screen relative bg-gray-100"
      id="mobile-layout"
    >
      <div className="h-10 bg-white absolute top-0 left-0 right-0 z-50 shadow-[0_4px_10px_rgba(0,0,0,0.1)]">
        <TopBar />
      </div>
      <div
        className="flex-1 overflow-y-hidden pt-10"
        style={{ backgroundColor: colors.common.ashGray }}
      >
        <div className="w-[calc(100%-0.75rem)]  max-w-[calc(100%-0.75rem)] min-h-full h-fit bg-white m-1.5 rounded-lg">
          {children}
        </div>
      </div>

      {!openMenuList ? (
        <div className="fixed bottom-0 left-0 right-0 z-[9999] shadow-[0_-4px_10px_rgba(0,0,0,0.1)]">
          <BottomNavigation />
        </div>
      ) : undefined}
    </div>
  );
};

export default memo(MobileLayout);
