import React, { memo } from 'react';

const ReportIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 24 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.23256 4.48828H3.32558C2.0412 4.48828 1 5.52948 1 6.81386V23.0929C1 24.3774 2.0412 25.4185 3.32558 25.4185H10.3023"
        stroke="white"
        stroke-width="2"
        strokeLinecap="round"
      />
      <path
        d="M14.3721 4.48828H17.279C18.5635 4.48828 19.6046 5.52948 19.6046 6.81386V17.279"
        stroke="white"
        stroke-width="2"
        strokeLinecap="round"
      />
      <path
        d="M5.65137 7.27907V5.06977C5.65137 4.74867 5.91167 4.48837 6.23276 4.48837C6.55386 4.48837 6.81907 4.22795 6.87407 3.9116C7.04634 2.92059 7.71415 1 10.3025 1C12.8909 1 13.5587 2.92059 13.731 3.9116C13.786 4.22795 14.0513 4.48837 14.3723 4.48837C14.6933 4.48837 14.9537 4.74867 14.9537 5.06977V7.27907C14.9537 7.66438 14.6414 7.97674 14.256 7.97674H6.34904C5.96373 7.97674 5.65137 7.66438 5.65137 7.27907Z"
        stroke="white"
        stroke-width="2"
        strokeLinecap="round"
      />
      <path d="M15 20H24" stroke="white" strokeLinecap="round" />
      <path d="M15 23H24" stroke="white" strokeLinecap="round" />
      <path d="M15 26H24" stroke="white" strokeLinecap="round" />
    </svg>
  );
};

export default memo(ReportIcon);
