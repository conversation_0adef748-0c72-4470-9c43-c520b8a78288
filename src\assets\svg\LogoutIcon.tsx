import { IconProps } from '@/types/icon';

export default function LogoutIcon(props: IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clip-path="url(#clip0_40002223_3029)">
        <path
          d="M12 22C10.6167 22 9.31667 21.7375 8.1 21.2125C6.88333 20.6875 5.825 19.975 4.925 19.075C4.025 18.175 3.3125 17.1167 2.7875 15.9C2.2625 14.6833 2 13.3833 2 12C2 10.7833 2.20417 9.62917 2.6125 8.5375C3.02083 7.44583 3.58333 6.46667 4.3 5.6C4.48333 5.36667 4.70417 5.25833 4.9625 5.275C5.22083 5.29167 5.44167 5.38333 5.625 5.55C5.80833 5.71667 5.92917 5.93333 5.9875 6.2C6.04583 6.46667 5.95833 6.74167 5.725 7.025C5.19167 7.70833 4.77083 8.47083 4.4625 9.3125C4.15417 10.1542 4 11.05 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76667 20 12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12C20 11.05 19.8458 10.1542 19.5375 9.3125C19.2292 8.47083 18.8083 7.70833 18.275 7.025C18.0417 6.74167 17.9542 6.46667 18.0125 6.2C18.0708 5.93333 18.1917 5.71667 18.375 5.55C18.5583 5.38333 18.7792 5.29167 19.0375 5.275C19.2958 5.25833 19.5167 5.36667 19.7 5.6C20.4167 6.46667 20.9792 7.44583 21.3875 8.5375C21.7958 9.62917 22 10.7833 22 12C22 13.3833 21.7375 14.6833 21.2125 15.9C20.6875 17.1167 19.975 18.175 19.075 19.075C18.175 19.975 17.1167 20.6875 15.9 21.2125C14.6833 21.7375 13.3833 22 12 22ZM12 13C11.7167 13 11.4792 12.9042 11.2875 12.7125C11.0958 12.5208 11 12.2833 11 12V3C11 2.71667 11.0958 2.47917 11.2875 2.2875C11.4792 2.09583 11.7167 2 12 2C12.2833 2 12.5208 2.09583 12.7125 2.2875C12.9042 2.47917 13 2.71667 13 3V12C13 12.2833 12.9042 12.5208 12.7125 12.7125C12.5208 12.9042 12.2833 13 12 13Z"
          fill="#001926"
        />
      </g>
      <defs>
        <clipPath id="clip0_40002223_3029">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
