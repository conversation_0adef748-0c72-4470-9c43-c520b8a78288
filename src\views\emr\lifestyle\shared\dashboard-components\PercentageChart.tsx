import React from 'react';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
  <PERSON>,
  Tooltip,
} from 'recharts';

import { ChartTitle, LegendItem } from './ChartTitle';
import { CustomBarCursor } from './CustomBarCursor';

import { calculateBarWidth } from './utils/chartUtils';

interface DataPoint {
  [key: string]: any;
  date: string;
}

interface ChartDataItem {
  key: string;
  name: string;
  color: string;
  radius?: [number, number, number, number];
}

interface PercentageChartProps<T extends DataPoint> {
  data: T[];
  dataKeys: ChartDataItem[];
  title?: string;
  height?: number;
  showLegend?: boolean;
  maxBarWidth?: number;
  xAxisKey?: string;
}

const PercentageChart = <T extends DataPoint>({
  data,
  dataKeys,
  title = '',
  height = 286,
  showLegend = false,
  maxBarWidth = 35,
  xAxisKey = 'date',
}: PercentageChartProps<T>) => {
  // Custom tick component for X-axis
  const CustomizedAxisTick = (props: any) => {
    const { x, y, payload } = props;
    const date = new Date(payload.value);
    const day = date.getDate();
    const month = date.toLocaleString('en-US', { month: 'short' });

    return (
      <g transform={`translate(${x},${y})`}>
        <text
          x={0}
          y={0}
          dy={12}
          textAnchor="middle"
          fill="#6b7280"
          fontSize={11}
        >
          {day}
        </text>
        <text
          x={0}
          y={12}
          dy={12}
          textAnchor="middle"
          fill="#6b7280"
          fontSize={9}
        >
          {month}
        </text>
      </g>
    );
  };

  interface TooltipProps {
    active?: boolean;
    payload?: Array<{
      dataKey: string;
      value: number;
      name: string;
      color: string;
    }>;
    label?: string | number | Date;
    dataKeys: ChartDataItem[];
    xAxisKey: string;
  }

  const CustomTooltip = ({
    active,
    payload,
    label,
    dataKeys,
    xAxisKey,
  }: TooltipProps) => {
    if (active && payload && payload.length) {
      const labelString = label ? String(label) : '';
      const date = label ? new Date(labelString) : new Date();
      const isValidDate = !isNaN(date.getTime());

      // Create a map of the payload items for easier access
      const payloadMap = new Map(payload.map((item) => [item.dataKey, item]));

      // Define the desired order: Fat, Carbs, Protein
      const orderedItems = [
        { key: 'fat_percentage', name: 'Fat' },
        { key: 'carbs_percentage', name: 'Carbs' },
        { key: 'protein_percentage', name: 'Protein' },
      ];

      return (
        <div className="bg-white p-2 border border-gray-200 rounded shadow-md">
          {isValidDate && label ? (
            <p className="text-sm font-medium">
              {date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
              })}
            </p>
          ) : (
            label && <p className="text-sm font-medium">{labelString}</p>
          )}
          {orderedItems.map((item) => {
            const entry = payloadMap.get(item.key);
            if (!entry) return null;

            const dataItem = dataKeys.find((d) => d.key === entry.dataKey);
            const color = dataItem?.color || entry.color;
            const name = dataItem?.name || entry.name;

            return (
              <p key={item.key} className="text-sm" style={{ color }}>
                {name}: {entry.value.toFixed(1)}%
              </p>
            );
          })}
        </div>
      );
    }
    return null;
  };

  // Custom cursor component with dynamic width
  const CustomCursor = (props: any) => (
    <CustomBarCursor
      {...props}
      barWidth={calculateBarWidth({
        dataLength: data.length,
        maxBarWidth,
        margin: 50,
      })}
    />
  );

  // Create legend items only if showLegend is true
  const legendItems = showLegend
    ? dataKeys.map((item) => (
        <LegendItem key={item.key} color={item.color} label={item.name} />
      ))
    : null;

  return (
    <div className="w-full flex flex-col" style={{ height: `${height}px` }}>
      <ChartTitle
        title={title}
        className="px-4 pt-3 pb-3"
        rightContent={
          showLegend ? (
            <div className="flex items-center gap-3">{legendItems}</div>
          ) : null
        }
      />
      <div className="flex-1">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{
              top: 20,
              right: 25,
              left: 25,
              bottom: 2,
            }}
            barGap={0}
            barCategoryGap="10%"
          >
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#e5e7eb"
              horizontal={true}
              vertical={true}
            />
            <XAxis
              dataKey={xAxisKey}
              axisLine={false}
              tickLine={false}
              interval={0}
              minTickGap={1}
              height={50}
              tick={<CustomizedAxisTick />}
              tickMargin={10}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              domain={[0, 100]}
              ticks={[0, 20, 40, 60, 80, 100]}
              tickFormatter={(value) => `${value}%`}
              width={40}
              tickMargin={8}
            />
            <Tooltip
              content={({ active, payload, label }) => (
                <CustomTooltip
                  active={active}
                  payload={payload}
                  label={label}
                  dataKeys={dataKeys}
                  xAxisKey={xAxisKey}
                />
              )}
              cursor={<CustomCursor />}
            />
            <Legend wrapperStyle={{ display: 'none' }} />
            {dataKeys.map((item, index) => (
              <Bar
                key={item.key}
                dataKey={item.key}
                stackId="a"
                fill={item.color}
                name={item.name}
                radius={item.radius || [4, 4, 0, 0]}
                maxBarSize={maxBarWidth}
                className="transition-all duration-200 hover:opacity-90"
                style={{
                  cursor: 'pointer',
                  transition: 'all 0.2s ease-in-out',
                }}
              />
            ))}
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default PercentageChart;
