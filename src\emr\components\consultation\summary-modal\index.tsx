import { useForm } from 'react-hook-form';

import { BiSave, BiX } from 'react-icons/bi';

import { SummarizeConversationRes } from '@/query/speech';

import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import PrimaryButton from '@/core/components/primary-button';

import SummaryForm from '../summary-form';

export const SummaryModalMode = {
  Generated: 'Generated',
  Manual: 'Manual',
} as const;

type SummaryModalMode =
  (typeof SummaryModalMode)[keyof typeof SummaryModalMode];
type Data = SummarizeConversationRes;

export type SummaryModalProps = {
  isOpen?: boolean;
  data?: Data;
  mode?: SummaryModalMode;
  onClose?: () => void;
  onSave?: (data: Data['summary']) => void;
  loading?: boolean;
};

export default function SummaryModal({
  isOpen,
  data,
  mode = SummaryModalMode.Generated,
  onClose = () => {},
  onSave = () => {},
  loading,
}: SummaryModalProps) {
  const form = useForm<SummarizeConversationRes['summary']>();

  const handleOpenChange = (open: boolean) => {
    if (!open && onClose) {
      onClose();
    }
  };

  const handleSave = form.handleSubmit((submitted) => {
    onSave(submitted);
  });

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="p-0 max-h-[95vh] overflow-auto  rounded-thin-scrollbar max-w-202 gap-0">
        <DialogHeader className="bg-white sticky top-0 z-10 flex flex-row items-center justify-between border-b border-[#C2CDD6]">
          <DialogTitle className="py-3 pl-6.5 pr-7.5 text-lg font-semibold">
            {`Consultation Summary`}
          </DialogTitle>
          <button
            className="absolute border right-6.5 top-0 p-1 rounded-full hover:bg-black/5"
            onClick={onClose}
            aria-label="Close"
            type="button"
          >
            <BiX size={22} />
          </button>
        </DialogHeader>

        <SummaryForm
          className="py-5 pl-6.5 pr-7.5"
          editable={mode === SummaryModalMode.Manual}
          expanded={mode === SummaryModalMode.Generated}
          data={data}
          form={form}
        />

        <DialogFooter className="sticky bottom-0 bg-white border-t border-t-[#C2CDD6]">
          <div className="flex w-full px-6.5 py-3 justify-start">
            <PrimaryButton
              className="py-2.5 px-5 bg-black text-white hover:bg-black/80 rounded-full flex items-center gap-[5px]"
              onClick={handleSave}
              isLoading={loading}
            >
              Save Changes
              <BiSave />
            </PrimaryButton>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
