import { styled } from '@mui/material';

export const StyledDatePicker = styled('div')(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    height: 40,
    fontSize: '0.85rem',
    '& .MuiInputBase-input': {
      padding: { xs: '11px 14px', md: '8.5px 14px' },
    },
    '& fieldset': {
      border: `1px solid ${theme.palette.grey[600]} !important`,
      transition: theme.transitions.create(['border-color', 'box-shadow']),
    },
    '&:hover fieldset': {
      borderColor: theme.palette.primary.main,
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.primary.main,
    },
  },
  '& .MuiOutlinedInput-input.Mui-disabled': {
    opacity: 1,
    color: 'black !important',
    WebkitTextFillColor: 'black !important',
  },
  '& fieldset': {
    border: theme.shape.border,
  },
}));
