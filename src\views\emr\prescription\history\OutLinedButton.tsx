import React, { FC, memo } from 'react';

import { Button, ButtonProps } from '@mui/material';

const OutlinedButton: FC<Omit<ButtonProps, 'variant'>> = ({ sx, ...props }) => {
  return (
    <Button
      variant="outlined"
      sx={{
        py: 0,
        px: 1,
        textColor: 'black',
        maxHeight: '32px',
        backgroundColor: 'transparent',
        borderColor: '#8b8b8b',
        color: 'black',
        textTransform: 'none',
        '&:hover': {
          borderColor: '#8b8b8b',
        },
        '&:disabled': {
          borderColor: '#ccc',
          color: '#999',
          backgroundColor: 'transparent',
          opacity: 0.6,
        },
        ...sx,
      }}
      {...props}
    />
  );
};

export default memo(OutlinedButton);
