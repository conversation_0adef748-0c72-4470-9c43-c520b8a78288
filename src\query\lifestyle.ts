import { LifestyleNoteRecords } from '@/store/lifestyle-note-store';

import { LifestyleSource } from '@/emr/types/lifestyle';

import { DateType } from '@/core/components/date-range-picker/types';
import { arcaAxios } from '@/core/lib/interceptor';

const controllers = new Map<string, AbortController>();

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the from query/emr/lifestyle/index.ts.
 */
export const getLifestyleQuestions = async (source: LifestyleSource) => {
  const key = `getLifestyleQuestions-${source}`;
  if (controllers.has(key)) {
    controllers.get(key)!.abort();
  }
  const controller = new AbortController();
  controllers.set(key, controller);

  try {
    const response = await arcaAxios.get(
      `/lifestyle/question?source=${source}`,
      {
        signal: controller.signal,
      }
    );
    controllers.delete(key);
    return response;
  } catch (error) {
    controllers.delete(key);
    throw error;
  }
};

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the from query/emr/lifestyle/index.ts.
 */
export const getPatientLifestyle = async (
  patientId: string,
  source: LifestyleSource,
  fromDate?: DateType,
  toDate?: DateType
) => {
  const params = new URLSearchParams({
    patientId,
    source,
  });

  if (fromDate) params.append('fromDate', fromDate as string);
  if (toDate) params.append('toDate', toDate as string);

  return await arcaAxios.get(`/patient/lifestyle?${params.toString()}`);
};

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the from query/emr/lifestyle/index.ts.
 */
export const createLifestyleData = async (data: any, patientId: string) => {
  return await arcaAxios.post(
    `/patient/lifestyle?patientId=${patientId}`,
    data
  );
};

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the from query/emr/lifestyle/index.ts.
 */
export const updateLifestyleData = async (data: any, id: string) => {
  return await arcaAxios.patch(`/patient/lifestyle?id=${id}`, data);
};

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the from query/emr/lifestyle/index.ts.
 */
export const createLifestyleNote = async (data: LifestyleNoteRecords) => {
  return await arcaAxios.post(`/patient/lifestyle/note`, data);
};

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the from query/emr/lifestyle/index.ts.
 */
export const getLifestyleNotes = async (patientId: string) => {
  return await arcaAxios.get(`/patient/lifestyle/note?patientId=${patientId}`);
};

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the from query/emr/lifestyle/index.ts.
 */
export const updateLifestyleNotes = async (
  id: string,
  data: LifestyleNoteRecords
) => {
  return await arcaAxios.patch(`/patient/lifestyle/note?id=${id}`, data);
};
