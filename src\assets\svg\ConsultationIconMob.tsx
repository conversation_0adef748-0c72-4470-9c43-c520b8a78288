import React from 'react';
import type { SVGProps } from 'react';

const ConsultationIconMob = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M13.5 22q-2.7 0-4.6-1.9T7 15.5v-.575q-2.15-.35-3.575-2.013T2 9V4q0-.425.288-.712T3 3h2q0-.425.288-.712T6 2t.713.288T7 3v2q0 .425-.288.713T6 6t-.712-.288T5 5H4v4q0 1.65 1.175 2.825T8 13t2.825-1.175T12 9V5h-1q0 .425-.288.713T10 6t-.712-.288T9 5V3q0-.425.288-.712T10 2t.713.288T11 3h2q.425 0 .713.288T14 4v5q0 2.25-1.425 3.913T9 14.925v.575q0 1.875 1.313 3.188T13.5 20t3.188-1.312T18 15.5v-1.675q-.875-.325-1.437-1.088T16 11q0-1.25.875-2.125T19 8t2.125.875T22 11q0 .975-.562 1.738T20 13.825V15.5q0 2.7-1.9 4.6T13.5 22M19 12q.425 0 .713-.288T20 11t-.288-.712T19 10t-.712.288T18 11t.288.713T19 12m0-1"
      ></path>
    </svg>
  );
};

export default ConsultationIconMob;
