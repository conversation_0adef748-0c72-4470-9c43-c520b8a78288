import { TestItem } from '@/store/emr/lab/package-store';

import {
  ACTION_CELL_PROPS,
  CELL_TEXT_PROPS,
  MAX_ITEMS_PER_COLUMN,
} from '@/constants/emr/lab';

import ActionButton from '@/views/emr/doctor-profile/personal-info/shared/ActionButton';

import { TestSelectCheckbox } from './Components';

export function getTestRowsForDetailMode(
  testItems: TestItem[],
  useTwoColumns: boolean,
  toggleTestSelection: (id: string | number) => void
) {
  if (useTwoColumns) {
    const firstColumnItems = testItems.slice(0, MAX_ITEMS_PER_COLUMN);
    const secondColumnItems = testItems.slice(MAX_ITEMS_PER_COLUMN);

    return Array.from(
      { length: Math.max(firstColumnItems.length, secondColumnItems.length) },
      (_, index) => {
        const item1 = firstColumnItems[index];
        const item2 = secondColumnItems[index];

        return {
          checkbox1: item1
            ? {
                value: (
                  <TestSelectCheckbox
                    checked={item1.selected}
                    onChange={() => toggleTestSelection(item1.id)}
                  />
                ),
                cellProps: ACTION_CELL_PROPS,
              }
            : { value: null, cellProps: ACTION_CELL_PROPS },

          testName1: item1
            ? { value: item1.testName, cellProps: CELL_TEXT_PROPS }
            : { value: null, cellProps: CELL_TEXT_PROPS },
          checkbox2: item2
            ? {
                value: (
                  <TestSelectCheckbox
                    checked={item2.selected}
                    onChange={() => toggleTestSelection(item2.id)}
                  />
                ),
                cellProps: ACTION_CELL_PROPS,
              }
            : { value: null, cellProps: ACTION_CELL_PROPS },
          testName2: item2
            ? { value: item2.testName, cellProps: CELL_TEXT_PROPS }
            : { value: null, cellProps: CELL_TEXT_PROPS },
        };
      }
    );
  } else {
    return testItems.map((item) => ({
      checkbox1: {
        value: (
          <TestSelectCheckbox
            checked={item.selected}
            onChange={() => toggleTestSelection(item.id)}
          />
        ),
        cellProps: ACTION_CELL_PROPS,
      },
      testName1: {
        value: item.testName,
        cellProps: CELL_TEXT_PROPS,
      },
    }));
  }
}

export function getTestRowsForCreateMode(
  testItems: TestItem[],
  useTwoColumns: boolean,
  removeTest: (id: string | number) => void
) {
  if (useTwoColumns) {
    const firstColumnItems = testItems.slice(0, MAX_ITEMS_PER_COLUMN);
    const secondColumnItems = testItems.slice(MAX_ITEMS_PER_COLUMN);

    return Array.from(
      { length: Math.max(firstColumnItems.length, secondColumnItems.length) },
      (_, index) => {
        const item1 = firstColumnItems[index];
        const item2 = secondColumnItems[index];

        return {
          testName1: item1
            ? { value: item1.testName, cellProps: CELL_TEXT_PROPS }
            : { value: null, cellProps: CELL_TEXT_PROPS },
          close1: item1
            ? {
                value: (
                  <ActionButton
                    actionFor="close"
                    onClick={() => removeTest(item1.id)}
                    iconSize={14}
                  />
                ),
                cellProps: ACTION_CELL_PROPS,
              }
            : { value: null, cellProps: ACTION_CELL_PROPS },
          testName2: item2
            ? { value: item2.testName, cellProps: CELL_TEXT_PROPS }
            : { value: null, cellProps: CELL_TEXT_PROPS },
          close2: item2
            ? {
                value: (
                  <ActionButton
                    actionFor="close"
                    onClick={() => removeTest(item2.id)}
                    iconSize={14}
                  />
                ),
                cellProps: ACTION_CELL_PROPS,
              }
            : { value: null, cellProps: ACTION_CELL_PROPS },
        };
      }
    );
  } else {
    return testItems.map((item) => ({
      testName1: {
        value: item.testName,
        cellProps: CELL_TEXT_PROPS,
      },
      close1: {
        value: (
          <ActionButton
            actionFor="close"
            onClick={() => removeTest(item.id)}
            iconSize={14}
          />
        ),
        cellProps: ACTION_CELL_PROPS,
      },
    }));
  }
}
