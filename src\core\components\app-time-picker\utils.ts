import { TimeOption } from './types';

export const getCurrentTime = () => {
  const now = new Date();
  let hour = now.getHours();
  const minute = now.getMinutes();
  const period = hour >= 12 ? 'PM' : 'AM';
  hour = hour % 12 || 12;
  return {
    hour: hour.toString().padStart(2, '0'),
    minute: minute.toString().padStart(2, '0'),
    period,
  };
};

export const hourOptions: TimeOption[] = Array.from({ length: 12 }, (_, i) => {
  const val = (i + 1).toString().padStart(2, '0');
  return { value: val, label: val };
});

export const minuteOptions: TimeOption[] = Array.from(
  { length: 60 },
  (_, i) => {
    const val = i.toString().padStart(2, '0');
    return { value: val, label: val };
  }
);

export const periodOptions: TimeOption[] = [
  { value: 'AM', label: 'AM' },
  { value: 'PM', label: 'PM' },
];

export const convertToTime = (value: {
  hour: string;
  minute: string;
  period: string;
}) => {
  const hour = parseInt(value.hour, 10) % 12 || 12;
  const minute = parseInt(value.minute, 10);
  const period = value.period;
  return `${hour}:${minute.toString().padStart(2, '0')} ${period}`;
};

export const convertToTimeObject = (value?: string) => {
  if (!value) return getCurrentTime();
  if (value.includes('AM') || value.includes('PM')) {
    const [hour, min] = value.split(':');
    const [minute, period] = min.split(' ');

    return {
      hour: hour.toString().padStart(2, '0'),
      minute: minute.toString().padStart(2, '0'),
      period,
    };
  }
  const [hour, minute] = value.split(':');
  const period = parseInt(hour, 10) >= 12 ? 'PM' : 'AM';
  const hour12 = parseInt(hour, 10) % 12 || 12;
  return {
    hour: hour12.toString().padStart(2, '0'),
    minute,
    period,
  };
};

export const getCurrentTimeInString = () => convertToTime(getCurrentTime());
