# ARCA EMR Authentication Setup

Quick setup guide for Azure AD B2C authentication in ARCA EMR.

## Environment Configuration

Create `.env` file with your Azure AD B2C settings:

```env
# API Configuration
NEXT_PUBLIC_URL=http://localhost:3001
NEXT_PUBLIC_API_URL=your-api-url
NEXT_PUBLIC_SUBSCRIPTION_KEY=your-subscription-key

# Azure AD B2C Configuration
NEXT_PUBLIC_CLIENT_ID=your-client-id
NEXT_PUBLIC_TENANT_NAME=your-tenant-name
NEXT_PUBLIC_TENANT_ID=your-tenant-id
NEXT_PUBLIC_SIGNIN_POLICY=your-signin-policy
```

## Application Startup

```bash
npm install
npm run dev
```

## Testing Authentication

1. Navigate to the application
2. You'll be redirected to Azure AD B2C login
3. After successful login, you'll return to the application

## Troubleshooting

- Check environment variables are correct
- Verify Azure AD B2C tenant and user flows are active
- Review browser console for MSAL errors

---

_For detailed technical documentation, see [AUTHENTICATION_SYSTEM.md](./AUTHENTICATION_SYSTEM.md)_
