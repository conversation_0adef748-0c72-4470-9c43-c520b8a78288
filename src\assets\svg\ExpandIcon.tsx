import { IconProps } from '@/types/icon';

export default function ExpandIcon(props: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="13"
      height="13"
      viewBox="0 0 13 13"
      fill="none"
      {...props}
    >
      <rect width="13" height="13" fill="#1E1E1E" />
      <path
        d="M-2245 -657C-2245 -658.105 -2244.1 -659 -2243 -659H3183C3184.1 -659 3185 -658.105 3185 -657V2170C3185 2171.1 3184.1 2172 3183 2172H-2243C-2244.1 2172 -2245 2171.1 -2245 2170V-657Z"
        fill="#444444"
      />
      <path
        d="M-2243 -658H3183V-660H-2243V-658ZM3184 -657V2170H3186V-657H3184ZM3183 2171H-2243V2173H3183V2171ZM-2244 2170V-657H-2246V2170H-2244ZM-2243 2171C-2243.55 2171 -2244 2170.55 -2244 2170H-2246C-2246 2171.66 -2244.66 2173 -2243 2173V2171ZM3184 2170C3184 2170.55 3183.55 2171 3183 2171V2173C3184.66 2173 3186 2171.66 3186 2170H3184ZM3183 -658C3183.55 -658 3184 -657.552 3184 -657H3186C3186 -658.657 3184.66 -660 3183 -660V-658ZM-2243 -660C-2244.66 -660 -2246 -658.657 -2246 -657H-2244C-2244 -657.552 -2243.55 -658 -2243 -658V-660Z"
        fill="white"
        fillOpacity="0.1"
      />
      <g clipPath="url(#clip0_40000008_1249)">
        <rect x="-369" y="-207" width="412" height="1689" fill="#E7EBEF" />
        <g filter="url(#filter0_d_40000008_1249)">
          <path
            d="M25 -167C29.4183 -167 33 -163.418 33 -159L32.9999 1454C32.9999 1458.42 29.4182 1462 24.9999 1462L-351 1462C-355.418 1462 -359 1458.42 -359 1454L-359 -159C-359 -163.418 -355.418 -167 -351 -167L25 -167Z"
            fill="#FCFCFC"
          />
        </g>
        <g filter="url(#filter1_d_40000008_1249)">
          <rect
            x="27"
            y="-16"
            width="1461"
            height="380"
            rx="8"
            transform="rotate(90 27 -16)"
            fill="#E7EBEF"
          />
          <rect
            x="26.5"
            y="-15.5"
            width="1460"
            height="379"
            rx="7.5"
            transform="rotate(90 26.5 -15.5)"
            stroke="#637D92"
          />
        </g>
        <g filter="url(#filter2_d_40000008_1249)">
          <rect
            x="27"
            y="-16"
            width="45.1513"
            height="380"
            rx="8"
            transform="rotate(90 27 -16)"
            fill="#FCFCFC"
          />
          <rect
            x="26.5"
            y="-15.5"
            width="44.1513"
            height="379"
            rx="7.5"
            transform="rotate(90 26.5 -15.5)"
            stroke="#637D92"
          />
        </g>
        <circle
          cx="6.5"
          cy="6.53711"
          r="11.3083"
          fill="#E7EBEF"
          stroke="black"
          stroke-width="0.383333"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.8671 1.76815L9.20913 4.4253H11.1458V5.26991H7.76737V1.89146H8.61199V3.82816L11.2691 1.17017L11.8671 1.76815ZM1.85509 7.80375H5.23354V11.1822H4.38892V9.2455L1.73177 11.9035L1.13379 11.3064L3.79178 8.64836H1.85509V7.80375Z"
          fill="black"
        />
        <path
          d="M1.99219 7.30386C1.78886 7.30386 1.59386 7.38463 1.45008 7.52841C1.3063 7.67219 1.22553 7.86719 1.22553 8.07053C1.22553 8.27386 1.3063 8.46886 1.45008 8.61264C1.59386 8.75642 1.78886 8.83719 1.99219 8.83719H3.11536L0.590729 11.3618C0.446972 11.5056 0.366211 11.7006 0.366211 11.9039C0.366211 12.1072 0.446972 12.3021 0.590729 12.4459C0.734485 12.5897 0.92946 12.6704 1.13276 12.6704C1.33606 12.6704 1.53104 12.5897 1.67479 12.4459L4.2922 9.82849V11.1372C4.2922 11.5604 4.6349 11.9039 5.05886 11.9039C5.48283 11.9039 5.73276 11.5604 5.73276 11.1372V7.30386H1.99219ZM2.6661 5.77053C2.86943 5.77053 3.06443 5.68975 3.20821 5.54598C3.35199 5.4022 3.43276 5.20719 3.43276 5.00386V3.47053H4.96609C5.16943 3.47053 5.36443 3.38975 5.50821 3.24598C5.65199 3.1022 5.73276 2.90719 5.73276 2.70386C5.73276 2.50053 5.65199 2.30552 5.50821 2.16175C5.36443 2.01797 5.16943 1.93719 4.96609 1.93719H1.9002L1.89943 5.00386C1.89943 5.20719 1.9802 5.4022 2.12398 5.54598C2.26776 5.68975 2.46276 5.77053 2.6661 5.77053ZM10.3328 7.30386C10.1294 7.30386 9.93442 7.38463 9.79065 7.52841C9.64687 7.67219 9.5661 7.86719 9.5661 8.07053V9.60386H8.03276C7.82943 9.60386 7.63442 9.68463 7.49065 9.82841C7.34687 9.97219 7.2661 10.1672 7.2661 10.3705C7.2661 10.5739 7.34687 10.7689 7.49065 10.9126C7.63442 11.0564 7.82943 11.1372 8.03276 11.1372H11.0994V8.07053C11.0994 7.86719 11.0187 7.67219 10.8749 7.52841C10.7311 7.38463 10.5361 7.30386 10.3328 7.30386ZM11.3241 0.628495L8.79943 3.15313V1.93719C8.79943 1.73386 8.71865 1.53886 8.57488 1.39508C8.4311 1.2513 8.23609 1.17053 8.03276 1.17053C7.82943 1.17053 7.63442 1.2513 7.49065 1.39508C7.34687 1.53886 7.2661 1.73386 7.2661 1.93719V5.77053H11.0994C11.3028 5.77053 11.4978 5.68975 11.6415 5.54598C11.7853 5.4022 11.8661 5.20719 11.8661 5.00386C11.8661 4.80053 11.7853 4.60552 11.6415 4.46175C11.4978 4.31797 11.3028 4.23719 11.0994 4.23719H9.8835L12.4081 1.71333C12.7079 1.41356 12.7079 0.929028 12.4081 0.629261C12.1084 0.329495 11.6238 0.328728 11.3241 0.628495Z"
          fill="black"
        />
      </g>
      <rect x="-368.5" y="-206.5" width="411" height="1688" stroke="#001926" />
      <defs>
        <filter
          id="filter0_d_40000008_1249"
          x="-361"
          y="-167"
          width="396"
          height="1633"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_40000008_1249"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_40000008_1249"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_40000008_1249"
          x="-355"
          y="-16"
          width="384"
          height="1465"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_40000008_1249"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_40000008_1249"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_d_40000008_1249"
          x="-355"
          y="-16"
          width="384"
          height="49.1514"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_40000008_1249"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_40000008_1249"
            result="shape"
          />
        </filter>
        <clipPath id="clip0_40000008_1249">
          <rect x="-369" y="-207" width="412" height="1689" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
