'use client';

import { memo, useMemo } from 'react';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

export type TabItem = {
  label: string;
  path: string;
  disabled?: boolean;
  onClick?: () => void;
};

interface PageTabProps {
  tabs: TabItem[];
  className?: string;
  onTabClick?: (tab: TabItem) => void;
}

const PageTab = ({ tabs, className = '', onTabClick }: PageTabProps) => {
  const pathname = usePathname();

  const activeTabPath = useMemo(() => {
    return pathname;
  }, [pathname]);

  const isAttitudeTab = activeTabPath.includes('/attitude');

  return (
    <div
      className={`mx-2 bg-white ${!isAttitudeTab ? 'border-b border-gray-200' : ''} ${className}`}
    >
      <div className="flex">
        {tabs.map((tab) => (
          <Link
            key={tab.path}
            href={tab.disabled ? '#' : tab.path}
            prefetch={!tab.disabled}
            className={`py-1 text-sm font-medium border-b-2 transition-colors relative min-w-10 px-4 ${
              activeTabPath === tab.path
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-600 hover:text-gray-900'
            } ${
              tab.disabled
                ? 'opacity-50 cursor-not-allowed hover:text-gray-600 pointer-events-none'
                : 'cursor-pointer'
            }`}
            onClick={(e) => {
              if (tab.disabled) return;

              if (onTabClick) {
                e.preventDefault();
                onTabClick(tab);
              } else if (tab.onClick) {
                e.preventDefault();
                tab.onClick();
              }
            }}
          >
            {tab.label}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default memo(PageTab);
