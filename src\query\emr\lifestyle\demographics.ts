import { api } from '@/core/lib/interceptor';
import { Demographics } from '@/types/emr/lifestyle/demographics';

const baseUrl = '/lifestyle/v0.1/patient/lifestyle/demographics';

export const getPatientDemographics = async (patientId: string) => {
  const { data } = await api.get<Demographics>(baseUrl, {
    params: { patientId },
  });
  return data;
};

export const createPatientDemographics = async (
  patientId: string,
  demographics: Omit<Demographics, 'id'>
) => {
  const { data } = await api.post<Demographics>(baseUrl, demographics, {
    params: { patientId },
  });
  return data;
};

export const updatePatientDemographics = async (
  id: string,
  demographics: Demographics
) => {
  const { data } = await api.put<Demographics>(baseUrl, demographics, {
    params: { id },
  });
  return data;
};
