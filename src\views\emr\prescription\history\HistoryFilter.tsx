import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';

import {
  DateRange as ReactDateRange,
  Range,
  RangeKeyDict,
} from 'react-date-range';

import { Modal } from '@mui/material';
import { Item, ItemText } from '@radix-ui/react-select';
import dayjs from 'dayjs';

import '@core/components/date-range-picker/styles.scss';

import { cn } from '@/lib/utils';

import { usePrescriptionStore } from '@/store/emr/prescription';

import colors from '@/utils/colors';
import { filterBy, FilterByType, filterOptions } from '@/utils/filter-by-util';

import {
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { prescriptionHistoryView } from '@/types/emr/prescription';

import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';

const { DETAILS } = prescriptionHistoryView;
const { CUSTOM_DATE, ALL } = filterBy;

const HistoryFilter = () => {
  const {
    historyFilter,
    setHistoryFilter,
    historyView,
    customStartDate,
    customEndDate,
    setCustomDateRange,
  } = usePrescriptionStore();

  const [dateRangeOpen, setDateRangeOpen] = useState(false);

  const [localDateRange, setLocalDateRange] = useState<Range[]>([
    {
      startDate: customStartDate
        ? dayjs(customStartDate).toDate()
        : dayjs().toDate(),
      endDate: customEndDate ? dayjs(customEndDate).toDate() : dayjs().toDate(),
      key: 'selection',
    },
  ]);

  const selectedFilter = useMemo(() => {
    return filterOptions.find((filter) => filter.key === historyFilter);
  }, [historyFilter]);

  const selectedValue = useMemo(() => {
    if (selectedFilter?.value) {
      return `${selectedFilter.value}`;
    } else {
      return 'Select Filter';
    }
  }, [selectedFilter?.value]);

  const handleSelectDate = useCallback((rangesByKey: RangeKeyDict) => {
    setLocalDateRange([rangesByKey.selection]);
  }, []);

  const handleCloseDateRange = useCallback(() => {
    setDateRangeOpen(false);

    if (localDateRange[0]?.startDate && localDateRange[0]?.endDate) {
      setHistoryFilter(CUSTOM_DATE);
      setCustomDateRange(
        dayjs(localDateRange[0].startDate),
        dayjs(localDateRange[0].endDate)
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [localDateRange, setCustomDateRange]);

  const handleValueChange = useCallback(
    (value: FilterByType) => {
      if (value === CUSTOM_DATE) {
        setDateRangeOpen(true);
        return;
      }
      setHistoryFilter(value);
    },
    [setHistoryFilter]
  );

  const handleClearAll = useCallback(() => {
    setHistoryFilter(ALL);
  }, [setHistoryFilter]);

  useEffect(() => {
    return () => {
      setHistoryFilter(ALL);
    };
  }, [setHistoryFilter]);

  if (historyView === DETAILS) {
    return null;
  }

  return (
    <div className="w-[299px] flex items-center gap-3 whitespace-nowrap ml-auto ">
      <button
        onClick={handleClearAll}
        className="text-sm text-blue-600 hover:text-blue-800  cursor-pointer"
      >
        Clear Filter
      </button>
      <span className="text-sm text-gray-700">Filter By</span>
      <Select
        value={
          selectedFilter?.key === CUSTOM_DATE
            ? 'custom_date'
            : selectedFilter?.key
        }
        onValueChange={handleValueChange}
      >
        <SelectTrigger
          id="history-filter"
          className={` text-black h-8 border border-[#637D92]  p-0 focus:ring-0 focus:ring-offset-0 text-sm px-2`}
        >
          <SelectValue placeholder="Select Filter">{selectedValue}</SelectValue>
        </SelectTrigger>
        <SelectContent>
          <div className="max-h-70 overflow-y-auto">
            {filterOptions?.map((option) => (
              <Item
                key={option.key}
                value={option.key}
                className={cn(
                  'flex w-full select-none items-center',
                  'px-2 text-sm rounded-sm py-1.5 cursor-default',
                  'outline-none focus:bg-accent focus:text-accent-foreground',
                  'data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
                  `data-[state=checked]:text-[${colors.common.skyBlue}]`,
                  {
                    [`text-[${colors.common.skyBlue}]`]:
                      option.key === CUSTOM_DATE &&
                      selectedFilter?.key === CUSTOM_DATE,
                  }
                )}
              >
                <ItemText>{option.value}</ItemText>
              </Item>
            ))}
          </div>
        </SelectContent>
      </Select>
      <Modal
        open={dateRangeOpen}
        onClose={handleCloseDateRange}
        classes={{ root: 'w-screen h-screen flex items-center justify-center' }}
      >
        <div className="w-fit h-fit p-1 bg-white rounded-md">
          <ReactDateRange
            ranges={localDateRange}
            onChange={handleSelectDate}
            moveRangeOnFirstSelection={false}
          />
        </div>
      </Modal>
    </div>
  );
};

export default memo(HistoryFilter);
