import { TabProps } from '@mui/material';
import { BoxProps } from '@mui/material/Box';
import { TabsProps } from '@mui/material/Tabs';

export type TabPanelProps = {
  children?: React.ReactNode;
  dir?: string;
  index: number;
  value: number;
};

type TabData = {
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
  value?: string;
  id?: string;
};

export type AppTabProps = TabsProps & {
  tabs: TabData[];
  disabled?: boolean;
  panelContainerProps?: BoxProps;
  wrapperProps?: BoxProps;
  orientation?: TabsProps['orientation'];
  variant?: 'standard' | 'outlined';
  activeTab?: string | number;
  onTabChange?: (tabLabel: number) => void;
  readonly?: boolean;
  tabProps?: TabProps;
};
