# 🗂️ Speaker Identification and Dual Microphone Strategy for Real-Time Transcription

## 📌 Objective

To reliably **differentiate between doctor and patient** during **live transcription** using Azure’s **Speech SDK**, by:

1. Implementing **speaker identification** via Azure's **Voice Profile Recognition**.
2. Exploring a **dual microphone setup** to improve accuracy in noisy environments.

---

## 1. 🎙️ Speaker Identification with Azure Speech SDK

### 🧠 Overview

Azure’s Speech SDK supports **Speaker Recognition** through **Voice Profiles**, which enables identification of known speakers (like the doctor) in real-time audio.

### ✅ Use Case

- **Doctor**: Pre-enrolled voice profile
- **Patient**: Not enrolled; treated as an unknown speaker (e.g., `Guest1`, `Guest2`)
- **Goal**: Map transcript segments to actual roles (Doctor vs. <PERSON>ient)

---

### 🏗️ Architecture Flow

```plaintext
Microphone Input
   ↓
Azure Speech SDK (with Diarization + Voice Profile Matching)
   ↓
Real-time Transcription + Speaker Tagging
   ↓
Application UI (Doctor/Patient roles displayed)
```

---

### 🛠️ Implementation Steps

#### ✅ 1. Enroll Doctor Voice Profile

```ts
const client = new speechsdk.VoiceProfileClient(speechConfig);
const profileType = speechsdk.VoiceProfileType.TextIndependentIdentification;

client.createProfileAsync(profileType, 'en-us', (profile) => {
  client.enrollProfileAsync(profile, audioConfig, (result) => {
    console.log('Enrollment completed:', result.enrollmentResult.reason);
  });
});
```

- Requires \~15-30 seconds of the doctor’s speech.
- Once enrolled, store the profile ID securely.

---

#### ✅ 2. Identify Speaker During Transcription

```ts
const speakerRecognizer = new speechsdk.SpeakerRecognizer(
  speechConfig,
  audioConfig
);
const model = speechsdk.SpeakerIdentificationModel.fromProfiles([
  doctorProfile,
]);

speakerRecognizer.recognizeOnceAsync(model, (result) => {
  const identifiedProfileId = result.profileId;
  if (identifiedProfileId === doctorProfile.profileId) {
    console.log('Doctor is speaking');
  }
});
```

> 📌 This can be used in parallel with **ConversationTranscriber**.

---

#### ⚙️ SDK Properties to Enable

```ts
speechConfig.setProperty(
  'ConversationTranscription.DiarizationEnabled',
  'true'
);
speechConfig.setProperty(
  'SpeechServiceResponse_RequestProfanityFilter',
  'false'
);
```

---

### 🧪 Output Sample

```json
[
  { "speakerId": "Doctor", "text": "How are you feeling today?" },
  { "speakerId": "Patient", "text": "I'm having chest pain." }
]
```

---

## 2. 🎧 Dual Microphone Setup (Optional, for Enhancement)

### 🧠 Purpose

In noisy or overlapping speech environments, a **dual mic input** allows:

- One mic near the doctor
- One mic near the patient

Azure Speech SDK currently accepts **one audio input stream** at a time, but dual-mic setups can be **merged into stereo** or **simulated via audio preprocessing**.

---

### 🔧 Options for Dual Mic Integration

#### ✅ A. Stereo Input Simulation

- Use external audio interface or device with two inputs (e.g., USB mixer).
- Left channel = Doctor mic, Right channel = Patient mic.
- Preprocess audio to split channels before feeding into the SDK.

#### ✅ B. Audio Preprocessing (Advanced)

- Use a local mixer (e.g., Web Audio API or Python's PyDub/FFmpeg) to merge and isolate speakers.
- Feed combined stream into SDK.
- Not real-time out-of-the-box, but helps with offline summaries.

---

### 🔄 Integration Possibilities

| Mic Setup          | Transcription Style        | Accuracy  |
| ------------------ | -------------------------- | --------- |
| Single Mic         | Speaker diarization        | Moderate  |
| Dual Mic (merged)  | Channel-based separation   | High      |
| Dual Mic + Profile | Channel + Profile matching | Very High |

---

## ✅ Recommendations

| Task                                  | Priority | Owner          |
| ------------------------------------- | -------- | -------------- |
| Implement speaker profile for doctor  | High     | Frontend       |
| Enable diarization in Speech SDK      | High     | Frontend       |
| Set up dual mic support (if feasible) | Medium   | Hardware/Infra |
| Map speakers dynamically in UI        | High     | Frontend       |

---

## 📚 References

- [Azure Speaker Recognition](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/speaker-recognition-overview)
- [Voice Profile Enrollment](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/how-to-use-voice-profile)
- [Conversation Transcription](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/conversation-transcription)
- [Audio Configuration](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/how-to-use-audio-input-streams)
