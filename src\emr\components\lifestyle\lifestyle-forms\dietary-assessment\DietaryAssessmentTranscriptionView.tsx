import React, { Dispatch, SetStateAction, useCallback } from 'react';

import { useForm } from 'react-hook-form';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import { lifestyleModes, RenderFormModesTypes } from '@/constants/lifestyle';

import ControlledTextarea from '../shared/ControlledTextarea';
import ModalContainer from '../shared/ModalContainer';

type Props = {
  setRenderForm?: Dispatch<SetStateAction<RenderFormModesTypes>>;
  renderForm?: RenderFormModesTypes;
};

const DietaryAssessmentTranscriptionView: React.FC<Props> = ({
  setRenderForm,
  renderForm,
}) => {
  const isMobile = useIsMobile();
  const { formMode } = useLifestyleUtilStore();

  const { handleSubmit, control } = useForm({
    defaultValues: { record: 'Doctor' },
  });
  const onSubmit = useCallback((data: any) => {
    console.log(data);
  }, []);

  return (
    <ModalContainer
      title="Dietary Assessment"
      showSaveButton={
        (formMode === lifestyleModes.VIEW && !isMobile) ||
        formMode === lifestyleModes.CREATE ||
        formMode === lifestyleModes.EDIT
      }
      showTitle={!isMobile}
      onSubmit={handleSubmit(onSubmit)}
      setRenderForm={setRenderForm}
      renderForm={renderForm}
      showSwitchRecordButton={formMode !== lifestyleModes.CREATE && isMobile}
      showEditButton={formMode === lifestyleModes.VIEW}
    >
      <div style={{ minHeight: 'calc(100vh - 28.5rem)' }} className="w-full ">
        <ControlledTextarea
          control={control}
          name={'record'}
          disabled={formMode === lifestyleModes.VIEW}
          variant="transparent"
          style={{ minHeight: 'calc(100vh - 28.5rem)' }}
          className="w-full "
        />
      </div>
    </ModalContainer>
  );
};

export default DietaryAssessmentTranscriptionView;
