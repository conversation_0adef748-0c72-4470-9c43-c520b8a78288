import { useFormContext } from 'react-hook-form';

import TextInput from '@core/components/text-input';

import { countryOptions, states } from '@/utils/constants/master';

import { FormRow, SelectField } from './Components';

export interface MiniAddressSectionProps {
  title: string;
  type: string;
  value: string;
  isFieldDisabled: (_fieldName: string) => boolean;
  renderEditIcon: (_fieldName: string) => JSX.Element | null;
}

export default function MiniAddressSection(props: MiniAddressSectionProps) {
  const { register, watch, setValue } = useFormContext();
  const { type, title, value, isFieldDisabled, renderEditIcon } = props;

  return (
    <div className="w-full flex flex-col gap-y-4 md:gap-y-6 border border-[#9fb9ce] rounded-lg md:rounded-md py-3 md:py-6 px-1 md:px-8 ">
      <FormRow className="flex-col md:flex-row">
        <TextInput
          key={`${type}.${value}`}
          label={title}
          color="white"
          {...register(`${type}.${value}`)}
          placeholder={type === 'hometownDetails' ? 'Ranipet' : 'Orissa'}
          disabled={isFieldDisabled(`${type}.${value}`)}
          endDecoration={renderEditIcon(`${type}.${value}`)}
          className="flex-grow flex-1 pr-0 md:pr-6"
        />
        <SelectField
          id={`${type}.state`}
          label="State"
          options={states}
          value={watch(`${type}.state`)}
          onChange={(value) => {
            setValue(`${type}.state`, value);
          }}
          placeholder="Select"
          disabledInput={isFieldDisabled(`${type}.state`)}
          endDecoration={renderEditIcon(`${type}.state`)}
          wrapperClassName="flex-grow flex-1 pl-0 md:pl-6"
        />
      </FormRow>

      <FormRow className="flex-col md:flex-row">
        <TextInput
          key={`${type}.district`}
          label="District"
          color="white"
          {...register(`${type}.district`)}
          placeholder={type === 'hometownDetails' ? 'Ranipet' : 'Oraiiyya'}
          disabled={isFieldDisabled(`${type}.district`)}
          endDecoration={renderEditIcon(`${type}.district`)}
          className="flex-grow flex-1 pr-0 md:pr-6"
        />
        <SelectField
          id={`${type}.country`}
          label="Country"
          options={countryOptions}
          value={watch(`${type}.country`)}
          onChange={(value) => {
            setValue(`${type}.country`, value);
          }}
          placeholder="Select"
          disabledInput={isFieldDisabled(`${type}.country`)}
          endDecoration={renderEditIcon(`${type}.country`)}
          wrapperClassName="flex-grow flex-1 pl-0 md:pl-6"
        />
      </FormRow>
    </div>
  );
}
