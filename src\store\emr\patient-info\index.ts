import { toast } from 'sonner';
import { create } from 'zustand';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import { getPatient, getPatientVitals } from '@/query/patient';

import { PatientInfo, PatientInfoVitals } from '@/types/emr/patient-info';

type PatientInfoState = {
  patient: PatientInfo | null;
  vitals: PatientInfoVitals[];
  isSearching: boolean;
  isError: boolean;
  isLoadingVitals: boolean;
};

type PatientInfoAction = {
  setVitals: (vitals: PatientInfoVitals) => void;
  setPatient: (patient: PatientInfo | null) => void;
  selectPatient: (id: string) => Promise<PatientInfo | null>;
  getPatientVitals: (id: string | undefined) => Promise<PatientInfoVitals[]>;
};

type PatientInfoStore = PatientInfoState & PatientInfoAction;

const initialState: PatientInfoState = {
  patient: useCurrentPatientStore.getState().patient,
  vitals: [],
  isSearching: false,
  isError: false,
  isLoadingVitals: false,
};

export const usePatientInfoStore = create<PatientInfoStore>((set) => ({
  ...initialState,
  setPatient: (patient) => set({ patient }),
  setVitals: (vitals) =>
    set((state) => ({ vitals: [...state.vitals, vitals] })),

  selectPatient: async (id) => {
    try {
      set({ isError: false, isSearching: true });
      const { data } = await getPatient(id);

      if (!data) {
        toast.error('Patient not found');
        return null;
      }
      set({ patient: data, isSearching: false });
      return data;
    } catch (err) {
      set({ isError: true, isSearching: true });

      console.error(err);

      toast.error('There was an issue getting the selected patient');
    }
  },
  getPatientVitals: async (id) => {
    try {
      set({ isLoadingVitals: true });

      if (!id) {
        set({ isLoadingVitals: false });
        return [];
      }
      const { data } = await getPatientVitals(id);
      set({ vitals: data?.reverse(), isLoadingVitals: false });
      return data;
    } catch (err) {
      console.error(err);
      set({ isLoadingVitals: false });
      toast.error('Error fetching patient data');
    }
  },
}));
