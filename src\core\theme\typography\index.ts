import { getCSSVar } from '@core/theme/utils';

const typography = {
  fontFamily: [
    'var(--font-archivo)',
    'var(--font-inter)',
    'Archivo',
    'Inter',
    '-apple-system',
    'BlinkMacSystemFont',
    '"Segoe UI"',
    'Roboto',
    '"Helvetica Neue"',
    'Arial',
    'sans-serif',
  ].join(','),
  h1: {
    fontFamily: 'var(--font-archivo), Archivo, sans-serif',
    fontWeight: 700,
  },
  h2: {
    fontFamily: 'var(--font-archivo), Archivo, sans-serif',
    fontWeight: 600,
  },
  h3: {
    fontFamily: 'var(--font-archivo), Archivo, sans-serif',
    fontWeight: 600,
  },
  h4: {
    fontFamily: 'var(--font-archivo), Archivo, sans-serif',
    fontWeight: 600,
  },
  h5: {
    fontFamily: 'var(--font-archivo), Archivo, sans-serif',
    fontWeight: 600,
  },
  h6: {
    fontFamily: 'var(--font-archivo), Archivo, sans-serif',
    fontWeight: 600,
  },
  body1: {
    fontFamily: 'var(--font-inter), Inter, sans-serif',
  },
  body2: {
    fontFamily: 'var(--font-inter), Inter, sans-serif',
  },
  button: {
    fontFamily: 'var(--font-inter), Inter, sans-serif',
    fontWeight: 500,
  },
  caption: {
    fontFamily: 'var(--font-inter), Inter, sans-serif',
  },
  overline: {
    fontFamily: 'var(--font-inter), Inter, sans-serif',
    letterSpacing: getCSSVar('--letter-spacing', '-2.2%'),
  },
};

export default typography;
