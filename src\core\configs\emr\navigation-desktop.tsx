import ConsultationIcon from '@/assets/svg/consultation-icon.svg';
import DashboardIcon from '@/assets/svg/DashboardIcon';
import LifestyleIcon from '@/assets/svg/lifestyle-icon.svg';
import PatientInfo from '@/assets/svg/patient-info-icon.svg';
import PrescriptionIcon from '@/assets/svg/prescription-icon.svg';
import ReportsIcon from '@/assets/svg/reports-icon.svg';

import { mapObjectToArray } from '@/helpers/utils';

import departments from '@/constants/departments';
import { routes } from '@/constants/routes';

import { SidebarItem } from '@/core/layout/shared/side-bar/types';

const { lifestyle } = departments;
const {
  EMR_PATIENT_INFO,
  EMR_CONSULTATION,
  EMR_REPORTS,
  EMR_LIFE_STYLE,
  EMR_PRESCRIPTION,
  EMR_DASHBOARD,
} = routes;

const emrNavigation: SidebarItem[] = [
  {
    label: 'Dashboard',
    path: EMR_DASHBOARD,
    icon: <DashboardIcon />,
  },
  { label: 'Patient Info', path: EMR_PATIENT_INFO, icon: <PatientInfo /> },
  { label: 'Consulting', path: EMR_CONSULTATION, icon: <ConsultationIcon /> },
  { label: 'Labs', path: EMR_REPORTS, icon: <ReportsIcon /> },
  {
    label: 'Prescriptions',
    path: EMR_PRESCRIPTION,
    icon: <PrescriptionIcon />,
  },

  {
    label: 'Lifestyle',
    path: EMR_LIFE_STYLE,
    icon: <LifestyleIcon />,
    department: mapObjectToArray(lifestyle),
  },
];

export default emrNavigation;
