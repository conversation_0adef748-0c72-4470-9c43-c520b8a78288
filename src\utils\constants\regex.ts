export const regex = {
  ALPHABETIC_REGEX: /^[A-Za-z\s]$/,
  NUMBERS_AND_SPECIAL_CHARS_REGEX: /^[\d\s\W]$/,
  MOBILE_NUMBER_PATTERN: /^(\+91[\-\s]?)?[6-9]\d{9}$/,
  ALPHANUMERIC_REGEX: /^[A-Za-z0-9\s]$/,
  EMAIL_PATTERN: /\S+@\S+\.\S+/,
  NUMERIC_REGEX: /^[0-9]$/,
  DECIMAL_SEPARATOR: /^\.$/,
  NUMBERS_ONLY_REGEX: /^[0-9]$/,
  DIGIT_ONLY: /^\d$/,
  ACCOUNT_NUMBER_PATTERN: /^\d{8,20}$/,
  PHONE_NUMBER_PATTERN:
    /^(?:\(\d{3}\)\s?|\d{3}[-.\s]?)?\d{3}[-.\s]?\d{4}|\d{11}$/,
  RESIDENTIAL_MOBILE_NUMBER_PATTERN: /^\d{11}$/,
  AADHAR_PATTERN: /^[2-9]{1}[0-9]{3}[0-9]{4}[0-9]{4}$/,
  BRACKET_TEXT_REGEX: /(\(.*?\))/g,
  ALPHANUMERIC_WITH_DOT_REGEX: /^[A-Za-z0-9.]$/,
  IMAGE_FILE_EXTENSION_REGEX: /\.(jpeg|jpg|png|gif)$/,
  DURATION_REGEX: /^(\d+)([DMW])$/,
  NON_DIGIT_GLOBAL_REGEX: /\D/g,
  NON_ALPHABET_GLOBAL_REGEX: /[^a-zA-Z]/g,
  NON_ALPHABET_WITH_SPACE_REGEX: /[^a-zA-Z\s]/g,
};
