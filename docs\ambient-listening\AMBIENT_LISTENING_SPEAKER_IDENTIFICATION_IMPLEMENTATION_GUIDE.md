# 🏥 Ambient Listening with Speaker Identification - Implementation Guide

## 📋 Table of Contents

1. [Executive Summary](#-executive-summary)
2. [Current Implementation Analysis](#-current-implementation-analysis)
3. [Speaker Identification Integration](#-speaker-identification-integration)
4. [Dual Microphone Setup](#-dual-microphone-setup)
5. [Backend Enhancements](#-backend-enhancements)
6. [Implementation Roadmap](#-implementation-roadmap)
7. [Gaps and Blockers](#-gaps-and-blockers)
8. [Code-Level Changes](#-code-level-changes)

---

## 📌 Executive Summary

This document provides a comprehensive implementation guide for enhancing the ARCA EMR ambient listening feature with speaker identification capabilities. The current system uses Azure Speech SDK's ConversationTranscriber for real-time transcription but relies on post-processing AI to differentiate speakers. This guide outlines how to implement real-time speaker identification using Azure Voice Profiles and support for dual microphone setups.

---

## 🔍 Current Implementation Analysis

### Frontend Architecture

#### Component Structure

```
src/lib/add_record/
├── index.tsx                 # Main ambient listening component
└── modals/
    └── RecordConsultation.tsx # Recording UI and controls
```

#### Key Features

1. **Language Support**
   - Multi-language support (English, Malayalam, Tamil, Kannada, Telugu, Bengali, Hindi)
   - Language selection UI before recording
   - Preference saved in doctor's EMR customization settings

2. **Recording Flow**

   ```mermaid
   graph LR
   A[Start] --> B[Language Selection]
   B --> C[Initialize Speech Engine]
   C --> D[Start Recording]
   D --> E[Real-time Transcription]
   E --> F[Stop Recording]
   F --> G[Send to Backend]
   G --> H[AI Processing]
   H --> I[Display Summary]
   ```

3. **State Management**
   - Recording states: IDLE, RECORDING, PAUSED
   - Modal states: INITIAL, LANGUAGE_SELECTION, RECORD_CONSULTATION, LOADING, SHOW_SUMMARY
   - Transcript history maintained during recording

4. **Technical Implementation**
   - Uses `speechsdk.ConversationTranscriber` for transcription
   - Audio captured from default microphone
   - Real-time transcript display with auto-scrolling
   - Visual feedback with sine wave animation during recording

### Backend Architecture

#### API Endpoints

1. **`/summary` (POST)**
   - Receives raw transcript text
   - Processes through two OpenAI calls:
     - Speaker identification
     - Medical summary generation
   - Returns structured conversation and summary

#### Current Speaker Identification

```javascript
// Current approach uses AI post-processing
async identifySpeaker(transcript) {
    // OpenAI prompt to identify doctor vs patient
    // Returns: [{speaker: "doctor", message: "..."}, ...]
}
```

### Limitations of Current System

1. **Post-Processing Only**: Speaker identification happens after recording, not in real-time
2. **AI Dependency**: Relies entirely on OpenAI's interpretation, which may be inaccurate
3. **No Voice Profiles**: Cannot leverage known voice characteristics
4. **Single Microphone**: No spatial audio separation
5. **Generic Speaker Labels**: Cannot identify specific individuals

---

## 🎯 Speaker Identification Integration

### Architecture Enhancement

```mermaid
graph TB
    A[Microphone Input] --> B[Azure Speech SDK]
    B --> C{Voice Profile Matching}
    C -->|Match Found| D[Tagged Transcript<br/>"Dr. Smith: ..."]
    C -->|No Match| E[Generic Label<br/>"Patient: ..."]
    D --> F[Backend API]
    E --> F
    F --> G[Enhanced Summary]
```

### Implementation Steps

#### 1. Doctor Voice Profile Enrollment

**Frontend Component**: `DoctorVoiceEnrollment.tsx`

```typescript
interface VoiceEnrollmentProps {
  doctorId: string;
  onEnrollmentComplete: (profileId: string) => void;
}

const DoctorVoiceEnrollment: React.FC<VoiceEnrollmentProps> = ({
  doctorId,
  onEnrollmentComplete
}) => {
  const [enrollmentProgress, setEnrollmentProgress] = useState(0);
  const [isEnrolling, setIsEnrolling] = useState(false);

  const startEnrollment = async () => {
    const speechToken = await getSpeechToken();
    const speechConfig = speechsdk.SpeechConfig.fromAuthorizationToken(
      speechToken,
      'eastus'
    );

    const client = new speechsdk.VoiceProfileClient(speechConfig);
    const profileType = speechsdk.VoiceProfileType.TextIndependentIdentification;

    // Create profile
    client.createProfileAsync(profileType, 'en-us', (profile) => {
      const audioConfig = speechsdk.AudioConfig.fromDefaultMicrophoneInput();

      // Enroll profile
      client.enrollProfileAsync(profile, audioConfig, (result) => {
        if (result.reason === speechsdk.ResultReason.EnrolledVoiceProfile) {
          // Save profile ID to backend
          saveDoctorVoiceProfile(doctorId, profile.profileId);
          onEnrollmentComplete(profile.profileId);
        }
      });
    });
  };

  return (
    <div className="voice-enrollment-container">
      <h3>Voice Profile Setup</h3>
      <p>Please read the following text for 15-30 seconds:</p>
      <div className="enrollment-text">
        "I am enrolling my voice to enhance the ambient listening feature.
        This will help the system accurately identify me during patient consultations..."
      </div>
      <button onClick={startEnrollment} disabled={isEnrolling}>
        {isEnrolling ? 'Recording...' : 'Start Voice Enrollment'}
      </button>
      <ProgressBar value={enrollmentProgress} max={100} />
    </div>
  );
};
```

#### 2. Enhanced Recording Component

**Modified**: `RecordConsultation.tsx`

```typescript
// Add voice profile initialization
const initializeSpeechEngineWithProfile = async (
  language: string,
  doctorProfileId?: string
) => {
  const speechToken = await getSpeechToken();
  const speechConfig = speechsdk.SpeechConfig.fromAuthorizationToken(
    speechToken,
    'eastus'
  );

  speechConfig.speechRecognitionLanguage = language;

  // Enable speaker diarization
  speechConfig.setProperty(
    'ConversationTranscription.DiarizationEnabled',
    'true'
  );

  // Set up speaker identification if profile exists
  if (doctorProfileId) {
    const speakerRecognizer = new speechsdk.SpeakerRecognizer(
      speechConfig,
      audioConfig
    );

    const model = speechsdk.SpeakerIdentificationModel.fromProfiles([
      { profileId: doctorProfileId },
    ]);

    // Link recognizer to transcriber
    recognizer.addSpeakerRecognizer(speakerRecognizer, model);
  }

  const audioConfig = speechsdk.AudioConfig.fromDefaultMicrophoneInput();
  const recognizer = new speechsdk.ConversationTranscriber(
    speechConfig,
    audioConfig
  );

  // Enhanced transcription handler
  recognizer.transcribing = (s, e) => {
    const event = JSON.parse(e.privResult.privJson);
    const speakerId = event.SpeakerId || 'Unknown';

    // Map speaker ID to role
    const speaker = speakerId === doctorProfileId ? 'Doctor' : `Patient`;

    setTranscriptWithSpeaker({
      speaker,
      text: event.Text,
      timestamp: event.Offset,
    });
  };

  return recognizer;
};
```

#### 3. Backend API Enhancement

**New Endpoint**: `/api/voice-profiles`

```javascript
// New voice profile management endpoints
app.post('/api/voice-profiles', async (req, res) => {
  const { doctorId, profileId } = req.body;

  // Store in database
  await doctorRepository.updateVoiceProfile(doctorId, profileId);

  // Encrypt profile ID for security
  const encryptedProfileId = encryptionService.encrypt(profileId);

  res.json({
    success: true,
    profileId: encryptedProfileId,
  });
});

app.get('/api/voice-profiles/:doctorId', async (req, res) => {
  const profile = await doctorRepository.getVoiceProfile(req.params.doctorId);
  res.json({ profileId: profile?.profileId });
});
```

---

## 🎧 Dual Microphone Setup

### Implementation Strategy

#### Option 1: Stereo Channel Separation

```typescript
// Enhanced audio configuration for dual mic
const configureDualMicAudio = () => {
  // Create custom audio stream
  const pushStream = speechsdk.AudioInputStream.createPushStream();

  // Web Audio API setup
  navigator.mediaDevices
    .getUserMedia({
      audio: {
        channelCount: 2,
        echoCancellation: true,
        noiseSuppression: true,
      },
    })
    .then((stream) => {
      const audioContext = new AudioContext();
      const source = audioContext.createMediaStreamSource(stream);
      const splitter = audioContext.createChannelSplitter(2);

      source.connect(splitter);

      // Process channels separately
      // Channel 0: Doctor's mic
      // Channel 1: Patient's mic

      const merger = audioContext.createChannelMerger(2);
      splitter.connect(merger, 0, 0);
      splitter.connect(merger, 1, 1);

      // Convert to SDK format
      const processor = audioContext.createScriptProcessor(4096, 2, 2);
      processor.onaudioprocess = (e) => {
        const leftChannel = e.inputBuffer.getChannelData(0); // Doctor
        const rightChannel = e.inputBuffer.getChannelData(1); // Patient

        // Tag audio segments with spatial information
        pushStream.write(taggedAudioBuffer);
      };
    });

  return speechsdk.AudioConfig.fromStreamInput(pushStream);
};
```

#### Option 2: Device Selection UI

```typescript
const MicrophoneSelector: React.FC = () => {
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);
  const [doctorMic, setDoctorMic] = useState<string>('');
  const [patientMic, setPatientMic] = useState<string>('');

  useEffect(() => {
    navigator.mediaDevices.enumerateDevices().then(deviceList => {
      const audioInputs = deviceList.filter(d => d.kind === 'audioinput');
      setDevices(audioInputs);
    });
  }, []);

  return (
    <div className="mic-selector">
      <Select
        label="Doctor's Microphone"
        value={doctorMic}
        onChange={setDoctorMic}
      >
        {devices.map(device => (
          <option key={device.deviceId} value={device.deviceId}>
            {device.label || `Microphone ${device.deviceId}`}
          </option>
        ))}
      </Select>

      <Select
        label="Patient's Microphone"
        value={patientMic}
        onChange={setPatientMic}
      >
        {devices.map(device => (
          <option key={device.deviceId} value={device.deviceId}>
            {device.label || `Microphone ${device.deviceId}`}
          </option>
        ))}
      </Select>
    </div>
  );
};
```

---

## 🔧 Backend Enhancements

### 1. Enhanced Summary Processing

```javascript
// Enhanced summary handler with real-time speaker tags
class EnhancedSummaryHandler {
  async processTranscriptWithSpeakers(transcriptData) {
    // transcriptData already contains speaker information
    // [{speaker: "Doctor", text: "...", timestamp: 123}, ...]

    // Skip speaker identification step
    const structuredConversation = transcriptData.map((item) => ({
      speaker: item.speaker.toLowerCase(),
      message: item.text,
    }));

    // Direct to summary generation
    const summary = await this.generateMedicalSummary(structuredConversation);

    return {
      conversation: structuredConversation,
      summary: summary,
      metadata: {
        processingTime: Date.now() - startTime,
        speakerIdentificationMethod: 'voice-profile',
      },
    };
  }
}
```

### 2. Voice Profile Management Service

```javascript
// New service for voice profile operations
class VoiceProfileService {
  async createProfile(doctorId, audioSample) {
    // Store encrypted profile data
    const profileData = {
      doctorId,
      profileId: generateUUID(),
      createdAt: new Date(),
      audioFingerprint: await generateAudioFingerprint(audioSample),
    };

    await cosmosService.create('VoiceProfiles', profileData);
    return profileData.profileId;
  }

  async verifyProfile(profileId, audioSample) {
    // Implement voice verification logic
    const profile = await cosmosService.get('VoiceProfiles', profileId);
    const match = await compareAudioFingerprints(
      profile.audioFingerprint,
      audioSample
    );
    return match.confidence > 0.85;
  }
}
```

### 3. Real-time Processing Pipeline

```javascript
// WebSocket support for real-time updates
const setupRealtimeTranscription = (ws) => {
  ws.on('transcription-segment', async (data) => {
    const { speaker, text, timestamp, sessionId } = data;

    // Store in temporary session storage
    await redisService.append(`session:${sessionId}`, {
      speaker,
      text,
      timestamp,
    });

    // Emit to connected clients
    ws.emit('transcription-update', {
      speaker: speaker === 'Doctor' ? doctorName : 'Patient',
      text,
      timestamp,
    });
  });
};
```

---

## 📅 Implementation Roadmap

### Phase 1: Voice Profile Infrastructure (Week 1-2)

- [ ] Database schema for voice profiles
- [ ] Voice enrollment UI component
- [ ] Backend API for profile management
- [ ] Security and encryption implementation

### Phase 2: Real-time Speaker Identification (Week 3-4)

- [ ] Integrate speaker recognition with transcriber
- [ ] Update transcript display with speaker tags
- [ ] Test with multiple languages
- [ ] Performance optimization

### Phase 3: Dual Microphone Support (Week 5-6)

- [ ] Microphone selection UI
- [ ] Audio channel separation logic
- [ ] Spatial audio processing
- [ ] Testing with various hardware setups

### Phase 4: Backend Enhancement (Week 7-8)

- [ ] Remove redundant AI speaker identification
- [ ] Optimize summary generation
- [ ] Add real-time WebSocket support
- [ ] Comprehensive testing and deployment

---

## 🚧 Gaps and Blockers

### Technical Limitations

1. **Azure SDK Limitations**
   - ConversationTranscriber may not fully support real-time speaker recognition
   - Need to verify API compatibility and quotas
   - Potential latency issues with voice profile matching

2. **Hardware Dependencies**
   - Dual microphone setup requires specific hardware
   - Not all devices support stereo input
   - USB audio interfaces may be needed

3. **Browser Compatibility**
   - Web Audio API support varies across browsers
   - MediaDevices API permissions required
   - Chrome/Edge recommended for best performance

### Implementation Challenges

1. **Voice Profile Management**
   - Secure storage of voice profiles
   - GDPR compliance for biometric data
   - Profile update/re-enrollment workflow

2. **Real-time Performance**
   - Network latency impacts
   - Processing overhead with speaker matching
   - Buffering strategies for smooth playback

3. **User Experience**
   - Enrollment process friction
   - Fallback for unidentified speakers
   - Visual indicators for active speaker

### Mitigation Strategies

1. **Progressive Enhancement**
   - Start with single mic + voice profiles
   - Add dual mic as optional enhancement
   - Maintain backward compatibility

2. **Caching and Optimization**
   - Cache voice profiles locally
   - Implement efficient matching algorithms
   - Use WebWorkers for audio processing

3. **Comprehensive Testing**
   - Multiple browser testing
   - Various microphone configurations
   - Network condition simulations

---

## 💻 Code-Level Changes

### Frontend Changes

#### 1. Update Types (`src/types/speech.ts`)

```typescript
export interface EnhancedTranscriptSegment {
  speaker: 'Doctor' | 'Patient' | 'Unknown';
  speakerId?: string;
  text: string;
  timestamp: number;
  confidence?: number;
}

export interface VoiceProfile {
  profileId: string;
  doctorId: string;
  createdAt: Date;
  lastUsed?: Date;
  enrollmentStatus: 'pending' | 'completed' | 'failed';
}
```

#### 2. New Store (`src/store/voice-profiles.ts`)

```typescript
interface VoiceProfileStore {
  profiles: Map<string, VoiceProfile>;
  currentProfile: VoiceProfile | null;
  enrollProfile: (doctorId: string) => Promise<void>;
  loadProfile: (doctorId: string) => Promise<void>;
  deleteProfile: (profileId: string) => Promise<void>;
}
```

#### 3. Updated Queries (`src/query/speech.ts`)

```typescript
export async function enrollVoiceProfile(
  doctorId: string,
  audioData: Blob
): Promise<VoiceProfile> {
  const formData = new FormData();
  formData.append('doctorId', doctorId);
  formData.append('audio', audioData);

  const { data } = await arcaAxios.post('/api/voice-profiles/enroll', formData);
  return data;
}

export async function getVoiceProfile(doctorId: string): Promise<VoiceProfile> {
  const { data } = await arcaAxios.get(`/api/voice-profiles/${doctorId}`);
  return data;
}
```

### Backend Changes

#### 1. Database Schema

```sql
-- Voice Profiles Table
CREATE TABLE voice_profiles (
  id UUID PRIMARY KEY,
  doctor_id UUID REFERENCES doctors(id),
  profile_id VARCHAR(255) UNIQUE,
  azure_profile_id VARCHAR(255),
  enrollment_audio_url TEXT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  status VARCHAR(50)
);

-- Transcription Sessions Table
CREATE TABLE transcription_sessions (
  id UUID PRIMARY KEY,
  doctor_id UUID REFERENCES doctors(id),
  patient_id UUID REFERENCES patients(id),
  voice_profile_id UUID REFERENCES voice_profiles(id),
  start_time TIMESTAMP,
  end_time TIMESTAMP,
  transcript JSONB,
  summary JSONB
);
```

#### 2. API Routes

```javascript
// Voice profile routes
router.post(
  '/api/voice-profiles/enroll',
  authMiddleware,
  voiceProfileHandler.enroll
);
router.get(
  '/api/voice-profiles/:doctorId',
  authMiddleware,
  voiceProfileHandler.get
);
router.delete(
  '/api/voice-profiles/:profileId',
  authMiddleware,
  voiceProfileHandler.delete
);
router.post(
  '/api/voice-profiles/verify',
  authMiddleware,
  voiceProfileHandler.verify
);

// Enhanced summary route
router.post(
  '/api/summary/enhanced',
  authMiddleware,
  enhancedSummaryHandler.process
);
```

---

## 📊 Success Metrics

1. **Accuracy Metrics**
   - Speaker identification accuracy > 95%
   - Reduction in post-processing time by 50%
   - False positive rate < 5%

2. **Performance Metrics**
   - Real-time transcription latency < 500ms
   - Voice enrollment time < 30 seconds
   - System resource usage < 20% increase

3. **User Experience Metrics**
   - Doctor adoption rate > 80%
   - Patient satisfaction score > 4.5/5
   - Support ticket reduction by 30%

---

## 🔒 Security Considerations

1. **Voice Profile Security**
   - Encrypt all voice data at rest and in transit
   - Implement access controls for profile management
   - Regular security audits for biometric data

2. **Privacy Compliance**
   - GDPR compliance for EU users
   - HIPAA compliance for US healthcare
   - Clear consent workflows for voice enrollment

3. **Data Retention**
   - Automatic profile expiration after 1 year
   - Patient consent for recording storage
   - Secure deletion procedures

---

## 📚 References

1. [Azure Speaker Recognition Documentation](https://docs.microsoft.com/azure/cognitive-services/speech-service/speaker-recognition-overview)
2. [Web Audio API Specification](https://www.w3.org/TR/webaudio/)
3. [HIPAA Compliance for Voice Data](https://www.hhs.gov/hipaa/for-professionals/security/guidance/index.html)
4. [Azure Speech SDK JavaScript Reference](https://docs.microsoft.com/javascript/api/microsoft-cognitiveservices-speech-sdk/)
