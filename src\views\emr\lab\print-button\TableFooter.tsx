import React, { memo } from 'react';

import { useCustomiseEmrStore } from '@/store/emr/doctor-profile/customise-emr';

const TableFooter = () => {
  const { customiseEmrData } = useCustomiseEmrStore();
  const mostRecentData = customiseEmrData?.length
    ? [...customiseEmrData].sort(
        (a, b) =>
          new Date(b.updated_on || '').getTime() -
          new Date(a.updated_on || '').getTime()
      )[0]
    : null;
  return (
    <div className="w-full flex justify-between">
      {mostRecentData?.digitalSignature ? (
        <img
          src={mostRecentData.digitalSignature}
          alt="Digital Signature"
          style={{ maxHeight: 40, maxWidth: 160 }}
        />
      ) : null}
    </div>
  );
};

export default memo(TableFooter);
