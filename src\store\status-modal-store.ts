import { ReactNode } from 'react';

import { create } from 'zustand';

export type ModalType =
  | 'success'
  | 'error'
  | 'warning'
  | 'info'
  | 'confirmation';

export interface ModalConfig {
  type: ModalType;
  title?: ReactNode;
  message: ReactNode;
  duration?: number; // Auto-close duration in milliseconds (0 = no auto-close)
  className?: string;
  showIcon?: boolean;
  actions?: {
    primary?: {
      label: string;
      onClick: () => void;
      variant?: 'default' | 'destructive' | 'outline';
    };
    secondary?: {
      label: string;
      onClick: () => void;
      variant?: 'default' | 'destructive' | 'outline';
    };
  };
}

export interface StatusModalState {
  isOpen: boolean;
  config: ModalConfig | null;
}

export interface StatusModalActions {
  showModal: (config: ModalConfig) => void;
  hideModal: () => void;
  // Convenience methods for different modal types
  showSuccess: (message: ReactNode, options?: Partial<ModalConfig>) => void;
  showError: (message: ReactNode, options?: Partial<ModalConfig>) => void;
  showWarning: (message: ReactNode, options?: Partial<ModalConfig>) => void;
  showInfo: (message: ReactNode, options?: Partial<ModalConfig>) => void;
  showConfirmation: (
    message: ReactNode,
    onConfirm: () => void,
    onCancel?: () => void,
    options?: Partial<ModalConfig>
  ) => void;
}

export type StatusModalStore = StatusModalState & StatusModalActions;

const initialState: StatusModalState = {
  isOpen: false,
  config: null,
};

const defaultConfigs: Record<ModalType, Partial<ModalConfig>> = {
  success: {
    duration: 2000,
    showIcon: true,
    className: 'border-green-200 bg-green-50',
  },
  error: {
    duration: 0, // Don't auto-close errors
    showIcon: true,
    className: 'border-red-200 bg-red-50',
  },
  warning: {
    duration: 4000,
    showIcon: true,
    className: 'border-yellow-200 bg-yellow-50',
  },
  info: {
    duration: 3000,
    showIcon: true,
    className: 'border-blue-200 bg-blue-50',
  },
  confirmation: {
    duration: 0, // Don't auto-close confirmations
    showIcon: true,
    className: 'border-gray-200 bg-gray-50',
  },
};

export const useStatusModalStore = create<StatusModalStore>((set, get) => ({
  ...initialState,

  showModal: (config) => {
    const defaultConfig = defaultConfigs[config.type] || {};
    const finalConfig = { ...defaultConfig, ...config };

    set({
      isOpen: true,
      config: finalConfig,
    });

    // Auto-close if duration is set
    if (finalConfig.duration && finalConfig.duration > 0) {
      setTimeout(() => {
        const { isOpen } = get();
        if (isOpen) {
          set({ isOpen: false });
        }
      }, finalConfig.duration);
    }
  },

  hideModal: () => {
    set({ isOpen: false });
  },

  showSuccess: (message, options = {}) => {
    get().showModal({
      type: 'success',
      message,
      ...options,
    });
  },

  showError: (message, options = {}) => {
    get().showModal({
      type: 'error',
      message,
      ...options,
    });
  },

  showWarning: (message, options = {}) => {
    get().showModal({
      type: 'warning',
      message,
      ...options,
    });
  },

  showInfo: (message, options = {}) => {
    get().showModal({
      type: 'info',
      message,
      ...options,
    });
  },

  showConfirmation: (message, onConfirm, onCancel, options = {}) => {
    get().showModal({
      type: 'confirmation',
      message,
      actions: {
        primary: {
          label: 'Confirm',
          onClick: () => {
            onConfirm();
            get().hideModal();
          },
        },
        secondary: {
          label: 'Cancel',
          onClick: () => {
            onCancel?.();
            get().hideModal();
          },
          variant: 'outline',
        },
      },
      ...options,
    });
  },
}));
