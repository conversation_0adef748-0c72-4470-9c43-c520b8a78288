'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider } from '@mui/material/styles';

import { getTheme } from '@core/theme';
import '@core/theme/types';
import { Mode, mode as themeMode } from '@core/theme/types';

interface ThemeContextType {
  mode: Mode;
  toggleTheme: () => void;
  setTheme: (mode: Mode) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a MuiThemeProvider');
  }
  return context;
};

interface MuiThemeProviderProps {
  children: React.ReactNode;
}

const MuiThemeProvider: React.FC<MuiThemeProviderProps> = ({ children }) => {
  const [mode, setMode] = useState<Mode>(themeMode.light);

  useEffect(() => {
    const detectTheme = () => {
      const isDarkClass = document.documentElement.classList.contains('dark');

      const prefersDark = window.matchMedia(
        '(prefers-color-scheme: dark)'
      ).matches;

      const shouldBeDark = isDarkClass || prefersDark;

      setMode(shouldBeDark ? 'dark' : 'light');
    };

    // Initial detection
    detectTheme();

    // Listen for changes to the dark class
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === 'attributes' &&
          mutation.attributeName === 'class'
        ) {
          detectTheme();
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
    });

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = () => detectTheme();
    mediaQuery.addEventListener('change', handleChange);

    return () => {
      observer.disconnect();
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  const toggleTheme = () => {
    const newMode = mode === 'light' ? 'dark' : 'light';
    setMode(newMode);

    // Update the HTML class to sync with Tailwind
    if (newMode === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const setTheme = (newMode: Mode) => {
    setMode(newMode);

    // Update the HTML class to sync with Tailwind
    if (newMode === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const theme = getTheme({ mode });

  const contextValue: ThemeContextType = {
    mode,
    toggleTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </ThemeContext.Provider>
  );
};

export default MuiThemeProvider;
