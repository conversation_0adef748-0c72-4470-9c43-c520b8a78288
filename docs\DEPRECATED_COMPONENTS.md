# Deprecated Components - ARCA EMR

## Overview

This document lists deprecated components, types, and functions that should not be used in new development. These items are marked for removal in future versions.

## ⚠️ Important Notice

**Do not use any components, types, or functions listed in this document for new development.** They are maintained only for backward compatibility and will be removed in future releases.

## Deprecated Components

### UI Components

#### `src/lib/text_input/index.tsx`

**Status**: Deprecated  
**Replacement**: Use `@core/components/app-text-field` instead  
**Reason**: Legacy component with inconsistent styling and limited functionality

```tsx
// ❌ Don't use (deprecated)
import TextInput from '@/lib/text_input';

// ✅ Use instead
import AppTextField from '@core/components/app-text-field';
```

#### `src/lib/common/date_picker/index.tsx`

**Status**: Deprecated  
**Replacement**: Use `@core/components/app-date-picker` instead  
**Reason**: Legacy date picker with accessibility issues

```tsx
// ❌ Don't use (deprecated)
import DatePicker from '@/lib/common/date_picker';

// ✅ Use instead
import AppDatePicker from '@core/components/app-date-picker';
```

#### `src/core/components/modal.tsx`

**Status**: Deprecated  
**Replacement**: Use `@core/components/app-modal` instead  
**Reason**: Legacy modal without proper accessibility and keyboard handling

```tsx
// ❌ Don't use (deprecated)
import Modal from '@core/components/modal';

// ✅ Use instead
import AppModal from '@core/components/app-modal';
```

#### `src/lib/VitalStatCard.tsx`

**Status**: Deprecated  
**Replacement**: Use the new vital card component instead  
**Reason**: Legacy vital statistics display component

```tsx
// ❌ Don't use (deprecated)
import VitalStatCard from '@/lib/VitalStatCard';

// ✅ Use instead
// Use the new vital card component from the appropriate module
```

## Deprecated Types

### Icon Types (`src/types/icon.ts`)

**Status**: Deprecated  
**Replacement**: Use `SVGProps<SVGSVGElement>` instead  
**Reason**: Legacy icon type definitions

```typescript
// ❌ Don't use (deprecated)
import { IconType } from '@/types/icon';

// ✅ Use instead
import { SVGProps } from 'react';
type IconProps = SVGProps<SVGSVGElement>;
```

### Patient Types (`src/types/index.ts`)

**Status**: Deprecated  
**Replacement**: Use types from `@/types/mrd/manage-patient/` instead  
**Reason**: Centralized type definitions in module-specific locations

```typescript
// ❌ Don't use (deprecated)
import { PatientType, VitalsType } from '@/types';

// ✅ Use instead
import { Patient } from '@/types/mrd/manage-patient/patient';
import { Vitals } from '@/types/mrd/manage-patient/vitals';
```

## Deprecated Store Functions

### Lifestyle Stores

**Files**:

- `src/store/lifestyle.ts`
- `src/store/lifestyle-utils-store.ts`

**Status**: Deprecated  
**Replacement**: Use `@/store/emr/lifestyle/` instead  
**Reason**: Moved to module-specific store organization

```typescript
// ❌ Don't use (deprecated)
import { useLifestyleStore } from '@/store/lifestyle';

// ✅ Use instead
import { useLifestyleStore } from '@/store/emr/lifestyle';
```

## Deprecated Query Functions

### Patient Queries (`src/query/patient.ts`)

**Deprecated Functions**:

- `updatePatient()` → Use `updatePatient` from `@/query/mrd/manage-patient/manage`
- `getPatient()` → Use `getPatient` from `@/query/mrd/manage-patient/manage`
- `getPatientVitals()` → Use `getPatientVitals` from `@/query/mrd/manage-patient/manage`
- `uploadVitals()` → Use `uploadVitals` from `@/query/mrd/search-patient`

```typescript
// ❌ Don't use (deprecated)
import { updatePatient, getPatient } from '@/query/patient';

// ✅ Use instead
import { updatePatient, getPatient } from '@/query/mrd/manage-patient/manage';
```

### Lifestyle Queries (`src/query/lifestyle.ts`)

**Status**: All functions deprecated  
**Replacement**: Use functions from `@/query/emr/lifestyle/index.ts`  
**Reason**: Moved to module-specific query organization

```typescript
// ❌ Don't use (deprecated)
import { getLifestyleData } from '@/query/lifestyle';

// ✅ Use instead
import { getLifestyleData } from '@/query/emr/lifestyle';
```

## Migration Guidelines

### For Existing Code

1. **Identify Usage**: Search codebase for deprecated imports
2. **Plan Migration**: Create migration plan for each deprecated component
3. **Test Thoroughly**: Ensure functionality remains intact after migration
4. **Update Gradually**: Migrate components one at a time

### For New Development

1. **Check Documentation**: Always check if a component is deprecated before use
2. **Use Core Components**: Prefer `@core/components/` over legacy components
3. **Follow Module Structure**: Use module-specific stores and queries
4. **Type Safety**: Use modern TypeScript types from appropriate modules

## Search and Replace Patterns

### Common Replacements

```bash
# Text Input
find: import.*from.*['"]@/lib/text_input['"]
replace: import AppTextField from '@core/components/app-text-field'

# Date Picker
find: import.*from.*['"]@/lib/common/date_picker['"]
replace: import AppDatePicker from '@core/components/app-date-picker'

# Modal
find: import.*from.*['"]@core/components/modal['"]
replace: import AppModal from '@core/components/app-modal'

# Patient Types
find: import.*from.*['"]@/types['"]
replace: import { Patient } from '@/types/mrd/manage-patient/patient'

# Lifestyle Store
find: import.*from.*['"]@/store/lifestyle['"]
replace: import.*from '@/store/emr/lifestyle'
```

## Removal Timeline

### Phase 1 (Current)

- Components marked as deprecated
- Warning messages in development
- Documentation updated

### Phase 2 (Next Release)

- Deprecation warnings in console
- Migration guides provided
- New components fully tested

### Phase 3 (Future Release)

- Deprecated components removed
- Breaking change notifications
- Migration required for upgrade

## Getting Help

### Migration Support

1. **Check Replacement**: Each deprecated item lists its replacement
2. **Review Examples**: New components include usage examples
3. **Test Changes**: Thoroughly test after migration
4. **Ask Team**: Consult team for complex migrations

### Common Issues

**Styling Differences**: New components may have different default styles

- **Solution**: Review and update custom CSS

**Prop Changes**: New components may have different prop interfaces

- **Solution**: Check component documentation for new prop structure

**Behavior Changes**: New components may behave differently

- **Solution**: Test thoroughly and adjust usage as needed

## Related Documentation

- [Core Components](./CORE_COMPONENTS.md) - Modern component alternatives
- [Folder Structure Guide](./FOLDER_STRUCTURE_GUIDE.md) - Current organization patterns
- [Code Format Rules](./CODE_FORMAT_RULES.md) - Development standards
