import React, { memo } from 'react';

import AppModal from '@/core/components/app-modal';
import { SuccessIcon } from '@/core/components/status-modal/modal-icons';

interface PrescriptionPaymentSuccessModalProps {
  open: boolean;
  onClose: () => void;
}

const PrescriptionPaymentSuccessModal: React.FC<
  PrescriptionPaymentSuccessModalProps
> = ({ open, onClose }) => {
  return (
    <AppModal
      open={open}
      onClose={onClose}
      classes={{
        root: 'w-[250px] !rounded-3xl relative',
        modal: '!rounded-3xl',
        header: 'hidden',
      }}
    >
      <div className="px-6 pb-4 pt-0 text-center">
        <div className="flex justify-center mb-4">
          <div className="scale-75">
            <SuccessIcon />
          </div>
        </div>
        <h3 className="text-lg font-semibold text-[#001926] mb-2">
          Payment Processed Successfully!
        </h3>
      </div>
    </AppModal>
  );
};

export default memo(PrescriptionPaymentSuccessModal);
