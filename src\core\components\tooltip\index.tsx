import React, { FC, memo } from 'react';

import { Tooltip as MuiTooltip, TooltipProps } from '@mui/material';

type Props = TooltipProps & {
  wrapperClassName?: string;
};

const Tooltip: FC<Props> = ({
  children,
  hidden,
  wrapperClassName = 'w-full',
  ...props
}) => {
  if (hidden) return <div className={wrapperClassName}>{children}</div>;

  return (
    <MuiTooltip arrow {...props}>
      <div className={wrapperClassName}>{children}</div>
    </MuiTooltip>
  );
};

export default memo(Tooltip);
