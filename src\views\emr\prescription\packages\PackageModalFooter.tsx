import React from 'react';

import { Box } from '@mui/material';

import { usePrescriptionPackageStore } from '@/store/emr/prescription/package';
import { useUserStore } from '@/store/userStore';

import PenIcon from '@/assets/svg/PenIcon';

import { commonButtonProps } from '@/constants/emr/lab';
import { PERMISSION_KEYS } from '@/constants/permission-keys';

import { Button } from '@/components/ui/button';

import PrimaryButton from '@/core/components/primary-button';
import {
  prescriptionModalModes,
  prescriptionPackageTypes,
} from '@/types/emr/prescription';

const { DETAIL, CREATE, ADD, VIEW } = prescriptionModalModes;

export const PackageModalFooter = () => {
  const {
    modalMode,
    setModalMode,
    updateExistingPackage,
    createNewPackage,
    selectedPackage,
    addSelectedPackageToMedicines,
    addNewMedicineToPackage,
    isCreating,
    clearAllMedicines,
    handleCancelWithCheck,
    setShowDeleteConfirmationModal,
    medicineItems,
    selectAllMedicines,
    activePackageType,
  } = usePrescriptionPackageStore();

  const isSelectedAnyMedicine = medicineItems.some((item) => item.selected);
  const { permissions } = useUserStore();

  const hasManagePermission = permissions.includes(
    PERMISSION_KEYS.EMR_MEDICINE_PACKAGE_MANAGE
  );

  const handleSavePackage = () => {
    if (modalMode === CREATE && selectedPackage) {
      updateExistingPackage();
    } else {
      createNewPackage();
    }
  };

  return (
    <div className="pb-2 px-4 flex justify-between relative">
      {modalMode === VIEW || modalMode === DETAIL ? (
        <Button
          onClick={addNewMedicineToPackage}
          className={`${commonButtonProps.className} !min-w-[110px]`}
          variant="outline"
          disabled={
            !hasManagePermission &&
            activePackageType === prescriptionPackageTypes.DEPARTMENT
          }
        >
          <span>Add New Package</span> <span className="text-base">+</span>
        </Button>
      ) : (
        <div />
      )}
      {modalMode === CREATE || modalMode === ADD ? (
        <Box display="flex" gap={1}>
          {selectedPackage && (
            <Button
              variant="link"
              onClick={() => setShowDeleteConfirmationModal(true)}
              {...commonButtonProps}
              className={`${commonButtonProps.className} bg-gray-200 text-black !min-w-[110px] hover:bg-gray-300`}
            >
              Delete Package
            </Button>
          )}
          <Button
            onClick={handleCancelWithCheck}
            variant="outline"
            className={`${commonButtonProps.className} text-gray-600 border-gray-300 !min-w-[110px] hover:bg-gray-50`}
          >
            Cancel
          </Button>
          <PrimaryButton
            onClick={handleSavePackage}
            {...commonButtonProps}
            className={`${commonButtonProps.className} !min-w-[110px]`}
            isLoading={isCreating}
          >
            Save Package
          </PrimaryButton>
        </Box>
      ) : modalMode === DETAIL ? (
        <Box display="flex" gap={1}>
          <Button
            onClick={
              !isSelectedAnyMedicine ? selectAllMedicines : clearAllMedicines
            }
            variant="link"
            {...commonButtonProps}
            className={`${commonButtonProps.className} bg-gray-200 text-black !min-w-[110px] hover:bg-gray-300`}
          >
            {!isSelectedAnyMedicine ? 'Select All' : 'Deselect All'}
          </Button>
          <Button
            variant="outline"
            onClick={() => setModalMode(CREATE)}
            {...commonButtonProps}
            className={`${commonButtonProps.className} !min-w-[110px]`}
          >
            Edit Package&nbsp;&nbsp;
            <PenIcon className="w-3 h-4" />
          </Button>
          <Button
            onClick={addSelectedPackageToMedicines}
            {...commonButtonProps}
            className={`${commonButtonProps.className} !min-w-[110px]`}
          >
            Add to Prescription
          </Button>
        </Box>
      ) : null}
    </div>
  );
};
