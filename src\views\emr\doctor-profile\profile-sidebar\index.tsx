import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import Avatar from '@/views/emr/doctor-profile/profile-sidebar/avatar';

import './styles.scss';
import Tabs from './tabs';

export default function ProfileSidebar() {
  const { data: userData } = useUserStore();

  const { doctorProfile } = useDoctorStore();

  return (
    <div className="col-span-2 profile-sidebar  h-full bg-white inline-flex flex-col 2xl:gap-5.5 gap-1 rounded-base shadow-base over-flow-hidden">
      <div className="card  grid gap-1  place-items-center px-1 pt-3 pb-2 2xl:pb-5  rounded-base border border-[#DAE1E7]">
        <Avatar />
        <div className=" grid  gap-0.5 w-full  text-white  px-1  ">
          <div className="font-semibold leading-7 text-lg  xl:text-xl text-center word-break w-full">
            {doctorProfile?.general?.fullName || userData?.name}
          </div>
          <div className="font-semibold leading-7 text-center w-full">
            {doctorProfile?.general?.designation ?? ''}
          </div>
          <div className="font-extralight capitalize text-center w-full">
            {doctorProfile?.general?.department ?? ''}
          </div>
        </div>
      </div>

      <Tabs />
    </div>
  );
}
