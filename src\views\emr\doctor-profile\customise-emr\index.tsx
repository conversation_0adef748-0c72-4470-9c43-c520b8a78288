'use client';

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import { debounce, maxBy } from 'lodash';
import { PencilIcon } from 'lucide-react';
import * as yup from 'yup';

import {
  EmrTypes,
  MultilevelDropdownType,
  useCustomiseEmrStore,
} from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { languageOptions } from '@/utils/constants/master';
import { getDisplayedItems } from '@/utils/emr/doctor-profile/customise-emr';

import InfoIcon from '@/assets/svg/InfoIcon';

import {
  extraNoteOptions,
  GRID_LAYOUTS,
  tilesLayoutButtonLabels,
  vitalOptions,
} from '@/constants/emr/doctor-profile/customise-emr';
import { PERMISSION_KEYS } from '@/constants/permission-keys';

import ControlledImageUploader from '@/components/controlled-inputs/ControlledImageUploader';
import PaymentGatewaySection from '@/components/payment/PaymentGatewaySection';

import { useFileUpload } from '@/emr/hooks/use-file-upload';

import MultiLevelDropdown from '@/core/components/multi-level-dropdown';
import CustomPopover from '@/core/components/popover';
import PrimaryButton from '@/core/components/primary-button';
import { hasPermission } from '@/core/lib/auth/permissions';

import { OutlinedButton } from '../personal-info/Components';

import { NotesPopover, PatientVitalsPopover } from './InfoContent';

export type CustomiseEmrType = {
  created_on?: string;
  tile_layout?: string | null;
  selected_tiles: string[][];
  medical_note_summary_template?: string;
  doc_assist_preference?: string;
  preferred_language_for_ambient_listening?: string;
  extraNote1?: string;
  extraNote2?: string;
  extraNote3?: string;
  id?: string;

  paymentPatientRegistration?: boolean;
  paymentAppointmentBooking?: boolean;
  paymentPrescription?: boolean;
  paymentLabTest?: boolean;
} & Record<string, any>;

const validationSchema = yup.object({
  tile_layout: yup.string().nullable().notRequired(),
  selected_tiles: yup
    .array()
    .of(yup.array().of(yup.string().required()).required().defined())
    .required()
    .defined()
    .test('validate-rows', 'All fields are required', function (value) {
      const { tile_layout } = this.parent;

      const rowsToValidate = tile_layout ? parseInt(tile_layout[0]) : 0;

      if (!value) return false;

      for (let i = 0; i < rowsToValidate; i++) {
        if (value[i]?.some((field) => !field)) {
          return this.createError({
            message: 'All fields are required',
            path: 'selected_tiles',
          });
        }
      }

      return true;
    }),

  paymentPatientRegistration: yup.boolean().notRequired(),
  paymentAppointmentBooking: yup.boolean().notRequired(),
  paymentPrescription: yup.boolean().notRequired(),
  paymentLabTest: yup.boolean().notRequired(),
});

const CustomiseEmrFrom = () => {
  const {
    dropdownOptions,
    updateCustomEmrEntry,
    customiseEmrData,
    isLoading,
    isDataLoading,
    fetchDropdownOptions,
    createCustomEmrEntry,
    fetchCustomiseEmr,
  } = useCustomiseEmrStore();

  const { doctorProfile } = useDoctorStore();
  const { data: userData } = useUserStore();
  const { fetchUserPermissions } = useUserStore();

  const { fileUpload } = useFileUpload({
    userId: doctorProfile?.id || userData?.id,
    type: 'document',
  });

  const previousIdRef = useRef<string | null>(null);
  const isFetchedRef = useRef(false);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceFetch = useCallback(
    debounce((id: string) => fetchCustomiseEmr(id), 300),
    []
  );

  const [openVilatInfo, setOpenVilatInfo] = useState<HTMLElement | null>(null);
  const [openNoteInfo, setOpenNoteInfo] = useState<HTMLElement | null>(null);
  const [isLetterHeadEditable, setIsLetterHeadEditable] = useState(false);
  const [isFormInitialized, setIsFormInitialized] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const isVilatInfoOpen = Boolean(openVilatInfo);
  const isNoteInfoOpen = Boolean(openNoteInfo);

  const getDefaultPaymentValues = () => {
    const { permissions = [] } = useUserStore.getState();
    return {
      paymentPatientRegistration: hasPermission(
        permissions,
        PERMISSION_KEYS.MRD_PAYMENT_PATIENT_REGISTRATION
      ),
      paymentAppointmentBooking: hasPermission(
        permissions,
        PERMISSION_KEYS.EMR_PAYMENT_APPOINTMENT_BOOKING
      ),
      paymentPrescription: hasPermission(
        permissions,
        PERMISSION_KEYS.EMR_PAYMENT_PRESCRIPTION
      ),
      paymentLabTest: hasPermission(
        permissions,
        PERMISSION_KEYS.EMR_PAYMENT_LAB_TEST
      ),
    };
  };

  const methods = useForm<CustomiseEmrType>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: yupResolver(validationSchema) as any,
    defaultValues: {
      tile_layout: '',
      selected_tiles: [[]],
      medical_note_summary_template: '',
      doc_assist_preference: '',
      preferred_language_for_ambient_listening: 'English',
      extraNote1: '',
      extraNote2: '',
      extraNote3: '',
      letterHeadDetails: '',
      organizationLogo: '',
      digitalSignature: '',
      paymentPatientRegistration: false,
      paymentAppointmentBooking: false,
      paymentPrescription: false,
      paymentLabTest: false,
    },
  });

  const {
    setValue,
    watch,
    handleSubmit,
    reset,
    clearErrors,
    formState: { errors },
  } = methods;

  const selectedButton = watch('tile_layout');

  const displayedItems = useMemo(
    () => getDisplayedItems(selectedButton as keyof typeof GRID_LAYOUTS),
    [selectedButton]
  );

  const getMedicalNoteSummaryOptions = useMemo(
    () => dropdownOptions?.medical_note_summary_template?.[0]?.data || [],
    [dropdownOptions?.medical_note_summary_template]
  );

  const getMedicalDocAssistOptions = useMemo(
    () => dropdownOptions?.doc_assist_preference?.[0]?.data || [],
    [dropdownOptions?.doc_assist_preference]
  );

  const getVitalOptions = useMemo(() => {
    return vitalOptions.map(({ key, value }) => {
      const subOptions =
        Array.isArray(dropdownOptions[key]) && dropdownOptions[key].length > 0
          ? dropdownOptions[key][0].data?.map(
              (item: MultilevelDropdownType) => ({
                key: item?.key,
                value: item?.value,
                subOptions: Array.isArray(item?.subparameters)
                  ? item?.subparameters?.map((sub) => ({
                      key: sub.key,
                      value: sub.value,
                    }))
                  : [],
              })
            ) || []
          : [];
      return {
        key,
        value,
        subOptions,
      };
    });
  }, [dropdownOptions]);

  const [selectedExtraNotes, setSelectedExtraNotes] = useState<{
    [key: string]: string;
  }>({});

  const getFilteredOptions = (excludeKey: string) => {
    return extraNoteOptions.filter(
      (option) =>
        !Object.entries(selectedExtraNotes).some(
          ([key, selected]) => selected === option.value && key !== excludeKey
        )
    );
  };

  const handleClear = (fieldName: string) => {
    setSelectedExtraNotes((prev) => {
      const updatedNotes = { ...prev };
      delete updatedNotes[fieldName];
      return updatedNotes;
    });

    setValue(fieldName, '');
  };

  const handleSelect = (name: string, value: string) => {
    setSelectedExtraNotes((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const lastResetDataRef = useRef<{ [key: string]: string[][] }>({});

  const handleGridChange = useCallback(
    (rows: number) => {
      const existingTiles = watch('selected_tiles');
      const key = `${rows}x3`;

      const prevKey = `${existingTiles?.length || 0}x3`;
      if (existingTiles?.length) {
        lastResetDataRef.current[prevKey] = existingTiles.map((row) => [
          ...row,
        ]);
      }

      const newTiles = Array.from({ length: rows }, (_, rowIndex) =>
        Array(3)
          .fill('')
          .map(
            (_, colIndex) =>
              lastResetDataRef.current[key]?.[rowIndex]?.[colIndex] ?? ''
          )
      );

      setValue('selected_tiles', newTiles);
      clearErrors('selected_tiles');
    },
    [setValue, clearErrors, watch]
  );

  useEffect(() => {
    if (isSaving) return;

    if (customiseEmrData && customiseEmrData.length > 0) {
      const lastCreatedEmrData = maxBy<EmrTypes>(
        customiseEmrData,
        (item) => new Date(item.created_on as string)
      );

      if (lastCreatedEmrData) {
        const defaultPaymentValues = getDefaultPaymentValues();
        const transformedEmrData = {
          ...lastCreatedEmrData,
          tile_layout: selectedButton || lastCreatedEmrData.tile_layout,
          selected_tiles:
            selectedButton === lastCreatedEmrData.tile_layout
              ? lastCreatedEmrData.selected_tiles?.map((tileArray) =>
                  tileArray.map((tile) => tile.key)
                ) || []
              : [],

          paymentPatientRegistration:
            lastCreatedEmrData.paymentPatientRegistration ??
            defaultPaymentValues.paymentPatientRegistration,
          paymentAppointmentBooking:
            lastCreatedEmrData.paymentAppointmentBooking ??
            defaultPaymentValues.paymentAppointmentBooking,
          paymentPrescription:
            lastCreatedEmrData.paymentPrescription ??
            defaultPaymentValues.paymentPrescription,
          paymentLabTest:
            lastCreatedEmrData.paymentLabTest ??
            defaultPaymentValues.paymentLabTest,
        };

        reset(transformedEmrData);
        setIsFormInitialized(true);
      }
    } else if (!isDataLoading) {
      const defaultPaymentValues = getDefaultPaymentValues();
      reset((currentValues) => ({
        ...currentValues,
        ...defaultPaymentValues,
      }));
      setIsFormInitialized(true);
    }
  }, [customiseEmrData, reset, selectedButton, isDataLoading, isSaving]);

  const onSubmit = async (data: CustomiseEmrType) => {
    setIsSaving(true);

    const lastCreatedObject = maxBy<EmrTypes>(
      customiseEmrData,
      (item) => new Date(item.created_on as string)
    );

    let organizationLogoUrl = data.organizationLogo;
    let digitalSignatureUrl = data.digitalSignature;

    try {
      if (data.organizationLogo instanceof File) {
        organizationLogoUrl = await fileUpload(data.organizationLogo);
      }

      if (data.digitalSignature instanceof File) {
        digitalSignatureUrl = await fileUpload(data.digitalSignature);
      }

      const [rows] = data?.tile_layout?.split('x').map(Number) ?? [];
      const filteredTiles = data.selected_tiles.slice(0, rows);

      const selectedTileObjects = filteredTiles.map((tileArray, rowIndex) =>
        tileArray.map((tile, colIndex) => {
          const allOptions = Object.values(dropdownOptions)
            .flat()
            .flatMap((category) => category?.data ?? []);
          const matchedObject = allOptions.find((opt) => opt.key === tile);
          const fallbackTile =
            lastCreatedObject?.selected_tiles?.[rowIndex]?.[colIndex];

          if (matchedObject) {
            return {
              key: matchedObject.key,
              value: matchedObject.value,
              abbreviationCode: matchedObject.abbreviationCode,
              unit: matchedObject.unit,
            };
          }
          if (fallbackTile) {
            return {
              key: fallbackTile.key,
              value: fallbackTile.value,
              abbreviationCode: fallbackTile.abbreviationCode,
              unit: fallbackTile.unit,
            };
          }
          return { key: tile, value: tile, abbreviationCode: '', unit: '' };
        })
      );

      const finalData = {
        doc_assist_preference: data.doc_assist_preference,
        preferred_language_for_ambient_listening:
          data.preferred_language_for_ambient_listening,
        selected_tiles: selectedTileObjects,
        tile_layout: data.tile_layout,
        medical_note_summary_template: data.medical_note_summary_template,
        extraNote1: data.extraNote1,
        extraNote2: data.extraNote2,
        extraNote3: data.extraNote3,
        letterHeadDetails: data.letterHeadDetails,
        organizationLogo: organizationLogoUrl,
        digitalSignature: digitalSignatureUrl,

        paymentPatientRegistration: data.paymentPatientRegistration,
        paymentAppointmentBooking: data.paymentAppointmentBooking,
        paymentPrescription: data.paymentPrescription,
        paymentLabTest: data.paymentLabTest,
      };

      if (customiseEmrData && lastCreatedObject?.id) {
        await updateCustomEmrEntry(lastCreatedObject?.id as string, finalData);

        if (doctorProfile?.id) {
          await fetchCustomiseEmr(doctorProfile.id);
        }
      } else {
        await createCustomEmrEntry(finalData, doctorProfile?.id as string);
      }
    } catch (error) {
      console.error('Error in onSubmit:', error);
    } finally {
      setIsSaving(false);
    }
  };

  useEffect(() => {
    if (doctorProfile?.id && previousIdRef.current !== doctorProfile.id) {
      previousIdRef.current = doctorProfile.id;
      debounceFetch(doctorProfile.id);
    }
  }, [debounceFetch, doctorProfile?.id]);

  useEffect(() => {
    if (!isFetchedRef.current) {
      fetchDropdownOptions('medical_note_summary_template');
      fetchDropdownOptions('doc_assist_preference');
      isFetchedRef.current = true;
    }
  }, [fetchDropdownOptions]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        await fetchUserPermissions();
      } catch (error) {
        console.error('Error in fetchData:', error);
      }
    };
    fetchData();
  }, [fetchUserPermissions]);

  const handleVilatInfoClick = (event: React.MouseEvent<HTMLElement>) => {
    setOpenVilatInfo(event.currentTarget as HTMLElement);
  };

  const handleNoteInfoClick = (event: React.MouseEvent<HTMLElement>) => {
    setOpenNoteInfo(event.currentTarget as HTMLElement);
  };

  const handleClose = () => {
    setOpenVilatInfo(null);
    setOpenNoteInfo(null);
  };

  const handleLetterHeadEditToggle = () => {
    setIsLetterHeadEditable(!isLetterHeadEditable);
  };

  const paymentPatientRegistration = watch('paymentPatientRegistration');
  const paymentAppointmentBooking = watch('paymentAppointmentBooking');
  const paymentPrescription = watch('paymentPrescription');
  const paymentLabTest = watch('paymentLabTest');

  const paymentFeatures = useMemo(
    () => [
      {
        id: 'patientRegistration',
        label: 'Patient Registration',
        permission: PERMISSION_KEYS.MRD_PAYMENT_PATIENT_REGISTRATION,
        checked: !!paymentPatientRegistration,
      },
      {
        id: 'appointmentBooking',
        label: 'Appointment Booking',
        permission: PERMISSION_KEYS.EMR_PAYMENT_APPOINTMENT_BOOKING,
        checked: !!paymentAppointmentBooking,
      },
      {
        id: 'prescription',
        label: 'Prescription',
        permission: PERMISSION_KEYS.EMR_PAYMENT_PRESCRIPTION,
        checked: !!paymentPrescription,
      },
      {
        id: 'labTest',
        label: 'Lab Master',
        permission: PERMISSION_KEYS.EMR_PAYMENT_LAB_TEST,
        checked: !!paymentLabTest,
      },
    ],
    [
      paymentPatientRegistration,
      paymentAppointmentBooking,
      paymentPrescription,
      paymentLabTest,
    ]
  );

  const handlePaymentFeatureToggle = (featureId: string, checked: boolean) => {
    const fieldMap: Record<string, keyof CustomiseEmrType> = {
      patientRegistration: 'paymentPatientRegistration',
      appointmentBooking: 'paymentAppointmentBooking',
      prescription: 'paymentPrescription',
      labTest: 'paymentLabTest',
    };

    const fieldName = fieldMap[featureId];
    if (fieldName) {
      setValue(fieldName, checked);
    }
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)} className="row-span-12">
        <div className="w-full mt-4 mx-auto  overflow-y-hidden flex flex-col h-full ">
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Consultation Screen</h3>
            <div className="mt-4 gap-5 flex justify-start w-full">
              <p className="text-base">Tiles Layout</p>
              <div className="flex items-center">
                <div className="flex gap-3">
                  {tilesLayoutButtonLabels.map((label) => (
                    <OutlinedButton
                      key={label}
                      label={label}
                      isSelected={selectedButton === label}
                      onClick={() => {
                        setValue('tile_layout', label);
                        handleGridChange(parseInt(label[0]));
                      }}
                    />
                  ))}
                </div>

                <button
                  type="button"
                  className="ml-2 "
                  onClick={handleVilatInfoClick}
                >
                  <InfoIcon height={'20'} width={'20'} />
                </button>
              </div>
              <CustomPopover
                open={isVilatInfoOpen}
                anchorEl={openVilatInfo}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'left',
                }}
              >
                <PatientVitalsPopover />
              </CustomPopover>
            </div>
            {selectedButton && (
              <div className="grid grid-rows-auto gap-2 max-w-lg mt-4">
                {displayedItems.map((row, rowIndex) => (
                  <div key={rowIndex} className="flex gap-3 ">
                    {row.map((_, colIndex) => (
                      <div key={colIndex} className="flex flex-col">
                        <MultiLevelDropdown
                          options={getVitalOptions}
                          name={`selected_tiles.${rowIndex}.${colIndex}`}
                          error={errors.selected_tiles?.[rowIndex]?.[colIndex]}
                          isMultiLevel
                          loading={isLoading}
                          onSelect={(value, key) => {
                            methods.clearErrors(
                              `selected_tiles.${rowIndex}.${colIndex}`
                            );
                            if (key) {
                              fetchDropdownOptions(key);
                            }
                          }}
                        />
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="mt-3 max-w-[41%] xl:max-w-[36%] 2xl:max-w-[32%]">
            <MultiLevelDropdown
              options={languageOptions}
              name="preferred_language_for_ambient_listening"
              label="Preferred Language for Ambient Listening"
              maxHeight="max-h-35 2xl:max-h-50"
            />
          </div>
          <div className="flex gap-4 max-w-5xl mt-10 mr-62">
            <div className="w-1/2">
              <MultiLevelDropdown
                options={getMedicalNoteSummaryOptions}
                name="medical_note_summary_template"
                label="Medical Note Summary Template"
                maxHeight="max-h-35 xl:max-h-50"
                isLoading={isLoading}
              />
            </div>
            <div className="w-1/2">
              <MultiLevelDropdown
                options={getMedicalDocAssistOptions}
                name="doc_assist_preference"
                label="Doc Assist Preference"
                maxHeight="max-h-35 2xl:max-h-50"
                isLoading={isLoading}
              />
            </div>
          </div>

          <div className="mt-10 max-w-5xl mr-110">
            <div className="flex items-center">
              <label>Extra Notes</label>
              <button
                type="button"
                className="ml-2 mt-2 "
                onClick={handleNoteInfoClick}
              >
                <InfoIcon height={'20'} width={'20'} />
              </button>
              <CustomPopover
                open={isNoteInfoOpen}
                anchorEl={openNoteInfo}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'left',
                }}
              >
                <NotesPopover />
              </CustomPopover>
            </div>

            <div className="flex gap-4 mt-3">
              <div className="flex-1">
                <MultiLevelDropdown
                  options={getFilteredOptions('extraNote1')}
                  name="extraNote1"
                  maxHeight="max-h-35 xl:max-h-50"
                  onSelect={(value) => handleSelect('extraNote1', value)}
                  isClearable
                  onClear={() => handleClear('extraNote1')}
                />
              </div>
              <div className="flex-1">
                <MultiLevelDropdown
                  options={getFilteredOptions('extraNote2')}
                  name="extraNote2"
                  maxHeight="max-h-35 xl:max-h-50"
                  onSelect={(value) => handleSelect('extraNote2', value)}
                  isClearable
                  onClear={() => handleClear('extraNote2')}
                />
              </div>
              <div className="flex-1">
                <MultiLevelDropdown
                  options={getFilteredOptions('extraNote3')}
                  name="extraNote3"
                  maxHeight="max-h-35 xl:max-h-50"
                  onSelect={(value) => handleSelect('extraNote3', value)}
                  isClearable
                  onClear={() => handleClear('extraNote3')}
                />
              </div>
            </div>
          </div>
          <div className="mt-10 max-w-5xl">
            <label>Print Template</label>
            <div className="flex gap-16 mt-5">
              <div>
                <div className="flex items-center gap-2 mb-2 ">
                  <label className="text-base text-black">
                    Letter Head Details
                  </label>
                </div>
                <div className="relative">
                  <textarea
                    {...methods.register('letterHeadDetails')}
                    rows={5}
                    className={`w-full p-3 pr-10 border border-gray-300 rounded-md resize-none text-sm h-51 ml-4 ${
                      isLetterHeadEditable
                        ? 'focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white'
                        : 'bg-gray-50 cursor-default'
                    }`}
                    placeholder="Text"
                    defaultValue="Text"
                    readOnly={!isLetterHeadEditable}
                  />
                  <button
                    type="button"
                    onClick={handleLetterHeadEditToggle}
                    className="absolute top-3 right-3 p-1 text-gray-500 hover:text-gray-700"
                  >
                    <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
                  </button>
                </div>
              </div>
              <div className="mr-10">
                <ControlledImageUploader
                  name="organizationLogo"
                  control={methods.control}
                  label="Organisation Logo"
                  accept=".png, .jpg, .jpeg"
                  maxSizeInMB={5}
                  showError={true}
                />
              </div>
              <div>
                <ControlledImageUploader
                  name="digitalSignature"
                  control={methods.control}
                  label="Digital Signature"
                  accept=".png, .jpg, .jpeg"
                  maxSizeInMB={5}
                  showError={true}
                />
              </div>
            </div>
          </div>

          {isFormInitialized && (
            <PaymentGatewaySection
              paymentFeatures={paymentFeatures}
              onFeatureToggle={handlePaymentFeatureToggle}
            />
          )}

          <div className="fixed bottom-0 right-10 bg-transparent p-4  flex justify-end">
            <PrimaryButton
              type="submit"
              className="capitalize text-xl gap-2"
              isLoading={isDataLoading}
            >
              Save Changes
            </PrimaryButton>
          </div>
        </div>
      </form>
    </FormProvider>
  );
};

export default CustomiseEmrFrom;
