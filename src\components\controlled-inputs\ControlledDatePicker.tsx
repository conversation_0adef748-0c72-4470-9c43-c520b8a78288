import React from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import AppDatePicker, {
  AppDatePickerProps,
} from '@core/components/app-date-picker';

export type ControlledDatePickerProps<T extends FieldValues> = Omit<
  AppDatePickerProps,
  'value' | 'onChange'
> & {
  name: Path<T>;
  control: Control<T>;
  rules?: any;
  disableFuture?: boolean;
};

const ControlledDatePicker = <T extends FieldValues>({
  name,
  control,
  rules,
  disableFuture,
  ...props
}: ControlledDatePickerProps<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState }) => (
        <AppDatePicker
          {...props}
          value={field.value}
          onChange={field.onChange}
          error={props.error || fieldState.error?.message}
          disableFuture={disableFuture}
        />
      )}
    />
  );
};

export default ControlledDatePicker;
