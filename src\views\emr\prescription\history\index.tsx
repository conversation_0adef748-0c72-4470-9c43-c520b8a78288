import React, { memo, useMemo } from 'react';

import { Box, styled } from '@mui/material';

import { usePrescriptionStore } from '@/store/emr/prescription';

import {
  prescriptionHistoryView,
  PrescriptionHistoryViewType,
  type PrescriptionHistory,
} from '@/types/emr/prescription';

import HistoryDetails from './HistoryDetails';
import HistoryList from './HistoryList';

const Container = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  padding: theme.spacing(0.5),
  overflowY: 'auto',
  height: '100%',
  width: '100%',
}));

const { LIST, DETAILS } = prescriptionHistoryView;

const PrescriptionHistory = () => {
  const historyView = usePrescriptionStore((state) => state.historyView);

  const historyComponents = useMemo<
    Record<PrescriptionHistoryViewType, React.ReactNode>
  >(
    () => ({
      [LIST]: <HistoryList />,
      [DETAILS]: <HistoryDetails />,
    }),
    []
  );

  return <Container>{historyComponents[historyView]}</Container>;
};

export default memo(PrescriptionHistory);
