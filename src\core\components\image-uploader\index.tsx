import React, { FC, useCallback, useEffect, useRef, useState } from 'react';

import { AiFillFilePdf } from 'react-icons/ai';
import { BiUpload } from 'react-icons/bi';

import usePdf from '@/hooks/use-pdf';

import { regex } from '@/utils/constants/regex';

import PreviewModal from '../file-input/PreviewModal';
import InputLabel, { InputLabelProps } from '../input-label';

import PreviewButton from './PreviewButton';
import RemoveButton from './RemoveButton';
import './styles.scss';

export type ImageUploadProps = InputLabelProps & {
  value: any;
  onChange: any;
  accept?: string;
  maxSizeInMB?: number;
  indicationLabel?: string;
  showError?: boolean;
  readonly?: boolean;
};

const getSelectedFileName = (file: File | null | string): string => {
  if (typeof file === 'string') {
    return decodeURIComponent(file.split('/').pop() || '');
  } else if (file instanceof File && file) {
    return file.name;
  }
  return '';
};

const ImageUploader: FC<ImageUploadProps> = ({
  value,
  onChange,
  accept,
  maxSizeInMB,
  indicationLabel,
  showError = true,
  label,
  required,
  readonly = false,
}) => {
  const { previewPdf } = usePdf();

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const [selectedFile, setSelectedFile] = useState<File | null | string>(value);
  const [preview, setPreview] = useState<string | undefined>();
  const [error, setError] = useState<string | null>(null);
  const [isPDF, setIsPDF] = useState<boolean>(false);
  const [showPreviewOption, setShowPreviewOption] = useState<boolean>(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  useEffect(() => {
    setSelectedFile(value);
  }, [value]);

  useEffect(() => {
    if (!selectedFile) {
      setPreview(undefined);
      setIsPDF(false);
      return;
    }

    if (selectedFile instanceof File) {
      const fileType = selectedFile.type;
      if (fileType === 'application/pdf') {
        setIsPDF(true);
        setPreview(undefined);
      } else {
        setIsPDF(false);
        const objectUrl: string = URL.createObjectURL(selectedFile);
        setPreview(objectUrl);
        return () => URL.revokeObjectURL(objectUrl);
      }
    } else if (typeof selectedFile === 'string' && selectedFile.trim() !== '') {
      setIsPDF(selectedFile.endsWith('.pdf'));
      setPreview(selectedFile);
    } else {
      setPreview(undefined);
      setIsPDF(false);
    }
  }, [selectedFile]);

  const onSelectFile = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!e.target.files || e.target.files.length === 0) {
        if (selectedFile) {
          setSelectedFile(selectedFile);
          onChange(selectedFile);
        } else {
          setSelectedFile(null);
        }
        return;
      }

      const file = e.target.files[0];
      const acceptPattern = accept
        ?.split(',')
        .map((ext) => ext.trim().replace('.', '\\.').replace('*', '.*'))
        .join('|');

      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      if (
        !fileExtension ||
        !new RegExp(`(${acceptPattern})$`, 'i').test(`.${fileExtension}`)
      ) {
        setError(`Invalid file type! Only ${accept} are allowed.`);
        e.target.value = '';
        return;
      }

      if (maxSizeInMB && file.size > maxSizeInMB * 1024 * 1024) {
        setError(`File size must be less than ${maxSizeInMB} MB.`);
        e.target.value = '';
        return;
      }

      setError(null);
      setSelectedFile(file);
      onChange(file);
    },
    [accept, maxSizeInMB, onChange, selectedFile]
  );

  const openFilePreview = useCallback(() => {
    if (!selectedFile) return;

    if (typeof selectedFile === 'string') {
      if (selectedFile.endsWith('.pdf')) {
        previewPdf(selectedFile);
      } else if (selectedFile.match(regex.IMAGE_FILE_EXTENSION_REGEX)) {
        setImagePreview(selectedFile);
      }
    } else if (selectedFile instanceof File) {
      const fileUrl = URL.createObjectURL(selectedFile);

      if (selectedFile.type === 'application/pdf') {
        previewPdf(fileUrl);
      } else if (selectedFile.type.startsWith('image/')) {
        setImagePreview(fileUrl);
      }
      setTimeout(() => URL.revokeObjectURL(fileUrl), 5000);
    }
  }, [previewPdf, selectedFile]);

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
    setPreview(undefined);
    setIsPDF(false);
    onChange(null);
  }, [onChange]);

  const renderImageFileUploader = useCallback(() => {
    if (!selectedFile && !readonly) {
      return (
        <div
          className="flex flex-col items-center gap-2 h-45 w-42 justify-center flex-1 m-1 mt-2"
          onClick={() => {
            if (readonly) return;
            fileInputRef.current?.click();
          }}
        >
          <BiUpload />
          <span>Upload file</span>
          <input
            type="file"
            onChange={onSelectFile}
            className="hidden"
            accept={accept}
            ref={fileInputRef}
            readOnly={readonly}
          />
        </div>
      );
    }

    if (isPDF) {
      return (
        <div
          className="flex flex-col items-center rounded justify-center w-42 h-45 bg-gray-200 text-red-500 overflow-visible relative m-1 mt-2"
          onClick={() => {
            openFilePreview();
            setShowPreviewOption(false);
          }}
        >
          <RemoveButton
            showPreviewOption={showPreviewOption && !readonly}
            onClick={handleRemoveFile}
          />
          <AiFillFilePdf size={48} />
          <span className="text-xs text-gray-600 w-full text-center">
            {getSelectedFileName(selectedFile)}
          </span>
          <PreviewButton
            showPreviewOption={showPreviewOption}
            className="rounded-b"
          />
        </div>
      );
    }

    if (preview) {
      return (
        <div className="max-w-42 max-h-45 w-fit h-fit relative overflow-visible m-1 mt-2">
          <RemoveButton
            showPreviewOption={showPreviewOption && !readonly}
            onClick={handleRemoveFile}
          />
          <div
            className="flex flex-col items-center rounded justify-center max-w-42 max-h-45 w-fit h-fit overflow-hidden relative"
            onClick={() => {
              openFilePreview();
              setShowPreviewOption(false);
            }}
          >
            <img
              src={preview}
              className="max-w-42 max-h-45 object-contain"
              alt="image"
            />
            <PreviewButton showPreviewOption={showPreviewOption} />
          </div>
        </div>
      );
    }

    return null;
  }, [
    accept,
    handleRemoveFile,
    isPDF,
    onSelectFile,
    openFilePreview,
    preview,
    readonly,
    selectedFile,
    showPreviewOption,
  ]);

  return (
    <div className="flex flex-col w-full h-full">
      <InputLabel
        label={label}
        required={required}
        indicationLabel={indicationLabel}
        error={Boolean(error)}
      />
      <div className="flex flex-col rounded-lg w-fit custom-dashed-border">
        <div
          className="w-fit min-w-42 min-h-45 flex flex-col justify-center items-center overflow-hidden cursor-pointer"
          onMouseEnter={() => setShowPreviewOption(true)}
          onMouseLeave={() => setShowPreviewOption(false)}
        >
          {renderImageFileUploader()}
        </div>
        {error && showError && (
          <span className="text-red-500 text-sm mt-2">{error}</span>
        )}
      </div>
      <PreviewModal
        imagePreview={imagePreview}
        onClose={() => setImagePreview(null)}
      />
    </div>
  );
};

export default ImageUploader;
