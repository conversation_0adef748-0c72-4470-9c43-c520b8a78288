import React, { memo } from 'react';

import AppButton from '@/core/components/app-button';
import AppModal from '@/core/components/app-modal';

interface PaymentConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  onProceedToPay: () => void;
  loading?: boolean;
}

const PaymentConfirmationModal: React.FC<PaymentConfirmationModalProps> = ({
  open,
  onClose,
  onProceedToPay,
  loading = false,
}) => {
  return (
    <AppModal
      open={open}
      onClose={onClose}
      title="Confirmation to Make Payment"
      classes={{
        root: 'w-86 max-w-md px-2',
        header: '!border-b-0 ',
        modal: '!p-0',
      }}
    >
      <div className="flex gap-3 mt-0 mb-2">
        <AppButton
          variant="outlined"
          onClick={onClose}
          fullWidth
          disabled={loading}
        >
          Cancel
        </AppButton>
        <AppButton onClick={onProceedToPay} fullWidth loading={loading}>
          Proceed to Pay
        </AppButton>
      </div>
    </AppModal>
  );
};

export default memo(PaymentConfirmationModal);
