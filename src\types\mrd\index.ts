export type Vitals = {
  bloodPressure: string;
  bmi: string;
  diastolic: string;
  height: string;
  pulse: string;
  rr: string;
  systolic: string;
  weight: string;
};

export type PatientRecord = {
  id: string;
  patientId: string;
  created_by: string;
  created_on: string;
  update_by: string;
  updated_on: string;
  vitals: Vitals;
  _attachments: string;
  _etag: string;
  _rid: string;
  _self: string;
  _ts: number;
};

export interface PatientI {
  name: string;
  sex: string;
  age: string;
  dob: string;
  height: string;
  weight: string;
  address: string;
  aadhar: string;
  abha: string;
  contact: {
    phone: string;
    email: string;
  };
  insurance: {
    provider: string;
    id: string;
  };
  vitals?: PatientRecord[];
  id: string;
  created_on: string;
  updated_on: string;
  last_consultation_date: string | null;
  _rid: string;
  _self: string;
  _etag: string;
  _attachments: string;
  _ts: number;
}
