import { SilentRequest } from '@azure/msal-browser';
import { toast } from 'sonner';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useTestReportFilterStore } from '@/store/emr/lab/test-report-filter';

import API_CONFIG from '@/core/configs/api';

import { msalInstance, Scope } from './msal';

export const appURL = API_CONFIG.APP_BASE_URL;

export async function login() {
  try {
    useDoctorStore.getState().clearStorage();
    useCurrentPatientStore.getState().clear();
    useTestReportFilterStore.getState().resetState();
    localStorage?.clear();

    await msalInstance.initialize();

    const res = await msalInstance.loginPopup({
      scopes: [Scope.ApiRead],
    });

    toast.success(`Welcome ${res?.account?.name}`);
  } catch (error) {
    console.error(error);
    toast.error('Lo<PERSON> failed');
  }
}

export async function logout() {
  try {
    await msalInstance.initialize();
    await msalInstance.logoutPopup({
      account: msalInstance.getActiveAccount(),
    });
    useDoctorStore.getState().clearStorage();
    useCurrentPatientStore.getState().clear();
    useTestReportFilterStore.getState().resetState();
    localStorage?.clear();
    window.location.href = '/login';
  } catch (error) {
    console.error(error);
    // toast.error('Logout failed');
  }
}

export async function getToken() {
  await msalInstance.initialize();

  const accounts = msalInstance.getAllAccounts();

  if (accounts.length === 0) {
    await logout();
    window.location.href = '/login';
    throw new Error('No accounts found');
  }

  try {
    const silentTokenRequest: SilentRequest = {
      scopes: [Scope.ApiRead],
      account: accounts[0],
    };

    const tokenResponse =
      await msalInstance.acquireTokenSilent(silentTokenRequest);

    if (!tokenResponse?.idToken) {
      await logout();
      window.location.href = '/login';
      throw new Error('Token retrieval failed');
    }

    return tokenResponse.idToken;
  } catch (error) {
    console.error('Error acquiring token:', error);
    await logout();
    window.location.href = '/login';
    throw new Error('Failed to acquire token');
  }
}

export function getActiveAccount() {
  try {
    const account = msalInstance.getActiveAccount();

    return account || null;
  } catch (_error) {
    console.error('Error getting active account:', _error);
    return null;
  }
}

export async function setFirstAccountAsActive() {
  try {
    const accounts = msalInstance.getAllAccounts();

    if (accounts.length) {
      msalInstance.setActiveAccount(accounts[0]);
    }
  } catch (error) {
    console.error(error);
    toast.error('Error setting active account');
  }
}
