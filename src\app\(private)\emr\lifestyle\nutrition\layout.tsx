'use client';

import { memo } from 'react';

import { routes } from '@/constants/routes';

import PageTab, { TabItem } from '@/views/emr/lifestyle/shared/page-tab';
import PageTitle from '@/views/emr/lifestyle/shared/PageTitle';

const tabs: TabItem[] = [
  {
    label: 'Dashboard',
    path: routes.EMR_LIFESTYLE_NUTRITION,
  },
  {
    label: 'Practice',
    path: routes.EMR_LIFESTYLE_NUTRITION_PRACTICE,
  },
  {
    label: 'Attitude',
    path: routes.EMR_LIFESTYLE_NUTRITION_ATTITUDE,
  },
  {
    label: 'Knowledge',
    path: routes.EMR_LIFESTYLE_NUTRITION_KNOWLEDGE,
  },
];

const NutritionLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div id="nutrition-layout" className="h-full flex flex-col w-full">
      {/* Page Title */}
      <PageTitle title="Consultation Timeline" />

      {/* Tab Navigation */}
      <PageTab tabs={tabs} />

      {/* Content */}
      <div className="flex-1 overflow-hidden w-full">{children}</div>
    </div>
  );
};

export default memo(NutritionLayout);
