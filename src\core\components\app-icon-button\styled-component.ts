import { IconButton, styled } from '@mui/material';

import { AppIconButtonProps } from './type';

export const MuiIconButton = styled(IconButton, {
  shouldForwardProp: (prop) => prop !== 'variant',
})<AppIconButtonProps>(({
  theme,
  variant,
  size = 'medium',
  color = 'default',
}) => {
  const sizeStyles = {
    small: {
      height: 20,
      width: 20,
      '& svg': { height: 16, width: 16 },
    },
    medium: {
      height: 30,
      width: 30,
      '& svg': { height: 20, width: 20 },
    },
    large: {
      height: 40,
      width: 40,
      '& svg': { height: 24, width: 24 },
    },
  };

  const colorMap: Record<NonNullable<AppIconButtonProps['color']>, string> = {
    primary: theme.palette.primary.main,
    secondary: theme.palette.secondary.main,
    error: theme.palette.error.main,
    warning: theme.palette.warning.main,
    info: theme.palette.info.main,
    success: theme.palette.success.main,
    default: theme.palette.grey[700],
    inherit: 'inherit',
  };

  const iconButtonColor = colorMap[color] || colorMap.default;

  const outlinedStyle =
    variant === 'outlined'
      ? {
          border: `1px solid ${iconButtonColor}`,
          borderRadius: '50%',
          padding: 5,
        }
      : {};

  return {
    ...sizeStyles[size],
    color: iconButtonColor,
    ...outlinedStyle,
  };
});
