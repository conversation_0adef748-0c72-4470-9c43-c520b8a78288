import React from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import colors from '@/utils/colors';

import { LifestyleQuestion } from '@/emr/types/lifestyle';

import PatientDetails from '../shared/PatientDetails';
import RenderFields from '../shared/RenderFields';

type Props = {
  data: LifestyleQuestion;
  isMaximized?: boolean;
};

const NutritionMonitoringItem: React.FC<Props> = ({ data, isMaximized }) => {
  const methods = useForm<LifestyleQuestion>({ defaultValues: data });

  return (
    <FormProvider {...methods}>
      <div className="flex flex-col gap-3">
        <PatientDetails cardBgColor={colors.common.lightGray} />
        <div className="flex flex-col gap-2">
          {data?.sections?.map((q, i) => (
            <RenderFields
              key={q.section_title}
              questions={q?.questions || []}
              icon={q?.icon}
              sectionTitle={q?.section_title}
              type={q?.type}
              control={methods.control}
              isReadOnly={true}
              isMaximized={isMaximized}
              name={`sections.${i}.questions`}
            />
          ))}
        </div>
      </div>
    </FormProvider>
  );
};

export default NutritionMonitoringItem;
