import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export type Conversation = {
  speaker: 'doctor' | 'patient';
  message: string;
};

export type AmbientSummary = {
  presentingcomplaint?: string;
  historyofpresenting?: string;
  pastmedicalhistory?: string;
  pastsurgicalhistory?: string;
  familyhistory?: string;
  addictionhistory?: string;
  diethistory?: string;
  physicalactivityhistory?: string;
  stresshistory?: string;
  sleephistory?: string;
  currentmedicationhistory?: string;
  [key: string]: any; // For any additional fields
};

type AmbientStoreState = {
  // Conversation data
  conversation: Conversation[];
  // Summary data
  summary: AmbientSummary | null;
  // Loading states
  isLoading: boolean;
  error: string | null;
  // Actions
  setConversation: (conversation: Conversation[]) => void;
  setSummary: (summary: AmbientSummary) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
};

const initialState = {
  conversation: [],
  summary: null,
  isLoading: false,
  error: null,
};

export const useAmbientStore = create<AmbientStoreState>()(
  devtools(
    (set) => ({
      ...initialState,
      setConversation: (conversation) => set({ conversation }),
      setSummary: (summary) => set({ summary }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      reset: () => set(initialState),
    }),
    {
      name: 'ambient-listening-store',
    }
  )
);

// Selectors
export const ambientSelectors = {
  selectConversation: (state: AmbientStoreState) => state.conversation,
  selectSummary: (state: AmbientStoreState) => state.summary,
  selectIsLoading: (state: AmbientStoreState) => state.isLoading,
  selectError: (state: AmbientStoreState) => state.error,
};

// Action to process and set ambient data from API response
export const processAmbientData = (data: {
  conversation: Conversation[];
  summary: AmbientSummary;
}) => {
  const { setConversation, setSummary } = useAmbientStore.getState();

  // Process and set conversation data
  if (data.conversation && Array.isArray(data.conversation)) {
    setConversation(data.conversation);
  }

  // Process and set summary data
  if (data.summary) {
    // Transform summary keys to match form field names if needed
    const processedSummary = Object.entries(data.summary).reduce(
      (acc, [key, value]) => {
        // Convert snake_case to camelCase if needed
        const camelKey = key.replace(/([-_][a-z])/g, (group) =>
          group.toUpperCase().replace('-', '').replace('_', '')
        );
        return { ...acc, [camelKey]: value };
      },
      {} as AmbientSummary
    );

    setSummary(processedSummary);
  }
};

export default useAmbientStore;
