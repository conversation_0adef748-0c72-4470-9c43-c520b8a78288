import { SummarizeConversationRes } from '@/query/speech';

export type HistoryField = keyof Omit<
  SummarizeConversationRes['summary'],
  | 'vitals'
  | 'anthropometry'
  | 'generalPhysicalExamination'
  | 'systemicExamination'
  | 'owner'
>;

export type VitalField = keyof Required<
  SummarizeConversationRes['summary']
>['vitals'];

export type AnthropometryField = keyof Required<
  SummarizeConversationRes['summary']
>['anthropometry'];

export type GeneralPhysicalExaminationField = keyof Required<
  SummarizeConversationRes['summary']
>['generalPhysicalExamination'];

export type SystemicExaminationField = keyof Required<
  SummarizeConversationRes['summary']
>['systemicExamination'];
export type Field = {
  label: string;
  key:
    | HistoryField
    | VitalField
    | AnthropometryField
    | GeneralPhysicalExaminationField
    | SystemicExaminationField;
  unit?: string;
};

export const historyFields: Field[] = [
  {
    label: 'Presenting Complaints',
    key: 'presentingComplaints',
  },
  {
    label: 'History of Presenting Complaints',
    key: 'historyOfPresenting',
  },
  {
    label: 'Past Medical History',
    key: 'pastMedicalHistory',
  },
  {
    label: 'Past Surgical History',
    key: 'pastSurgicalHistory',
  },
  {
    label: 'Family History',
    key: 'familyHistory',
  },
  {
    label: 'Addiction History',
    key: 'addictionHistory',
  },
];

export const lifestyleHealthHistoryFields: Field[] = [
  {
    label: 'Diet History',
    key: 'dietHistory',
  },
  {
    label: 'Physical Activity History',
    key: 'physicalActivityHistory',
  },
  {
    label: 'Stress History',
    key: 'stressHistory',
  },
  {
    label: 'Sleep History',
    key: 'sleepHistory',
  },
];

export const vitalSignFields: Field[] = [
  {
    label: 'Heart Rate',
    key: 'heartRate',
    unit: '(bpm)',
  },
  {
    label: 'Systolic BP',
    key: 'systolicPressure',
    unit: '(mmHg)',
  },
  {
    label: 'Diastolic BP',
    key: 'diastolicPressure',
    unit: '(mmHg)',
  },
  {
    label: 'Respiratory Rate',
    key: 'respiratoryRate',
    unit: '(/min)',
  },
  {
    label: 'SpO2',
    key: 'spO2',
    unit: '(%)',
  },
  {
    label: 'Temperature',
    key: 'temperature',
    unit: '(°F)',
  },
];

export const anthropometryFields: Field[] = [
  {
    label: 'Height',
    key: 'height',
    unit: '(cm)',
  },
  {
    label: 'Weight',
    key: 'weight',
    unit: '(kg)',
  },
  {
    label: 'BMI',
    key: 'bmi',
    unit: '(kg/m²)',
  },
  {
    label: 'Waist Circumference',
    key: 'waistCircumference',
    unit: '(cm)',
  },
];

export const generalPhysicalExaminationFields: Field[] = [
  {
    label: 'Pallor',
    key: 'pallor',
  },
  {
    label: 'Icterus',
    key: 'icterus',
  },
  {
    label: 'Cyanosis',
    key: 'cyanosis',
  },
  {
    label: 'Clubbing',
    key: 'clubbing',
  },
];

export const generalExaminationFields: Field[] = [
  {
    label: 'Pedal Enema',
    key: 'pedalEnema',
  },
  {
    label: 'Lymphadenopathy',
    key: 'lymphadenopathy',
  },
];

export const systemicExaminationFields: Field[] = [
  {
    label: 'Neurological Examination',
    key: 'neurologicalExamination',
  },
  {
    label: 'Cardiovascular Examination ',
    key: 'cardiovascularExamination',
  },
  {
    label: 'Respiratory Examination ',
    key: 'respiratoryExamination',
  },
  {
    label: 'Abdomen Examination ',
    key: 'abdomenExamination',
  },
  {
    label: 'Rheumatological Examination',
    key: 'rheumatologicalExamination',
  },
];

export const noteModes = {
  CREATE: 'create',
  EDIT: 'edit',
  VIEW: 'view',
  NONE: '',
} as const;

export type ModeType = (typeof noteModes)[keyof typeof noteModes];

export const FINALIZED = 'finalized';
