import React, { memo, ReactNode } from 'react';

import { cn } from '@/lib/utils';

import { usePrescriptionStore } from '@/store/emr/prescription';

import {
  prescriptionTabs,
  PrescriptionTabType,
} from '@/types/emr/prescription';

import PackageSelector from './packages';

import HistoryFilter from './history/HistoryFilter';
import HistorySearch from './history/HistorySearch';
import { PackageTypeButtons } from './packages/PackageTypeButtons';
import MedicineSearch from './shared/MedicineSearch';

const { NEW_PRESCRIPTION, PRESCRIPTION_HISTORY } = prescriptionTabs;

const tabs = [
  { key: NEW_PRESCRIPTION, label: 'New Prescription' },
  { key: PRESCRIPTION_HISTORY, label: 'Prescription History' },
];

const prescriptionSearch: Record<PrescriptionTabType, ReactNode> = {
  [NEW_PRESCRIPTION]: <MedicineSearch placeholder="Search by Medicine" />,
  [PRESCRIPTION_HISTORY]: <HistorySearch />,
};

const prescriptionFilter: Record<PrescriptionTabType, ReactNode> = {
  [NEW_PRESCRIPTION]: <PackageSelector />,
  [PRESCRIPTION_HISTORY]: <HistoryFilter />,
};

const PrescriptionTabSection = () => {
  const { setSelectedMedicines, switchActiveTab, activeTab } =
    usePrescriptionStore();

  return (
    <div className="w-full flex flex-col gap-2 mt-1 pl-4 justify-center align-center border-l-2">
      <div className="flex w-full">
        {tabs.map(({ key, label }) => (
          <div key={key} className="w-1/2 text-center border-b-2">
            <button
              onClick={() => {
                switchActiveTab(key);
                if (key === NEW_PRESCRIPTION) {
                  setSelectedMedicines([]);
                }
              }}
              className={cn(
                'py-1 px-4 w-full font-medium border-b-2 whitespace-nowrap overflow-hidden text-ellipsis',
                activeTab === key
                  ? 'border-primary'
                  : 'border-transparent text-gray-500'
              )}
            >
              {label}
            </button>
          </div>
        ))}
      </div>

      {activeTab === NEW_PRESCRIPTION ? (
        <div className="flex gap-2 items-center">
          <div className="min-w-fit">{prescriptionFilter[activeTab]}</div>
          <div className="flex-1 relative">{prescriptionSearch[activeTab]}</div>
        </div>
      ) : (
        <>
          <div className="flex gap-2 items-center px-2">
            <div className="min-w-fit w-48">
              <PackageTypeButtons disabled />
            </div>
            <div className="flex-1 relative">
              {prescriptionSearch[activeTab]}
            </div>
          </div>
          <div className="flex gap-2">
            <div className="min-w-fit ml-auto px-4">
              {prescriptionFilter[activeTab]}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default memo(PrescriptionTabSection);
