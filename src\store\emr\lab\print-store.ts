import { create } from 'zustand';

import { TestResultItem } from '@/types/emr/lab';

type PrintState = {
  printItems: TestResultItem[];
  printOpen: boolean;
};

type PrintAction = {
  setPrintItems: (items: TestResultItem[]) => void;
  onExpand: (items: TestResultItem[]) => void;
  onClose: () => void;
  setPrintOpen: (printOpen: boolean) => void;
};

type PrintStore = PrintState & PrintAction;

const initialValue: PrintState = {
  printItems: [],
  printOpen: false,
};

export const useLabPrintStore = create<PrintStore>((set) => ({
  ...initialValue,
  setPrintItems: (items) => set({ printItems: items }),

  setPrintOpen: (printOpen) => set({ printOpen }),
  onExpand: (items) => set({ printItems: items }),
  onClose: () => set({ printItems: [] }),
}));
