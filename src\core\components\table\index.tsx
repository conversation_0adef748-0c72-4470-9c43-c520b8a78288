import React, { memo } from 'react';

import MuiTable from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';

import { TableBodyContent, TableHeader } from './Components';
import { StyledTableContainer, StyledTableHead } from './styled-components';
import { TableProps } from './types';

const Table: React.FC<TableProps> = ({
  headers,
  rows,
  loading,
  tableContainerProps = {},
  stickyHeader = false,
  noDataMessage,
  sortOptions,
}) => {
  return (
    <StyledTableContainer {...tableContainerProps}>
      <MuiTable stickyHeader={stickyHeader} sx={{ width: '100%' }}>
        <StyledTableHead>
          <TableHeader headers={headers} sortOptions={sortOptions} />
        </StyledTableHead>
        <TableBody>
          <TableBodyContent
            headers={headers}
            rows={rows}
            loading={loading}
            noDataMessage={noDataMessage}
          />
        </TableBody>
      </MuiTable>
    </StyledTableContainer>
  );
};

export default memo(Table);
