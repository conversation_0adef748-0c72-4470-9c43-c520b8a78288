import { Header } from '@/core/components/table/types';
import { fileCellProps } from '@/types/emr/doctor-profile/personal-info';

type Option = {
  label: string;
  value: string;
};

export const maritalStatus = [
  { value: 'single', label: 'Single' },
  { value: 'married', label: 'Married' },
];

export const yesNoOptions = [
  { value: 'yes', label: 'Yes' },
  { value: 'no', label: 'No' },
];

export const nationalityOptions = [
  { value: 'Indian', label: 'Indian' },
  { value: 'NRI', label: 'NRI' },
  { value: 'Other', label: 'Other' },
];

export const religionOptions = [
  { value: 'Hinduism', label: 'Hinduism' },
  { value: 'Sikhism', label: 'Sikhism' },
  { value: 'Jainism', label: 'Jainism' },
  { value: 'Buddhism', label: 'Buddhism' },
  { value: 'Christianity', label: 'Christianity' },
  { value: 'Islam', label: 'Islam' },
  { value: 'Others', label: 'Others' },
];

export const casteOptions = [
  { value: 'General', label: 'General' },
  { value: 'SC', label: 'Scheduled Caste (SC)' },
  { value: 'ST', label: 'Scheduled Tribe (ST)' },
  { value: 'OBC', label: 'Other Backward Class (OBC)' },
  { value: 'Others', label: 'Others' },
];

export const bloodGroups: Option[] = [
  { label: 'A+', value: 'A+' },
  { label: 'A-', value: 'A-' },
  { label: 'B+', value: 'B+' },
  { label: 'B-', value: 'B-' },
  { label: 'AB+', value: 'AB+' },
  { label: 'AB-', value: 'AB-' },
  { label: 'O+', value: 'O+' },
  { label: 'O-', value: 'O-' },
];

export const relationships: Option[] = [
  {
    label: 'Parent',
    value: 'Parent',
  },
  {
    label: 'Spouse',
    value: 'Spouse',
  },
  {
    label: 'Sibling',
    value: 'Sibling',
  },
  {
    label: 'Child',
    value: 'Child',
  },
];

export const bankOptions = [
  { value: 'SBI', label: 'State Bank of India' },
  { value: 'HDFC', label: 'HDFC Bank' },
  { value: 'ICICI', label: 'ICICI Bank' },
  { value: 'AXIS', label: 'Axis Bank' },
  { value: 'BOB', label: 'Bank of Baroda' },
];

export const bankBranchOptions = [
  { value: 'Branch1', label: 'Mumbai ' },
  { value: 'Branch2', label: 'Delhi ' },
  { value: 'Branch3', label: 'Bangalore ' },
  { value: 'Branch4', label: 'Pune ' },
  { value: 'Branch5', label: 'Chennai ' },
];

export const familyStatusOptions = [
  { value: 'not_verified', label: 'Not Verified' },
  { value: 'in_review', label: 'In Review' },
  { value: 'verified', label: 'Verified' },
  { value: 'rejected', label: 'Rejected' },
];

export const languages = [
  'Arabic',
  'Bengali',
  'English',
  'French',
  'Hindi',
  'Kannada',
  'Malayalam',
  'Marathi',
  'Telugu',
  'Urdu',
];

export const languagesOptions = languages.map((lang) => ({
  label: lang,
  value: lang,
}));

export const READ = 'read';
export const WRITE = 'write';
export const SPEAK = 'speak';

export const fluencyLevels = [READ, WRITE, SPEAK];

export const tabValues = {
  GENERAL_DETAILS: 'general',
  PERSONAL_DETAILS: 'personal',
  EMERGENCY_DETAILS: 'emergency',
  BANK_DETAILS: 'bank',
  LANGUAGES_KNOWN: 'languagesKnown',
  QUALIFICATION: 'qualification',
  CERTIFICATION: 'certification',
  MEDICAL_INSURANCE: 'medicalInsurance',
  FAMILY_DETAILS: 'family',
  DOCUMENTS: 'documents',
  EMPLOYMENT_BG: 'employmentBG',
} as const;

export type TabValues = (typeof tabValues)[keyof typeof tabValues];

export const PERSONAL_INFO_TAB_STORE_KEY = 'personalInfoTab';

export const profileTabs = {
  GENERAL_DETAILS: 'General details',
  PERSONAL_DETAILS: 'Personal details',
  EMERGENCY_DETAILS: 'Emergency details',
  BANK_DETAILS: 'Bank details',
  LANGUAGES_KNOWN: 'Language preferences ',
  QUALIFICATION: 'Qualification details',
  CERTIFICATION: 'Certifications details',
  MEDICAL_INSURANCE: 'Medical Insurance details',
  FAMILY_DETAILS: 'Family details',
  EMPLOYMENT_BACKGROUND: 'Employment background details',
  DOCUMENT_DETAILS: 'Documents details',
};

const statusHeader: Header = { key: 'status', header: 'Status' };

const actionsHeaders: Header[] = [
  { key: 'edit', header: '' },
  { key: 'delete', header: '' },
];

export const qualificationHeaders: Header[] = [
  { key: 'qualification', header: 'Qualification' },
  { key: 'specialization', header: 'Specialization' },
  { key: 'university', header: 'University' },
  { key: 'institute', header: 'Institute' },
  { key: 'duration', header: 'Duration' },
  { key: 'yearOfCompletion', header: 'YOC' },
  { key: 'marks', header: 'Marks' },
  { key: 'doc1', header: 'Doc 1', cellProps: fileCellProps },
  { key: 'doc2', header: 'Doc 2', cellProps: fileCellProps },
  statusHeader,
  ...actionsHeaders,
];

export const certificationHeaders: Header[] = [
  { key: 'name', header: 'Name' },
  { key: 'regNo', header: 'Reg No' },
  { key: 'validFrom', header: 'Valid From' },
  { key: 'validTo', header: 'Valid To' },
  { key: 'updatedOn', header: 'Date of Updation' },
  statusHeader,
  ...actionsHeaders,
];

export const medicalInsuranceHeaders: Header[] = [
  { key: 'policyName', header: 'Policy' },
  { key: 'policyNumber', header: 'Policy Number' },
  { key: 'from', header: 'From' },
  { key: 'to', header: 'To' },
  statusHeader,
  ...actionsHeaders,
];

export const familyHeaders: Header[] = [
  { key: 'name', header: 'Name' },
  { key: 'relation', header: 'Relation' },
  { key: 'dependent', header: 'Dependent' },
  { key: 'dob', header: 'DOB' },
  { key: 'aadharNumber', header: 'Aadhar No' },
  { key: 'occupation', header: 'Occupation' },
  { key: 'documents', header: 'Doc', cellProps: fileCellProps },
  statusHeader,
  ...actionsHeaders,
];

export const employmentBackgroundHeaders: Header[] = [
  {
    key: 'hospitalName',
    header: 'Previous Employer',
    cellProps: { sx: { whiteSpace: 'nowrap' } },
  },
  { key: 'designation', header: 'Designation' },
  { key: 'duration', header: 'Duration' },
  { key: 'salary', header: 'Salary' },
  { key: 'doc1', header: 'Doc 1', cellProps: fileCellProps },
  { key: 'doc2', header: 'Doc 2', cellProps: fileCellProps },
  statusHeader,
  ...actionsHeaders,
];
