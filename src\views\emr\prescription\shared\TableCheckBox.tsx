import { Control, Controller } from 'react-hook-form';

import { Checkbox } from '@mui/material';
interface ControlledCheckboxProps {
  name: string;
  control: Control<any>;
  defaultChecked?: boolean;
}

export const ControlledCheckbox = ({
  name,
  control,
}: ControlledCheckboxProps) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { value, onChange, ...fieldProps } }) => (
        <Checkbox
          {...fieldProps}
          size="small"
          checked={!!value}
          onChange={(e) => onChange(e.target.checked)}
          sx={{
            color: 'black',
            '&.Mui-checked': {
              color: 'black',
            },
          }}
        />
      )}
    />
  );
};
