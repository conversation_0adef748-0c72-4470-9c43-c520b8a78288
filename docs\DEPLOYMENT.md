# Deployment Guide

This document outlines the deployment process and environments for the ARCA EMR application.

## Deployment Environments

### Production Environment

- **Platform**: Azure Static Web Apps
- **Trigger**: Merge to `main` branch
- **Automation**: GitHub Actions workflow
- **Workflow File**: `.github/workflows/azure-static-web-apps-purple-stone-0bb45d510.yml`

### Test Environment

- **Platform**: Vercel
- **Trigger**: Merge to `staging` branch
- **Automation**: Automatic deployment via Vercel integration

## Deployment Flow

```mermaid
graph LR
A[Feature Branch] --> B[Pull Request to staging]
B --> C[Merge to staging]
C --> D[Deploy to Test - Vercel]
D --> E[Testing & QA]
E --> F[Pull Request to main]
F --> G[Merge to main]
G --> H[Deploy to Production - Azure]
```

## Branch Strategy

### Staging Branch (`staging`)

- Development integration branch
- Automatically deploys to test environment on Vercel
- Used for testing and validation before production release

### Main Branch (`main`)

- Production-ready code
- Automatically deploys to Azure Static Web Apps
- Only merge after thorough testing on staging

## Deployment Process

### To Test Environment

1. Create feature branch from `staging`
2. Develop and test locally
3. Create pull request to `staging` branch
4. Code review and approval
5. Merge to `staging` branch
6. Automatic deployment to Vercel test environment

### To Production Environment

1. Ensure testing is complete on `staging` branch
2. Create pull request from `staging` to `main` branch
3. Final review and approval
4. Merge to `main` branch
5. GitHub Actions triggers Azure Static Web Apps deployment

## Configuration Files

### Azure Static Web Apps

- Workflow: `.github/workflows/azure-static-web-apps-purple-stone-0bb45d510.yml`
- Build configuration handled by GitHub Actions

### Vercel

- Configuration: Managed through Vercel dashboard
- Automatic deployment via Vercel GitHub integration

## Environment Variables

Ensure the following environment variables are configured in each deployment environment:

- Production (Azure): Configure in Azure Static Web Apps settings
- Test (Vercel): Configure in Vercel project settings

Reference `.env.example` for required environment variables:

- `NEXT_PUBLIC_URL`
- `NEXT_PUBLIC_API_URL`
- `NEXT_PUBLIC_SUBSCRIPTION_KEY`
- `NEXT_PUBLIC_API_BASE_URL`
- `NEXT_PUBLIC_SPEECH_ENGINE_SUBSCRIPTION_KEY`
- `NEXT_PUBLIC_CLIENT_ID`
- `NEXT_PUBLIC_TENANT_NAME`
- `NEXT_PUBLIC_SIGNIN_POLICY`

## Rollback Strategy

### Production Rollback

1. Access Azure Static Web Apps portal
2. Navigate to deployment history
3. Select previous stable deployment
4. Activate rollback

### Test Rollback

1. Access Vercel dashboard
2. Navigate to deployments
3. Select previous deployment
4. Promote to current

## Monitoring and Logs

### Production

- Azure Static Web Apps provides built-in monitoring
- Access logs through Azure portal
- GitHub Actions logs available in repository

### Test

- Vercel provides deployment logs and analytics
- Access through Vercel dashboard

## Troubleshooting

### Common Issues

1. **Build Failures**: Check build logs in respective platforms
2. **Environment Variables**: Verify all required variables are set
3. **Branch Protection**: Ensure proper branch protection rules are configured

### Support Contacts

- Azure Issues: Check Azure Static Web Apps documentation
- Vercel Issues: Check Vercel documentation
- GitHub Actions: Review workflow logs in GitHub Actions tab
