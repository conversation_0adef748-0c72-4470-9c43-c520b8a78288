import { getCSSVariableColor } from '@core/theme/utils';

export const chartColors = {
  light: {
    chart1: getCSSVariableColor('--chart-1', [12, 76, 61]),
    chart2: getCSSVariableColor('--chart-2', [173, 58, 39]),
    chart3: getCSSVariableColor('--chart-3', [197, 37, 24]),
    chart4: getCSSVariableColor('--chart-4', [43, 74, 66]),
    chart5: getCSSVariableColor('--chart-5', [27, 87, 67]),
  },
  dark: {
    chart1: getCSSVariableColor('--chart-1', [220, 70, 50]),
    chart2: getCSSVariableColor('--chart-2', [160, 60, 45]),
    chart3: getCSSVariableColor('--chart-3', [30, 80, 55]),
    chart4: getCSSVariableColor('--chart-4', [280, 65, 60]),
    chart5: getCSSVariableColor('--chart-5', [340, 75, 55]),
  },
};
