import React, { memo, useMemo, useState } from 'react';
import { useEffect } from 'react';

import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import { CgExpand } from 'react-icons/cg';

import EditableText from '@/lib/common/editable_text';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import {
  LifestyleNoteRecords,
  useLifestyleNoteStore,
} from '@/store/lifestyle-note-store';

import { noteModes } from '@/utils/constants/consultation';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { StyledTypography } from '../consultation/Common';

import LifestyleNoteModal from './LifestyleNoteModal';

const { VIEW, CREATE } = noteModes;

const LifestyleNote = () => {
  const { getLifestyleNotes, records, setMode, mode, resetRecord } =
    useLifestyleNoteStore();
  const { patient } = useCurrentPatientStore();

  const [selectedTitle, setSelectedTitle] = useState<string | null>(null);
  const [open, setOpen] = useState(false);
  const [showCancelButton, setShowCancelButton] = useState(true);

  const groupedRecordByDate = useMemo<
    Record<string, LifestyleNoteRecords[]>
  >(() => {
    const notes = records?.reduce(
      (acc, record) => {
        const date = formatDate(record.updated_on, DateFormats.READABLE_DATE);
        acc[date] = acc[date] || [];
        acc[date].push(record);
        return acc;
      },
      {} as Record<string, LifestyleNoteRecords[]>
    );
    return notes || {};
  }, [records]);

  useEffect(() => {
    patient?.id && getLifestyleNotes(patient?.id);
  }, [getLifestyleNotes, patient?.id]);

  useEffect(() => {
    resetRecord();
  }, [resetRecord]);

  return (
    <div className="border border-[#DAE1E7] rounded-lg shadow-custom-xs p-1.5 xl:p-2.5 h-auto flex flex-col gap-1">
      <div className="flex items-center justify-between">
        <span className="font-medium text-sm xl:text-base -tracking-[2.2%]">
          Notes
        </span>
        <div className="flex items-center gap-5">
          <IconButton
            onClick={() => {
              setSelectedTitle('Lifestyle Notes');
              setOpen(true);
              setShowCancelButton(false);
              const newMode = records?.length > 0 ? VIEW : CREATE;
              if (mode !== newMode) {
                setMode(newMode);
              }
            }}
            size="small"
          >
            <CgExpand className="text-black text-xl" />
          </IconButton>
        </div>
      </div>
      <div className="w-full h-full min-h-10">
        {Object.keys(groupedRecordByDate).map((date) => (
          <div key={date} className="flex flex-row items-start">
            <div className="flex-grow max-w-[calc(100%-100px)]">
              {groupedRecordByDate[date].map((record) => (
                <Box key={`${record.updated_on}-${record.author}`}>
                  <EditableText
                    defaultValue={record.note}
                    editable={false}
                    emptyPlaceholder=""
                  />
                </Box>
              ))}
            </div>
            <div className="mx-2 w-[100px] text-right">
              <StyledTypography variant="caption">{date}</StyledTypography>
            </div>
          </div>
        ))}
      </div>
      <LifestyleNoteModal
        onClose={() => setOpen(false)}
        open={open}
        selectedTitle={selectedTitle}
        setOpen={setOpen}
        showCancelButton={showCancelButton}
        setShowCancelButton={setShowCancelButton}
      />
    </div>
  );
};

export default memo(LifestyleNote);
