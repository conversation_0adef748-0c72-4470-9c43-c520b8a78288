import React from 'react';

import {
  Control,
  UseFormClearErrors,
  FieldErrors,
  Controller,
} from 'react-hook-form';

import dayjs from 'dayjs';

import {
  getCellStyling,
  getActionButtonCellProps,
  getInstructionsStyling,
} from '@/utils/constants/prescription';
import {
  allowOnlyNumbers,
  allowOnlyNumbersOnPaste,
  preventLeadingZero,
} from '@/utils/validation';

import { reportHeaders, editableKeys } from '@/constants/emr/lab';

import ActionButton from '@/views/emr/doctor-profile/personal-info/shared/ActionButton';
import ModalDatePicker from '@/views/emr/doctor-profile/personal-info/shared/ModalDatePicker';
import TableTextarea from '@/views/emr/doctor-profile/personal-info/shared/TableTextarea';

import { Row } from '@/core/components/table/types';
import { actionButtonCellProps } from '@/types/emr/doctor-profile/personal-info';
import { LabTestItem } from '@/types/emr/lab';

import InstructionsSelector from './InstructionsSelector';

interface LabTestForm {
  labTest: LabTestItem[];
}
interface LabTestRowProps {
  index: number;
  control: Control<any>;
  clearErrors: UseFormClearErrors<any>;
  watchLabTest: LabTestItem[] | undefined;
  errors: FieldErrors<LabTestForm>;
  handleRemove: (index: number, id: string) => void;
  selectedSearchTests: LabTestItem[];
  defaultLabTestRow: LabTestItem;
  isValidationEnabled: boolean;
  onCostChange: () => void;
}

const LabTestRow = ({
  index,
  control,
  clearErrors,
  watchLabTest,
  errors,
  handleRemove,
  selectedSearchTests,
  defaultLabTestRow,
  onCostChange,
}: LabTestRowProps) => {
  const isRowNonEmpty = (row: LabTestItem): boolean => {
    return Object.keys(row).some((key) => {
      if (key === 'close' || key === 'id') return false;
      return row[key as keyof LabTestItem] !== '';
    });
  };

  const isFieldValid = (index: number, key: string): boolean => {
    const fieldError = errors?.labTest?.[index]?.[key];
    return !fieldError;
  };

  const row: Row = {};
  const labTest = selectedSearchTests[index];
  const labTestId = labTest?.id as string;

  const isNonEmpty = isRowNonEmpty(watchLabTest?.[index] ?? defaultLabTestRow);

  reportHeaders.forEach(({ key }: { key: string }) => {
    const isValid = isFieldValid(index, key);

    const cellStyling = getCellStyling({
      isNonEmpty,
      isValid,
    });

    const instructionsStyling = getInstructionsStyling({
      isNonEmpty,
      isValid,
    });

    if (key === 'close') {
      row[key] = {
        value: isNonEmpty ? (
          <ActionButton
            actionFor="close"
            onClick={() => handleRemove(index, labTestId)}
          />
        ) : (
          ''
        ),
        cellProps: getActionButtonCellProps(),
      };
    } else if (key === 'instructions') {
      row[key] = {
        value: isNonEmpty ? (
          <InstructionsSelector
            name={`labTest.${index}.${key}`}
            control={control}
            clearErrors={clearErrors}
            isNotValid={isNonEmpty && !isValid}
            rules={{
              required: {
                value: true,
              },
            }}
          />
        ) : (
          ''
        ),
        cellProps: instructionsStyling,
      };
    } else if (key === 'toBeDoneBy') {
      row[key] = {
        value: isNonEmpty ? (
          <Controller
            name={`labTest.${index}.${key}`}
            control={control}
            render={({ field }) => (
              <ModalDatePicker
                value={field.value || null}
                onChange={field.onChange}
                disabled={false}
                sx={{ width: '100%' }}
                minDate={dayjs()}
              />
            )}
          />
        ) : (
          ''
        ),
        cellProps: cellStyling,
      };
    } else {
      const isRequired = ['quantity', 'cost'].includes(key);
      const isEditable = editableKeys.includes(key);
      const quantityCellProps = {
        ...cellStyling,
        sx: {
          ...(cellStyling?.sx || {}),
          ...(actionButtonCellProps?.sx || {}),
        },
      };

      const cellProps =
        key === 'no'
          ? getActionButtonCellProps()
          : key === 'quantity'
            ? quantityCellProps
            : cellStyling;

      const handleQuantityKeyDown: React.KeyboardEventHandler<
        HTMLTextAreaElement
      > = (e) => {
        allowOnlyNumbers(e);
        preventLeadingZero(e.currentTarget.value, e.key, e);
      };

      const handleQuantityPaste: React.ClipboardEventHandler<
        HTMLTextAreaElement
      > = (e) => {
        allowOnlyNumbersOnPaste(e);
        const pasted = e.clipboardData.getData('Text').trim();
        const isLeadingZero = pasted.length && pasted.startsWith('0');
        if (isLeadingZero) {
          e.preventDefault();
        }
      };
      const extraProps =
        key === 'quantity'
          ? {
              onKeyDown: handleQuantityKeyDown,
              onPaste: handleQuantityPaste,
            }
          : key === 'cost'
            ? {
                onKeyDown: handleQuantityKeyDown,
                onPaste: handleQuantityPaste,
              }
            : {};

      row[key] = {
        value: isNonEmpty ? (
          <TableTextarea
            style={{ textAlign: 'center' }}
            name={`labTest.${index}.${key}`}
            control={control}
            rules={
              key === 'quantity'
                ? {
                    required: true,
                    validate: (val) => Number(val) > 0,
                  }
                : isRequired
                  ? { required: true }
                  : {}
            }
            showInputError={false}
            readOnly={!isEditable}
            onChange={() => {
              if (key === 'cost') {
                onCostChange();
              }
            }}
            {...extraProps}
          />
        ) : (
          ''
        ),
        cellProps,
      };
    }
  });

  return row;
};

export default LabTestRow;
