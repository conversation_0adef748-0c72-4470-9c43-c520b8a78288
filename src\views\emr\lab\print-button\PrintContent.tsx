import React, { memo, useMemo, useRef } from 'react';

import { useReactToPrint } from 'react-to-print';

import { useCustomiseEmrStore } from '@/store/emr/doctor-profile/customise-emr';
import { useLabPrintStore } from '@/store/emr/lab/print-store';

import { getLabTestsTableRows } from '@/utils/emr/lab/result';

import { printTableHeader } from '@/constants/emr/lab';

import AppButton from '@/core/components/app-button';
import Table from '@/core/components/table';
import { Row } from '@/core/components/table/types';
import '@/styles/emr/lab/print.css';

import AppSign from './AppSign';
import PatientDetails from './PatientDetails';
import PrintHeader from './PrintHeader';
import TableFooter from './TableFooter';

const PrintContent = () => {
  const { printItems, setPrintOpen, printOpen } = useLabPrintStore();
  const contentRef = useRef<HTMLDivElement>(null);
  const { customiseEmrData } = useCustomiseEmrStore();
  const mostRecentData = customiseEmrData?.length
    ? [...customiseEmrData].sort(
        (a, b) =>
          new Date(b.updated_on || '').getTime() -
          new Date(a.updated_on || '').getTime()
      )[0]
    : null;

  const handlePrint = useReactToPrint({
    contentRef,
    documentTitle: 'lab tests',
    onBeforePrint: async () => setPrintOpen(true),
    onAfterPrint: () => setPrintOpen(false),
    onPrintError: () => setPrintOpen(false),
  });

  const rows = useMemo<Row[]>(
    () =>
      getLabTestsTableRows({
        testResult: printItems,
      }),
    [printItems]
  );

  // Calculate total cost of all checked items
  const totalCost = useMemo(() => {
    if (!printItems?.length) return 0;
    return printItems.reduce((sum, item) => {
      const departments = Object.values(item.labTests || {});
      const tests = departments.flat();
      return (
        sum +
        tests.reduce((deptSum, test) => {
          // cost can be string or number, fallback to 0 if not parsable
          const cost =
            typeof test.cost === 'string' ? parseFloat(test.cost) : test.cost;
          return deptSum + (isNaN(cost) ? 0 : cost || 0);
        }, 0)
      );
    }, 0);
  }, [printItems]);

  return (
    <div
      ref={contentRef}
      className="w-full h-full max-h-full flex flex-col print-content-print"
    >
      <div className="flex-1 p-base overflow-y-auto">
        <PrintHeader />
        {/* Divider above Lab Tests heading */}
        <div
          className="w-full border-t mb-2"
          style={{ borderColor: '#E5E7EB' }}
        ></div>
        <div className="w-full flex justify-center p-base font-bold text-base">
          Lab Tests
        </div>
        {/* Divider below Lab Tests heading */}
        <div
          className="w-full border-t mb-2"
          style={{ borderColor: '#E5E7EB' }}
        ></div>
        <PatientDetails />
        <div className="flex-1 flex flex-col gap-base pb-base w-full justify-between">
          <Table
            headers={printTableHeader}
            rows={rows}
            stickyHeader
            noDataMessage="No test results found"
            tableContainerProps={{
              sx: {
                '& th': {
                  whiteSpace: 'nowrap',
                  fontSize: '0.875rem',
                  padding: '8px 16px',
                  backgroundColor: '#64707D',
                },
              },
            }}
          />
        </div>
      </div>
      <div className="flex-shrink-0 w-full flex flex-col p-base no-print">
        <div className="w-full flex justify-between items-end mb-2 pt-2">
          <TableFooter />
        </div>
        {/* Divider above total cost */}
        <div
          className="w-full border-t"
          style={{ borderColor: '#E5E7EB' }}
        ></div>
        {/* Total Cost Display - Figma style with margin */}
        <div className="w-full flex justify-end items-center mt-4 mb-1">
          <span className="text-sm font-semibold mr-4">Total Amount :</span>
          <span className="text-sm font-bold text-[#012436]">
            ₹ {totalCost.toFixed(2)}
          </span>
        </div>
        {/* Divider below total cost */}
        <div
          className="w-full border-t mb-2"
          style={{ borderColor: '#E5E7EB' }}
        ></div>
        {/* Print Button */}
        {!printOpen && (
          <div className="w-full flex justify-end mb-2">
            <AppButton onClick={handlePrint} className="w-32">
              Print
            </AppButton>
          </div>
        )}
        {/* Powered by ARCA */}
        <div className="w-full flex justify-start mb-2">
          <AppSign />
        </div>
      </div>
    </div>
  );
};

export default memo(PrintContent);
