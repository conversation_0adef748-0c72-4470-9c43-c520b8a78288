import React from 'react';

interface Props {
  title: string;
  description?: string;
  colSpan?: string;
  minHeight?: string;
  embedUrl?: string;
}

const ChartPlaceholder: React.FC<Props> = ({
  title,
  description = 'Chart will be embedded here',
  colSpan = 'col-span-1',
  minHeight = '300px',
  embedUrl,
}) => {
  return (
    <div
      className={`${colSpan} bg-white rounded-lg border border-[#DAE1E7] p-4 flex items-center justify-center`}
      style={{ borderRadius: '8px', minHeight }}
    >
      {embedUrl ? (
        <iframe
          src={embedUrl}
          className="w-full h-full rounded"
          title={title}
        />
      ) : (
        <div className="text-center text-gray-500">
          <h3 className="text-lg font-medium mb-2">{title}</h3>
          <p className="text-sm">{description}</p>
        </div>
      )}
    </div>
  );
};

export default ChartPlaceholder;
