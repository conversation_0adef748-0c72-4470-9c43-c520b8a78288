import { useState, useCallback, useRef, useEffect } from 'react';

import { components } from 'react-select';
import type { SingleValue } from 'react-select';

import { Box, Divider, Typography } from '@mui/material';

import { usePrescriptionStore } from '@/store/emr/prescription';

import AsyncSearch from '@/core/components/search';
import type { PrescriptionItem } from '@/types/emr/prescription';

interface MedicineOption {
  value: string;
  label: string;
  medicine: PrescriptionItem;
}

interface MedicineSearchProps {
  placeholder?: string;
  onChange?: (medicine: PrescriptionItem | null) => void;
}

const MedicineSearch: React.FC<MedicineSearchProps> = ({
  placeholder = 'Search by Medicine',
  onChange,
}) => {
  const { searchMedicines, selectMedicine } = usePrescriptionStore();

  const [inputValue, setInputValue] = useState('');
  const [selectedOption, setSelectedOption] = useState<MedicineOption | null>(
    null
  );
  const [isSearching, setIsSearching] = useState(false);

  // Refs for search handling
  const abortControllerRef = useRef<AbortController | null>(null);
  const currentSearchRef = useRef<string>('');

  // Add state to hold options for manual search trigger
  const [manualOptions, setManualOptions] = useState<MedicineOption[]>([]);

  // Create immediate search without debouncing for real-time results
  const performSearch = useCallback(
    async (searchTerm: string) => {
      if (!searchTerm.trim()) {
        return [];
      }

      // Cancel any pending requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();
      setIsSearching(true);

      try {
        const results = await searchMedicines(searchTerm);
        // Only update if this is still the current search
        if (currentSearchRef.current === searchTerm) {
          setIsSearching(false);
          return results;
        }
        return [];
      } catch (error) {
        if (
          error instanceof Error &&
          error.name !== 'AbortError' &&
          error.message !== 'Request aborted'
        ) {
          console.error('Error searching medicines:', error);
        }
        setIsSearching(false);
        return [];
      }
    },
    [searchMedicines]
  );

  // Transform results to options
  const transformResults = useCallback(
    (medicines: PrescriptionItem[]): MedicineOption[] => {
      return medicines
        .filter((medicine) => !!medicine.id)
        .map((medicine) => ({
          value: medicine.id as string,
          label: medicine.BrandName || medicine.GenericName || '',
          medicine: { ...medicine }, // Preserve original quantity and cost from API
        }));
    },
    []
  );

  // Load options with immediate search for real-time results
  const loadOptions = useCallback(
    (
      inputVal: string,
      callback: (options: readonly MedicineOption[]) => void
    ) => {
      const trimmedInput = inputVal.trim();

      // If input is empty, return empty options immediately
      if (trimmedInput.length === 0) {
        // Clear any pending requests
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        callback([]);
        return;
      }

      // Update the current search ref
      currentSearchRef.current = trimmedInput;

      // Perform immediate search
      performSearch(trimmedInput)
        .then((medicines) => {
          // Check if the search term is still current
          if (currentSearchRef.current === trimmedInput) {
            try {
              const options = transformResults(medicines);
              callback(options);
            } catch (error) {
              console.error('Error transforming results:', error);
              callback([]);
            }
          }
        })
        .catch((error) => {
          console.error('Search error:', error);
          // Only show empty results if this is still the current search
          if (currentSearchRef.current === trimmedInput) {
            callback([]);
          }
        });
    },
    [performSearch, transformResults]
  );

  // Handle input change
  const handleInputChange = useCallback(
    (newValue: string, { action }: { action: string }) => {
      if (action === 'input-change') {
        setInputValue(newValue);
        const trimmedValue = newValue.trim();

        // If input is cleared, immediately clear everything
        if (!trimmedValue) {
          // Cancel all pending operations
          if (abortControllerRef.current) {
            abortControllerRef.current.abort();
          }
          currentSearchRef.current = '';
          setIsSearching(false);
        } else {
          // Update search term
          currentSearchRef.current = trimmedValue;
        }
      }
    },
    []
  );

  // Handler for Enter key press in input
  const handleInputKeyDown = async (
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === 'Enter') {
      const trimmedInput = inputValue.trim();
      if (trimmedInput.length > 0) {
        setIsSearching(true);
        const medicines = await performSearch(trimmedInput);
        const options = transformResults(medicines);
        setManualOptions(options);
        setIsSearching(false);
      }
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const handleChange = (newValue: SingleValue<MedicineOption>) => {
    const medicine = newValue ? newValue.medicine : null;
    if (medicine) {
      console.log('Selected medicine from search:', {
        id: medicine.id,
        quantity: medicine.quantity,
        Cost: medicine.Cost,
        cost: medicine.cost,
      });

      // Preserve original quantity and cost from search results
      const originalQuantity = parseFloat(medicine.quantity || '1');
      const totalCost = parseFloat(
        String(medicine.Cost || medicine.cost || '0')
      );
      const unitCost =
        originalQuantity > 0 ? String(totalCost / originalQuantity) : '0';

      const medicineWithDefaults = {
        ...medicine,
        quantity: medicine.quantity || '', // Use original quantity if available, empty string if not
        cost: String(medicine.Cost || medicine.cost || '0'),
        unitCost: unitCost, // Store exact unit cost without rounding
      };

      console.log('Medicine with defaults:', medicineWithDefaults);

      selectMedicine(medicineWithDefaults);
      if (onChange) onChange(medicineWithDefaults);
    } else {
      selectMedicine(null);
      if (onChange) onChange(null);
    }

    setSelectedOption(newValue);
    setInputValue('');

    // Clear selection after a short delay
    setTimeout(() => {
      setSelectedOption(null);
    }, 800);
  };

  const CustomOption = (props: any) => {
    const { medicine } = props.data;

    const medicineDetails = [
      { label: medicine.DrugFormulation, flex: 2 },
      { label: medicine.BrandName, flex: 4 },
      { label: medicine.Strength, flex: 4 },
      { label: medicine.Measure, flex: 1 },
      { label: medicine.UnitOfMeasure, flex: 1 },
    ];

    return (
      <>
        <components.Option {...props}>
          <Box display="flex" alignItems="center" px={1} py={0}>
            {medicineDetails.map((detail, index) => (
              <Box
                key={index}
                flex={detail.flex}
                textAlign="left"
                ml={index > 0 ? 1 : 0}
              >
                <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                  {detail.label}
                </Typography>
              </Box>
            ))}
          </Box>
        </components.Option>
        <Divider sx={{ my: 0 }} />
      </>
    );
  };

  return (
    <AsyncSearch<MedicineOption>
      loadOptions={loadOptions}
      onChange={handleChange}
      placeholder={placeholder}
      defaultOptions={manualOptions.length > 0 ? manualOptions : []}
      isLoading={isSearching}
      cacheOptions={false}
      components={{
        Option: CustomOption,
        LoadingIndicator: () => null,
      }}
      value={selectedOption}
      inputValue={inputValue}
      onInputChange={handleInputChange}
      onKeyDown={handleInputKeyDown}
      noOptionsMessage={({ inputValue }) => {
        if (isSearching) return 'Searching...';
        return inputValue.trim().length === 0
          ? 'Start typing to search medicines'
          : 'No medicines found';
      }}
    />
  );
};

export default MedicineSearch;
