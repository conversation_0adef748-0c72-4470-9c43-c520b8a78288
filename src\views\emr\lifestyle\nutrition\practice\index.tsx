'use client';

import { memo } from 'react';

import { useNutritionPracticeStore } from '@/store/emr/lifestyle/nutrition/practice';

import AppTab from '@/core/components/app-tab';

import TwentyFourHourDietaryRecallTab from './24-hour-dietary-recall-tab';
import FoodFrequencyQuestionnaire from './food-frequency-questionnaire';
import FoodIntakePatternsTab from './food-intake-patterns-tab';

const tabs = [
  { label: 'Food Intake Pattern', content: <FoodIntakePatternsTab /> },
  {
    label: '24 Hour Dietary Recall',
    content: <TwentyFourHourDietaryRecallTab />,
  },
  {
    label: 'Food Frequency Questionnaire',
    content: <FoodFrequencyQuestionnaire />,
  },
];

const NutritionPracticeView = () => {
  const { activeTab, setActiveTab } = useNutritionPracticeStore();

  return (
    <div className="h-full flex flex-col p-2 w-full">
      <AppTab tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
    </div>
  );
};

export default memo(NutritionPracticeView);
