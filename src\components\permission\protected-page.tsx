'use client';

import { ReactNode } from 'react';

import { PERMISSION_KEYS } from '@/constants/permission-keys';

import PermissionGuard from '@/core/guard/PermissionGuard';

import NoAccessMessage from './no-access-message';

type ProtectedPageProps = {
  children: ReactNode;
  requiredPermissions: string[];
  showLayout?: boolean;
};

// Base protected page component
export function ProtectedPage({
  children,
  requiredPermissions,
}: ProtectedPageProps) {
  return (
    <PermissionGuard
      requiredPermissions={requiredPermissions}
      fallback={
        <div className="flex bg-white justify-between w-full gap-base h-full">
          <div className="w-full h-full">
            <NoAccessMessage />
          </div>
        </div>
      }
    >
      {children}
    </PermissionGuard>
  );
}

// EMR Specific Page Protectors
type PageWrapperProps = {
  children: ReactNode;
  className?: string;
};

// Patient Info Page Wrapper
export const PatientInfoPage = ({
  children,
  className = '',
}: PageWrapperProps) => (
  <ProtectedPage
    requiredPermissions={[
      PERMISSION_KEYS.EMR_PATIENT_INFO_VIEW,
      PERMISSION_KEYS.EMR_PATIENT_INFO_EDIT,
    ]}
  >
    <div className={className}>{children}</div>
  </ProtectedPage>
);

// Consultation Page Wrapper
export const ConsultationPage = ({
  children,
  className = '',
}: PageWrapperProps) => (
  <ProtectedPage
    requiredPermissions={[PERMISSION_KEYS.EMR_CONSULTATION_MANAGE]}
  >
    <>{children}</>
  </ProtectedPage>
);

// Reports Page Wrapper
export const ReportsPage = ({ children, className = '' }: PageWrapperProps) => (
  <ProtectedPage requiredPermissions={[PERMISSION_KEYS.EMR_REPORTS_MANAGE]}>
    <div className={className}>{children}</div>
  </ProtectedPage>
);

// Prescription Page Wrapper
export const PrescriptionPage = ({ children }: PageWrapperProps) => (
  <ProtectedPage
    requiredPermissions={[
      PERMISSION_KEYS.EMR_PRESCRIPTION_VIEW,
      PERMISSION_KEYS.EMR_PRESCRIPTION_MANAGE,
    ]}
  >
    <>{children}</>
  </ProtectedPage>
);

// Doctor Profile Page Wrapper
export const DoctorProfilePage = ({
  children,
  className = '',
}: PageWrapperProps) => (
  <ProtectedPage
    requiredPermissions={[PERMISSION_KEYS.EMR_DOCTOR_PROFILE_VIEW]}
  >
    <div className={className}>{children}</div>
  </ProtectedPage>
);
