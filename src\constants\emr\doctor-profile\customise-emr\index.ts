import colors from '@/utils/colors';

export const medicalNoteSummaryOptions = [
  { value: 'General Examination' },
  { value: 'Neurological Examination' },
  { value: 'Cardiovascular Examination' },
  { value: 'Respiratory Examination' },
  { value: 'Gastrointestinal Examination' },
  { value: 'Musculoskeletal Examination' },
  { value: 'Endocrine Examination' },
  { value: 'Hematological Examination' },
  { value: 'Oncological Examination' },
  { value: 'Psychiatric Examination' },
  { value: 'Obstetric and Gynecological Examination' },
  { value: 'Pediatric Examination' },
  { value: 'Geriatric Examination' },
  { value: 'Dermatological Examination' },
  { value: 'Ophthalmological Examination' },
  { value: 'Otolaryngological (ENT) Examination' },
  { value: 'Urological Examination' },
  { value: 'Rheumatological Examination' },
  { value: 'Infectious Disease Examination' },
  { value: 'Emergency Medicine Summary' },
  { value: 'Intensive Care Unit (ICU) Summary' },
  { value: 'Preoperative Evaluation' },
  { value: 'Postoperative Summary' },
  { value: 'Nutrition and Lifestyle Assessment' },
  { value: 'Pain Management Summary' },
  { value: 'Physical Therapy and Rehabilitation Summary' },
  { value: 'Sports Medicine Summary' },
  { value: 'Preventive Health Summary' },
  { value: 'Allergy and Immunology Summary' },
  { value: 'Sleep Medicine Summary' },
  { value: 'Palliative Care Summary' },
  { value: 'Toxicology Examination' },
  { value: 'Genetic and Metabolic Disorder Summary' },
  { value: 'Neonatal and Perinatal Examination' },
  { value: 'Sexual and Reproductive Health Summary' },
  { value: 'Occupational Health Examination' },
  { value: 'Addiction Medicine Summary' },
  { value: 'Transplant Medicine Summary' },
  { value: 'Functional Medicine Summary' },
  { value: 'AI-Generated Free-Text Summary' },
];

export const vitalOptions = [
  { key: 'vital_signs', value: 'Vital Signs' },
  { key: 'anthropometry', value: 'Anthropometry (Body Measurements)' },
  { key: 'hematology', value: 'Hematology' },
  { key: 'biochemistry', value: 'Biochemistry' },
  {
    key: 'renal_function_urine_analysis',
    value: 'Renal Function & Urine Analysis',
  },
  { key: 'cardiovascular_metrics', value: 'Cardiovascular Metrics' },
  { key: 'pulmonary_function_metrics', value: 'Pulmonary Function Metrics' },
  { key: 'endocrine_hormonal_metrics', value: 'Endocrine & Hormonal Metrics' },
  {
    key: 'neurological_cognitive_metrics',
    value: 'Neurological & Cognitive Metrics',
  },
  {
    key: 'nutrition_metabolism_metrics',
    value: 'Nutrition & Metabolism Metrics',
  },
  {
    key: 'miscellaneous_clinical_metrics',
    value: 'Miscellaneous Clinical Metrics',
  },
];

export const docAssistOptions = [
  { value: 'General Practitioner Mode' },
  { value: 'Internal Medicine' },
  { value: 'Neurology' },
  { value: 'Cardiology' },
  { value: 'Pulmonology' },
  { value: 'Gastroenterology' },
  { value: 'Endocrinology' },
  { value: 'Hematology' },
  { value: 'Oncology' },
  { value: 'Psychiatry' },
  { value: 'Obstetrics and Gynecology' },
  { value: 'Pediatrics' },
  { value: 'Geriatrics' },
  { value: 'Dermatology' },
  { value: 'Ophthalmology' },
  { value: 'Otolaryngology (ENT)' },
  { value: 'Urology' },
  { value: 'Nephrology' },
  { value: 'Rheumatology' },
  { value: 'Infectious Disease' },
  { value: 'Emergency Medicine' },
  { value: 'Critical Care/ICU Mode' },
  { value: 'Surgery (General Surgery)' },
  { value: 'Orthopedic Surgery' },
  { value: 'Plastic Surgery' },
  { value: 'Neurosurgery' },
  { value: 'Cardiothoracic Surgery' },
  { value: 'Vascular Surgery' },
  { value: 'Anesthesiology' },
  { value: 'Radiology' },
  { value: 'Nuclear Medicine' },
  { value: 'Pathology' },
  { value: 'Physical Medicine & Rehabilitation' },
  { value: 'Sports Medicine' },
  { value: 'Preventive Medicine' },
  { value: 'Lifestyle Medicine' },
  { value: 'Nutrition & Dietetics' },
  { value: 'Pain Management' },
  { value: 'Sleep Medicine' },
  { value: 'Palliative Care' },
  { value: 'Addiction Medicine' },
  { value: 'Transplant Medicine' },
  { value: 'Genetics & Precision Medicine' },
  { value: 'Telemedicine Mode' },
  { value: 'Functional Medicine' },
  { value: 'Occupational Health' },
  { value: 'AI-Driven Research Mode' },
  { value: 'Medical Student/Resident Learning Mode' },
  { value: 'Clinical Documentation Specialist Mode' },
];

export const tilesLayoutButtonLabels = ['1 x 3', '2 x 3', '3 x 3'];

export const departmentOptions = [
  {
    key: 'clinical_departments_medical_specialties',
    value: 'Clinical Departments (Medical Specialties)',
  },
  {
    key: 'clinical_departments_surgical_specialties',
    value: 'Clinical Departments (Surgical Specialties)',
  },
  {
    key: 'obstetrics_gynecology',
    value: "Obstetrics & Gynecology (Women's Health)",
  },
  {
    key: 'pediatrics_neonatology',
    value: 'Pediatrics & Neonatology',
  },
  {
    key: 'emergency_critical_care_anesthesia',
    value: 'Emergency, Critical Care & Anesthesia',
  },
  {
    key: 'oncology_cancer_care',
    value: 'Oncology (Cancer Care)',
  },
  {
    key: 'mental_health_behavioral_sciences',
    value: 'Mental Health & Behavioral Sciences',
  },
  {
    key: 'diagnostics_laboratory_medicine',
    value: 'Diagnostics & Laboratory Medicine',
  },
  {
    key: 'rehabilitation_allied_health_services',
    value: 'Rehabilitation & Allied Health Services',
  },
  {
    key: 'lifestyle_preventive_medicine',
    value: 'Lifestyle & Preventive Medicine',
  },
  {
    key: 'other_specialty_departments',
    value: 'Other Specialty Departments',
  },
  {
    key: 'hospital_administration_support_services',
    value: 'Hospital Administration & Support Services',
  },
];

export const extraNoteOptions = [
  { key: 'summary', value: 'Summary' },
  { key: 'medication', value: 'Medication' },
  { key: 'notes', value: 'Notes' },
];

export const VITAL_KEYS = [
  'miscellaneous_clinical_metrics',
  'medical_note_summary_template',
  'doc_assist_preference',
  'vital_signs',
  'anthropometry',
  'hematology',
  'biochemistry',
  'renal_function_urine_analysis',
  'cardiovascular_metrics',
  'pulmonary_function_metrics',
  'endocrine_hormonal_metrics',
  'neurological_cognitive_metrics',
  'nutrition_metabolism_metrics',
] as const;

export const noteInfoSections = [
  {
    title: 'Summary',
    items: ['Diabetes', 'CKD'],
  },
  {
    title: 'Medication',
    items: ['Paracetamol, 500mg; 3 tablets, 1x/day.'],
  },
  {
    title: 'Notes',
    items: ['Next visit on 12/05/2024'],
  },
];

export const infoHealthMetrics = [
  {
    label: 'Height',
    value: '174',
    unit: 'cm',
    change: '0.0',
    trend: 'neutral',
    color: colors.common.deepChampagne,
  },
  {
    label: 'Weight',
    value: '76',
    unit: 'kg',
    change: '0.3',
    trend: 'up',
    color: colors.common.aquaGreen,
  },
  {
    label: 'BMI',
    value: '21',
    unit: '',
    change: '0.15',
    trend: 'up',
    color: colors.common.aquaGreen,
  },
];

export const InfoPatient = {
  name: 'Mary Mathews',
  age: '30 yrs',
  gender: 'F',
  location: 'Thrissur, KL',
  initials: 'MM',
};

export const GRID_LAYOUTS = {
  SMALL: '1 x 3',
  MEDIUM: '2 x 3',
  LARGE: '3 x 3',
};
