import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

// Common interfaces
export interface PatientData {
  id?: string;
  name?: string;
  age?: string | number;
  mobile?: string;
  gender?: string;
}

export interface DoctorData {
  name: string;
  qualification?: string;
  registration?: string;
}

export interface OrganizationData {
  name?: string;
  address?: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
}

export interface CommonPrintData {
  patient?: PatientData;
  doctor?: string | DoctorData;
  organization?: OrganizationData;
  created_on?: string;
  date?: string;
  id?: string;
}

// Common styles that are shared across all print templates
export const commonPrintStyles = `
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: Arial, sans-serif;
    font-size: 11px;
    line-height: 1.3;
    color: #000;
    background: white;
    padding: 15px;
  }
  
  .print-container {
    width: 100%;
    max-width: 210mm;
    margin: 0 auto;
    background: white;
    border: 1px solid #ccc;
    padding: 20px;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 15px;
  }
  
  .doctor-info {
    text-align: left;
    flex: 1;
  }
  
  .doctor-info .doctor-name {
    font-family: Archivo, Arial, sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 150%;
    letter-spacing: -2.2%;
    color: #001926;
    margin-bottom: 5px;
  }
  
  .doctor-info p {
    margin-bottom: 3px;
    font-family: Archivo, Arial, sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 150%;
    letter-spacing: -2.2%;
    color: #001926;
  }
  
  .hospital-info {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    flex: 1;
    justify-content: flex-end;
  }
  
  .logo-section .logo-placeholder {
    width: 80px;
    height: 40px;
    background-color: #D9D9D9;
    border-radius: 4px;
  }
  
  .hospital-details {
    text-align: right;
  }
  
  .page-title {
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    color: #012436;
    margin: 20px 0;
  }
  
  .patient-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 10px 0;
    width: 100%;
    border-bottom: 1px solid #ddd;
  }
  
  .patient-info span {
    flex: 1;
    text-align: left;
    padding: 0 5px;
    font-size: 12px;
    color: #012436;
    white-space: nowrap;
    margin-right: 10px;
  }
  
  .divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 15px 0;
  }
  
  .signature-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin: 15px 0;
    color: #C2CDD6;
  }
  
  .signature-container {
    display: flex;
    flex-direction: column;
  }
  
  .signature-box {
    width: 150px;
    height: 40px;
    background-color: #C2CDD6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
  }
  
  .page-number {
    font-size: 12px;
    color: #666;
    margin-top: 10px;
  }
  
  .powered-by {
    text-align: center;
    margin: 30px auto 0;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    position: relative;
    width: 100%;
  }
  
  .arca-logo {
    height: 16px;
    width: auto;
    margin-left: 5px;
  }
  
  .arca-logo-text {
    font-weight: bold;
    color: #000;
    font-size: 14px;
    margin-left: 5px;
    letter-spacing: 0.5px;
  }
  
  .hidden {
    display: none;
  }
  
  @media print {
    body {
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
      padding: 0;
    }
    
    .print-container {
      border: none;
      padding: 15px;
      margin: 0;
    }
    
    @page {
      size: A4;
      margin: 0.5in;
    }
  }
`;

export const generatePrintHeader = (
  data: CommonPrintData,
  organizationLogo?: string,
  letterHeadDetails?: string
): string => {
  const doctorName =
    typeof data.doctor === 'string'
      ? data.doctor
      : (data.doctor as DoctorData)?.name || 'Doctor';

  const letterHeadLines = letterHeadDetails
    ? letterHeadDetails.split('\n')
    : [];

  return `
    <div class="header">
      <div class="doctor-info">
        <p class="doctor-name">${doctorName}</p>
        ${letterHeadLines.map((line) => `<p style="margin: 0; font-size: 12px; color: #001926;">${line.trim()}</p>`).join('')}
      </div>
      <div class="hospital-info">
        <div class="logo-section">
          ${
            organizationLogo
              ? `<img src="${organizationLogo}" alt="Organization Logo" style="width: 90px; height: 90px; object-fit: contain;" />`
              : '<div class="logo-placeholder"></div>'
          }
        </div>
        <div class="hospital-details" style="text-align: right;">
          <p style="margin: 0; font-size: 12px; line-height: 1.4; color: #001926;">${data.organization?.name || ''}</p>
          ${data.organization?.address ? `<p style="margin: 4px 0 0 0; font-size: 12px; line-height: 1.4; color: #001926;">${data.organization.address}</p>` : ''}
          ${data.organization?.phone ? `<p style="margin: 4px 0 0 0; font-size: 12px; line-height: 1.4; color: #001926;">${data.organization.phone}</p>` : ''}
        </div>
      </div>
    </div>
  `;
};

// Common patient info generator
export const generatePatientInfo = (
  data: CommonPrintData,
  dateFormat: string = DateFormats.DATE_DD_MM_YYYY_SLASH
): string => {
  const displayDate = data.created_on || data.date || new Date().toISOString();

  return `
    <div class="patient-info">
      <span><strong>Patient Name:</strong> ${data.patient?.name || 'N/A'}</span>
      <span><strong>Patient ID:</strong> ${data.patient?.id || data.id?.split('-')[0] || 'N/A'}</span>
      <span><strong>Age:</strong> ${data.patient?.age || 'N/A'}</span>
      <span><strong>Mobile:</strong> ${data.patient?.mobile || 'N/A'}</span>
      <span><strong>Date:</strong> ${formatDate(displayDate, dateFormat)}</span>
      ${data.patient?.gender ? `<span><strong>Gender:</strong> ${data.patient.gender}</span>` : ''}
    </div>
  `;
};

// Common footer generator
export const generatePrintFooter = (
  currentPage: number = 1,
  totalPages: number = 1,
  digitalSignature?: string
): string => {
  return `
    <div class="divider"></div>
    
    <div class="signature-row">
      <div class="signature-container">
        ${
          digitalSignature
            ? `<img src="${digitalSignature}" alt="Digital Signature" style="width: 100px; height: 41px; object-fit: contain;" />`
            : '<div class="signature-box"></div>'
        }
      </div>
      <div class="page-number">${String(currentPage).padStart(2, '0')} out of ${String(totalPages).padStart(2, '0')}</div>
    </div>
    
    <div class="divider"></div>
    
    <div class="powered-by">
      Powered By <img src="/images/auth-logo.png" alt="Arca Logo" class="arca-logo" onerror="this.style.display='none'; this.nextElementSibling?.classList.remove('hidden')" />
      <span class="arca-logo-text hidden">ARCA</span>
    </div>
  `;
};

// Page title generator
export const generatePageTitle = (title: string): string => {
  return `<div class="page-title">${title}</div>`;
};
