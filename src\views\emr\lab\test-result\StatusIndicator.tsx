import React, { useCallback, useState } from 'react';

import { FiUpload } from 'react-icons/fi';

import { useTestStore } from '@/store/emr/lab/reports-store';

import colors from '@/utils/colors';

import { StatusType } from '@/types/emr/lab';

import UploadFileModal from './UploadFileModal';

interface StatusIndicatorProps {
  status: StatusType;
  id: string;
  fetchTestResult?: () => void;
  fileMetadata?: any[]; // add fileMetadata
}

const statusColors: Record<StatusType, string> = {
  Ready: colors.status.ready,
  Awaited: colors.status.awaited,
  'Not Paid': colors.status.notPaid,
  Upload: colors.status.upload,
  Uploaded: colors.status.uploaded,
};

export const getBgColor = (status: StatusType) => {
  const displayStatus = status === 'Uploaded' ? 'Uploaded' : 'Upload';
  return statusColors[displayStatus];
};

const statusStyles: Record<StatusType, string> = {
  Ready: `bg-[${statusColors.Ready}] text-white`,
  Awaited: `bg-[${statusColors.Awaited}] text-black`,
  'Not Paid': `bg-[${statusColors['Not Paid']}] text-black`,
  Upload: `bg-transparent text-black cursor-pointer cursor-pointer`,
  Uploaded: `bg-[${statusColors.Uploaded}] text-black`,
};

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  id,
  fetchTestResult,
  fileMetadata,
}) => {
  const [open, setOpen] = useState(false);
  const { fetchRecentTest } = useTestStore();

  const displayStatus = status === 'Uploaded' ? 'Uploaded' : 'Upload';
  const displayStatusType = status === 'Uploaded' ? status : 'Upload';

  // Always allow click for both Upload and Uploaded
  const onClick = useCallback(() => {
    setOpen(true);
  }, []);

  return (
    <>
      <div
        onClick={onClick}
        className={`w-full h-full text-center py-4 font-medium flex justify-center items-center gap-2 cursor-pointer ${statusStyles[displayStatusType]}`}
      >
        {displayStatusType === 'Upload' && <FiUpload fontSize="small" />}
        <span className="whitespace-nowrap">{displayStatus}</span>
      </div>
      <UploadFileModal
        open={open}
        onClose={() => setOpen(false)}
        id={id}
        fileMetadata={fileMetadata}
        status={status}
        onUploadSuccess={() => {
          fetchRecentTest();
          fetchTestResult?.();
          setOpen(false);
        }}
      />
    </>
  );
};

export default StatusIndicator;
