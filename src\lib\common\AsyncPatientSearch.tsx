import { ActionMeta, GroupBase, SingleValue } from 'react-select';

import { debounce } from 'lodash';
import AsyncSelect, { AsyncProps } from 'react-select/async';

import { getToken } from '@core/lib/auth/services';

import { getPatientBySearchString } from '@/query/patient';

const promiseOptions = (
  inputValue: string,
  callback: (options: any) => void
) => {
  getToken().then((token) => {
    getPatientBySearchString(inputValue, token || '').then((patients) => {
      callback(patients.data?.items ?? []);
    });
  });
};

interface AsyncPatientSearchProps<T>
  extends AsyncProps<T, false, GroupBase<T>> {
  defaultOptions?: T[];
  onChange: (newValue: SingleValue<T>, actionMeta: ActionMeta<unknown>) => void;
}

const AsyncPatientSearch = <T extends unknown>({
  defaultOptions = [],
  ...rest
}: AsyncPatientSearchProps<T>) => {
  return (
    <AsyncSelect<T>
      loadOptions={debounce(promiseOptions, 1000)}
      defaultOptions={defaultOptions}
      isClearable
      cacheOptions
      {...rest}
    />
  );
};

export default AsyncPatientSearch;
