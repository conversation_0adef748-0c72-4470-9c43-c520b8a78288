import { ChevronDown } from 'lucide-react';

import { fetchAllDoctors, useDoctorStore } from '@/store/mrd/queue/doctor';

import AppSelect from '@/core/components/app-select';

const DoctorSelect = () => {
  const { doctors, setSelectedDoctor, selectedDoctor } = useDoctorStore();

  const options = doctors.map((item) => ({ value: item.id, label: item.name }));

  const handleChange = (selected: any) => {
    const doctor = doctors.find((item) => item.id === selected?.value) || null;
    setSelectedDoctor(doctor);
  };

  const handleMenuOpen = async () => {
    if (!doctors.length) {
      await fetchAllDoctors();
    }
  };

  const customStyles = {
    control: (base: any) => ({
      ...base,
      borderRadius: '20px',
      border: '1px solid #64707D',
      minHeight: '34px',
      height: '34px',
      fontSize: '12px',
      padding: '0 8px 0 8px',
      minWidth: '150px',
    }),
    valueContainer: (base: any) => ({
      ...base,
      padding: '0',
      minHeight: '34px',
    }),
    indicatorSeparator: () => ({
      display: 'none',
    }),
    dropdownIndicator: (base: any) => ({
      ...base,
      padding: '0 4px',
      color: '#000000',
      fontSize: '14px',
    }),
  };

  return (
    <AppSelect
      options={options}
      value={
        selectedDoctor
          ? { value: selectedDoctor.id, label: selectedDoctor.name }
          : null
      }
      onChange={handleChange}
      onMenuOpen={handleMenuOpen}
      placeholder="Select a Doctor"
      styles={customStyles}
      components={{
        DropdownIndicator: () => <ChevronDown size={15} />,
      }}
    />
  );
};

export default DoctorSelect;
