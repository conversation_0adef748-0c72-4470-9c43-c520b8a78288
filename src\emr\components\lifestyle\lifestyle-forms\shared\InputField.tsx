import React, { FC, InputHTMLAttributes, memo } from 'react';

import TextInput from '@core/components/text-input';

type Variant = 'gray' | 'white' | 'transparent';
type Size = 'small' | 'medium' | 'large';

export type InputFieldProps = Omit<
  InputHTMLAttributes<HTMLInputElement>,
  'size'
> & {
  label?: string;
  className?: string;
  variant?: Variant;
  size?: Size;
  iconClassName?: string;
  endDecoration?: React.ReactNode;
  endIconSecondary?: React.ReactNode;
  inputClassName?: string;
};

const inputStyles: Record<Variant, string> = {
  gray: '*:text-black *:bg-gray-300 *:focus:ring-gray-300',
  white: '*:text-gray-600 *:bg-white *:focus:ring-gray-300',
  transparent: '*:text-black *:bg-transparent *:focus:ring-0 *:focus:border-0',
};

const sizeStyles: Record<Size, string> = {
  small: '*:text-[0.7rem] *:md:text-xs *:h-6 *:px-1',
  medium: '*:text-sm *:h-8 *:px-2',
  large: '*:text-lg *:h-10 *:px-3',
};

const disabledClasses =
  'disabled:*:text-black disabled:*:bg-transparent disabled:*:focus:ring-0 disabled:*:focus:border-0';

const InputField: FC<InputFieldProps> = ({
  label,
  className = '',
  variant = 'white',
  size = 'medium',
  iconClassName,
  inputClassName,
  ...props
}) => {
  return (
    <TextInput
      label={label}
      iconClassName={iconClassName}
      className={`items-start gap-1 w-full ${className}`}
      labelClassName={size === 'small' ? 'text-xs' : 'text-xs xl:text-sm'}
      inputClassName={`w-14 !w-full *:border *:border-transparent *:rounded-md *:focus:ring-2 ${disabledClasses} ${inputStyles[variant]} ${sizeStyles[size]} ${inputClassName}`}
      {...props}
    />
  );
};

export default memo(InputField);
