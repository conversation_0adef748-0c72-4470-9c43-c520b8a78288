import React, { useState, useRef, useEffect } from 'react';

import { cn } from '@/lib/utils';

type ActivityStatusSelectorPropsWithAll = {
  value?: 'active' | 'inactive' | 'all';
  onChange?: (value: 'active' | 'inactive' | 'all') => void;
  disabled?: boolean;
  placeholder?: string;
  showAllOption: true;
  className?: string;
};

type ActivityStatusSelectorPropsWithoutAll = {
  value?: 'active' | 'inactive';
  onChange?: (value: 'active' | 'inactive') => void;
  disabled?: boolean;
  placeholder?: string;
  showAllOption?: false;
  className?: string;
};

type ActivityStatusSelectorProps =
  | ActivityStatusSelectorPropsWithAll
  | ActivityStatusSelectorPropsWithoutAll;

export default function ActivityStatusSelector({
  value,
  onChange,
  disabled = false,
  placeholder = 'Select activity',
  showAllOption = false,
  className,
}: ActivityStatusSelectorProps) {
  const getOptions = () => {
    if (showAllOption) {
      return [
        { label: 'All Activity', value: 'all' as const },
        { label: 'Active', value: 'active' as const },
        { label: 'Inactive', value: 'inactive' as const },
      ];
    }

    if (value === 'active') {
      return [
        { label: 'Active', value: 'active' as const },
        { label: 'Inactive', value: 'inactive' as const },
      ];
    } else if (value === 'inactive') {
      return [
        { label: 'Inactive', value: 'inactive' as const },
        { label: 'Active', value: 'active' as const },
      ];
    }

    return [
      { label: 'Active', value: 'active' as const },
      { label: 'Inactive', value: 'inactive' as const },
    ];
  };

  const options = getOptions();

  const handleChange = (newValue: string) => {
    if (onChange) {
      if (showAllOption) {
        (onChange as (value: 'active' | 'inactive' | 'all') => void)(
          newValue as 'active' | 'inactive' | 'all'
        );
      } else {
        (onChange as (value: 'active' | 'inactive') => void)(
          newValue as 'active' | 'inactive'
        );
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-[#06C6A7] text-white';
      case 'inactive':
        return 'bg-[#DAE1E7] text-black';
      case 'all':
        return 'bg-transparent border border-black text-black';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const handleToggle = () => {
    if (disabled) return;

    if (showAllOption) {
      return;
    }

    if (value === 'active') {
      handleChange('inactive');
    } else if (value === 'inactive') {
      handleChange('active');
    }
  };

  const displayText = () => {
    if (value === 'active') return 'Active';
    if (value === 'inactive') return 'Inactive';
    if (value === 'all') return 'All Activity';
    return placeholder;
  };

  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showAllOption]);

  const handleOptionClick = (optionValue: string) => {
    handleChange(optionValue);
    setIsOpen(false);
  };

  if (showAllOption) {
    return (
      <div className="relative" ref={dropdownRef}>
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className={cn(
            'h-6 border-0 rounded px-3 text-sm w-auto min-w-[60px] cursor-pointer',
            value && getStatusColor(value),
            disabled && 'cursor-not-allowed opacity-50',
            className
          )}
          style={{ fontSize: '14px', fontWeight: 'normal' }}
          disabled={disabled}
        >
          {displayText()}
        </button>

        {isOpen && options.length > 0 && (
          <div className="absolute top-full left-0 mt-1 rounded-lg min-w-[70px] bg-white shadow-lg border border-gray-200 z-[99999]">
            {options.map((option) => (
              <div
                key={option.value}
                onClick={() => handleOptionClick(option.value)}
                className="h-6 text-sm cursor-pointer hover:bg-gray-50 px-2 py-1 first:rounded-t-lg last:rounded-b-lg"
              >
                {option.label}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <button
      type="button"
      onClick={handleToggle}
      className={cn(
        'h-6 border-0 rounded px-3 text-sm w-auto min-w-[60px] cursor-pointer',
        value && getStatusColor(value),
        disabled && 'cursor-not-allowed opacity-50',
        className
      )}
      style={{ fontSize: '14px', fontWeight: 'normal' }}
      disabled={disabled}
    >
      {displayText()}
    </button>
  );
}
