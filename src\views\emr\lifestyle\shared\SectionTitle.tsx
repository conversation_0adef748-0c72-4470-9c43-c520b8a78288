import React, { FC, memo, useCallback, useState } from 'react';

import { isValidURL } from '@/utils/emr/lifestyle';

import AppIcon from '@/core/components/app-icon';

type Props = {
  title: string;
  icon?: string;
  renderTitleEnd?: () => React.ReactNode;
};

const SectionTitle: FC<Props> = ({
  title,
  icon,
  renderTitleEnd = () => null,
}) => {
  const [errorOnImage, setErrorOnImage] = useState(false);

  const renderIcon = useCallback(() => {
    if (!icon) return null;

    if (icon.length <= 4) {
      return <span className="text-2xl">{icon}</span>;
    } else if (isValidURL(icon) && !errorOnImage) {
      return (
        <img
          src={icon}
          className="w-6 h-6"
          alt="i"
          onError={() => setErrorOnImage(true)}
        />
      );
    } else {
      return (
        <span className="text-2xl text-black">
          <AppIcon icon={icon} />
        </span>
      );
    }
  }, [icon, errorOnImage]);

  return (
    <div className="flex justify-between w-full items-center">
      <div className="flex items-center space-x-2">
        {renderIcon()}
        <h3 className="text-lg font-medium">{title}</h3>
      </div>
      <div>{renderTitleEnd()}</div>
    </div>
  );
};

export default memo(SectionTitle);
