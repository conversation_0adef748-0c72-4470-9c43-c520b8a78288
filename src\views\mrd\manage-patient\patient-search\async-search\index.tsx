import React, { memo } from 'react';

import AppAsyncSearch from '@core/components/app-async-search';

import { useMrdPatientSearch } from '@/store/mrd/manage-patient/patient-search';

import { SearchPatient } from '@/types/mrd/manage-patient/patient';

import DropdownIndicator from './DropdownIndicator';

const AsyncSearch = ({}) => {
  const { searchPatient, recentSearches, selectPatient, searchSelected } =
    useMrdPatientSearch();

  return (
    <AppAsyncSearch<SearchPatient>
      placeholder="Search by name, patient id"
      value={searchSelected}
      defaultOptions={recentSearches}
      loadOptions={searchPatient}
      getOptionLabel={(option) => option.name}
      onChange={selectPatient}
      components={{ DropdownIndicator: DropdownIndicator as any }}
    />
  );
};

export default memo(AsyncSearch);
