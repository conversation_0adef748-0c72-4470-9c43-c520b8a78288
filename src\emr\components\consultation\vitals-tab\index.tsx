import React, { FC, memo, useMemo, useEffect } from 'react';

import dayjs from 'dayjs';
import { maxBy } from 'lodash';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import {
  EmrTypes,
  useCustomiseEmrStore,
} from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { usePatientInfoStore } from '@/store/emr/patient-info';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { normalizeString } from '@/utils/textUtil';

import {
  calculateAnalyticsOfVitals,
  calculateVitalsAnalytics,
  VitalAnalyticsValue,
} from '@/helpers/vitals';

import VitalsCard from '@/components/vitals-card';

import AppTab from '@/core/components/app-tab';

import {
  getVitalValue,
  getAnalyticsKey,
  mapToAnalyticsKeys,
  convertToVitalCard,
} from './vitals-utils';

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const getTabTitle = (updatedOn: string, i: number) => {
  const date = dayjs(updatedOn);
  const isToday = date.isSame(dayjs(), 'day');

  return isToday && i === 0 ? 'Today' : formatDate(date, DATE_DD_MM_YYYY_SLASH);
};

const VitalsTab: FC = () => {
  const { patient } = useCurrentPatientStore();
  const { doctorProfile } = useDoctorStore();

  const { vitals, getPatientVitals, isLoadingVitals } = usePatientInfoStore();
  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();

  useEffect(() => {
    if (patient?.id) {
      getPatientVitals(patient.id);
    }
  }, [patient?.id, getPatientVitals]);

  const lastCreatedEmrData = maxBy<EmrTypes>(
    customiseEmrData,
    (item) => new Date(item.created_on as string)
  );

  const processedVitals = useMemo(() => {
    if (!vitals || vitals.length === 0) return [];

    return vitals.map((vital) => ({
      ...vital.vitals,
      updatedOn: vital.updated_on,
      vitalStatuses: vital.vitalStatuses,
    })) as any[];
  }, [vitals]);

  const recentVitals = useMemo(
    () => processedVitals.slice(-3).reverse(),
    [processedVitals]
  );

  const cleanUnit = (unit: string) => {
    const cleanedUnit = unit.split(' ')[0];

    if (cleanedUnit === 'C' || cleanedUnit === '°C') {
      return '°F';
    }
    return cleanedUnit;
  };

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile?.id as string);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  const tabs = recentVitals.map((vitalRecord, i) => {
    const label = getTabTitle(vitalRecord.updatedOn, i);
    const previousVitalRecord =
      i < recentVitals.length - 1 ? recentVitals[i + 1] : {};
    let statCards = [];
    if (lastCreatedEmrData?.selected_tiles?.length ?? 0 > 0) {
      const selectedTiles = lastCreatedEmrData?.selected_tiles?.flat() || [];
      const formattedKeys = selectedTiles.map((tile) =>
        normalizeString(tile.value)
      );
      const analyticsKeys = mapToAnalyticsKeys(formattedKeys);
      const analytics = calculateAnalyticsOfVitals(
        [previousVitalRecord, vitalRecord],
        analyticsKeys
      );
      statCards = selectedTiles.map((tile) => {
        const topic = tile?.abbreviationCode || normalizeString(tile?.value);
        const unit = cleanUnit(tile?.unit as string);
        const key = normalizeString(tile?.value);
        const data = getVitalValue(vitalRecord, key) || '--';
        const info =
          (analytics[getAnalyticsKey(key)] as VitalAnalyticsValue) || '';
        return convertToVitalCard(
          data,
          unit,
          topic,
          info,
          vitalRecord.vitalStatuses
        );
      });
    } else {
      const analytics = calculateVitalsAnalytics([
        previousVitalRecord,
        vitalRecord,
      ]);
      statCards = [
        convertToVitalCard(
          vitalRecord.height,
          'cm',
          'Height',
          analytics.height,
          vitalRecord.vitalStatuses
        ),
        convertToVitalCard(
          vitalRecord.weight,
          'kg',
          'Weight',
          analytics.weight,
          vitalRecord.vitalStatuses
        ),
        convertToVitalCard(
          vitalRecord.bmi,
          '',
          'BMI',
          analytics.bmi,
          vitalRecord.vitalStatuses
        ),
        convertToVitalCard(
          vitalRecord?.sbp && vitalRecord?.dbp
            ? `${vitalRecord?.sbp ?? ''}/${vitalRecord?.dbp ?? ''}`
            : '',
          '',
          'BP',
          analytics.bloodPressure,
          vitalRecord.vitalStatuses
        ),
        convertToVitalCard(
          vitalRecord.pulse,
          'BPM',
          'Pulse',
          analytics.heartRate,
          vitalRecord.vitalStatuses
        ),
        convertToVitalCard(
          vitalRecord.rr,
          'Un',
          'RR',
          analytics.respiratoryRate,
          vitalRecord.vitalStatuses
        ),
        convertToVitalCard(
          vitalRecord.spO2,
          '%',
          'SpO2',
          analytics.spO2,
          vitalRecord.vitalStatuses
        ),
        convertToVitalCard(
          vitalRecord?.sbp && vitalRecord?.dbp
            ? (() => {
                const systolic = parseFloat(vitalRecord.sbp);
                const diastolic = parseFloat(vitalRecord.dbp);
                if (!isNaN(systolic) && !isNaN(diastolic)) {
                  return (systolic - diastolic).toString();
                }
                return '';
              })()
            : '',
          'mmHg',
          'PP',
          analytics.pp,
          vitalRecord.vitalStatuses
        ),
      ];
    }
    return {
      label,
      content: (
        <div className="w-full grid grid-cols-3 gap-2 py-2">
          {statCards.map((vitalCard) => (
            <VitalsCard
              key={vitalCard.label}
              loading={isLoadingVitals}
              {...vitalCard}
            />
          ))}
        </div>
      ),
      id: vitalRecord.updatedOn,
    };
  });

  let fallbackContent = null;
  if (recentVitals.length === 0) {
    if (lastCreatedEmrData?.selected_tiles?.length ?? 0 > 0) {
      const selectedTiles = lastCreatedEmrData?.selected_tiles.flat();
      const mostRecentVitalStatuses = vitals?.[0]?.vitalStatuses;
      fallbackContent = (
        <div className="w-full grid grid-cols-3 gap-2 py-2.5">
          {selectedTiles?.map((tile) => {
            const topic =
              tile?.abbreviationCode || normalizeString(tile?.value);
            const unit = cleanUnit(tile?.unit as string);
            const data = '--';
            const vitalCard = convertToVitalCard(
              data,
              unit,
              topic,
              undefined,
              mostRecentVitalStatuses
            );
            return (
              <VitalsCard
                key={topic}
                loading={isLoadingVitals}
                {...vitalCard}
              />
            );
          })}
        </div>
      );
    } else {
      fallbackContent = (
        <div className="w-full grid grid-cols-3 gap-3 py-2.5">
          <div className="text-center text-gray-500 col-span-3 py-4">
            No vitals data available
          </div>
        </div>
      );
    }
  }

  return (
    <>
      {recentVitals.length > 0 ? (
        <AppTab
          tabs={tabs}
          wrapperProps={{ className: 'w-full !z-10 bg-white' }}
          panelContainerProps={{ className: 'w-full' }}
          scrollButtons={false}
          sx={{
            minHeight: '34px',
            '& .MuiTab-root': {
              p: '0px !important',
              minHeight: '34px',
            },
          }}
        />
      ) : (
        fallbackContent
      )}
    </>
  );
};

export default memo(VitalsTab);
