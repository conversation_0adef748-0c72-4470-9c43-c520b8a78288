import { getCSSVariableColor } from '@core/theme/utils';

export const darkColors = {
  background: getCSSVariableColor('--background', [222.2, 84, 4.9]),
  foreground: getCSSVariableColor('--foreground', [210, 40, 98]),
  card: getCSSVariableColor('--card', [222.2, 84, 4.9]),
  cardForeground: getCSSVariableColor('--card-foreground', [210, 40, 98]),
  popover: getCSSVariableColor('--popover', [222.2, 84, 4.9]),
  popoverForeground: getCSSVariableColor('--popover-foreground', [210, 40, 98]),
  primary: getCSSVariableColor('--primary', [210, 40, 98]),
  primaryForeground: getCSSVariableColor(
    '--primary-foreground',
    [222.2, 47.4, 11.2]
  ),
  secondary: getCSSVariableColor('--arca-blue-2', [200, 97, 25]),
  secondaryLight: getCSSVariableColor('--arca-blue-2-light', [200, 96, 85]),
  secondaryForeground: getCSSVariableColor(
    '--secondary-foreground',
    [210, 40, 98]
  ),
  muted: getCSSVariableColor('--muted', [217.2, 32.6, 17.5]),
  mutedForeground: getCSSVariableColor('--muted-foreground', [215, 20.2, 65.1]),
  accent: getCSSVariableColor('--accent', [217.2, 32.6, 17.5]),
  accentForeground: getCSSVariableColor('--accent-foreground', [210, 40, 98]),
  destructive: getCSSVariableColor('--destructive', [0, 62.8, 30.6]),
  destructiveForeground: getCSSVariableColor(
    '--destructive-foreground',
    [210, 40, 98]
  ),
  border: getCSSVariableColor('--border', [217.2, 32.6, 17.5]),
  input: getCSSVariableColor('--input', [217.2, 32.6, 17.5]),
  ring: getCSSVariableColor('--ring', [212.7, 26.8, 83.9]),
};
