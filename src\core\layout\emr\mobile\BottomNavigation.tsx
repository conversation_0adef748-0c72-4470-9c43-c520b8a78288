import React, { memo, useState, useRef, useCallback, useEffect } from 'react';

import emrNavItems from '@/core/configs/emr/navigation-mobile';

import NavLink from './NavLink';

const BottomNavigation = () => {
  const [hideText, setHideText] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const textRefs = useRef<Record<string, HTMLSpanElement | null>>({});

  const checkOverflow = useCallback(() => {
    if (!containerRef.current) return;

    const overflow = emrNavItems.some((item) => {
      const textElement = textRefs.current[item?.label as string];
      return textElement && textElement.scrollWidth > textElement.clientWidth;
    });

    setHideText(overflow);
  }, []);

  useEffect(() => {
    checkOverflow();
  }, [checkOverflow]);

  return (
    <div
      ref={containerRef}
      className="h-16 w-full bg-white rounded-lg gap-1 shadow-custom-xs flex p-1 px-2 pb-2"
    >
      {emrNavItems.map((item) => (
        <NavLink
          key={item.path}
          {...item}
          hideText={hideText}
          textRefs={textRefs}
          checkOverflow={checkOverflow}
        />
      ))}
    </div>
  );
};

export default memo(BottomNavigation);
