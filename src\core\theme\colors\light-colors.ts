import { getCSSVariableColor } from '@core/theme/utils';

export const lightColors = {
  background: getCSSVariableColor('--background', [0, 0, 100]),
  foreground: getCSSVariableColor('--foreground', [222.2, 84, 4.9]),
  card: getCSSVariableColor('--card', [0, 0, 100]),
  cardForeground: getCSSVariableColor('--card-foreground', [222.2, 84, 4.9]),
  popover: getCSSVariableColor('--popover', [0, 0, 100]),
  popoverForeground: getCSSVariableColor(
    '--popover-foreground',
    [222.2, 84, 4.9]
  ),
  primary: getCSSVariableColor('--primary', [200, 96, 11]),
  primaryForeground: getCSSVariableColor('--primary-foreground', [210, 40, 98]),
  secondary: getCSSVariableColor('--arca-blue-2', [200, 97, 25]),
  secondaryLight: getCSSVariableColor('--arca-blue-2-light', [200, 96, 85]),
  secondaryForeground: getCSSVariableColor(
    '--secondary-foreground',
    [222.2, 47.4, 11.2]
  ),
  muted: getCSSVariableColor('--muted', [210, 40, 96.1]),
  mutedForeground: getCSSVariableColor(
    '--muted-foreground',
    [215.4, 16.3, 46.9]
  ),
  accent: getCSSVariableColor('--accent', [210, 40, 96.1]),
  accentForeground: getCSSVariableColor(
    '--accent-foreground',
    [222.2, 47.4, 11.2]
  ),
  destructive: getCSSVariableColor('--destructive', [0, 84.2, 60.2]),
  destructiveForeground: getCSSVariableColor(
    '--destructive-foreground',
    [210, 40, 98]
  ),
  border: getCSSVariableColor('--border', [214.3, 31.8, 91.4]),
  input: getCSSVariableColor('--input', [214.3, 31.8, 91.4]),
  ring: getCSSVariableColor('--ring', [222.2, 84, 4.9]),
  disabledText: '#808080',
};
