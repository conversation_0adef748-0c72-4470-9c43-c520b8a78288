import React, { memo, useCallback, useEffect, useState } from 'react';

import { useFieldArray, useForm } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import { Stack } from '@mui/material';
import { toast } from 'sonner';
import * as yup from 'yup';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import {
  discardDefaultValues,
  getUniqueId,
} from '@/utils/emr/doctor-profile/personal-info';
import { getErrorMessages } from '@/utils/error-message';

import { profileTabs } from '@/constants/emr/doctor-profile/personal-info';

import AddButton from '@/emr/components/lifestyle/lifestyle-forms/shared/AddButton';

import {
  defaultInsurance,
  Insurance,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import DeleteModal from '../shared/DeleteModal';
import SaveButton from '../shared/SaveButton';
import Title from '../shared/Title';

import InsuranceMob from './InsuranceMob';
import InsuranceTable from './InsuranceTable';

const insuranceSchema = yup.object({
  insurance: yup.array().of(
    yup.object({
      validFrom: yup.date().nullable(),
      validTo: yup
        .date()
        .nullable()
        .test('is-after', 'Enter a valid date', function (value) {
          const { validFrom } = this.parent;
          if (!validFrom || !value) return true;
          return value > validFrom;
        }),
    })
  ),
});

export type FormData = {
  insurance: Insurance[];
};

const MedicalInsuranceTab = () => {
  const { data: userData } = useUserStore();
  const isMobile = useIsMobile();

  const [itemToEdit, setItemToEdit] = useState<number | null>(null);
  const [itemToDelete, setItemToDelete] = useState<ItemToDelete>(null);
  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    updateDoctorProfile,
    doctorProfile,
    isDeleting,
    createDoctorProfile,
    setTabName,
    deleteDoctorProfileTableItem,
  } = useDoctorStore();

  const {
    handleSubmit,
    control,
    reset,
    register,
    getValues,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      insurance: [isMobile ? defaultInsurance : {}],
    },
    resolver: !isMobile ? (yupResolver(insuranceSchema) as any) : undefined,
  });

  const { append, fields, remove } = useFieldArray<FormData>({
    control,
    name: 'insurance',
    shouldUnregister: true,
  });

  const onSubmit = useCallback(
    (data: FormData) => {
      const insurance = discardDefaultValues(data?.insurance, defaultInsurance);

      const finalData = insurance?.map((item) => ({
        ...item,
        uuId: getUniqueId(item?.uuId, 'INS'),
      }));

      if (doctorProfile?.id) {
        updateDoctorProfile(doctorProfile.id, {
          insurance: finalData,
        });
      } else {
        createDoctorProfile({
          insurance: finalData,
          username: userData?.email,
        });
      }
      setItemToEdit(null);
      setIsSubmitted(true);
      setEditableField(new Set());
    },
    [createDoctorProfile, doctorProfile, updateDoctorProfile, userData?.email]
  );

  const handleItemEdit = useCallback(
    (index: number) => () => {
      setItemToEdit(index);
    },
    []
  );

  const handleOnAdd = useCallback(() => {
    setItemToEdit(fields.length);
    append(defaultInsurance);
  }, [append, fields.length]);

  const handleOnDelete = useCallback(
    (item: ItemToDelete) => setItemToDelete(item),
    []
  );

  const handleDelete = useCallback(async () => {
    if (!itemToDelete) return;

    const { uuId, index } = itemToDelete;

    if (uuId && doctorProfile?.id) {
      const filteredInsurance = doctorProfile.insurance?.filter(
        (item) => item?.uuId !== uuId
      );

      await deleteDoctorProfileTableItem(doctorProfile.id, {
        insurance: filteredInsurance,
      });
    } else {
      if (index === 0 && isMobile) {
        reset({
          insurance: [defaultInsurance] as Insurance[],
        });
      } else {
        remove(index);
      }
    }

    setItemToDelete(null);
  }, [
    itemToDelete,
    doctorProfile?.id,
    doctorProfile?.insurance,
    deleteDoctorProfileTableItem,
    remove,
    reset,
    isMobile,
  ]);

  const handleCancel = useCallback(() => {
    setItemToDelete(null);
  }, []);

  useEffect(() => {
    if (doctorProfile?.insurance) {
      if (isMobile) {
        reset({
          insurance: doctorProfile.insurance?.length
            ? doctorProfile.insurance
            : ([defaultInsurance] as Insurance[]),
        });
      } else {
        reset({ insurance: doctorProfile.insurance });
      }
    }
  }, [doctorProfile?.insurance, isMobile, reset]);

  useEffect(() => {
    setTabName(profileTabs.MEDICAL_INSURANCE);
  }, [setTabName]);

  useEffect(() => {
    const errorMessages = getErrorMessages(errors);
    if (errorMessages.length && !isMobile) {
      toast.error(errorMessages[0]);
    }
  }, [errors, isMobile]);

  return (
    <Stack
      component="form"
      spacing={2}
      pb={{ xs: 8, md: 0 }}
      onSubmit={handleSubmit(onSubmit)}
    >
      <Title
        title="Medical Insurance"
        onAdd={handleOnAdd}
        showEndButton={!isMobile}
      />
      {isMobile ? (
        <InsuranceMob
          fields={fields}
          control={control}
          register={register}
          editableField={editableField}
          getValues={getValues}
          isSubmitted={isSubmitted}
          setEditableField={setEditableField}
          handleOnDelete={handleOnDelete}
        />
      ) : (
        <InsuranceTable
          fields={fields}
          control={control}
          itemToEdit={itemToEdit}
          handleItemEdit={handleItemEdit}
          handleOnDelete={handleOnDelete}
          error={errors}
        />
      )}
      {isMobile && (
        <div className="fixed bottom-34 md:static right-6 bg-transparent">
          <AddButton size="medium" onClick={handleOnAdd} />
        </div>
      )}
      <SaveButton />
      <DeleteModal
        open={!!itemToDelete}
        onClose={handleCancel}
        onDelete={handleDelete}
        isLoading={isDeleting}
      />
    </Stack>
  );
};

export default memo(MedicalInsuranceTab);
