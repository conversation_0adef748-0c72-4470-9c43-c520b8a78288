import React, {
  Dispatch,
  memo,
  SetStateAction,
  useCallback,
  useEffect,
} from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { toast } from 'sonner';

import Loading from '@/lib/common/loading';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useLifestyleStore } from '@/store/lifestyle';
import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import { areSectionsEqual } from '@/utils/constants/lifeStyle';

import {
  lifestyleFormMode,
  lifestyleModes,
  mobileViews,
  RenderFormModesTypes,
} from '@/constants/lifestyle';

import { LifestyleQuestion, lifestyleSource } from '@/emr/types/lifestyle';

import ModalContainer from '../shared/ModalContainer';
import PatientDetails from '../shared/PatientDetails';
import RenderFields from '../shared/RenderFields';

type Props = {
  setRenderForm?: Dispatch<SetStateAction<RenderFormModesTypes>>;
  renderForm?: RenderFormModesTypes;
  isAmbientRecord?: boolean;
};

const { VIEW, CREATE } = lifestyleModes;

const DietaryAssessmentModal: React.FC<Props> = ({
  setRenderForm,
  renderForm,
  isAmbientRecord = false,
}) => {
  const isMobile = useIsMobile();
  const {
    questions,
    createLifestyleData,
    updating,
    getPatientLifestyle,
    updateLifestyleData,
    loading,
  } = useLifestyleStore();
  const { patient } = useCurrentPatientStore();
  const { closeModal, formMode, dataToEdit, setMobilePage } =
    useLifestyleUtilStore();

  const methods = useForm<LifestyleQuestion>({
    defaultValues: questions || {},
  });

  const { handleSubmit, control, reset } = methods;

  const onSubmit = useCallback(
    async (data: LifestyleQuestion) => {
      if (areSectionsEqual(data?.sections ?? [], questions?.sections)) {
        toast.warning('No changes made');
        return;
      }

      const payload = {
        source: lifestyleSource.DIETARY_ASSESSMENT_SOURCE,
        sections: data?.sections,
        status: lifestyleFormMode.EDITABLE,
      };

      if (dataToEdit) {
        await updateLifestyleData(payload, dataToEdit?.id || '');
      } else {
        await createLifestyleData(payload, patient?.id || '');
        setMobilePage(mobileViews.TIMELINE);
      }
      getPatientLifestyle(patient?.id || '', data?.source);
      closeModal();
    },
    [
      questions?.sections,
      dataToEdit,
      getPatientLifestyle,
      patient?.id,
      closeModal,
      updateLifestyleData,
      createLifestyleData,
      setMobilePage,
    ]
  );

  useEffect(() => {
    if (dataToEdit && formMode !== lifestyleModes.CREATE) {
      reset(dataToEdit);
    } else {
      reset((prev) => ({ ...prev, sections: questions?.sections ?? [] }));
    }
  }, [reset, dataToEdit, formMode, questions?.sections]);

  return (
    <FormProvider {...methods}>
      <ModalContainer
        title="Dietary Assessment Summary"
        onSubmit={handleSubmit(onSubmit)}
        showTitle={!isMobile}
        setRenderForm={setRenderForm}
        renderForm={renderForm}
        showSwitchRecordButton={
          formMode !== CREATE && isMobile && isAmbientRecord
        }
        loading={updating}
        showEditButton={dataToEdit?.status === lifestyleFormMode.EDITABLE}
      >
        <PatientDetails />
        <div className="flex flex-col gap-2 w-full h-full">
          {!loading ? (
            <>
              {questions?.sections?.map((q, i) => (
                <RenderFields
                  key={q.section_id}
                  control={control}
                  isReadOnly={formMode === VIEW}
                  questions={q?.questions}
                  icon={q.icon}
                  sectionTitle={q.section_title}
                  type={q.type}
                  name={`sections.${i}.questions`}
                />
              ))}
            </>
          ) : (
            <div className="flex flex-1 justify-center items-center h-full">
              <Loading />
            </div>
          )}
        </div>
      </ModalContainer>
    </FormProvider>
  );
};

export default memo(DietaryAssessmentModal);
