// import DashboardIcon from '@/assets/svg/dashboard-icon.svg';
import DashboardIcon from '@/assets/svg/DashboardIcon';
import ManagePatientsIcon from '@/assets/svg/manage-patients-icon.svg';
// import MessageIcon from '@/assets/svg/message-icon.svg';

import { routes } from '@/constants/routes';

import { SidebarItem } from '@/core/layout/shared/side-bar/types';

const {
  MRD_DASHBOARD,
  MRD_MANAGE_PATIENTS,
  // MRD_MESSAGES
} = routes;

const mrdNavigation: SidebarItem[] = [
  {
    label: 'Dashboard',
    path: MRD_DASHBOARD,
    icon: <DashboardIcon />,
  },
  {
    label: 'Manage Patients',
    path: MRD_MANAGE_PATIENTS,
    icon: <ManagePatientsIcon />,
  },
  // {
  //   label: 'Messages',
  //   path: MRD_MESSAGES,
  //   icon: <MessageIcon />,
  //   disabled: true,
  // },
];

export default mrdNavigation;
