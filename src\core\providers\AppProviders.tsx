'use client';

import React, { FC } from 'react';

import { Toaster } from 'sonner';

import dynamic from 'next/dynamic';

import StatusModal from '@core/components/status-modal';

import AuthProvider from './AuthProviders';
import MuiThemeProvider from './MuiThemeProvider';

const StageWise = dynamic(() => import('@/core/components/stage-wise'), {
  ssr: false,
});

type Props = {
  children: React.ReactNode;
};

const AppProviders: FC<Props> = ({ children }) => {
  return (
    <AuthProvider>
      <MuiThemeProvider>
        <Toaster richColors closeButton />
        <StageWise />
        <StatusModal />
        {children}
      </MuiThemeProvider>
    </AuthProvider>
  );
};

export default AppProviders;
