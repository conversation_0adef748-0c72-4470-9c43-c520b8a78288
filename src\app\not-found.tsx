'use client';

import { FaHome } from 'react-icons/fa';
import { MdErrorOutline } from 'react-icons/md';

import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-white">
      <div className="text-center space-y-6 p-8 max-w-lg mx-auto">
        <div className="flex justify-center">
          <MdErrorOutline className="text-[#012436] text-8xl animate-pulse" />
        </div>
        <h1 className="text-4xl font-bold text-[#012436]">404</h1>
        <h2 className="text-2xl font-semibold text-[#323F49]">
          Page Not Found
        </h2>
        <p className="text-[#637D92] max-w-md mx-auto">
          The page you are looking for might have been removed, had its name
          changed, or is temporarily unavailable.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/"
            className="inline-flex items-center gap-2 px-6 py-3 rounded-lg text-white bg-[#012436] hover:bg-[#001926] transition-colors duration-200"
          >
            <FaHome />
            <span>Return Home</span>
          </Link>
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center gap-2 px-6 py-3 rounded-lg text-[#012436] border border-[#012436] hover:bg-[#012436] hover:text-white transition-colors duration-200"
          >
            <span>Go Back</span>
          </button>
        </div>
      </div>
    </div>
  );
}
