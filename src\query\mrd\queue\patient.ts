import { arcaAxios } from '@/core/lib/interceptor';

export const getPatientVitals = async (id: string) => {
  return await arcaAxios.get(`/patient/vitals?patientId=${id}`);
};

export async function updateAppointment(params: {
  queueId: string;
  payload: {
    time?: string;
    status?: string;
    queuePosition?: number;
    department?: string;
  };
}) {
  const { data } = await arcaAxios.patch(
    `queue?queueId=${params.queueId}`,
    params.payload
  );

  return data;
}
