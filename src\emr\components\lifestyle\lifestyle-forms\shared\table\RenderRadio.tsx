import React, { FC } from 'react';

import {
  Control,
  Controller,
  Path,
  useFormContext,
  useWatch,
} from 'react-hook-form';

import { Radio, RadioProps, styled } from '@mui/material';

import { LifestyleQuestion, SubQuestions } from '@/emr/types/lifestyle';

const StyledRadio = styled(Radio)(({ theme }) => ({
  transform: 'scale(0.75)',
  color: 'black !important',
  '&.Mui-checked': {
    color: 'black !important',
    fontWeight: 'bold',
    '& .MuiSvgIcon-root': {
      fontWeight: 'bold',
      strokeWidth: '2px',
      filter: 'brightness(0.8) contrast(1.2)',
    },
  },
  '&.Mui-disabled': {
    color: 'black !important',
    opacity: 1,
  },
  '&.Mui-disabled.Mui-checked': {
    color: 'black !important',
    opacity: 1,
    fontWeight: 'bold',
    '& .MuiSvgIcon-root': {
      fontWeight: 'bold',
      strokeWidth: '2px',
      filter: 'brightness(0.8) contrast(1.2)',
    },
  },
  '& .MuiSvgIcon-root': {
    color: 'black !important',
  },
  '&.Mui-disabled .MuiSvgIcon-root': {
    color: 'black !important',
    opacity: 1,
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: 14,
    paddingTop: 0.5,
    paddingBottom: 0.5,
  },
}));

type ControlledRadioProps = RadioProps & {
  control: Control<LifestyleQuestion>;
  name: Path<LifestyleQuestion>;
};

const ControlledRadio = ({ control, name, ...rest }: ControlledRadioProps) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { onChange, ...field } }) => (
        <StyledRadio
          onChange={() => onChange(rest.value)}
          {...field}
          {...rest}
        />
      )}
    />
  );
};

type Props = {
  isReadOnly?: boolean;
  name: Path<LifestyleQuestion>;
  isMaximized?: boolean;
  values: SubQuestions[];
};

const RenderRadio: FC<Props> = ({ isReadOnly, name, isMaximized, values }) => {
  const { control } = useFormContext<LifestyleQuestion>();
  const row = useWatch({ control, name });

  return (
    <div
      className={`flex flex-nowrap items-center ${
        !isMaximized ? 'bg-white rounded-md md:bg-transparent' : ''
      }`}
    >
      {' '}
      {values?.map((q: SubQuestions) => (
        <ControlledRadio
          key={q.value}
          control={control}
          size="small"
          name={`${name}.value` as Path<LifestyleQuestion>}
          disabled={isReadOnly}
          value={q?.value}
          checked={q?.value === row?.value}
        />
      ))}
    </div>
  );
};

export default RenderRadio;
