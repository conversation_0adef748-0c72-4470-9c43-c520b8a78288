'use client';

import React, { memo, useEffect, useState } from 'react';

import { DatePicker } from '@/lib/common/date_picker';
import { Pagination } from '@/lib/Pagination';

import SearchInput from '@/components/common/SearchInput';

import TableV2 from '@/core/components/table-v2';

interface CurrentAppointment {
  id: string;
  patientId: string;
  patientName: string;
  appointmentTime?: string;
  appointmentDate?: string;
  status: string;
  department: string;
}

interface CurrentAppointmentsProps {
  title?: string;
  appointments: CurrentAppointment[];
  isLoading: boolean;
  totalItems: number;
  totalPages: number;
  currentPage: number;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  showDateColumn?: boolean; // true for MRD (shows date & time), false for EMR (shows only time)
  showDoctorSelect?: boolean; // true for MRD, false for EMR
  showDatePicker?: boolean; // true for MRD, false for EMR
  doctorSelectComponent?: React.ReactNode; // Doctor select component for MRD
  selectedDate?: Date | null; // Selected date for date picker
  onDateChange?: (date: Date | null) => void; // Date change handler
}

const CurrentAppointments: React.FC<CurrentAppointmentsProps> = ({
  title = 'Current Appointments',
  appointments,
  isLoading,
  totalItems,
  totalPages,
  currentPage,
  searchQuery,
  onSearchChange,
  onPageChange,
  onPageSizeChange,
  showDateColumn = true,
  showDoctorSelect = false,
  showDatePicker = false,
  doctorSelectComponent,
  selectedDate,
  onDateChange,
}) => {
  const [searchInput, setSearchInput] = useState(searchQuery);

  // Helper functions
  const formatStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Table configuration
  const tableHeaders = [
    {
      key: 'patient',
      header: 'Patient',
    },
    {
      key: showDateColumn ? 'dateTime' : 'time',
      header: showDateColumn ? 'Date & Time' : 'Time',
    },
    {
      key: 'status',
      header: 'Status',
    },
  ];

  const tableRows = appointments.map((appointment) => ({
    patient: {
      value: (
        <div>
          <div
            style={{
              fontSize: '14px',
              fontWeight: 400,
              color: '#000000',
            }}
          >
            {appointment.patientName}
          </div>
        </div>
      ),
    },
    [showDateColumn ? 'dateTime' : 'time']: {
      value: showDateColumn ? (
        <div>
          <div
            style={{
              fontSize: '14px',
              fontWeight: 400,
              color: '#000000',
            }}
          >
            {appointment.appointmentDate
              ? new Date(appointment.appointmentDate).toLocaleDateString(
                  'en-US',
                  {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                  }
                )
              : '---'}
          </div>
          <div
            style={{
              fontSize: '12px',
              fontWeight: 400,
              color: '#64707D',
            }}
          >
            {appointment.appointmentTime || '---'}
          </div>
        </div>
      ) : (
        <div
          style={{
            fontSize: '14px',
            fontWeight: 400,
            color: '#000000',
          }}
        >
          {appointment.appointmentTime || '---'}
        </div>
      ),
    },
    status: {
      value: (
        <span
          style={{
            fontSize: '14px',
            fontWeight: 400,
            color: '#000000',
          }}
        >
          {formatStatus(appointment.status)}
        </span>
      ),
    },
  }));

  // Handle search input with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onSearchChange(searchInput);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchInput, onSearchChange]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  return (
    <div
      className="bg-white px-4 rounded-lg border border-[#DAE1E7] h-full flex flex-col current-appointments-container"
      style={{ borderRadius: '8px', minHeight: '250px' }}
    >
      {/* Header */}
      <div className="py-2">
        {/* Header Row - Title on left, Doctor Select and Date Picker on right */}
        <div
          className={`flex items-center justify-between mb-3 ${showDoctorSelect || showDatePicker ? 'border-b py-2' : ''}`}
        >
          <h3
            className="font-medium"
            style={{
              fontSize: '16px',
              fontWeight: 500,
              color: '#000000',
            }}
          >
            {title}
          </h3>

          {/* Doctor Select and Date Picker on right side */}
          {(showDoctorSelect || showDatePicker) && (
            <div className="flex items-center gap-3">
              {showDoctorSelect && doctorSelectComponent && (
                <div>{doctorSelectComponent}</div>
              )}
              {showDatePicker && (
                <div>
                  <DatePicker
                    value={selectedDate || undefined}
                    onChange={(newDate) => {
                      onDateChange?.(newDate || null);
                    }}
                    className="!py-2 !border-[#64707D]"
                  />
                </div>
              )}
            </div>
          )}
        </div>

        {/* Search Bar - Full width on next line */}
        <SearchInput
          value={searchInput}
          onChange={handleSearchChange}
          placeholder="Search by name, patient id"
        />
      </div>

      {/* Table Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex-1 overflow-auto">
          <TableV2
            headers={tableHeaders}
            rows={tableRows}
            loading={isLoading}
            noDataMessage="No appointments found"
            tableContainerProps={{
              sx: {
                '& .MuiTableContainer-root': {
                  boxShadow: 'none',
                  border: 'none',
                },
                '& .MuiTable-root': {
                  borderCollapse: 'separate',
                  borderSpacing: 0,
                  border: 'none',
                  '& .MuiTableHead-root': {
                    '& .MuiTableRow-root': {
                      '& .MuiTableCell-root': {
                        borderBottom: '1px solid #E5E7EB',
                        backgroundColor: 'transparent',
                        borderLeft: 'none',
                        borderRight: 'none',
                        borderTop: 'none',
                        fontSize: '14px',
                        fontWeight: 500,
                        color: '#64707D',
                        padding: '5px 16px',
                        textAlign: 'center',
                      },
                    },
                  },
                  '& .MuiTableBody-root': {
                    '& .MuiTableRow-root': {
                      backgroundColor: 'white !important',
                      '&:hover': {
                        backgroundColor: 'white !important',
                      },
                      '&:nth-of-type(even)': {
                        backgroundColor: 'white !important',
                      },
                      '& .MuiTableCell-root': {
                        borderBottom: '1px solid #DAE1E7',
                        borderLeft: 'none',
                        borderRight: 'none',
                        borderTop: 'none',
                        backgroundColor: 'white !important',
                      },
                      '&:last-child': {
                        '& .MuiTableCell-root': {
                          borderBottom: 'none',
                        },
                      },
                    },
                  },
                },
              },
            }}
          />
        </div>

        {/* Pagination */}
        {totalItems > 0 && (
          <div className="border-t border-gray-100 px-4 py-2">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              pageSize={10}
              onPageChange={onPageChange}
              onPageSizeChange={onPageSizeChange}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(CurrentAppointments);
