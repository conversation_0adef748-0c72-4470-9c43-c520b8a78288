import { arcaAxios } from '@/core/lib/interceptor';
import { Patient } from '@/types/mrd/manage-patient/patient';

export const getPatientById = async (id: string): Promise<Patient> => {
  const { data } = await arcaAxios.get(`/patient?id=${id}`);
  return data;
};

export const createPatient = async (data: Patient) => {
  return await arcaAxios.post('/patient', data);
};

export const updatePatient = async (data: Patient) => {
  return await arcaAxios.put(`/patient`, data);
};
