import React from 'react';

import { Control, Path } from 'react-hook-form';

import { default as ControlledTextField } from '@/components/controlled-inputs/ControlledTextField';

import { MedicalHistoryAddictionForm } from '@/types/emr/lifestyle/medical-history-addiction';

type DiagnosisFieldPath<T extends string> = `diagnosis.${number}.${T}`;

export type StyledTextFieldProps = Omit<
  React.ComponentProps<typeof ControlledTextField>,
  'control' | 'name'
> & {
  sx?: Record<string, unknown>;
  control: Control<MedicalHistoryAddictionForm>;
  name:
    | Path<MedicalHistoryAddictionForm>
    | DiagnosisFieldPath<'diseaseName'>
    | DiagnosisFieldPath<'diagnosisDuration'>
    | DiagnosisFieldPath<'treatmentHistory'>;
};

const StyledTextField: React.FC<StyledTextFieldProps> = (props) => (
  <ControlledTextField
    {...props}
    inputProps={{
      style: {
        fontSize: '14px',
        fontWeight: 400,
        height: '36px',
        padding: '8px 12px',
      },
    }}
    InputLabelProps={{
      style: {
        fontSize: '14px',
      },
    }}
    sx={{
      '& .MuiInputBase-root': {
        height: '36px',
      },
      '& .MuiInputLabel-shrink': {
        transform: 'translate(14px, -9px) scale(0.75)',
      },
      '& .MuiInputLabel-root': {
        transform: 'translate(14px, 10px) scale(1)',
      },
      ...props.sx,
    }}
  />
);

export default StyledTextField;
