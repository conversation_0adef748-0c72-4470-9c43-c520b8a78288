import React from 'react';
import type { SVGProps } from 'react';

const TranscriptViewIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="12"
      viewBox="0 0 14 12"
      fill="none"
      {...props}
    >
      <path
        d="M9.75962 4.1679C9.59899 2.96123 8.98599 1.86062 8.04466 1.08877C7.10333 0.316914 5.90395 -0.0685572 4.68919 0.0103479C3.47444 0.0892531 2.335 0.626643 1.50143 1.51379C0.667863 2.40093 0.2024 3.57159 0.199219 4.7889C0.199219 5.9241 0.593419 6.9675 1.25282 7.7895C1.73762 8.3949 1.99922 9.0639 1.99922 9.7725V11.9889H7.39922L7.39982 10.1889H8.59922C8.91748 10.1889 9.2227 10.0625 9.44775 9.83743C9.67279 9.61239 9.79922 9.30716 9.79922 8.9889V7.2315L10.9752 6.7275C11.1804 6.6399 11.1984 6.4311 11.1096 6.2913L9.75962 4.1679ZM1.39922 4.7889C1.3983 3.87404 1.74572 2.99314 2.3709 2.32521C2.99608 1.65727 3.85211 1.25242 4.76504 1.1929C5.67797 1.13339 6.57931 1.42369 7.28591 2.0048C7.99251 2.58592 8.45136 3.41426 8.56922 4.3215L8.60342 4.5867L9.52922 6.0417L8.59922 6.4401V8.9889H6.20042L6.19922 10.7889H3.19922V9.7725C3.19922 8.7891 2.84462 7.8573 2.18822 7.0389C1.676 6.40101 1.39756 5.607 1.39922 4.7889ZM12.2916 9.6501L11.2926 8.9841C11.8851 8.0977 12.2007 7.05512 12.1992 5.9889C12.201 4.92255 11.8857 3.87975 11.2932 2.9931L12.2916 2.3271C13.0159 3.41081 13.4014 4.68546 13.3992 5.9889C13.3992 7.3437 12.9912 8.6025 12.2916 9.6501Z"
        fill="black"
      />
    </svg>
  );
};

export default TranscriptViewIcon;
