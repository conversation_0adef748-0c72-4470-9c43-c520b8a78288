import React, { FC, memo, useCallback, useEffect, useState } from 'react';

import { LuCalendar } from 'react-icons/lu';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useLifestyleStore } from '@/store/lifestyle';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { NutritionAndDietForm } from '@/constants/lifestyle';

import { LifestyleSource, lifestyleSource } from '@/emr/types/lifestyle';

import DateRangePicker from '@/core/components/date-range-picker';
import { DateRange } from '@/core/components/date-range-picker/types';

type Props = {
  title: string;
  variant?: 'small' | 'medium';
  showCalender?: boolean;
  currentTab?: string;
};

const { DATE_DD_MM_YYYY_SLASH, STANDARD_DATE } = DateFormats;

const tabTitleToSourceMap: Record<string, LifestyleSource> = {
  [NutritionAndDietForm.DIETARY_ASSESSMENT]:
    lifestyleSource.DIETARY_ASSESSMENT_SOURCE,
  [NutritionAndDietForm.LIFESTYLE_AWARENESS_ADAPTATION]:
    lifestyleSource.AWARENESS_ADAPTATION_SOURCE,
  [NutritionAndDietForm.NUTRITION_MONITORING_SHEET]:
    lifestyleSource.NUTRITION_MONITORING_SHEET_SOURCE,
};

const MobilePageTitle: FC<Props> = ({
  title,
  variant = 'medium',
  showCalender = false,
  currentTab,
}) => {
  const { patient } = useCurrentPatientStore();
  const { getPatientLifestyle } = useLifestyleStore();

  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });

  const fetchByDate = useCallback(
    async (range: DateRange) => {
      const source = currentTab ? tabTitleToSourceMap[currentTab] : undefined;

      if (patient?.id) {
        await getPatientLifestyle(
          patient.id,
          source as LifestyleSource,
          range.from ? formatDate(range.from as string, STANDARD_DATE) : '',
          range.to ? formatDate(range.to as string, STANDARD_DATE) : ''
        );
      }
    },
    [patient?.id, getPatientLifestyle, currentTab]
  );

  useEffect(() => {
    const hasBothDates = dateRange?.from && dateRange?.to;
    const isCleared = !dateRange?.from && !dateRange?.to;

    const shouldFetch = (hasBothDates || isCleared) && !isCalendarOpen;

    if (shouldFetch) {
      fetchByDate(dateRange);
    }
  }, [dateRange, isCalendarOpen, fetchByDate]);

  const handleDateChange = (range: DateRange) => {
    setDateRange(range);
  };

  const hasSelectedDates = Boolean(dateRange?.from && dateRange?.to);

  const handleClearDates = useCallback(() => {
    setDateRange({ from: undefined, to: undefined });
    setIsCalendarOpen?.(false);
  }, [setIsCalendarOpen]);

  const renderFooter = useCallback(() => {
    if (hasSelectedDates) {
      return (
        <div className="w-full py-2 px-4 flex justify-end">
          <button
            className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded"
            onClick={handleClearDates}
          >
            Clear
          </button>
        </div>
      );
    }
    return null;
  }, [hasSelectedDates, handleClearDates]);

  return (
    <div className="py-1 flex items-center justify-between border-b min-h-8 ">
      <span
        className={`font-semibold ${variant === 'small' ? 'text-[17px]' : 'text-[20px]'}`}
      >
        {title}
      </span>
      {showCalender && (
        <div className={hasSelectedDates ? '' : 'w-0'}>
          <DateRangePicker
            value={dateRange}
            onChange={handleDateChange}
            setIsCalendarOpen={setIsCalendarOpen}
            calendarIcon={<LuCalendar size={17} color="black" />}
            format={DATE_DD_MM_YYYY_SLASH}
            separator="to"
            inputWrapperClassName={hasSelectedDates ? 'min-w-38' : 'min-w-0'}
            inputProps={{
              iconClassName: '!right-0 bg-white',
              inputClassName: 'h-full input:bg-transparent m-0',
              fieldClassName: '!text-[10px] rounded-none m-0 !px-1 h-[10px]',
              style: { outline: 'none', border: 'none' },
            }}
            renderFooter={renderFooter}
            isCalendarOpen={isCalendarOpen}
          />
        </div>
      )}
    </div>
  );
};

export default memo(MobilePageTitle);
