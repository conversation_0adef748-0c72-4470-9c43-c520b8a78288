'use client';

import { FC, ReactNode, useEffect } from 'react';

import { MsalProvider, useIsAuthenticated } from '@azure/msal-react';

import {
  getActiveAccount,
  setFirstAccountAsActive,
} from '@core/lib/auth/services';

import { msalInstance } from '@/core/lib/auth/msal';

export type AuthProviderProps = {
  children: ReactNode;
};

const AuthProvider: FC<AuthProviderProps> = function ({ children }) {
  const isAuthenticated = useIsAuthenticated();

  useEffect(() => {
    if (isAuthenticated && !getActiveAccount()) {
      setFirstAccountAsActive();
    }
  }, [isAuthenticated]);

  return <MsalProvider instance={msalInstance}>{children}</MsalProvider>;
};

export default AuthProvider;
