import React from 'react';

import Loading from '@/lib/common/loading';

import { usePrescriptionPackageStore } from '@/store/emr/prescription/package';

import PackageItem from './PackageItem';

export const PackageList = () => {
  const { isPackagesLoading, packages, selectedPackage, setSelectedPackage } =
    usePrescriptionPackageStore();

  if (isPackagesLoading) {
    return (
      <div className="w-1/4 border-r p-4 flex justify-center items-center">
        <Loading />
      </div>
    );
  }

  return (
    <div className="w-1/4 border-r p-4 overflow-auto">
      {packages.length === 0 ? (
        <p className="text-gray-500 text-sm">No packages found</p>
      ) : (
        <div>
          {packages.map((pkg) => (
            <PackageItem
              key={pkg.id}
              label={pkg.name}
              onClick={() => setSelectedPackage(pkg)}
              isActive={selectedPackage?.id === pkg.id}
            />
          ))}
        </div>
      )}
    </div>
  );
};
