import React, {
  Dispatch,
  FC,
  memo,
  RefObject,
  SetStateAction,
  useCallback,
} from 'react';

import {
  Control,
  FieldArrayWithId,
  FieldErrors,
  Path,
  UseFormGetValues,
  UseFormRegister,
  useWatch,
} from 'react-hook-form';

import { Stack } from '@mui/material';
import dayjs from 'dayjs';
import { isEqual } from 'lodash';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { INSURANCE_FIELD_REGEX } from '@/utils/emr/doctor-profile/personal-info';
import {
  allowAlphanumericInput,
  preventNonAlphabeticInput,
} from '@/utils/validation';

import PencilIcon from '@/assets/svg/PencilIcon';

import DatePicker from '@/core/components/date-picker';
import {
  defaultInsurance,
  Insurance,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import AutoResizeTextArea from '../shared/AutoResizeTextArea';
import MobileAddDeleteButtons from '../shared/MobileAddDeleteButtons';

import { FormData } from '.';

type Props = {
  fields: FieldArrayWithId<FormData, 'insurance', 'id'>[];
  control: Control<FormData>;
  register: UseFormRegister<FormData>;
  editableField: Set<string>;
  getValues: UseFormGetValues<FormData>;
  isSubmitted: boolean;
  setEditableField: Dispatch<SetStateAction<Set<string>>>;
  handleOnDelete: (_itemToDelete: ItemToDelete) => void;
  handleOnAdd?: () => void;
  lastFieldRef?: RefObject<HTMLDivElement>;
  errors?: FieldErrors<FormData>;
};

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const InsuranceMob: FC<Props> = ({
  fields,
  editableField,
  getValues,
  isSubmitted,
  setEditableField,
  control,
  handleOnDelete,
}) => {
  const { doctorProfile } = useDoctorStore();
  const watchInsurance = useWatch({
    control,
    name: 'insurance',
  });

  const handleEditClick = useCallback(
    (fieldName: Path<FormData>) => {
      setEditableField((prev) => new Set(prev.add(fieldName)));
    },
    [setEditableField]
  );

  const isFieldDisabled = useCallback(
    (fieldName: Path<FormData>) => {
      if (editableField.has(fieldName)) {
        return false;
      }

      const formValues = getValues();
      const fieldValue = formValues[fieldName as keyof FormData];

      const match = fieldName.match(INSURANCE_FIELD_REGEX);
      let doctorFieldValue;

      if (match) {
        const index = Number(match[1]);
        const key = match[2];
        doctorFieldValue =
          doctorProfile?.insurance?.[index]?.[key as keyof Insurance];
      }

      return (
        !!doctorFieldValue ||
        (isSubmitted &&
          !!fieldValue &&
          JSON.stringify(fieldValue) === JSON.stringify(doctorFieldValue))
      );
    },
    [editableField, getValues, doctorProfile?.insurance, isSubmitted]
  );

  const renderEditIcon = useCallback(
    (fieldName: Path<FormData>) => {
      return isFieldDisabled(fieldName) ? (
        <button type="button" onClick={() => handleEditClick(fieldName)}>
          <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
        </button>
      ) : null;
    },
    [isFieldDisabled, handleEditClick]
  );

  return (
    <>
      {fields?.map((field, index) => {
        return (
          <Stack key={field.id} spacing={2}>
            <AutoResizeTextArea
              label="Policy"
              placeholder="Policy Name"
              color="white"
              control={control}
              name={`insurance.${index}.policyName`}
              onKeyDown={preventNonAlphabeticInput}
              endDecoration={renderEditIcon(`insurance.${index}.policyName`)}
              disabled={isFieldDisabled(`insurance.${index}.policyName`)}
              className="w-full"
            />
            <AutoResizeTextArea
              label="Policy Number"
              placeholder="Policy Number"
              color="white"
              control={control}
              name={`insurance.${index}.policyNumber`}
              onKeyDown={allowAlphanumericInput}
              endDecoration={renderEditIcon(`insurance.${index}.policyNumber`)}
              disabled={isFieldDisabled(`insurance.${index}.policyNumber`)}
              className="w-full"
            />
            <div className="w-full flex gap-2">
              <DatePicker
                label="From"
                format={DATE_DD_MM_YYYY_SLASH}
                name={`insurance.${index}.validFrom`}
                control={control}
                disableInput={isFieldDisabled(`insurance.${index}.validFrom`)}
                renderEditIcon={() =>
                  renderEditIcon(`insurance.${index}.validFrom`)
                }
                maxDate={
                  watchInsurance?.[index]?.validTo
                    ? dayjs(watchInsurance[index].validTo)
                    : undefined
                }
              />
              <DatePicker
                label="To"
                format={DATE_DD_MM_YYYY_SLASH}
                name={`insurance.${index}.validTo`}
                control={control}
                disableInput={isFieldDisabled(`insurance.${index}.validTo`)}
                renderEditIcon={() =>
                  renderEditIcon(`insurance.${index}.validTo`)
                }
                minDate={
                  watchInsurance?.[index]?.validFrom
                    ? dayjs(watchInsurance[index].validFrom)
                    : undefined
                }
                rules={{
                  validate: (toValue) => {
                    const fromValue = watchInsurance?.[index]?.validFrom;
                    if (
                      fromValue &&
                      toValue &&
                      dayjs(toValue).isBefore(dayjs(fromValue))
                    ) {
                      return 'Enter a valid date';
                    }
                    return true;
                  },
                }}
              />
            </div>
            <AutoResizeTextArea
              label="Status"
              placeholder=""
              color="white"
              control={control}
              name={`insurance.${index}.status`}
              onKeyDown={preventNonAlphabeticInput}
              endDecoration={renderEditIcon(`insurance.${index}.status`)}
              disabled={isFieldDisabled(`insurance.${index}.status`)}
              className="w-full"
            />

            <MobileAddDeleteButtons
              onDelete={
                isEqual(watchInsurance[index], defaultInsurance)
                  ? undefined
                  : () => handleOnDelete({ index, uuId: field?.uuId })
              }
            />
          </Stack>
        );
      })}
    </>
  );
};

export default memo(InsuranceMob);
