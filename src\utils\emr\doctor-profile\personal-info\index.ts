import dayjs, { Dayjs } from 'dayjs';
import _ from 'lodash';

export const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((acc, key) => acc?.[key], obj);
};

export const FAMILY_FIELD_REGEX = /^family\.(\d+)\.(.+)$/;
export const INSURANCE_FIELD_REGEX = /^insurance\.(\d+)\.(.+)$/;

export const scrollToElementWithOffset = (
  element: HTMLElement | null,
  offsetRatio = 0.5,
  containerClass = '.overflow-auto'
) => {
  if (!element) return;

  const scrollableContainer = element.closest(containerClass);
  if (scrollableContainer) {
    const elementTop = element.offsetTop;
    const viewportHeight = window.innerHeight;
    const dynamicOffset = viewportHeight * offsetRatio;

    (scrollableContainer as HTMLElement).scrollTop = Math.max(
      0,
      elementTop - dynamicOffset
    );
  }
};

export const getMinMaxDate = (date?: string): Dayjs | undefined => {
  if (!date) return undefined;

  const parsedDate = dayjs(date);
  return parsedDate.isValid() ? parsedDate : undefined;
};

export const getUniqueId = (id?: string, prefix = 'FAM'): string => {
  if (id) return id;
  const now = new Date();
  const dateString = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getMilliseconds()).padStart(3, '0')}`;
  return `${prefix}${dateString}`;
};

export const discardDefaultValues = <T>(data: T[], defaultData: T) => {
  return _.reject(data, (item) => _.isEqual(item, defaultData));
};
