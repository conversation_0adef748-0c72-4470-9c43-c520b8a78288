type Option = {
  label: string;
  value: string;
};

export const nationalities: Option[] = [
  { label: 'Afghan', value: 'Afghan' },
  { label: 'Albanian', value: 'Albanian' },
  { label: 'Algerian', value: 'Algerian' },
  { label: 'Argentine/Argentinian', value: 'Argentine/Argentinian' },
  { label: 'Australian', value: 'Australian' },
  { label: 'Austrian', value: 'Austrian' },
  { label: 'Bangladeshi', value: 'Bangladeshi' },
  { label: 'Belgian', value: 'Belgian' },
  { label: 'Bolivian', value: 'Bolivian' },
  { label: 'Batswana', value: 'Batswana' },
  { label: 'Brazilian', value: 'Brazilian' },
  { label: 'Bulgarian', value: 'Bulgarian' },
  { label: 'Cambodian', value: 'Cambodian' },
  { label: 'Cameroonian', value: 'Cameroonian' },
  { label: 'Canadian', value: 'Canadian' },
  { label: 'Chilean', value: 'Chilean' },
  { label: 'Chinese', value: 'Chinese' },
  { label: 'Colombian', value: 'Colombian' },
  { label: 'Costa Rican', value: 'Costa Rican' },
  { label: 'Croatian', value: 'Croatian' },
  { label: 'Cuban', value: 'Cuban' },
  { label: 'Czech', value: 'Czech' },
  { label: 'Danish', value: 'Danish' },
  { label: 'Dominican', value: 'Dominican' },
  { label: 'Ecuadorian', value: 'Ecuadorian' },
  { label: 'Egyptian', value: 'Egyptian' },
  { label: 'Salvadorian', value: 'Salvadorian' },
  { label: 'English', value: 'English' },
  { label: 'Estonian', value: 'Estonian' },
  { label: 'Ethiopian', value: 'Ethiopian' },
  { label: 'Fijian', value: 'Fijian' },
  { label: 'Finnish', value: 'Finnish' },
  { label: 'French', value: 'French' },
  { label: 'German', value: 'German' },
  { label: 'Ghanaian', value: 'Ghanaian' },
  { label: 'Greek', value: 'Greek' },
  { label: 'Guatemalan', value: 'Guatemalan' },
  { label: 'Haitian', value: 'Haitian' },
  { label: 'Honduran', value: 'Honduran' },
  { label: 'Hungarian', value: 'Hungarian' },
  { label: 'Icelandic', value: 'Icelandic' },
  { label: 'Indian', value: 'Indian' },
  { label: 'Indonesian', value: 'Indonesian' },
  { label: 'Iranian', value: 'Iranian' },
  { label: 'Iraqi', value: 'Iraqi' },
  { label: 'Irish', value: 'Irish' },
  { label: 'Israeli', value: 'Israeli' },
  { label: 'Italian', value: 'Italian' },
  { label: 'Jamaican', value: 'Jamaican' },
  { label: 'Japanese', value: 'Japanese' },
  { label: 'Jordanian', value: 'Jordanian' },
  { label: 'Kenyan', value: 'Kenyan' },
  { label: 'Kuwaiti', value: 'Kuwaiti' },
  { label: 'Lao', value: 'Lao' },
  { label: 'Latvian', value: 'Latvian' },
  { label: 'Lebanese', value: 'Lebanese' },
  { label: 'Libyan', value: 'Libyan' },
  { label: 'Lithuanian', value: 'Lithuanian' },
  { label: 'Malagasy', value: 'Malagasy' },
  { label: 'Malaysian', value: 'Malaysian' },
  { label: 'Malian', value: 'Malian' },
  { label: 'Maltese', value: 'Maltese' },
  { label: 'Mexican', value: 'Mexican' },
  { label: 'Mongolian', value: 'Mongolian' },
  { label: 'Moroccan', value: 'Moroccan' },
  { label: 'Mozambican', value: 'Mozambican' },
  { label: 'Namibian', value: 'Namibian' },
  { label: 'Nepalese', value: 'Nepalese' },
  { label: 'Dutch', value: 'Dutch' },
  { label: 'New Zealand', value: 'New Zealand' },
  { label: 'Nicaraguan', value: 'Nicaraguan' },
  { label: 'Nigerian', value: 'Nigerian' },
  { label: 'Norwegian', value: 'Norwegian' },
  { label: 'Pakistani', value: 'Pakistani' },
  { label: 'Panamanian', value: 'Panamanian' },
  { label: 'Paraguayan', value: 'Paraguayan' },
  { label: 'Peruvian', value: 'Peruvian' },
  { label: 'Philippine', value: 'Philippine' },
  { label: 'Polish', value: 'Polish' },
  { label: 'Portuguese', value: 'Portuguese' },
  { label: 'Romanian', value: 'Romanian' },
  { label: 'Russian', value: 'Russian' },
  { label: 'Saudi', value: 'Saudi' },
  { label: 'Scottish', value: 'Scottish' },
  { label: 'Senegalese', value: 'Senegalese' },
  { label: 'Serbian', value: 'Serbian' },
  { label: 'Singaporean', value: 'Singaporean' },
  { label: 'Slovak', value: 'Slovak' },
  { label: 'South African', value: 'South African' },
  { label: 'Korean', value: 'Korean' },
  { label: 'Spanish', value: 'Spanish' },
  { label: 'Sri Lankan', value: 'Sri Lankan' },
  { label: 'Sudanese', value: 'Sudanese' },
  { label: 'Swedish', value: 'Swedish' },
  { label: 'Swiss', value: 'Swiss' },
  { label: 'Syrian', value: 'Syrian' },
  { label: 'Taiwanese', value: 'Taiwanese' },
  { label: 'Tajikistani', value: 'Tajikistani' },
  { label: 'Thai', value: 'Thai' },
  { label: 'Tongan', value: 'Tongan' },
  { label: 'Tunisian', value: 'Tunisian' },
  { label: 'Turkish', value: 'Turkish' },
  { label: 'Ukrainian', value: 'Ukrainian' },
  { label: 'Emirati', value: 'Emirati' },
  { label: 'British', value: 'British' },
  { label: 'American *', value: 'American *' },
  { label: 'Uruguayan', value: 'Uruguayan' },
  { label: 'Venezuelan', value: 'Venezuelan' },
  { label: 'Vietnamese', value: 'Vietnamese' },
  { label: 'Welsh', value: 'Welsh' },
  { label: 'Zambian', value: 'Zambian' },
  { label: 'Zimbabwean', value: 'Zimbabwean' },
];

export const states = [
  {
    label: 'Andhra Pradesh',
    value: 'Andhra Pradesh',
  },
  {
    label: 'Arunachal Pradesh',
    value: 'Arunachal Pradesh',
  },
  {
    label: 'Assam',
    value: 'Assam',
  },
  {
    label: 'Bihar',
    value: 'Bihar',
  },
  {
    label: 'Chhattisgarh',
    value: 'Chhattisgarh',
  },
  {
    label: 'Goa',
    value: 'Goa',
  },
  {
    label: 'Gujarat',
    value: 'Gujarat',
  },
  {
    label: 'Haryana',
    value: 'Haryana',
  },
  {
    label: 'Himachal Pradesh',
    value: 'Himachal Pradesh',
  },
  {
    label: 'Jharkhand',
    value: 'Jharkhand',
  },
  {
    label: 'Karnataka',
    value: 'Karnataka',
  },
  {
    label: 'Kerala',
    value: 'Kerala',
  },
  {
    label: 'Madhya Pradesh',
    value: 'Madhya Pradesh',
  },
  {
    label: 'Maharashtra',
    value: 'Maharashtra',
  },
  {
    label: 'Manipur',
    value: 'Manipur',
  },
  {
    label: 'Meghalaya',
    value: 'Meghalaya',
  },
  {
    label: 'Mizoram',
    value: 'Mizoram',
  },
  {
    label: 'Nagaland',
    value: 'Nagaland',
  },
  {
    label: 'Odisha',
    value: 'Odisha',
  },
  {
    label: 'Punjab',
    value: 'Punjab',
  },
  {
    label: 'Rajasthan',
    value: 'Rajasthan',
  },
  {
    label: 'Sikkim',
    value: 'Sikkim',
  },
  {
    label: 'Tamil Nadu',
    value: 'Tamil Nadu',
  },
  {
    label: 'Telangana',
    value: 'Telangana',
  },
  {
    label: 'Tripura',
    value: 'Tripura',
  },
  {
    label: 'Uttar Pradesh',
    value: 'Uttar Pradesh',
  },
  {
    label: 'Uttarakhand',
    value: 'Uttarakhand',
  },
  {
    label: 'West Bengal',
    value: 'West Bengal',
  },
  {
    label: 'Andaman and Nicobar Islands',
    value: 'Andaman and Nicobar Islands',
  },
  {
    label: 'Chandigarh',
    value: 'Chandigarh',
  },
  {
    label: 'Dadra and Nagar Haveli and Daman and Diu',
    value: 'Dadra and Nagar Haveli and Daman and Diu',
  },
  {
    label: 'Jammu and Kashmir',
    value: 'Jammu and Kashmir',
  },
  {
    label: 'Ladakh',
    value: 'Ladakh',
  },
  {
    label: 'Lakshadweep',
    value: 'Lakshadweep',
  },
  {
    label: 'Delhi',
    value: 'Delhi',
  },
  {
    label: 'Puducherry',
    value: 'Puducherry',
  },
];
export const countryOptions = [
  { value: 'India', label: 'India' },
  { value: 'United States', label: 'United States' },
  { value: 'China', label: 'China' },
  { value: 'Japan', label: 'Japan' },
  { value: 'Germany', label: 'Germany' },
  { value: 'United Kingdom', label: 'United Kingdom' },
  { value: 'France', label: 'France' },
  { value: 'Australia', label: 'Australia' },
  { value: 'Canada', label: 'Canada' },
  { value: 'South Korea', label: 'South Korea' },
];

export const cityOptions = [
  { value: 'Mumbai', label: 'Mumbai' },
  { value: 'Delhi', label: 'Delhi' },
  { value: 'Bengaluru', label: 'Bengaluru' },
  { value: 'Hyderabad', label: 'Hyderabad' },
  { value: 'Ahmedabad', label: 'Ahmedabad' },
  { value: 'Chennai', label: 'Chennai' },
  { value: 'Kolkata', label: 'Kolkata' },
  { value: 'Surat', label: 'Surat' },
  { value: 'Pune', label: 'Pune' },
  { value: 'Jaipur', label: 'Jaipur' },
];

export const languageOptions = [
  { key: 'english', value: 'English' },
  { key: 'hindi', value: 'Hindi' },
  { key: 'kannada', value: 'Kannada' },
  { key: 'malayalam', value: 'Malayalam' },
  { key: 'telugu', value: 'Telugu' },
  { key: 'tamil', value: 'Tamil' },
  { key: 'bengali', value: 'Bengali' },
];
