import React, { memo } from 'react';

import { cn } from '@/lib/utils';

import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import colors from '@/utils/colors';

import { mobileViews } from '@/constants/lifestyle';

const getBackgroundColor = (isActive: boolean) => {
  return isActive ? colors.common.azureBlue : colors.common.mediumGray;
};

const { NOW_CONSULTING, ADD_RECORD_MANUALLY, TIMELINE } = mobileViews;

const DetailsTimelineButtons = () => {
  const { setMobilePage, mobilePage, closeModal } = useLifestyleUtilStore();

  const handleClick = () => {
    setMobilePage(TIMELINE);
    closeModal();
  };

  const handlePatientClick = () => {
    setMobilePage(NOW_CONSULTING);
  };

  return (
    <div className="flex gap-2 text-white py-2">
      <button
        className={cn('p-2 flex-1 rounded-lg')}
        style={{
          backgroundColor: getBackgroundColor(
            mobilePage === NOW_CONSULTING || mobilePage === ADD_RECORD_MANUALLY
          ),
        }}
        onClick={handlePatientClick}
      >
        Patient Details
      </button>
      <button
        className={cn('p-2 flex-1 rounded-lg')}
        onClick={handleClick}
        style={{
          backgroundColor: getBackgroundColor(mobilePage === TIMELINE),
        }}
      >
        Timeline
      </button>
    </div>
  );
};

export default memo(DetailsTimelineButtons);
