import React from 'react';
import type { SVGProps } from 'react';

const TotalPatientsIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
  >
    <mask
      id="mask0_4535_30804"
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="25"
      height="24"
    >
      <rect x="0.5" width="24" height="24" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_4535_30804)">
      <path
        d="M12.5 10C11.4 10 10.4583 9.60833 9.675 8.825C8.89167 8.04167 8.5 7.1 8.5 6C8.5 4.9 8.89167 3.95833 9.675 3.175C10.4583 2.39167 11.4 2 12.5 2C13.6 2 14.5417 2.39167 15.325 3.175C16.1083 3.95833 16.5 4.9 16.5 6C16.5 7.1 16.1083 8.04167 15.325 8.825C14.5417 9.60833 13.6 10 12.5 10ZM12.5 8C13.05 8 13.5208 7.80417 13.9125 7.4125C14.3042 7.02083 14.5 6.55 14.5 6C14.5 5.45 14.3042 4.97917 13.9125 4.5875C13.5208 4.19583 13.05 4 12.5 4C11.95 4 11.4792 4.19583 11.0875 4.5875C10.6958 4.97917 10.5 5.45 10.5 6C10.5 6.55 10.6958 7.02083 11.0875 7.4125C11.4792 7.80417 11.95 8 12.5 8ZM4.5 22V15.225C4.5 14.6583 4.64167 14.1375 4.925 13.6625C5.20833 13.1875 5.6 12.8167 6.1 12.55C6.95 12.1167 7.9125 11.75 8.9875 11.45C10.0625 11.15 11.2333 11 12.5 11C13.7667 11 14.9375 11.15 16.0125 11.45C17.0875 11.75 18.05 12.1167 18.9 12.55C19.4 12.8167 19.7917 13.1875 20.075 13.6625C20.3583 14.1375 20.5 14.6583 20.5 15.225V20C20.5 20.55 20.3042 21.0208 19.9125 21.4125C19.5208 21.8042 19.05 22 18.5 22H10.25C9.48333 22 8.83333 21.7333 8.3 21.2C7.76667 20.6667 7.5 20.0167 7.5 19.25C7.5 18.4833 7.76667 17.8333 8.3 17.3C8.83333 16.7667 9.48333 16.5 10.25 16.5H13.075L14.625 13.2C14.2917 13.1333 13.95 13.0833 13.6 13.05C13.25 13.0167 12.8833 13 12.5 13C11.3 13 10.2333 13.1458 9.3 13.4375C8.36667 13.7292 7.60833 14.0333 7.025 14.35C6.85833 14.4333 6.72917 14.5542 6.6375 14.7125C6.54583 14.8708 6.5 15.0417 6.5 15.225V22H4.5ZM10.25 20H11.45L12.15 18.5H10.25C10.05 18.5 9.875 18.575 9.725 18.725C9.575 18.875 9.5 19.05 9.5 19.25C9.5 19.45 9.575 19.625 9.725 19.775C9.875 19.925 10.05 20 10.25 20ZM13.65 20H18.5V15.225C18.5 15.0417 18.4542 14.8708 18.3625 14.7125C18.2708 14.5542 18.15 14.4333 18 14.35C17.8 14.25 17.5833 14.1458 17.35 14.0375C17.1167 13.9292 16.8667 13.825 16.6 13.725L13.65 20Z"
        fill="#1C1B1F"
        fill-opacity="0.5"
      />
    </g>
  </svg>
);

export default TotalPatientsIcon;
