/**
 * Get CSS variable value with fallback
 */
export const getCSSVar = (
  variable: string,
  fallback: string = '0.5rem'
): string => {
  if (typeof window !== 'undefined') {
    const value = getComputedStyle(document.documentElement)
      .getPropertyValue(variable)
      .trim();
    return value || fallback;
  }
  return fallback;
};

export const getCSSVariableColor = (
  variable: string,
  fallbackHsl: [number, number, number]
): string => {
  if (typeof window !== 'undefined') {
    const cssValue = getComputedStyle(document.documentElement)
      .getPropertyValue(variable)
      .trim();
    if (cssValue) {
      return `hsl(${cssValue})`;
    }
  }
  return hslToRgb(fallbackHsl[0], fallbackHsl[1], fallbackHsl[2]);
};

/**
 * Color utilities and definitions for MUI theme
 * Based on Tailwind CSS variables from globals.css
 */
export const hslToRgb = (h: number, s: number, l: number): string => {
  s /= 100;
  l /= 100;

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
  const m = l - c / 2;
  let r = 0,
    g = 0,
    b = 0;

  if (0 <= h && h < 60) {
    r = c;
    g = x;
    b = 0;
  } else if (60 <= h && h < 120) {
    r = x;
    g = c;
    b = 0;
  } else if (120 <= h && h < 180) {
    r = 0;
    g = c;
    b = x;
  } else if (180 <= h && h < 240) {
    r = 0;
    g = x;
    b = c;
  } else if (240 <= h && h < 300) {
    r = x;
    g = 0;
    b = c;
  } else if (300 <= h && h < 360) {
    r = c;
    g = 0;
    b = x;
  }

  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  return `rgb(${r}, ${g}, ${b})`;
};
