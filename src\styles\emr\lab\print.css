/* Print-specific styles for PrintContent and AppSign positioning */

@media print {
  body {
    margin: 0;
    padding: 0;
    background: white;
    font-size: 12pt;
  }

  .print-content-print {
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    height: auto;
  }

  .print-content-print > div:first-child {
    page-break-inside: auto;
    page-break-after: auto;
    overflow: visible !important;
    height: auto !important;
  }

  .print-content-print table {
    page-break-inside: auto;
  }

  .print-content-print tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  /* Hide the no-print section during printing */
  .no-print {
    display: none !important;
  }

  /* AppSign appears on all print pages - centered at same level as page number */
  .order-history-print .print-app-sign {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: -5px;
    z-index: 1000;
  }

  .print-content-print {
    overflow: visible !important;
  }

  .order-history-print .print-footer {
    position: fixed;
    bottom: 10mm;
    left: 10mm;
    right: 10mm;
    background: white;
    z-index: 1000;
  }

  .order-history-print .print-footer .page-number {
    position: absolute;
    bottom: 0;
    right: 0;
    font-size: 12px;
    color: #666;
  }

  .order-history-print .print-footer .signature-container {
    position: absolute;
    bottom: 0;
    left: 0;
  }

  .order-history-print .print-footer .last-page-content {
    display: none;
  }

  @page {
    size: auto;
    margin: 10mm 10mm 25mm 10mm;
  }

  @page {
    @bottom-right {
      content: counter(page, decimal-leading-zero);
      font-size: 12px;
      color: #666;
      margin-right: 5mm;
      margin-bottom: 3mm;
    }
  }

  /* Show print-only footer during print */
  .order-history-print .print-only-last-page-footer {
    display: block;
    page-break-before: auto;
    page-break-inside: avoid;
    margin-top: 30px;
    padding: 0 20px;
    width: 100%;
  }

  .order-history-print .print-footer .last-page-content {
    width: 100%;
    position: relative;
  }

  .order-history-print table {
    page-break-inside: auto;
  }

  .order-history-print tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  .order-history-print .print-footer .last-page-content {
    page-break-before: avoid;
    page-break-inside: avoid;
  }

  .order-history-print .print-footer .last-page-content * {
    color: inherit !important;
    font-size: inherit !important;
  }

  .order-history-print .print-footer .last-page-content .border-t {
    border-top: 1px solid #e5e7eb !important;
  }

  @page :footer {
    display: none;
  }

  @page :header {
    display: none;
  }
}

@media screen {
  .print-content-print {
    max-height: calc(100vh - 100px);
    overflow-y: auto;
  }

  /* Hide print-only footer completely in screen view */
  .order-history-print .print-only-footer {
    display: none !important;
  }

  /* Hide print AppSign in screen view */
  .order-history-print .print-app-sign {
    display: none !important;
  }
}
