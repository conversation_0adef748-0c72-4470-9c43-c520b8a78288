import React from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import ImageUploader, {
  ImageUploadProps,
} from '@core/components/image-uploader';

type Props<T extends FieldValues> = Omit<
  ImageUploadProps,
  'value' | 'onChange'
> & {
  name: Path<T>;
  control: Control<T>;
};

const ControlledImageUploader = <T extends FieldValues>({
  name,
  control,
  ...props
}: Props<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <ImageUploader
          {...props}
          value={field.value}
          onChange={(file: File | null | string) => {
            field.onChange(file);
          }}
        />
      )}
    />
  );
};

export default ControlledImageUploader;
