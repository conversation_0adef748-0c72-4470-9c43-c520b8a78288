'use client';

import { useState, useCallback, useEffect, useRef } from 'react';

import { IconButton, Typography } from '@mui/material';
import { AiOutlineRight } from 'react-icons/ai';

import { useMobileMenuStore } from '@/store/mobileMenuStore';

import colors from '@/utils/colors';

import {
  PERSONAL_INFO_TAB_STORE_KEY,
  TabValues,
  tabValues,
} from '@/constants/emr/doctor-profile/personal-info';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import BankInfo from '../BankDetails';
import Certification from '../certification-tab';
import Documents from '../Documents';
import EmergencyDetailsSection from '../EmergencyDetails';
import EmploymentBackground from '../employment-bg-tab';
import Family from '../family-tab';
import GeneralSection from '../GeneralDetails';
import LanguagesKnownTab from '../languages-known-tab';
import MedicalInsuranceTab from '../medical-insurance-tab';
import { PersonalDetailsSection } from '../PersonalDetails';
import QualificationTab from '../qualification-tab';

const {
  GENERAL_DETAILS,
  BANK_DETAILS,
  CERTIFICATION,
  EMERGENCY_DETAILS,
  FAMILY_DETAILS,
  LANGUAGES_KNOWN,
  MEDICAL_INSURANCE,
  PERSONAL_DETAILS,
  QUALIFICATION,
  DOCUMENTS,
  EMPLOYMENT_BG,
} = tabValues;

const tabs = [
  {
    value: GENERAL_DETAILS,
    label: 'General Details',
    content: <GeneralSection />,
  },
  {
    value: PERSONAL_DETAILS,
    label: 'Personal Details',
    content: <PersonalDetailsSection />,
  },
  {
    value: EMERGENCY_DETAILS,
    label: 'Emergency Details',
    content: <EmergencyDetailsSection />,
  },
  { value: BANK_DETAILS, label: 'Bank Details', content: <BankInfo /> },
  { value: FAMILY_DETAILS, label: 'Family', content: <Family /> },
  {
    value: MEDICAL_INSURANCE,
    label: 'Medical Insurance',
    content: <MedicalInsuranceTab />,
  },
  { value: DOCUMENTS, label: 'Document', content: <Documents /> },
  {
    value: QUALIFICATION,
    label: 'Qualification',
    content: <QualificationTab />,
  },
  { value: CERTIFICATION, label: 'Certification', content: <Certification /> },
  {
    value: EMPLOYMENT_BG,
    label: 'Employment Background',
    content: <EmploymentBackground />,
  },
  {
    value: LANGUAGES_KNOWN,
    label: 'Languages Known',
    content: <LanguagesKnownTab />,
  },
];

export function ProfileFormMob() {
  const { isFromMenu } = useMobileMenuStore();

  const tabListRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<Record<string, HTMLButtonElement | null>>({});
  const contentContainerRef = useRef<HTMLDivElement>(null);
  const tabContentRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const [activeTab, setActiveTab] = useState<TabValues>(GENERAL_DETAILS);

  const onTabChange = useCallback((tab?: string) => {
    if (tab) {
      setActiveTab(tab as TabValues);
      localStorage.setItem(PERSONAL_INFO_TAB_STORE_KEY, tab);

      if (contentContainerRef.current) {
        contentContainerRef.current.scrollTop = 0;
      }

      requestAnimationFrame(() => {
        const tabContent = tabContentRefs.current[tab];
        if (tabContent) {
          tabContent.scrollTop = 0;
        }
      });

      // Scroll the selected tab into view
      if (tabRefs.current[tab]) {
        tabRefs.current[tab]?.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }
  }, []);

  useEffect(() => {
    if (isFromMenu) {
      onTabChange(GENERAL_DETAILS);
    } else {
      const storedTab =
        (localStorage.getItem(
          PERSONAL_INFO_TAB_STORE_KEY
        ) as TabValues | null) || GENERAL_DETAILS;
      onTabChange(storedTab);

      setTimeout(() => {
        if (tabRefs.current[storedTab]) {
          tabRefs.current[storedTab]?.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
          });
        }
      }, 100);
    }
  }, [onTabChange, isFromMenu]);

  const handleScrollRight = () => {
    if (tabListRef.current) {
      tabListRef.current.scrollBy({ left: 150, behavior: 'smooth' });
    }
  };

  return (
    <div className="w-full h-full overflow-hidden" ref={contentContainerRef}>
      <Tabs
        defaultValue={activeTab}
        value={activeTab}
        className="w-full h-full overflow-hidden"
        onValueChange={onTabChange}
      >
        <Typography
          variant="subtitle1"
          className="!font-archivo font-medium px-2"
          sx={{
            borderBottom: `1px solid ${colors.common.ashGray}`,
            fontSize: '20px',
            pb: 0.5,
          }}
        >
          Personal Info
        </Typography>

        <div className="flex flex-col w-full h-full overflow-hidden px-2">
          <div className="relative px-1 mt-3 flex items-center w-full border border-[#637D92] rounded-lg bg-white shadow-sm">
            <TabsList
              ref={tabListRef}
              className="gap-1 overflow-x-auto whitespace-nowrap flex-nowrap w-full flex justify-start scrollbar-hide overflow-y-hidden"
            >
              {tabs.map((tab) => (
                <TabsTrigger
                  key={tab.value}
                  value={tab.value}
                  ref={(el) => {
                    tabRefs.current[tab.value] = el;
                  }}
                  className="text-[14px] px-3 py-2 rounded-md transition-colors duration-200 border-none 
                  data-[state=active]:text-[#1AA6F1]"
                >
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
            <IconButton onClick={handleScrollRight}>
              <AiOutlineRight size={18} color="black" />
            </IconButton>
          </div>

          <div className="flex-1 overflow-hidden">
            {tabs.map((tab) => (
              <TabsContent
                key={tab.value}
                value={tab.value}
                ref={(el) => {
                  tabContentRefs.current[tab.value] = el;
                }}
                className="h-[calc(100dvh-16rem)] pb-10 overflow-auto data-[state=active]:flex-1 px-3"
              >
                {tab.content}
              </TabsContent>
            ))}
          </div>
        </div>
      </Tabs>
    </div>
  );
}
