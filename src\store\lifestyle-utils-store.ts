import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { isToday } from '@/utils/dateUtils/dayUtils';

import {
  LifestyleFormsType,
  lifestyleModes,
  LifestyleModesType,
  mobileViews,
  MobileViewType,
  NutritionAndDietForm,
} from '@/constants/lifestyle';

import { LifestyleQuestion, LifestyleSource } from '@/emr/types/lifestyle';

const { NOW_CONSULTING } = mobileViews;
const { DIETARY_ASSESSMENT } = NutritionAndDietForm;
const { CREATE, VIEW } = lifestyleModes;

type LifestyleUtilStore = {
  mobilePage: MobileViewType;
  currentTab: LifestyleFormsType;
  formMode: LifestyleModesType;
  dataToEdit: LifestyleQuestion | null;
  isModalOpen: boolean;
  isSubmittedToday: boolean;
  setMobilePage: (_page: MobileViewType) => void;
  setCurrentTab: (_tab: LifestyleFormsType) => void;
  setFormMode: (_mode: LifestyleModesType) => void;
  setDataToEdit: (_data: LifestyleQuestion | null) => void;
  closeModal: (_mode?: LifestyleModesType) => void;
  openModal: (_mode?: LifestyleModesType) => void;
  onExpand: (_data: LifestyleQuestion | null) => void;
  hasSubmissionToday: (
    _data: LifestyleQuestion[],
    _source: LifestyleSource
  ) => boolean;
};

/**
 * @deprecated This store is deprecated and will be removed in future versions.
 * Please use the store/emr/lifestyle instead.
 */
export const useLifestyleUtilStore = create<LifestyleUtilStore>()(
  persist(
    (set) => ({
      mobilePage: NOW_CONSULTING,
      currentTab: DIETARY_ASSESSMENT,
      formMode: CREATE,
      dataToEdit: null,
      isModalOpen: false,
      isSubmittedToday: false,

      setMobilePage: (page) => set({ mobilePage: page }),
      setCurrentTab: (tab) => set({ currentTab: tab }),
      setFormMode: (mode) => set({ formMode: mode }),
      setDataToEdit: (data) => set({ dataToEdit: data }),
      closeModal: (mode = CREATE) => {
        set({ isModalOpen: false, formMode: mode, dataToEdit: null });
      },
      openModal: (mode = CREATE) => {
        set({ isModalOpen: true, formMode: mode });
      },
      onExpand: (data) => {
        set({ dataToEdit: data, formMode: VIEW, isModalOpen: true });
      },

      hasSubmissionToday: (data, source) => {
        if (!data) return false;

        const isSubmittedToday = data?.some(
          (item) => isToday(item.created_on) && item.source === source
        );
        set({ isSubmittedToday });

        return isSubmittedToday;
      },
    }),
    {
      name: 'page-storage',
      partialize: (state) => ({
        mobilePage: state.mobilePage,
        currentTab: state.currentTab,
      }),
    }
  )
);
