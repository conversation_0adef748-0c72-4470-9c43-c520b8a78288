import React, { memo, useCallback, useEffect } from 'react';

import { Typography } from '@mui/material';

import { useBookConsultationStore } from '@/store/mrd/manage-patient/book-consultation';

import Loader from '@/core/components/app-loaders/Loader';
import AppTitle from '@/core/components/app-title';

import AppointmentCard from './AppointmentCard';

type UpcomingAppointmentsProps = {
  id: string;
};

const UpcomingAppointments = ({ id }: UpcomingAppointmentsProps) => {
  const {
    fetchAllDoctors,
    getFutureAppointments,
    appointments,
    reset: resetStore,
    futureAppointmentsLoading,
  } = useBookConsultationStore();

  useEffect(() => {
    fetchAllDoctors();
  }, [fetchAllDoctors]);

  useEffect(() => resetStore(), [resetStore]);

  useEffect(() => {
    getFutureAppointments(id);
  }, [getFutureAppointments, id]);

  const renderAppointments = useCallback(() => {
    if (futureAppointmentsLoading) {
      return <Loader />;
    }

    if (appointments.length === 0) {
      return (
        <Typography variant="body2" color="textSecondary" fontStyle="italic">
          No Upcoming Appointments
        </Typography>
      );
    }

    return appointments.map((appointment) => (
      <AppointmentCard key={appointment.queueId} appointment={appointment} />
    ));
  }, [appointments, futureAppointmentsLoading]);

  return (
    <div className="h-full w-full py-base flex flex-col gap-base overflow-y-auto">
      <AppTitle variant="h5">Upcoming Appointments</AppTitle>
      {renderAppointments()}
    </div>
  );
};

export default memo(UpcomingAppointments);
