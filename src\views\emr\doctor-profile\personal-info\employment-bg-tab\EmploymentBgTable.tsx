import React, { FC, memo, useMemo } from 'react';

import { Control, FieldArrayWithId } from 'react-hook-form';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import {
  allowNumbersTextAndDot,
  preventNonAlphabeticInput,
} from '@/utils/validation';

import { employmentBackgroundHeaders } from '@/constants/emr/doctor-profile/personal-info';

import Table from '@/core/components/table';
import { Row } from '@/core/components/table/types';
import {
  actionButtonCellProps,
  getFileCellProps,
  getInputCellProps,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import ActionButton from '../shared/ActionButton';
import ControlledDateRange from '../shared/ControlledDateRange';
import TableFilePicker from '../shared/TableFilePicker';
import TableTextarea from '../shared/TableTextarea';

import { FormData } from '.';

type Props = {
  fields: FieldArrayWithId<FormData, 'experience', 'id'>[];
  control: Control<FormData>;
  handleItemEdit: (index: number) => () => void;
  handleOnDelete: (itemToDelete: ItemToDelete) => void;
  itemToEdit: number | null;
};

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const EmploymentBgTable: FC<Props> = ({
  control,
  fields,
  itemToEdit,
  handleItemEdit,
  handleOnDelete,
}) => {
  const rows = useMemo<Row[]>(
    () =>
      fields?.map((field, index) => ({
        key: field.id,
        hospitalName: {
          value: (
            <TableTextarea
              name={`experience.${index}.hospitalName`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={preventNonAlphabeticInput}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        designation: {
          value: (
            <TableTextarea
              name={`experience.${index}.designation`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={preventNonAlphabeticInput}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        duration: {
          value: (
            <ControlledDateRange
              control={control}
              name={`experience.${index}.duration`}
              inputProps={{
                inputClassName: 'h-full input:bg-transparent w-full m-0',
                fieldClassName:
                  'text-sm disabled:bg-[rgb(229 231 235 / var(--tw-bg-opacity))] disabled:cursor-not-allowed rounded-none m-0 !px-1 h-[40px]',
                style: { outline: 'none', border: 'none' },
              }}
              format={DATE_DD_MM_YYYY_SLASH}
              disabled={itemToEdit !== index}
              separator="To"
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        salary: {
          value: (
            <TableTextarea
              name={`experience.${index}.salary`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={allowNumbersTextAndDot}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        doc1: {
          value: (
            <TableFilePicker
              name={`experience.${index}.doc1`}
              control={control}
              disabled={itemToEdit !== index}
              imageUrl={field?.doc1}
            />
          ),
          cellProps: getFileCellProps(itemToEdit !== index),
        },
        doc2: {
          value: (
            <TableFilePicker
              name={`experience.${index}.doc2`}
              control={control}
              disabled={itemToEdit !== index}
              imageUrl={field?.doc2}
            />
          ),
          cellProps: getFileCellProps(itemToEdit !== index),
        },
        status: {
          value: (
            <TableTextarea
              name={`experience.${index}.status`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={preventNonAlphabeticInput}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        edit: {
          value:
            itemToEdit !== index ? (
              <ActionButton actionFor="edit" onClick={handleItemEdit(index)} />
            ) : (
              <></>
            ),
          cellProps: actionButtonCellProps,
        },
        delete: {
          value: (
            <ActionButton
              actionFor="delete"
              onClick={() => handleOnDelete({ index, uuId: field?.uuId })}
            />
          ),
          cellProps: actionButtonCellProps,
        },
      })),
    [fields, control, handleOnDelete, itemToEdit, handleItemEdit]
  );

  return (
    <Table
      headers={employmentBackgroundHeaders}
      rows={rows}
      tableContainerProps={{
        sx: {
          '& tbody td': {
            minHeight: 30,
          },
        },
      }}
    />
  );
};

export default memo(EmploymentBgTable);
