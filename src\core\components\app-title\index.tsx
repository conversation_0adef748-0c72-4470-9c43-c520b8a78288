import React, { FC, memo, ReactNode } from 'react';

import { Typography, TypographyProps } from '@mui/material';

type Props = TypographyProps & {
  children: ReactNode;
};

const AppTitle: FC<Props> = ({ children, ...rest }) => {
  return (
    <Typography
      variant="h4"
      sx={{
        fontWeight: 500,
        fontSize: '16px',
        ...(rest.sx || {}),
      }}
      {...rest}
    >
      {children}
    </Typography>
  );
};

export default memo(AppTitle);
