import dayjs from 'dayjs';

/**
 * Returns the age from dob as a numeric value
 * @param dob Date of birth (string or Date)
 * @param referenceDate Optional reference date for calculation (default: now)
 * @returns Numeric value representing the age in years
 */
export function calculateAge(
  dob?: string | Date,
  referenceDate?: string | Date
): string {
  const now = referenceDate ? dayjs(referenceDate) : dayjs();
  const birth = dayjs(dob);
  if (!birth.isValid()) return '';
  const diffYears = now.diff(birth, 'year');
  if (diffYears >= 1) {
    return diffYears.toString();
  }
  const diffMonths = now.diff(birth, 'month');
  if (diffMonths >= 1) {
    return diffMonths.toString();
  }
  const diffDays = now.diff(birth, 'day');
  return diffDays.toString();
}
