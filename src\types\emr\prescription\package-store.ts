import { MedicineItem, PrescriptionPackageData } from './package';

import {
  PrescriptionModalMode,
  prescriptionModalModes,
  PrescriptionPackageType,
} from '.';

type PrescriptionPackageStoreState = {
  isModalOpen: boolean;
  modalMode: PrescriptionModalMode;
  packages: PrescriptionPackageData[];
  activePackageType: PrescriptionPackageType;
  selectedPackage: PrescriptionPackageData | null;
  newPackageName: string;
  editingPackageName: string;
  medicineItems: MedicineItem[];
  isLoading: boolean;
  isPackagesLoading: boolean;
  isCreating: boolean;
  hasUnsavedChanges: boolean;
  showUnsavedChangesModal: boolean;
  originalPackageName: string;
  originalMedicineItems: MedicineItem[];
  showDeleteConfirmationModal: boolean;
  isDeleting: boolean;
};

type PrescriptionPackageStoreActions = {
  openModal: (_packageType: PrescriptionPackageType) => void;
  closeModal: () => void;
  setModalMode: (_mode: PrescriptionModalMode) => void;
  setSelectedPackage: (_pkg: PrescriptionPackageData) => void;
  setPackageName: (_name: string) => void;
  addMultipleMedicines: (
    _medicines: Array<Omit<MedicineItem, 'selected'>>
  ) => void;
  removeMedicine: (_medicineId: string | number) => void;
  toggleMedicineSelection: (_medicineId: string | number) => void;
  startCreatePackage: (_modalMode: PrescriptionModalMode) => void;
  addSelectedPackageToMedicines: () => void;
  setShowUnsavedChangesModal: (_show: boolean) => void;
  checkUnsavedChanges: () => boolean;
  handleCancelWithCheck: () => void;
  resetUnsavedChanges: () => void;
  clearAllMedicines: () => void;
  selectAllMedicines: () => void;
  setShowDeleteConfirmationModal: (_show: boolean) => void;
  addNewMedicineToPackage: () => void;
  fetchPackages: () => Promise<void>;
  createNewPackage: () => void;
  updateExistingPackage: () => void;
  deletePackage: () => Promise<void>;
};

export type PrescriptionPackageStore = PrescriptionPackageStoreState &
  PrescriptionPackageStoreActions;

export const initialState: PrescriptionPackageStoreState = {
  isModalOpen: false,
  modalMode: prescriptionModalModes.VIEW,
  packages: [],
  activePackageType: null,
  selectedPackage: null,
  newPackageName: '',
  editingPackageName: '',
  medicineItems: [],
  isLoading: false,
  isPackagesLoading: false,
  isCreating: false,
  hasUnsavedChanges: false,
  showUnsavedChangesModal: false,
  originalPackageName: '',
  originalMedicineItems: [],
  showDeleteConfirmationModal: false,
  isDeleting: false,
};
