'use client';

import { IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';

import colors from '@/utils/colors';

export const StyledTableHeadV2 = styled(TableHead)(({ theme }) => ({
  ['& tr']: {
    ['& th']: {
      backgroundColor: theme.palette.secondary.main,
      fontSize: 14,
      paddingTop: theme.spacing(2),
      paddingBottom: theme.spacing(2),
      color: theme.palette.common.white,
      zIndex: 1000,
      fontWeight: theme.typography.fontWeightRegular,
      letterSpacing: theme.typography.overline.letterSpacing,
      borderRight: theme.shape.border,
      borderTop: theme.shape.border,
      borderBottom: theme.shape.border,
    },

    ['& th:first-of-type']: {
      borderLeft: theme.shape.border,
      borderTopLeftRadius: theme.shape.borderRadius,
    },
    ['& th:last-of-type']: {
      borderRight: theme.shape.border,
      borderTopRightRadius: theme.shape.borderRadius,
    },
  },
}));

export const StyledTableContainerV2 = styled(TableContainer)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius,

  ['& tbody td']: {
    fontSize: 14,
    paddingTop: theme.spacing(2),
    paddingBottom: theme.spacing(2),
    borderRight: theme.shape.border,
    borderBottom: theme.shape.border,
    minHeight: 40,
    letterSpacing: theme.typography.overline.letterSpacing,
    textAlign: 'center',
  },

  ['& tbody tr:last-of-type td:first-of-type']: {
    borderBottomLeftRadius: theme.shape.borderRadius,
  },

  ['& tbody tr:last-of-type td:last-of-type']: {
    borderBottomRightRadius: theme.shape.borderRadius,
  },

  ['& tbody tr td:first-of-type']: {
    borderLeft: theme.shape.border,
  },

  ['& tbody tr td:last-of-type']: {
    borderRight: theme.shape.border,
  },

  ['& tbody tr:nth-of-type(even)']: {
    backgroundColor: theme.palette.secondary.light,
  },
}));

export const SortIconButtonV2 = styled(IconButton)(({ theme }) => ({
  padding: 0,
  width: 26,
  height: 26,
  position: 'relative',
  '& .sort-icon': {
    color: colors.common.charcoalGray,
    width: 15,
    height: 15,
    position: 'absolute',
    transform: 'translate(-50%, -50%)',

    '&.asc': {
      top: '40%',
      left: '40%',
    },

    '&.desc': {
      top: '60%',
      left: '60%',
    },

    '&.selected': {
      color: theme.palette.common.white,
    },
  },
}));
