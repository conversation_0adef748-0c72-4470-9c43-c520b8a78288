import { differenceInSeconds } from 'date-fns';
import dayjs from 'dayjs';
import { mapKeys } from 'lodash';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import {
  AppointmentStatus,
  Department,
  PatientQueueLabStatus,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';

import { arcaAxios } from '@/core/lib/interceptor';

import { Conversation } from './speech';

const { STANDARD_DATE } = DateFormats;

export interface Contact {
  phone: string;
  email: string;
}

export interface Insurance {
  provider: string;
  id: string;
  proof: File | string;
}

export interface Patient {
  id?: string;
  name: string;
  age: number;
  sex: string;
  dob: Date | string;
  height: number;
  weight: number;
  address: string;
  city: string;
  state: string;
  idProof: File | string;
  aadhar: string;
  abha: string;
  contact: Contact;
  insurance: Insurance;
}

const formatPatientData = (data: Patient) => ({
  ...data,
});

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the updatePatient from query/mrd/manage-patient/manage function instead.
 */
export const updatePatient = async (data: Patient) => {
  return await arcaAxios.put('/patient', formatPatientData(data));
};

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the getPatient from query/mrd/manage-patient/manage function instead.
 */
export const getPatient = async (id: string) => {
  return await arcaAxios.get(`/patient?id=${id}`);
};

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the getPatientVitals from query/mrd/manage-patient/manage function instead.
 */
export const getPatientVitals = async (id: string) => {
  return await arcaAxios.get(`/patient/vitals?patientId=${id}`);
};

/**
 * @deprecated This type is deprecated and will be removed in future versions.
 * Please check the type/mrd/search-patient function instead.
 */
export type UploadVitalsParams = {
  heartRate: number;
  systolicPressure: number;
  diastolicPressure: number;
  respiratoryRate: number;
  spO2: number;
  temperature: number;
  height: number | string;
  weight: number | string;
  bmi: number | string;
  waistCircumference: number | string;
};

type UploadVitalsApiParams = Omit<
  UploadVitalsParams,
  'systolicPressure' | 'diastolicPressure'
> & {
  bloodPressure: string;
};

/**
 * @deprecated This function is deprecated and will be removed in future versions.
 * Please use the uploadVitals from query/mrd/search-patient function instead.
 */
export const uploadVitals = async (
  patientId: string,
  vitals: UploadVitalsParams
) => {
  const payload: UploadVitalsApiParams = {
    heartRate: vitals.heartRate,
    respiratoryRate: vitals.respiratoryRate,
    spO2: vitals.spO2,
    temperature: vitals.temperature,
    height: vitals.height,
    weight: vitals.weight,
    bmi: vitals.bmi,
    waistCircumference: vitals.waistCircumference,
    bloodPressure: `${vitals.systolicPressure}/${vitals.diastolicPressure}`,
  };

  return arcaAxios.post(`/patient/vitals?patientId=${patientId}`, {
    vitals: payload,
  });
};

export type GetPatientHistoryRes = {
  createdBy: string;
  createdOn: string;
  updatedOn: string;
  updated_on: string;
  id: string;
  patientId: string;
  status?: string;
  summary: {
    owner: {
      department?: string;
      id: string;
      name: string;
      email: string;
      role: string;
    };
    conversation?: Conversation[];
    presentingComplaints?: string;
    historyOfPresenting?: string;
    pastMedicalHistory?: string;
    pastSurgicalHistory?: string;
    familyHistory?: string;
    addictionHistory?: string;
    dietHistory?: string;
    physicalActivityHistory?: string;
    stressHistory?: string;
    sleepHistory?: string;
    currentMedicationHistory?: string;
    vitals?: {
      heartRate?: number;
      systolicPressure?: number;
      diastolicPressure?: number;
      respiratoryRate?: number;
      spO2?: number;
      temperature?: number;
    };
    anthropometry?: {
      height?: number;
      weight?: number;
      bmi?: number;
      waistCircumference?: number;
    };
    generalPhysicalExamination?: {
      pallor?: boolean;
      icterus?: boolean;
      cyanosis?: boolean;
      clubbing?: boolean;
      pedalEnema?: boolean;
      lymphadenopathy?: boolean;
      notes?: string;
    };
    heent?: string;
    systemicExamination?: {
      neurologicalExamination?: string;
      cardiovascularExamination?: string;
      respiratoryExamination?: string;
      abdomenExamination?: string;
      rheumatologicalExamination?: string;
    };
    recordingDuration?: number;
  };
}[];

export type GetPatientHistoryParams = {
  patientId: string;
  startDate?: string;
  endDate?: string;
};

export const getPatientHistory = async (
  params: GetPatientHistoryParams
): Promise<GetPatientHistoryRes> => {
  const { data } = await arcaAxios.get(`/patient/history`, {
    params,
  });

  if (!data?.length) {
    return [];
  }

  data.sort((a: any, b: any) => {
    return differenceInSeconds(new Date(b.updated_on), new Date(a.updated_on));
  });

  const formattedData = data.map((item: any) => {
    return {
      ...item,
      createdOn: new Date(item.created_on),
      summary: item.summary && {
        ...item.summary,
        presentingComplaints:
          item.summary.presentingcomplaints ||
          item.summary.presentingComplaints,
        historyOfPresenting:
          item.summary.historyofpresenting || item.summary.historyOfPresenting,
        pastMedicalHistory:
          item.summary.pastmedicalhistory || item.summary.pastMedicalHistory,
        pastSurgicalHistory:
          item.summary.pastsurgicalhistory || item.summary.pastSurgicalHistory,
        familyHistory: item.summary.familyhistory || item.summary.familyHistory,
        addictionHistory:
          item.summary.addictionhistory || item.summary.addictionHistory,
        dietHistory: item.summary.diethistory || item.summary.dietHistory,
        physicalActivityHistory:
          item.summary.physicalactivityhistory ||
          item.summary.physicalActivityHistory,
        stressHistory: item.summary.stresshistory || item.summary.stressHistory,
        sleepHistory: item.summary.sleephistory || item.summary.sleepHistory,
        currentMedicationHistory:
          item.summary.currentmedicationhistory ||
          item.summary.currentMedicationHistory,
        vitals: {
          ...item.summary.vitals,
        },
        anthropometry: {
          ...item.summary.anthropometry,
        },
        generalPhysicalExamination: {
          ...item.summary.generalphysicalexamination,
        },
        heent: item.summary.heent,
        systemicExamination: {
          ...item.summary.systemicexamination,
        },
        recordingDuration:
          item.summary.recordingduration || item.summary.recordingDuration,
      },
    };
  });

  return formattedData;
};

export const addToPatientHistory = async (id: string, data: any) => {
  return await arcaAxios.post(`/patient/history?patientId=${id}`, {
    summary: mapKeys(data, (_, key) => key.toLowerCase()),
  });
};

export const getPatientBySearchString = async (
  searchString: string,
  token: string
) => {
  return await arcaAxios.post(
    `/patient/search`,
    {
      pagesize: 10,
      continuetoken: null,
      query: `SELECT * FROM c WHERE 
        CONTAINS(c.contact.phone, '${searchString}') 
          OR CONTAINS(LOWER(c.id), '${searchString.toLowerCase()}')
          OR CONTAINS(LOWER(c.name), '${searchString.toLowerCase()}')
          OR CONTAINS(LOWER(c.aadhar), '${searchString.toLowerCase()}')
          OR CONTAINS(LOWER(c.abha), '${searchString.toLowerCase()}')
      `,
    },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
};

export const updatePatientHistory = async (
  id: string,
  patientId: string,
  data: GetPatientHistoryRes[0]
) => {
  return await arcaAxios.put(
    `/patient/history?patientId=${patientId}&id=${id}`,
    { ...data, summary: mapKeys(data?.summary, (_, key) => key.toLowerCase()) }
  );
};

export const getAppointmentId = async (
  doctorId: string,
  date: Date | string
): Promise<string | undefined> => {
  const { data } = await arcaAxios.get(`/appointment`, {
    params: {
      date: formatDate(date, STANDARD_DATE),
      doctorId: doctorId,
    },
  });

  return data.id;
};

export type QueueItem = {
  queueId: string;
  time: string;
  status: AppointmentStatus;
  patientStatus?: PatientStatus;
  queuePosition: number;
  department: Department;
  labStatus?: PatientQueueLabStatus;
  patient: {
    id: string;
    name: string;
    email: string;
    sex: string;
    address: string;
    city?: string;
    state?: string;
    age: number;
    dob: string;
    phone: string;
  };
  labTestStatus?: PatientQueueLabStatus;
};

export type GetPatientQueueRes = {
  appointmentId: string;
  doctorId: string;
  date: Date;
  queues: QueueItem[];
};

export function formatAppointmentRes(
  res: Record<string, any> | Record<string, any>
): GetPatientQueueRes {
  let queues: GetPatientQueueRes['queues'] = [];

  queues = res.queues.map((item: any) => {
    const statusParts = item.status.split('-');

    let status = statusParts[0];
    let patientStatus = '' as PatientStatus;

    if (statusParts.length > 1) {
      status = statusParts[0];
      patientStatus = statusParts[1];
    }

    const result: GetPatientQueueRes['queues'][number] = {
      queueId: item.queueId,
      time: item.time,
      status: status,
      patientStatus: patientStatus,
      queuePosition: item.queuePosition,
      department: item.department,
      labTestStatus: item.labStatus,
      patient: {
        id: item.patientId,
        name: item.patientName || '',
        email: item.patientEmail || '',
        sex: item.patientSex || '',
        address: item.patientAddress,
        age: item.patientAge || 0,
        dob: item.patientDoB || '',
        phone: item.patientPhone || '',
      },
    };

    return result;
  });

  queues.sort((a: any, b: any) => a.queuePosition - b.queuePosition);

  const formatted: GetPatientQueueRes = {
    appointmentId: res.appointmentId,
    queues: queues,
    doctorId: res.doctorId,
    date: dayjs(formatDate(res.date, STANDARD_DATE)).toDate(),
  };

  return formatted;
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
  searchQuery?: string;
  patientId?: string;
  status?: string | string[];
}

export interface PaginatedResponse<T> {
  data: T;
  currentPage: number;
  totalPages: number;
  totalItems: number;
}

export const getPatientQueue = async (
  doctorId: string,
  date: Date | string,
  { page, pageSize, searchQuery, status, patientId }: PaginationParams = {}
): Promise<PaginatedResponse<GetPatientQueueRes> | null> => {
  const appointmentId = await getAppointmentId(doctorId, date);

  if (!appointmentId) {
    return null;
  }

  // Create URLSearchParams to handle multiple values for the same parameter
  const searchParams = new URLSearchParams();

  // Add appointment ID
  searchParams.append('appointmentId', appointmentId);

  // Add search query if provided
  if (searchQuery) {
    searchParams.append('searchText', searchQuery);
  }

  // Add pagination if provided
  if (page !== undefined) {
    searchParams.append('page', page.toString());
  }
  if (pageSize !== undefined) {
    searchParams.append('pageSize', pageSize.toString());
  }

  // Add status parameters - multiple values for the same parameter
  if (status) {
    const statuses = Array.isArray(status) ? status : [status];
    statuses.forEach((s) => {
      searchParams.append('status', s);
    });
  }

  // Make the request with the custom query string
  const { data } = await arcaAxios.get(
    `appointment?${searchParams.toString()}`
  );

  if (!data) {
    return null;
  }

  const formattedData = formatAppointmentRes(data);

  return {
    data: formattedData,
    currentPage: data.currentPage || 1,
    totalPages: data.totalPages || 1,
    totalItems: data.totalQueueCount || formattedData?.queues?.length || 0,
  };
};

type UpdateQueueByIdArgs = {
  queueId: string;
  payload: {
    time: string;
    status: string;
    patientStatus?: string;
    queuePosition: number;
  };
};

export const updateQueueItemById = async (params: UpdateQueueByIdArgs) => {
  const { data } = await arcaAxios.patch(
    `/queue`,
    {
      time: params.payload.time,
      queuePosition: params.payload.queuePosition,
      status: `${params.payload.status}-${params.payload.patientStatus || PatientStatus.Booked}`,
    },
    {
      params: {
        queueId: params.queueId,
      },
    }
  );

  return formatAppointmentRes(data);
};

export const updateLastConsultedDate = async (
  patientId: string,
  date: string
) => {
  return await arcaAxios.patch(`/patient?id=${patientId}`, {
    last_consultation_date: date,
  });
};

type ReorderQueueArgs = {
  appointments: ({ queueId: string } & UpdateQueueByIdArgs['payload'])[];
};

export const reorderQueue = async ({ appointments }: ReorderQueueArgs) => {
  let queue = {};
  for (const appointment of appointments) {
    queue = await updateQueueItemById({
      queueId: appointment.queueId,
      payload: {
        time: appointment.time,
        status: appointment.status,
        patientStatus: appointment.patientStatus,
        queuePosition: appointment.queuePosition,
      },
    });
  }

  return queue;
};
