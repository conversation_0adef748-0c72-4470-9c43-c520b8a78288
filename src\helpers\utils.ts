import { useCallback } from 'react';

import { debounce } from 'lodash';

export function useDebounceFn<F extends (..._args: any[]) => any>(
  func: F,
  wait: number,
  deps: any[] = []
) {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounced = useCallback(debounce(func, wait), deps);

  return debounced;
}

export const mapObjectToArray = <T extends Record<string, string>>(
  obj: T
): string[] => {
  return Object.keys(obj).map((key) => {
    return obj[key];
  });
};
