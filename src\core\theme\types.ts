import '@mui/material/styles';
import { Components, CssVarsTheme, Palette, Theme } from '@mui/material/styles';
import { TypographyOptions } from '@mui/material/styles/createTypography';

declare module '@mui/material/styles' {
  interface Palette {
    accent: {
      pink: string;
      green: string;
      main: string;
      contrastText: string;
    };
    chart: {
      chart1: string;
      chart2: string;
      chart3: string;
      chart4: string;
      chart5: string;
    };
  }

  interface PaletteOptions {
    accent?: {
      pink?: string;
      green?: string;
      main?: string;
      contrastText?: string;
    };
    chart?: {
      chart1?: string;
      chart2?: string;
      chart3?: string;
      chart4?: string;
      chart5?: string;
    };
  }

  // Extend ThemeOptions to include shape with border
  interface ThemeOptions {
    shape?: {
      borderRadius?: number | string;
      border?: string;
    };
  }

  // Extend the theme interface for custom properties
  interface Theme {
    customShadows?: {
      xs: string;
      base: string;
    };
    shape: {
      borderRadius: number | string;
      border: string;
    };
  }
}

export {};

export const mode = {
  light: 'light',
  dark: 'dark',
} as const;

export type Mode = (typeof mode)[keyof typeof mode];

export type Settings = {
  mode: Mode;
};

export type ComponentSettings = Settings & {
  palette: Palette;
  typography: TypographyOptions;
};

export type OverrideComponent = Components<
  Omit<Theme, 'palette' | 'components'> & CssVarsTheme
>;
