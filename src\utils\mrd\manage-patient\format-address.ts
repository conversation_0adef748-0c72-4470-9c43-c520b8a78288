import { Address } from '@/types/mrd/manage-patient/patient';

type FormatAddressOptions = {
  fallback?: string;
  singleLine?: boolean;
};

export const formatAddress = (
  address?: Address,
  options?: FormatAddressOptions
): string => {
  const { fallback = '', singleLine = false } = options ?? {};
  if (!address) {
    return fallback;
  }
  if (typeof address === 'string') {
    return address;
  }

  if (!singleLine) {
    const parts: string[] = [];

    if (address.houseName) {
      parts.push(address.houseName);
    }
    if (address.post) {
      parts.push(`${address.post} P.O`);
    }
    if (address.street) {
      parts.push(address.street);
    }
    if (address.city) {
      parts.push(address.city);
    }
    if (address.district) {
      parts.push(`${address.district} Dist.`);
    }
    if (address.state) {
      parts.push(address.state);
    }
    if (address.pin) {
      parts.push(address.pin);
    }
    if (address.country) {
      parts.push(address.country);
    }

    return parts.join('\n');
  } else {
    const parts: string[] = [];

    if (address.houseName) {
      parts.push(address.houseName);
    }
    if (address.post) {
      parts.push(`${address.post} P.O`);
    }
    if (address.city) {
      parts.push(address.city);
    }
    if (address.district) {
      parts.push(`${address.district} Dist.`);
    }
    if (address.state) {
      parts.push(address.state);
    }
    if (address.pin) {
      parts.push(address.pin);
    }
    if (address.country) {
      parts.push(address.country);
    }

    return parts.join(', '); // comma-separated single line
  }
};
