'use client';

import React, { useCallback } from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import Prescriptions from '@/views/emr/prescription';

import { PrescriptionPage } from '@/components/permission/protected-page';

import Chat from '@/emr/components/chat';
import NoPatientView from '@/emr/components/shared/NoPatientView';

const PrescriptionContent = () => {
  const { patient, isActivePatient } = useCurrentPatientStore();

  const renderPrescription = useCallback(() => {
    if (!patient || !isActivePatient) {
      return <NoPatientView />;
    } else {
      return <Prescriptions />;
    }
  }, [isActivePatient, patient]);

  return (
    <>
      {renderPrescription()}
      {patient && <Chat />}
    </>
  );
};

const Prescription = () => (
  <PrescriptionPage>
    <PrescriptionContent />
  </PrescriptionPage>
);

export default Prescription;
