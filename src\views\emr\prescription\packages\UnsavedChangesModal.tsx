import React from 'react';

import { usePrescriptionPackageStore } from '@/store/emr/prescription/package';

import DeleteModal from '@/core/components/delete-modal';
import { prescriptionModalModes } from '@/types/emr/prescription';

const { DETAIL, CREATE, VIEW } = prescriptionModalModes;

export const UnsavedChangesModal = () => {
  const {
    setShowUnsavedChangesModal,
    modalMode,
    newPackageName,
    editingPackageName,
    showUnsavedChangesModal,
    selectedPackage,
    setModalMode,
  } = usePrescriptionPackageStore();

  const handleDelete = () => {
    if (selectedPackage) {
      setModalMode(DETAIL);
    } else {
      setModalMode(VIEW);
    }
    setShowUnsavedChangesModal(false);
  };

  const handleCancel = () => {
    setShowUnsavedChangesModal(false);
  };

  const getPackageName = () => {
    if (modalMode === CREATE && !selectedPackage) {
      return newPackageName || 'Package';
    } else if (modalMode === DETAIL) {
      return editingPackageName || selectedPackage?.name || 'Package';
    }
    return 'Package';
  };

  const getConfirmationMessage = () => {
    if (selectedPackage) {
      return `Changes will not be saved. Are you sure you want to proceed?`;
    } else {
      return 'Proceeding will delete the package created.';
    }
  };

  return (
    <DeleteModal
      open={showUnsavedChangesModal}
      onClose={handleCancel}
      onDelete={handleDelete}
      confirmationMessage={getConfirmationMessage()}
      bodyContent={
        <div>
          <span className="text-blue-500 font-bold">{getPackageName()}</span>{' '}
          not saved!
        </div>
      }
      classes={{ container: '!w-100' }}
      deleteButtonProps={{ sx: { minWidth: 180 } }}
      cancelButtonProps={{ sx: { minWidth: 180 } }}
      deleteButtonText={selectedPackage ? 'Continue' : 'Delete'}
    />
  );
};
