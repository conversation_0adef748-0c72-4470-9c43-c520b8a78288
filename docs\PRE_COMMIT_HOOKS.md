# Pre-commit Hooks

Automatic code quality checks before each commit.

## What Runs on Commit

- **ESLint**: Fixes imports, unused variables, React hooks
- **Prettier**: Formats code consistently

## Setup

Automatically installed with:

```bash
npm install
```

## Manual Commands

```bash
# Run pre-commit checks manually
npx lint-staged

# Check and fix all files
npm run lint:fix
npm run format:all
```

## Troubleshooting

- **Hook not running**: `npm run prepare`
- **Linting errors**: `npm run lint:fix`
- **Bypass hook** (not recommended): `git commit --no-verify`
