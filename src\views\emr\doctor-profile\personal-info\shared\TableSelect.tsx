import React, { memo } from 'react';

import { Controller, FieldValues, UseControllerProps } from 'react-hook-form';

import { SelectField, SelectFieldProps } from '../Components';

interface TableSelectProps<T extends FieldValues>
  extends UseControllerProps<T>,
    Omit<SelectFieldProps, 'id' | 'onChange' | 'value'> {}

const TableSelect = <T extends FieldValues>({
  options,
  wrapperClassName = '',
  control,
  name,
  ...rest
}: TableSelectProps<T>) => {
  return (
    <div className={`flex flex-col gap-2.5 ${wrapperClassName}`}>
      <Controller
        name={name}
        control={control}
        render={({ field: { onChange, value } }) => (
          <SelectField
            {...rest}
            id={`select-id-${name}`}
            onChange={onChange}
            options={options}
            value={value}
            placeholder="Select"
            selectClassName="!text-sm font-light border-none p-0 focus:border-none focus:ring-0 px-2 py-0 focus-within:ring-0 focus:ring-offset-0 focus:outline-none shadow-none focus:shadow-none disabled:bg-gray-200 disabled:opacity-100"
          />
        )}
      />
    </div>
  );
};

export default memo(TableSelect) as typeof TableSelect;
