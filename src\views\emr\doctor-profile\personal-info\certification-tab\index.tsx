import React, { memo, useCallback, useEffect, useState } from 'react';

import { useFieldArray, useForm } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import { Stack } from '@mui/material';
import { toast } from 'sonner';
import * as yup from 'yup';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import {
  discardDefaultValues,
  getUniqueId,
} from '@/utils/emr/doctor-profile/personal-info';
import { getErrorMessages } from '@/utils/error-message';

import { profileTabs } from '@/constants/emr/doctor-profile/personal-info';

import AddButton from '@/emr/components/lifestyle/lifestyle-forms/shared/AddButton';

import {
  Certification as CertificationType,
  defaultCertification,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import DeleteModal from '../shared/DeleteModal';
import SaveButton from '../shared/SaveButton';
import Title from '../shared/Title';

import CertificationMob from './CertificationMob';
import CertificationTable from './CertificationTable';

const certificationSchema = yup.object({
  certifications: yup.array().of(
    yup.object({
      validFrom: yup.date().nullable(),
      validTo: yup
        .date()
        .nullable()
        .test('is-after', 'Enter a valid date', function (value) {
          const { validFrom } = this.parent;
          if (!validFrom || !value) return true;
          return value > validFrom;
        }),
    })
  ),
});

export type FormData = {
  certifications: CertificationType[];
};

const Certification = () => {
  const { data: userData } = useUserStore();
  const isMobile = useIsMobile();

  const [itemToDelete, setItemToDelete] = useState<ItemToDelete>(null);
  const [itemToEdit, setItemToEdit] = useState<number | null>(null);
  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    updateDoctorProfile,
    doctorProfile,
    isDeleting,
    createDoctorProfile,
    setTabName,
    deleteDoctorProfileTableItem,
  } = useDoctorStore();

  const {
    handleSubmit,
    control,
    reset,
    register,
    getValues,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      certifications: [isMobile ? defaultCertification : undefined],
    },
    resolver: yupResolver(certificationSchema) as any,
  });

  const { append, fields, remove } = useFieldArray<FormData>({
    control,
    name: 'certifications',
    shouldUnregister: true,
  });

  const onSubmit = useCallback(
    (data: FormData) => {
      const certifications = discardDefaultValues(
        data?.certifications,
        defaultCertification
      );

      const finalData: CertificationType[] = certifications?.map((item) => {
        return {
          ...item,
          uuId: getUniqueId(item?.uuId, 'CRT'),
        };
      });

      if (doctorProfile?.id) {
        updateDoctorProfile(doctorProfile.id, {
          professionalDetails: {
            ...doctorProfile?.professionalDetails,
            certifications: finalData,
          },
        });
      } else {
        createDoctorProfile({
          professionalDetails: {
            ...doctorProfile?.professionalDetails,
            certifications: finalData,
          },
          username: userData?.email,
        });
      }
      setItemToEdit(null);
      setIsSubmitted(true);
      setEditableField(new Set());
    },
    [createDoctorProfile, doctorProfile, updateDoctorProfile, userData?.email]
  );

  const handleItemEdit = useCallback(
    (index: number) => () => setItemToEdit(index),
    []
  );

  const handleOnAdd = useCallback(() => {
    setItemToEdit(fields.length);
    append(defaultCertification);
  }, [append, fields.length]);

  const handleOnDelete = useCallback(
    (item: ItemToDelete) => setItemToDelete(item),
    []
  );

  const handleDelete = useCallback(async () => {
    if (itemToDelete) {
      if (itemToDelete?.uuId && doctorProfile?.id) {
        const certifications =
          doctorProfile?.professionalDetails?.certifications;

        const filteredCertification = certifications?.filter(
          (item) => item?.uuId !== itemToDelete?.uuId
        );

        await deleteDoctorProfileTableItem(doctorProfile.id, {
          professionalDetails: {
            ...doctorProfile?.professionalDetails,
            certifications: filteredCertification,
          },
        });
      } else {
        remove(itemToDelete?.index);
      }
      setItemToDelete(null);
    }
  }, [
    itemToDelete,
    doctorProfile?.id,
    doctorProfile?.professionalDetails,
    deleteDoctorProfileTableItem,
    remove,
  ]);

  const handleCancel = useCallback(() => {
    setItemToDelete(null);
  }, []);

  useEffect(() => {
    if (doctorProfile?.professionalDetails?.certifications?.length) {
      reset({
        certifications: doctorProfile?.professionalDetails?.certifications,
      });
    } else if (isMobile) {
      reset({
        certifications: [defaultCertification],
      });
    }
  }, [doctorProfile?.professionalDetails?.certifications, reset, isMobile]);

  useEffect(() => {
    setTabName(profileTabs.CERTIFICATION);
  }, [setTabName]);

  useEffect(() => {
    const errorMessages = getErrorMessages(errors);
    if (errorMessages.length && !isMobile) {
      toast.error(errorMessages[0]);
    }
  }, [errors, isMobile]);

  return (
    <Stack
      component="form"
      spacing={{ xs: 3, md: 2 }}
      pb={{ xs: 8, md: 0 }}
      onSubmit={handleSubmit(onSubmit)}
    >
      <Title
        title="Certification"
        onAdd={handleOnAdd}
        showEndButton={!isMobile}
      />
      {isMobile ? (
        <CertificationMob
          fields={fields}
          control={control}
          register={register}
          editableField={editableField}
          getValues={getValues}
          isSubmitted={isSubmitted}
          setEditableField={setEditableField}
          handleOnDelete={handleOnDelete}
        />
      ) : (
        <CertificationTable
          fields={fields}
          control={control}
          itemToEdit={itemToEdit}
          handleItemEdit={handleItemEdit}
          handleOnDelete={handleOnDelete}
          error={errors}
        />
      )}
      {isMobile && (
        <div className="fixed bottom-34 md:static right-6 bg-transparent">
          <AddButton size="medium" onClick={handleOnAdd} />
        </div>
      )}
      <SaveButton />
      <DeleteModal
        open={!!itemToDelete}
        onClose={handleCancel}
        onDelete={handleDelete}
        isLoading={isDeleting}
      />
    </Stack>
  );
};

export default memo(Certification);
