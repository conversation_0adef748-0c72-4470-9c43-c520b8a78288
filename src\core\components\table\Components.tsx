import { useCallback, useMemo } from 'react';

import { alpha, TableRow, useTheme } from '@mui/material';
import TableCell from '@mui/material/TableCell';

import Loading from '@/lib/common/loading';
import { cn } from '@/lib/utils';

import { MingcuteArrowDownFill } from '@/assets/svg/MingcuteArrowDownFill';
import { MingcuteArrowUpFill } from '@/assets/svg/MingcuteArrowUpFill';

import { SortIconButton } from './styled-components';
import {
  Cell,
  Header,
  HeaderProperty,
  SortButtonProps,
  SortOrder,
  SubCell,
  TableBodyContentProps,
  TableHeaderProps,
  sortOrder,
} from './types';

const { ASC, DESC } = sortOrder;

const SortableButton: React.FC<SortButtonProps> = ({ sortOrder }) => {
  return (
    <SortIconButton sx={{ p: 0 }} size="small">
      <MingcuteArrowUpFill
        className={cn('sort-icon asc', { selected: sortOrder === ASC })}
      />
      <MingcuteArrowDownFill
        className={cn('sort-icon desc', { selected: sortOrder === DESC })}
      />
    </SortIconButton>
  );
};

export const HeaderCell: React.FC<HeaderProperty> = ({
  key,
  cellProps = {},
  header,
  sortFieldName = null,
  sortOptions,
}) => {
  const { onClick = () => {}, ...restCellProps } = cellProps;

  const sortOrder = useMemo<SortOrder | null>(() => {
    if (sortFieldName && sortOptions) {
      const { fieldName, order } = sortOptions;
      return fieldName === sortFieldName ? order : null;
    }
    return null;
  }, [sortFieldName, sortOptions]);

  const onClickTableCell = useCallback(
    (e: React.MouseEvent<HTMLTableCellElement, MouseEvent>) => {
      if (sortFieldName && sortOptions) {
        sortOptions.onSort(sortFieldName, sortOrder === ASC ? DESC : ASC);
      }
      onClick?.(e);
    },
    [onClick, sortFieldName, sortOptions, sortOrder]
  );

  const renderTableHeader = useCallback(() => {
    if (sortFieldName && sortOptions) {
      return (
        <div className="flex justify-between items-center cursor-pointer">
          {header} <SortableButton sortOrder={sortOrder} />
        </div>
      );
    }
    return header;
  }, [header, sortFieldName, sortOptions, sortOrder]);

  return (
    <TableCell
      key={key}
      component="th"
      onClick={onClickTableCell}
      {...restCellProps}
    >
      {renderTableHeader()}
    </TableCell>
  );
};

export const SubHeaderCell = ({ header }: { header: Header }) => {
  const theme = useTheme();
  const dimWhiteColor = alpha(theme.palette.common.white, 0.4);

  const { cellProps = {} } = header;
  const { style, sx, ...rest } = cellProps;

  return (
    <TableCell
      style={{ padding: 0, ...style }}
      sx={{ ...sx }}
      colSpan={header?.subHeaders?.length}
      {...rest}
    >
      <TableRow>
        <TableCell
          colSpan={header?.subHeaders?.length}
          align="center"
          style={{
            paddingTop: 5,
            border: `1px solid ${dimWhiteColor}`,
            paddingBottom: 5,
          }}
        >
          {header.header}
        </TableCell>
      </TableRow>
      {header?.subHeaders?.map((subHeader) => {
        const { cellProps = {} } = subHeader;
        const { style, sx, ...rest } = cellProps;
        return (
          <TableCell
            key={subHeader.key}
            role="row"
            style={{
              border: `1px solid ${dimWhiteColor}`,
              paddingTop: 5,
              paddingBottom: 5,
              ...style,
            }}
            sx={{ ...sx }}
            {...rest}
          >
            {subHeader.header}
          </TableCell>
        );
      })}
    </TableCell>
  );
};

export const TableHeader: React.FC<TableHeaderProps> = ({
  headers,
  sortOptions,
}) => {
  return (
    <TableRow>
      {headers.map((header) =>
        header.subHeaders ? (
          <SubHeaderCell key={header.key} header={header} />
        ) : (
          <HeaderCell
            key={header.key}
            cellProps={header.cellProps}
            header={header.header}
            sortFieldName={header.sortFieldName}
            sortOptions={sortOptions}
          />
        )
      )}
    </TableRow>
  );
};

export const TableBodyContent: React.FC<TableBodyContentProps> = ({
  headers,
  rows,
  loading,
  noDataMessage = null,
}) => {
  if (loading) {
    return (
      <TableRow>
        <TableCell
          colSpan={headers.length}
          align="center"
          sx={{ height: '100px !important' }}
        >
          <Loading />
        </TableCell>
      </TableRow>
    );
  }
  if (rows.length === 0) {
    return (
      <TableRow>
        <TableCell
          colSpan={headers.length}
          align="center"
          sx={{ height: '100px !important' }}
        >
          {noDataMessage}
        </TableCell>
      </TableRow>
    );
  }

  return (
    <>
      {rows.map((row, rowIndex) => (
        <TableRow key={row?.key ? (row?.key as string) : `row-${rowIndex}`}>
          {headers.map((header) => {
            if (header.subHeaders) {
              return header.subHeaders.map((subHeader) => {
                const cell = (row[header.key] as SubCell)?.[subHeader.key]
                  ?.value;
                return (
                  <TableCell key={`${rowIndex}-${header.key}-${subHeader.key}`}>
                    {cell ?? ''}
                  </TableCell>
                );
              });
            }

            const cell = row[header.key] as Cell;
            if (cell?.returnNullForEmpty && !cell?.value) {
              return null;
            }
            return (
              <TableCell
                key={`${header.key}-${rowIndex}`}
                {...cell?.cellProps}
                rowSpan={
                  typeof cell?.cellProps?.rowSpan === 'number'
                    ? cell.cellProps.rowSpan
                    : undefined
                }
              >
                {cell?.value ?? ''}
              </TableCell>
            );
          })}
        </TableRow>
      ))}
    </>
  );
};
