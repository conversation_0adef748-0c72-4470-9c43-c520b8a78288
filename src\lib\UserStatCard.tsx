import { FC } from 'react';

import { archivo } from '@/utils/fonts';

import { VitalAnalyticsValue } from '@/helpers/vitals';

import { cn } from './utils';

export type UserStatCardInfo = VitalAnalyticsValue;

export interface UserStatCardProps {
  className?: string;
  data?: string | number;
  unit?: string;
  topic?: string;
  info?: UserStatCardInfo;
  size?: 'xs' | 'base';
}

const UserStatCard: FC<UserStatCardProps> = ({
  className,
  data,
  unit,
  topic,
  info,
  size = 'base',
}) => {
  return (
    <div
      className={cn(
        `
          py-2 h-15 xl:h-16  max-w-30 min-w-12 2xl:h-18 pl-1 xl:px-2 pr-0.5 rounded-lg
          border border-[#DAE1E7] bg-white flex
          flex-col gap-1 shadow-custom-xs justify-between
        `,
        archivo.className,
        className
      )}
    >
      <div className="flex">
        <span
          className={`
            ${size == 'xs' ? 'text-base' : 'text-[15px] xl:text-[18px] 2xl:text-[22px]'}
            font-extralight text-nowrap
          `}
        >
          {data || '--'}
        </span>
        &nbsp;
        <span
          title={unit}
          className={`
            ${size == 'xs' ? 'text-base' : 'text-[15px] xl:text-[18px] 2xl:text-[22px]'}
            font-extralight whitespace-nowrap truncate
          `}
        >
          {unit}
        </span>
      </div>

      <div className="flex items-center gap-0.5 w-full">
        <span
          className={`${
            size === 'xs'
              ? 'text-xs'
              : 'text-[10px] xl:text-[11px] 2xl:text-[12px]'
          } font-medium whitespace-nowrap`}
        >
          {topic}
        </span>
        <span className="text-xs whitespace-nowrap flex items-center gap-1">
          {info?.type === 'trend' && info.trend === 'inc' && (
            <span>
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M6.00354 11C8.76504 11 11.0035 8.76152 11.0035 6.00002C11.0035 3.23852 8.76504 1.00002 6.00354 1.00002C3.24204 1.00002 1.00354 3.23852 1.00354 6.00002C1.00354 8.76152 3.24204 11 6.00354 11Z"
                  fill="#06C6A7"
                  stroke="#06C6A7"
                  stroke-width="2"
                  stroke-linejoin="round"
                />
                <path
                  d="M8.25342 6.75018L6.00342 4.50018L3.75342 6.75018"
                  fill="#06C6A7"
                />
                <path
                  d="M8.25342 6.75018L6.00342 4.50018L3.75342 6.75018"
                  stroke="#FCFCFC"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
          )}

          {info?.type === 'trend' && info.trend === 'dec' && (
            <span className="rotate-180">
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M6.00354 11C8.76504 11 11.0035 8.76152 11.0035 6.00002C11.0035 3.23852 8.76504 1.00002 6.00354 1.00002C3.24204 1.00002 1.00354 3.23852 1.00354 6.00002C1.00354 8.76152 3.24204 11 6.00354 11Z"
                  fill="#c63906"
                  stroke="#c63906"
                  strokeWidth="2"
                  strokeLinejoin="round"
                />
                <path
                  d="M8.25342 6.75018L6.00342 4.50018L3.75342 6.75018"
                  fill="#c63906"
                />
                <path
                  d="M8.25342 6.75018L6.00342 4.50018L3.75342 6.75018"
                  stroke="#FCFCFC"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
          )}

          {info?.type === 'trend' && info.trend === 'flat' && (
            <span>
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M6.00354 11C8.76504 11 11.0035 8.76152 11.0035 6.00002C11.0035 3.23852 8.76504 1.00002 6.00354 1.00002C3.24204 1.00002 1.00354 3.23852 1.00354 6.00002C1.00354 8.76152 3.24204 11 6.00354 11Z"
                  fill="#F4C790"
                  stroke="#F4C790"
                  strokeWidth="2"
                  strokeLinejoin="round"
                />
                <path
                  d="M8.25342 6.75018L6.00342 4.50018L3.75342 6.75018"
                  fill="#F4C790"
                />
                <path
                  d="M8.25342 6.75018L6.00342 4.50018L3.75342 6.75018"
                  stroke="#FCFCFC"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
          )}
        </span>
        <span
          title={info?.value}
          className="text-[8px] ] xl:text-[9px] 2xl:text-[11px] truncate"
        >
          {info?.value || '--'}
        </span>
        <span className="bg-[#c63906]"></span>
      </div>
    </div>
  );
};

export default UserStatCard;
