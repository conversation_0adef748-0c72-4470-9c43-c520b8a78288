import React, { FC, useMemo } from 'react';

import { Path } from 'react-hook-form';

import { Theme } from '@mui/material';

import colors from '@/utils/colors';

import { lifestyleQuestionTypes } from '@/constants/lifestyle';

import { LifestyleQuestion, Questions } from '@/emr/types/lifestyle';

import Table from '@/core/components/table';
import { Header, Row } from '@/core/components/table/types';

import RenderRadio from './RenderRadio';
import RenderTimeRange from './RenderTimeRange';
import TableNumberFields from './TableNumberFields';
import TextFieldsWithAdd from './TextFieldsWithAdd';

type Props = {
  questions: Questions[];
  appendName: Path<LifestyleQuestion>;
  isReadOnly?: boolean;
  isMaximized?: boolean;
};

const {
  MULTI_NUMBER,
  MULTI_TEXT,
  MULTI_TIME_RANGE,
  RADIO_GROUP,
  TABLE_HEADER,
  TABLE_ROW,
  TABLE_HEADER_BOLD,
} = lifestyleQuestionTypes;

const LifestyleTable: FC<Props> = ({
  questions,
  appendName,
  isMaximized,
  isReadOnly,
}) => {
  const headers = useMemo<Header[]>(() => {
    const headerRow = questions?.find((q) => q.type === TABLE_HEADER);
    return (
      headerRow?.sub_questions?.map((q) => ({
        header: q.question,
        key: q.headerKey as string,
      })) ?? []
    );
  }, [questions]);

  const body = useMemo(() => {
    return questions
      .map<Row | undefined>((q, mapIndex) => {
        if (!q?.sub_questions || q.type !== TABLE_ROW) return undefined;

        return q.sub_questions.reduce<Row>((acc, question, reducerIndex) => {
          if (question.type === MULTI_TIME_RANGE) {
            return {
              ...acc,
              [question.headerKey as string]: {
                value: (
                  <RenderTimeRange
                    appendName={
                      `${appendName}.${mapIndex}.sub_questions.${reducerIndex}.sub_questions` as Path<LifestyleQuestion>
                    }
                    isMaximized={isMaximized}
                    isReadOnly={isReadOnly}
                  />
                ),
              },
            };
          }
          if (question.type === MULTI_TEXT) {
            return {
              ...acc,
              [question.headerKey as string]: {
                value: (
                  <TextFieldsWithAdd
                    appendName={
                      `${appendName}.${mapIndex}.sub_questions.${reducerIndex}.sub_questions` as Path<LifestyleQuestion>
                    }
                    isMaximized={isMaximized}
                    values={question?.sub_questions as any}
                    isReadOnly={isReadOnly}
                    name={
                      `${appendName}.${mapIndex}.sub_questions` as Path<LifestyleQuestion>
                    }
                  />
                ),
              },
            };
          }
          if (question.type === MULTI_NUMBER) {
            return {
              ...acc,
              [question.headerKey as string]: {
                value: (
                  <TableNumberFields
                    appendName={
                      `${appendName}.${mapIndex}.sub_questions.${reducerIndex}.sub_questions` as Path<LifestyleQuestion>
                    }
                    isMaximized={isMaximized}
                    values={question?.sub_questions as any}
                    isReadOnly={isReadOnly}
                    name={
                      `${appendName}.${mapIndex}` as Path<LifestyleQuestion>
                    }
                  />
                ),
              },
            };
          }
          if (question.type === TABLE_HEADER_BOLD) {
            return {
              ...acc,
              [question.headerKey as string]: {
                value: (
                  <div className="font-semibold">{question?.question}</div>
                ),
              },
            };
          }
          if (question.type === RADIO_GROUP) {
            return {
              ...acc,
              [question.headerKey as string]: {
                value: (
                  <RenderRadio
                    name={
                      `${appendName}.${mapIndex}` as Path<LifestyleQuestion>
                    }
                    isMaximized={isMaximized}
                    isReadOnly={isReadOnly}
                    values={question?.sub_questions as any}
                  />
                ),
                cellProps: { sx: { width: 'fit-content' } },
              },
            };
          }
          return {
            ...acc,
            [question.headerKey as string]: { value: question?.question },
          };
        }, {});
      })
      .filter((row): row is Row => row !== undefined);
  }, [questions, isMaximized, isReadOnly, appendName]);

  return (
    <Table
      headers={headers}
      rows={body}
      tableContainerProps={{ sx: getTableStyle }}
    />
  );
};

export default LifestyleTable;

const getTableStyle = (theme: Theme) => ({
  overflowX: 'auto',

  ['& .MuiTable-root']: {
    minWidth: '100%',
    backgroundColor: colors.common.lightBlue,
    borderCollapse: 'separate',
    borderSpacing: '0 1px',
    overflow: 'auto',
    [theme.breakpoints.down('sm')]: {
      minWidth: '600px',
    },
  },

  ['& .MuiTableCell-head']: {
    fontWeight: 'normal',
    borderRight: '1px solid #ddd',
    padding: 1,
    lineHeight: '1',
    height: 40,
    backgroundColor: colors.common.lightBlue,
    color: 'black',
    borderBottom: '1px solid #637D92',
    fontSize: 16,
    textAlign: 'center' as const,

    ['&:not(:last-child)']: {
      borderRight: '1px solid #637D92',
    },

    ['&:first-child']: {
      position: 'sticky',
      left: 0,
      zIndex: 5,
      backgroundColor: colors.common.lightBlue,
      textAlign: 'left',
    },

    [theme.breakpoints.down('sm')]: {
      fontSize: 14,
    },
  },

  ['& .MuiTableCell-body']: {
    padding: 1,
    lineHeight: '1',
    border: 'none',
    fontSize: 16,
    textAlign: 'center' as const,

    ['&:not(:last-child)']: {
      borderRight: '1px solid #637D92',
    },

    ['&:first-child']: {
      position: 'sticky',
      left: 0,
      zIndex: 5,
      paddingTop: 1,
      backgroundColor: colors.common.lightBlue,
      textAlign: 'left' as const,
    },

    [theme.breakpoints.down('sm')]: {
      fontSize: 14,
    },
  },
});
