import React, { useState } from 'react';

import ArrowDownIcon from '@/assets/svg/common/ArrowDownIcon';
import ArrowUpIcon from '@/assets/svg/common/ArrowUpIcon';

interface AccordionTableProps {
  title: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  className?: string;
}

const AccordionTable: React.FC<AccordionTableProps> = ({
  title,
  children,
  defaultExpanded = false,
  className = '',
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div
        className="flex items-center justify-between py-2 px-4  cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={toggleExpanded}
      >
        <h3 className="text-base font-medium text-[#001926]">{title}</h3>
        <div className="text-gray-500">
          {isExpanded ? (
            <ArrowUpIcon width={20} height={20} />
          ) : (
            <ArrowDownIcon width={20} height={20} />
          )}
        </div>
      </div>

      {/* Content */}
      {isExpanded && <div>{children}</div>}
    </div>
  );
};

export default AccordionTable;
