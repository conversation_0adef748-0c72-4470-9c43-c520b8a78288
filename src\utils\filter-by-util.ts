export const filterBy = {
  ALL: 'all',
  TODAY: 'today',
  LAST_15_DAYS: 'last15Days',
  LAST_30_DAYS: 'last30Days',
  PAST_3_MONTHS: 'past3Months',
  THIS_YEAR: 'thisYear',
  CUSTOM_DATE: 'custom',
} as const;

export type FilterByType = (typeof filterBy)[keyof typeof filterBy];

const {
  CUSTOM_DATE,
  LAST_15_DAYS,
  LAST_30_DAYS,
  PAST_3_MONTHS,
  THIS_YEAR,
  TODAY,
  ALL,
} = filterBy;

export const filterOptions: { key: FilterByType; value: string }[] = [
  { value: 'All', key: ALL },
  { value: 'Today', key: TODAY },
  { value: 'Last 15 Days', key: LAST_15_DAYS },
  { value: 'Last 30 Days', key: LAST_30_DAYS },
  { value: 'Past 3 Months', key: PAST_3_MONTHS },
  { value: 'This Year', key: THIS_YEAR },
  { value: 'Custom Date', key: CUSTOM_DATE },
];
