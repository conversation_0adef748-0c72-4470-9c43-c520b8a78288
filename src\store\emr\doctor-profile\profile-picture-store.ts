import { toast } from 'sonner';
import { create } from 'zustand';

import {
  uploadProfilePicture as apiUploadProfilePicture,
  getProfilePicture as apiGetProfilePicture,
  deleteProfilePicture as apiDeleteProfilePicture,
} from '@/query/emr/doctor-profile/personal-info';

import { getErrorMessage } from '@/utils/error-message';

interface ProfilePictureStore {
  isLoading: boolean;
  isDeleting: boolean;
  isUploading: boolean;
  previewUrl: string | null;
  fetchProfilePicture: (userId: string) => Promise<string | null>;
  uploadProfilePicture: (userId: string, file: File) => Promise<string | null>;
  deleteProfilePicture: (userId: string) => Promise<boolean>;
  onImagePreviewError: () => void;
}

export const useProfilePictureStore = create<ProfilePictureStore>((set) => ({
  isLoading: false,
  isDeleting: false,
  isUploading: false,
  previewUrl: null,

  fetchProfilePicture: async (userId) => {
    set({ isLoading: true });
    try {
      const data = await apiGetProfilePicture(userId);
      set({ previewUrl: data.profilePictureUrl, isLoading: false });
      return data.profilePictureUrl;
    } catch (error: any) {
      console.error(error);
      set({ isLoading: false });
      return null;
    }
  },

  uploadProfilePicture: async (userId, file) => {
    set({ isUploading: true });
    try {
      const formData = new FormData();
      formData.append('doctorId', userId);
      formData.append('file', file);
      const data = await apiUploadProfilePicture(formData);
      toast.success('Profile picture uploaded');
      set({ isUploading: false, previewUrl: data.profilePictureUrl });
      return data.profilePictureUrl;
    } catch (error: any) {
      const message = getErrorMessage(
        error,
        'Failed to upload profile picture'
      );
      toast.error(message);
      set({ isUploading: false });
      return null;
    }
  },

  deleteProfilePicture: async (userId) => {
    set({ isDeleting: true });
    try {
      await apiDeleteProfilePicture(userId);
      toast.success('Profile picture deleted');
      set({ isDeleting: false, previewUrl: null });
      return true;
    } catch (error: any) {
      const message = getErrorMessage(
        error,
        'Failed to delete profile picture'
      );
      toast.error(message);
      set({ isDeleting: false });
      return false;
    }
  },

  onImagePreviewError: () => {
    set({ previewUrl: null });
  },
}));
