import React, { FC, memo } from 'react';

import { Paper, Typography } from '@mui/material';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import ExpandSimpleIcon from '@/assets/svg/ExpandSimpleIcon';

import AppIconButton from '@/core/components/app-icon-button';
import { PrescriptionHistory } from '@/types/emr/prescription';

type Props = PrescriptionHistory & {
  onExpand?: () => void;
};

const HistoryCard: FC<Props> = ({
  id,
  updated_on,
  doctor,
  status,
  onExpand,
}) => {
  return (
    <Paper key={id} elevation={2} className="flex px-4 py-1 items-center">
      <Typography className="flex-1 text-left">
        {formatDate(updated_on, DateFormats.DATE_DD_MM_YYYY_SLASH)}
      </Typography>
      <Typography className="flex-1 text-left">Dr. {doctor}</Typography>
      <Typography className="flex-1 text-left font-medium text-black">
        {status || 'Unknown'}
      </Typography>
      <div className="ml-auto">
        <AppIconButton onClick={onExpand} variant="outlined">
          <ExpandSimpleIcon />
        </AppIconButton>
      </div>
    </Paper>
  );
};

export default memo(HistoryCard);
