import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { MobileMenuType } from '@/utils/constants/mobileMenu';

export type MobileViewType = MobileMenuType | string;

interface MenuStore {
  isMenuOpen: boolean;
  isProfileMenu: boolean;
  isFromMenu: boolean;
  isMenuClick: boolean;
  anchorEl: HTMLElement | null;
  mobileMenuPage: MobileViewType;
  selectedRoute: string;
  isNavigating: boolean;
  isPageLoading: boolean;
  isFromProfile: boolean;
  setIsMenuOpen: (_isOpen: boolean) => void;
  setIsFromMenu: (_isFromMenu: boolean) => void;
  setIsFromProfile: (_isFromProfile: boolean) => void;

  setIsProfileMenu: (_isOpen: boolean) => void;
  setAnchorEl: (_element: HTMLElement | null) => void;
  setMobileMenuPage: (_page: MobileViewType) => void;
  setSelectedRoute: (_route: string) => void;
  setMenuClick: (_click: boolean) => void;
  setIsNavigating: (_value: boolean) => void;
  setIsPageLoading: (_value: boolean) => void;
}

export const useMobileMenuStore = create<MenuStore>()(
  persist(
    (set) => ({
      isMenuOpen: false,
      isProfileMenu: false,
      anchorEl: null,
      mobileMenuPage: '',
      selectedRoute: '',
      isFromMenu: false,
      isMenuClick: false,
      isNavigating: false,
      isPageLoading: false,
      isFromProfile: false,
      setIsFromProfile: (isFromProfile) => set({ isFromProfile }),
      setIsMenuOpen: (isOpen) => set({ isMenuOpen: isOpen }),
      setIsProfileMenu: (isOpen) => set({ isProfileMenu: isOpen }),
      setAnchorEl: (element) => set({ anchorEl: element }),
      setMobileMenuPage: (page) => set({ mobileMenuPage: page }),
      setSelectedRoute: (route) => set({ selectedRoute: route }),
      setIsFromMenu: (isFromMenu) => set({ isFromMenu: isFromMenu }),
      setMenuClick: (isClick) => set({ isMenuClick: isClick }),
      setIsNavigating: (value) =>
        set((state) => ({ ...state, isNavigating: value })),
      setIsPageLoading: (value) =>
        set((state) => ({ ...state, isPageLoading: value })),
    }),
    {
      name: 'mobile-menu-storage',
      partialize: (state) => ({
        isMenuOpen: state.isMenuOpen,
        isProfileMenu: state.isProfileMenu,
        mobileMenuPage: state.mobileMenuPage,
      }),
    }
  )
);
