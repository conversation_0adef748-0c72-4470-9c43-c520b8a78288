import { useState } from 'react';

import Script from 'next/script';

import { useUserStore } from '@/store/userStore';

import './styles.scss';

export default function Chat() {
  const [webChatStarted, setWebChatStarted] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const docWindow = window as any;

  const { data: userData } = useUserStore();

  async function startChat() {
    if (webChatStarted) {
      return;
    }

    if (!docWindow.WebChat) {
      return;
    }

    const myHeaders = new Headers();
    myHeaders.append('Content-Type', 'application/json');
    const userdata = {
      user: {
        id: userData.email,
        name: userData.name,
      },
    };
    const userId = userdata.user.id;
    const username = userdata.user.name;
    const raw = JSON.stringify(userdata);
    const res = await fetch(
      'https://emr-dev-botfw.azurewebsites.net/api/directline/token',
      { method: 'POST', headers: myHeaders, body: raw }
    );
    const { token } = await res.json();

    docWindow.WebChat.renderWebChat(
      {
        directLine: docWindow.WebChat.createDirectLine({ token }),
        userID: userId,
        username: username ?? 'User',
        styleOptions: {
          botAvatarInitials: 'Bot',
          userAvatarInitials: username?.charAt(0).toUpperCase() ?? 'U',
        },
      },
      document.getElementById('webchat')
    );

    setWebChatStarted(true);
  }

  // Function to toggle the chat window visibility
  function toggleChat() {
    const chatContainer = document.getElementById('chat-container');

    if (
      chatContainer?.style.display === 'none' ||
      chatContainer?.style.display === ''
    ) {
      chatContainer.style.display = 'block';
      chatContainer.style.width = '25%';
      chatContainer.style.height = '70%';
      const chatIcon = document.getElementById('chat-icon');

      if (chatIcon) {
        chatIcon.style.display = 'none';
      }

      startChat();
    }
  }

  // Function to minimize the chat window
  function minimizeChat() {
    const chatContainer = document.getElementById('chat-container');

    if (!chatContainer) {
      return;
    }

    chatContainer.style.display = 'none';

    const chatIcon = document.getElementById('chat-icon');

    if (chatIcon) {
      chatIcon.style.display = 'flex';
    }
  }

  // Function to maximize or restore the chat window to 1/2 of the screen
  function toggleMaximize() {
    const chatContainer = document.getElementById('chat-container');
    const maximizeBtn = document.getElementById('maximize-btn');

    if (!chatContainer || !maximizeBtn) {
      return;
    }

    if (isMaximized) {
      chatContainer.style.width = '25%';
      chatContainer.style.height = '70%';
      maximizeBtn.textContent = '⬜';
    } else {
      chatContainer.style.width = '50%';
      chatContainer.style.height = '50%';
      maximizeBtn.textContent = '🗗';
    }

    setIsMaximized(!isMaximized);
  }

  return (
    <div>
      <Script src="https://cdn.botframework.com/botframework-webchat/latest/webchat.js" />

      <div id="chat-icon" onClick={toggleChat}>
        💬
      </div>

      <div id="chat-container">
        <div className="header">
          <button onClick={minimizeChat}>_</button>
          <button id="maximize-btn" onClick={toggleMaximize}>
            ⬜
          </button>
        </div>
        <div id="webchat" role="main"></div>
      </div>
    </div>
  );
}
