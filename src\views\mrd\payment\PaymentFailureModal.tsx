import React, { memo } from 'react';
import { ReactNode } from 'react';

import { AlertIcon } from '@/assets/svg/AlertIcon';

import AppButton from '@/core/components/app-button';
import AppIcon from '@/core/components/app-icon';
import AppModal from '@/core/components/app-modal';
import { SuccessIcon } from '@/core/components/status-modal/modal-icons';

interface PaymentFailureModalProps {
  open: boolean;
  onClose: () => void;
  onRetry?: () => void;
  errorMessage?: ReactNode;
  showCloseButton?: boolean;
  isSuccess?: boolean;
}

const PaymentFailureModal: React.FC<PaymentFailureModalProps> = ({
  open,
  onClose,
  onRetry,
  errorMessage = 'Payment failed. Please try again.',
  showCloseButton = true,
  isSuccess = false,
}) => {
  return (
    <AppModal
      open={open}
      onClose={onClose}
      classes={{
        root: 'w-[350px] !rounded-3xl relative',
        modal: '!rounded-3xl',
        header: 'hidden',
      }}
    >
      <>
        {showCloseButton && !isSuccess && (
          <AppButton
            variant="text"
            className="absolute top-0 left-75 !p-1 !min-w-0"
            onClick={onClose}
          >
            <AppIcon icon="ic:round-close" />
          </AppButton>
        )}
        <div className="px-6 pb-4 pt-0 text-center">
          <div className="flex justify-center mb-4">
            <div className="scale-75">
              {isSuccess ? <SuccessIcon /> : <AlertIcon />}
            </div>
          </div>
          <p className="text-base text-[#001926] mb-2">{errorMessage}</p>
        </div>
      </>
    </AppModal>
  );
};

export default memo(PaymentFailureModal);
