import React, { memo } from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import TextInput, { TextInputProps } from '@core/components/text-input';

type Props<T extends FieldValues> = TextInputProps & {
  control: Control<T>;
  name: Path<T>;
};

const ControlledTextField = <T extends FieldValues>({
  control,
  name,
  ...rest
}: Props<T>) => {
  return (
    <Controller<T>
      name={name}
      control={control}
      render={({ field }) => <TextInput {...rest} {...field} />}
    />
  );
};

export default memo(ControlledTextField) as typeof ControlledTextField;
