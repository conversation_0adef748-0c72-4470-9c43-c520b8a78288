'use client';

import { ReactNode, useEffect } from 'react';

import { useRouter, usePathname } from 'next/navigation';

import usePermission from '@/hooks/use-permission';

import { routes } from '@/constants/routes';

import navItems from '@/core/configs/nav-items';

export default function DepartmentGuard({
  children,
}: Readonly<{ children: ReactNode }>) {
  const router = useRouter();
  const pathname = usePathname();

  const { hasDepartment } = usePermission();

  useEffect(() => {
    const currentNav = navItems.find((nav) => pathname.startsWith(nav.path));
    if (currentNav?.department) {
      const hasAccess = hasDepartment(currentNav.department);
      if (!hasAccess) {
        router.push(routes.EMR_PATIENT_INFO);
      }
    }
  }, [pathname, hasDepartment, router]);

  return <>{children}</>;
}
