import { memo } from 'react';

import DatePicker, { DatePickerProps } from '@/core/components/date-picker';

type Props = DatePickerProps;

const TableDatePicker = ({ sx, disabled, isNotValid, ...rest }: Props) => {
  return (
    <DatePicker
      disableInput={disabled}
      disabled={disabled}
      sx={{
        width: '100%',
        height: '100%',
        padding: 0,
        '& .MuiInputBase-root': {
          border: 'none',
          fontSize: '0.8rem',
          ...(isNotValid && {
            color: 'red',
          }),
          '&.Mui-disabled': {
            backgroundColor: 'rgb(229 231 235 / var(--tw-bg-opacity))',
            color: 'black !important',
            opacity: '1 !important',
          },
        },
        '& .MuiOutlinedInput-root': {
          '& fieldset': { border: 'none' },

          '& .Mui-disabled': {
            '-webkit-text-fill-color': 'black !important',
          },
        },
        '& .MuiInputBase-input::placeholder': {
          fontSize: '0.8rem',
        },
        '& .MuiSvgIcon-root': {
          fontSize: '1.2rem',
        },
        ...sx,
      }}
      showHelperText={false}
      {...rest}
    />
  );
};

export default memo(TableDatePicker);
