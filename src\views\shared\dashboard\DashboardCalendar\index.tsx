import React, { useState } from 'react';

import { Box } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/en-gb'; // Import locale for Monday start
import { MdChevronLeft, MdChevronRight } from 'react-icons/md';

import {
  CalendarContainer,
  CalendarTitle,
  NavigationButton,
  MonthYearDisplay,
  CalendarWrapper,
  DateCalendarStyled,
} from './styled-components';

const DashboardCalendar: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(dayjs());
  const [view, setView] = useState<'day' | 'month' | 'year'>('day');

  const handleDateChange = (date: Dayjs | null) => {
    setSelectedDate(date);
    setView('day');
  };

  const currentYear = dayjs().year();
  const isCurrentYear = selectedDate && selectedDate.year() === currentYear;

  const handleMonthYearClick = () => {
    setView('year');
  };

  const handlePrevMonth = () => {
    if (selectedDate) {
      const newDate = selectedDate.subtract(1, 'month');
      setSelectedDate(newDate);
    }
  };

  const handleNextMonth = () => {
    if (selectedDate) {
      const newDate = selectedDate.add(1, 'month');
      setSelectedDate(newDate);
    }
  };

  const CustomCalendarHeader = () => (
    <Box sx={{ px: 4 }}>
      <CalendarTitle variant="h6">Calendar</CalendarTitle>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 2,
        }}
      >
        <NavigationButton onClick={handlePrevMonth}>
          <MdChevronLeft />
        </NavigationButton>
        <MonthYearDisplay variant="subtitle1" onClick={handleMonthYearClick}>
          {selectedDate ? selectedDate.format('MMMM') : ''}{' '}
          {isCurrentYear && selectedDate ? (
            <span style={{ fontWeight: 'bold' }}>
              {selectedDate.format('YYYY')}
            </span>
          ) : selectedDate ? (
            selectedDate.format('YYYY')
          ) : (
            ''
          )}
        </MonthYearDisplay>
        <NavigationButton onClick={handleNextMonth}>
          <MdChevronRight />
        </NavigationButton>
      </Box>
    </Box>
  );

  return (
    <CalendarContainer>
      <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="en-gb">
        <CalendarWrapper>
          <DateCalendarStyled
            value={selectedDate}
            onChange={handleDateChange}
            showDaysOutsideCurrentMonth
            openTo={view}
            views={['day', 'month', 'year']}
            view={view}
            onViewChange={(newView) => setView(newView)}
            dayOfWeekFormatter={(date) => date.format('ddd').toUpperCase()}
            slots={{
              calendarHeader: CustomCalendarHeader,
            }}
          />
        </CalendarWrapper>
      </LocalizationProvider>
    </CalendarContainer>
  );
};

export default DashboardCalendar;
