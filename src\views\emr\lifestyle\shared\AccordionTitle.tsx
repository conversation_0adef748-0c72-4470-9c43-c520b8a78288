import React, { FC, memo, useCallback, useMemo } from 'react';

import { cn } from '@/lib/utils';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import AppIcon from '@/core/components/app-icon';
import AppIconButton from '@/core/components/app-icon-button';

type Props = {
  expand?: boolean;
  doctorName?: string;
  finalised?: boolean;
  onExpand?: () => void;
  onFinalise?: () => void;
  onPrint?: () => void;
  open?: boolean;
  stepper?: string[];
  designation?: string;
  isAttitude?: boolean;
  department?: string;
  date?: string;
  isKnowledge?: boolean;
};

const AccordionTitle: FC<Props> = ({
  expand,
  doctorName,
  finalised,
  onExpand,
  onFinalise,

  open,
  stepper,
  designation,
  department,
  date,
  isKnowledge,
  isAttitude,
}) => {
  const doctorFirstLetter = useMemo(() => {
    return (
      doctorName
        ?.split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2) || '--'
    );
  }, [doctorName]);

  const formIndicator = useMemo(() => {
    if (!stepper?.length) return null;

    return stepper.map((step, index) => (
      <div
        key={index}
        className="flex items-center gap-1 text-black text-sm whitespace-nowrap"
      >
        <span className="truncate">{step}</span>
        {index < stepper.length - 1 && (
          <AppIcon
            className="text-gray-600 text-sm"
            icon="si:arrow-right-fill"
          />
        )}
      </div>
    ));
  }, [stepper]);

  const renderFields = useCallback((value?: React.ReactNode) => {
    if (!value) return null;
    return (
      <div
        title={typeof value === 'string' ? value : ''}
        className="flex items-center text-[#001926] text-sm font-normal leading-normal break-words"
      >
        {value}
      </div>
    );
  }, []);
  return (
    <>
      <div
        className={`flex items-center  flex-1 min-w-0 ${isKnowledge || isAttitude ? 'gap-13' : 'gap-3'}`}
      >
        <div className="flex items-center gap-2">
          <AppIconButton
            variant="outlined"
            sx={{ backgroundColor: 'white !important' }}
          >
            {expand ? (
              <div className="w-10 h-10 rounded-full bg-transparent flex items-center justify-center text-[#6B7280] font-semibold text-base">
                {doctorFirstLetter}
              </div>
            ) : (
              <AppIcon
                icon="mingcute:down-fill"
                className={cn('!transition-transform !duration-200 text-base', {
                  'transform -rotate-90': !open,
                })}
              />
            )}
          </AppIconButton>

          <div className="flex flex-wrap items-center gap-4">
            <span
              className="font-archivo text-[#001926] leading-normal break-words"
              style={{ fontWeight: 600, fontSize: '16px' }}
            >
              Dr {doctorName}
            </span>
            <div
              className="text-[#001926]  leading-normal "
              style={{ fontWeight: 300, fontSize: '14px' }}
            >
              {designation && renderFields(designation)}
            </div>
          </div>
        </div>

        <div className="flex items-center w-full flex-1 min-w-0 ">
          <div className="flex items-center gap-2 ml-10">
            {department && (
              <div className="text-[#001926] text-sm font-normal leading-normal break-words max-w-[200px]">
                {department}
              </div>
            )}
            <div className="text-[#001926] text-sm font-normal leading-normal ">
              {date &&
                renderFields(
                  formatDate(date, DateFormats.DATE_DD_MM_YYYY_SLASH)
                )}
            </div>
          </div>

          {formIndicator && (
            <div
              className={`flex items-center gap-2 whitespace-nowrap ${isKnowledge || isAttitude ? 'ml-24' : 'ml-10'}`}
            >
              {formIndicator}
            </div>
          )}
        </div>
      </div>
      <div className="flex items-center gap-1 w-31 justify-end">
        <AppIconButton
          onClick={(e) => {
            e.stopPropagation();
            onFinalise?.();
          }}
          disabled={finalised}
          sx={{ backgroundColor: 'white !important' }}
          variant="outlined"
        >
          <AppIcon
            className="text-black p-0.5"
            icon={finalised ? 'gg:lock' : 'qlementine-icons:unlock-16'}
          />
        </AppIconButton>

        <AppIconButton
          onClick={(e) => {
            e.stopPropagation();
            onExpand?.();
          }}
          variant="outlined"
          sx={{ backgroundColor: 'white !important' }}
        >
          <AppIcon
            icon={
              expand
                ? 'stash:shrink-diagonal'
                : 'material-symbols:expand-content'
            }
            className="p-0.5"
          />
        </AppIconButton>
      </div>
    </>
  );
};

export default memo(AccordionTitle);
