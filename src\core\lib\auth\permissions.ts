export type UserPermissions = {
  permissions: string[];
};

export const hasPermission = (permissions: string[], requiredPermission: string): boolean => {
  return permissions.includes(requiredPermission);
};

export const hasAnyPermission = (permissions: string[], requiredPermissions: string[]): boolean => {
  return requiredPermissions.some(permission => hasPermission(permissions, permission));
};

export const hasAllPermissions = (permissions: string[], requiredPermissions: string[]): boolean => {
  return requiredPermissions.every(permission => hasPermission(permissions, permission));
};
