import * as Dialog from '@radix-ui/react-dialog';
import { BiX } from 'react-icons/bi';

const content = {
  patient: {
    name: '<PERSON>',
    id: 'AF5459800',
  },
  summary:
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque suscipit quam et nisi dapibus tempor. Morbi nec tristique ex. Cras a commodo justo. Donec ipsum nunc, lobortis sit amet mi a, molestie bibendum odio. Aliquam ultricies turpis ut ante tincidunt bibendum. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque suscipit quam et nisi dapibus tempor. Morbi nec tristique ex. Cras a commodo justo. Donec ipsum nunc, lobortis sit amet mi a, molestie bibendum odio. Aliquam ultricies turpis ut ante tincidunt bibendum.',
  xray: 'https://images.unsplash.com/photo-1584555684040-bad07f46a21f?q=80&w=2889&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
};

const MedicalReport = () => {
  return (
    <div className="px-2.5 py-2.5 w-full text-[#001926]">
      <div className="pb-2.5 px-2.5 border-b flex items-center justify-between w-full">
        <span className="text-xl font-medium -tracking-[2.2%]">
          Medical Report
        </span>
        <Dialog.Close className="bg-slate-100 rounded-full cursor-pointer">
          <BiX className="text-xl" />
        </Dialog.Close>
      </div>

      <div className="flex justify-between border-b border-[#DAE1E7] m-2.5 pb-2.5">
        <div className="flex flex-col gap-[5px] text-[#001926]">
          <span className="text-xs font-light -tracking-[2.2%]">
            Patient Name
          </span>
          <span className="text-2xl font-medium -tracking-[2.2%]">
            {content.patient.name}
          </span>
        </div>
        <div className="flex flex-col gap-[5px] items-end text-[#001926]">
          <span className="text-xs font-light -tracking-[2.2%]">
            Patient ID
          </span>
          <span className="text-2xl font-medium -tracking-[2.2%]">
            {content.patient.id}
          </span>
        </div>
      </div>

      <div className="flex flex-col justify-start items-start gap-2.5 box-border overflow-y-scroll p-2.5 h-[60vh]">
        <div className="flex flex-col gap-[5px]">
          <div className="font-medium -tracking-[2.2%]">Scan summary</div>
          <p className="text-xs">{content.summary}</p>
          <div className="rounded-lg border overflow-hidden mt-4">
            <img
              src={content.xray}
              alt=""
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
export default MedicalReport;
