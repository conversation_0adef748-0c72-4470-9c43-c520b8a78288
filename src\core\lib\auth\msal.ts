import {
  AuthenticationResult,
  BrowserCacheLocation,
  Configuration,
  EventType,
  LogLevel,
  PublicClientApplication,
} from '@azure/msal-browser';

import API_CONFIG from '@/core/configs/api';

const { CLIENT_ID } = API_CONFIG;

export const b2cPolicies = {
  names: {
    signUpSignIn: 'B2C_1_susi_v2',
    forgotPassword: 'B2C_1_reset_v3',
    editProfile: 'B2C_1_edit_profile_v2',
  },
  authorities: {
    signUpSignIn: {
      authority:
        'https://erm20240520.b2clogin.com/erm20240520.onmicrosoft.com/B2C_1_emrapp',
    },
    forgotPassword: {
      authority:
        'https://erm20240520.b2clogin.com/erm20240520.onmicrosoft.com/B2C_1_reset_v3',
    },
    editProfile: {
      authority:
        'https://erm20240520.b2clogin.com/erm20240520.onmicrosoft.com/b2c_1_edit_profile_v2',
    },
  },
  authorityDomain: 'erm20240520.b2clogin.com',
};

export const Scope = {
  //INFO:
  // We use the client id as the scope to get the access token since we are using
  // our own api.
  // See https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-browser/docs/working-with-b2c.md#acquiring-an-access-token-for-your-own-api
  ApiRead: CLIENT_ID,
} as const;

export const config: Configuration = {
  auth: {
    clientId: CLIENT_ID,
    authority: b2cPolicies.authorities.signUpSignIn.authority,
    knownAuthorities: [b2cPolicies.authorityDomain],
    redirectUri: '/',
    postLogoutRedirectUri: '/login',
  },
  cache: {
    cacheLocation: BrowserCacheLocation.LocalStorage,
    storeAuthStateInCookie: false,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level: any, message: any, containsPii: any) => {
        if (containsPii) {
          return;
        }

        switch (level) {
          case LogLevel.Error:
            console.error(message);
            return;
          case LogLevel.Info:
            console.info(message);
            return;
          case LogLevel.Verbose:
            console.debug(message);
            return;
          case LogLevel.Warning:
            console.warn(message);
            return;
          default:
            return console.log(message);
        }
      },
      logLevel: LogLevel.Error,
    },
  },
};

export const msalInstance = new PublicClientApplication(config);

msalInstance.addEventCallback((event) => {
  if (event.eventType === EventType.LOGIN_SUCCESS) {
    const payload = event.payload as AuthenticationResult;

    msalInstance.setActiveAccount(payload.account);
  }
});
