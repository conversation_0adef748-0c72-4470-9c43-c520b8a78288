import React from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import AppRadio from '@/core/components/app-radio';
import { AppRadioProps } from '@/core/components/app-radio/type';

type Props<T extends FieldValues> = AppRadioProps & {
  name: Path<T>;
  control: Control<T>;
};

const ControlledRadio = <T extends FieldValues>({
  name,
  control,
  ...rest
}: Props<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <AppRadio
          name={name}
          value={field.value}
          onChange={field.onChange}
          errors={fieldState.error}
          {...rest}
        />
      )}
    />
  );
};

export default ControlledRadio;
