import { useEffect, useId, useRef, useState } from 'react';

import { DefaultSuggestionItem } from '@blocknote/core';
import { SuggestionMenuController } from '@blocknote/react';

import { Enum } from '@/lib/types/util';
import { cn } from '@/lib/utils';

import { searchMedicalCodes } from '@/query/medical-codes';

import { archivo } from '@/utils/fonts';

import RichTextEditor, {
  RichTextEditorProps,
  RichTextEditorRef,
} from '../rich_text_editor';

export type EditableTextProps = {
  className?: string;
  contentClassName?: string;
  editable?: boolean;
  bg?: RichTextEditorProps['bg'];
  maxHeight?: RichTextEditorProps['maxHeight'];
  label?: string;
  labelSize?: EditableTextLabelSize;
  labelClassName?: string;
  placeholder?: string;
  defaultValue?: string;
  value?: string;
  onChange?: (newValue: string) => void;
  onBlur?: () => void;
  emptyPlaceholder?: string;
  initialBlockType?: RichTextEditorProps['initialBlockType'];
};

export const EditableTextLabelSize = {
  Normal: 'Normal',
  Small: 'Small',
} as const;

export type EditableTextLabelSize = Enum<typeof EditableTextLabelSize>;

const processHTMLForDisplay = (htmlContent: string): string => {
  if (!htmlContent) return '';

  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlContent, 'text/html');

  const blockGroups = doc.querySelectorAll('.bn-block-group');

  blockGroups.forEach((group) => {
    let currentListCount = 1;
    let inNumberedList = false;

    Array.from(group.children).forEach((blockOuter) => {
      const blockContent = blockOuter.querySelector('.bn-block-content');

      if (!blockContent) return;

      const contentType = blockContent.getAttribute('data-content-type');

      if (contentType === 'numberedListItem') {
        if (!inNumberedList) {
          inNumberedList = true;
          currentListCount = 1;
        }

        blockContent.setAttribute('data-index', currentListCount.toString());
        currentListCount++;
      } else {
        inNumberedList = false;
      }
    });
  });

  return doc.body.innerHTML;
};

export default function EditableText({
  className,
  contentClassName,
  bg = 'colored',
  editable = true,
  label = '',
  labelSize = 'Normal',
  labelClassName = 'font-medium text-sm',
  defaultValue,
  maxHeight,
  placeholder,
  value,
  onChange,
  onBlur,
  emptyPlaceholder = '----',
  initialBlockType,
}: EditableTextProps) {
  const [localValue, setLocalValue] = useState<string>(defaultValue || '');
  const [processedDisplayValue, setProcessedDisplayValue] =
    useState<string>('');
  const inputId = useId();
  const editorRef = useRef<RichTextEditorRef | null>(null);

  const getSuggestions = async (
    query: string = ''
  ): Promise<DefaultSuggestionItem[]> => {
    if (!query) {
      return [];
    }

    const medicalCodes = await searchMedicalCodes(query);

    const editor = editorRef.current?.editor;

    const suggestions: DefaultSuggestionItem[] = medicalCodes.map((item) => ({
      title: item,
      key: 'bullet_list' as const,
      onItemClick: () => {
        if (editor) {
          editor.insertBlocks(
            [
              {
                type: 'bulletListItem',
                content: [
                  {
                    type: 'text',
                    text: item,
                    styles: {},
                  },
                ],
              },
            ],
            editor.getTextCursorPosition().block,
            'before'
          );
        }
      },
    }));

    return suggestions;
  };

  useEffect(() => {
    if (typeof value === 'string') {
      setLocalValue(value);
      setProcessedDisplayValue(processHTMLForDisplay(value));
    }
  }, [value]);

  useEffect(() => {
    if (defaultValue) {
      setLocalValue(defaultValue);
      setProcessedDisplayValue(processHTMLForDisplay(defaultValue));
    }
  }, [defaultValue]);

  return (
    <div className={cn('w-full', className)}>
      {label && (
        <label
          htmlFor={inputId}
          className={cn(
            'block w-full',
            archivo.className,
            labelSize === 'Small' ? 'text-sm' : 'text-sm',
            labelClassName
          )}
        >
          {label}
        </label>
      )}

      {editable ? (
        <RichTextEditor
          bg={bg}
          placeholder={placeholder}
          maxHeight={maxHeight}
          defaultValue={value || defaultValue} // Prioritize controlled value over defaultValue
          ref={editorRef}
          onChange={onChange}
          onBlur={onBlur}
          className={contentClassName}
          initialBlockType={initialBlockType}
        >
          <SuggestionMenuController
            triggerCharacter="/"
            minQueryLength={3}
            getItems={getSuggestions}
          />
        </RichTextEditor>
      ) : (
        <div
          className={cn(
            'rich-text-editor non-editable-content',
            contentClassName
          )}
        >
          {localValue ? (
            <div
              className={cn(
                'text-sm',
                className,
                'break-words whitespace-pre-wrap max-w-full'
              )}
              dangerouslySetInnerHTML={{
                __html: processedDisplayValue || localValue,
              }}
            ></div>
          ) : (
            <p className="text-sm text-gray-500">{emptyPlaceholder}</p>
          )}
        </div>
      )}
    </div>
  );
}
