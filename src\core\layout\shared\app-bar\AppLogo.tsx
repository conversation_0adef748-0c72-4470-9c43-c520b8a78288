import { memo } from 'react';

import Image from 'next/image';
import Link from 'next/link';

import { APP_LOGO } from '@/constants/image-url';

const AppLogo = () => {
  return (
    <Link
      href="/"
      className="flex items-center gap-base max-w-19 overflow-hidden bg-white p-1 px-4 rounded-base"
    >
      <Image
        src={APP_LOGO}
        alt="app-logo"
        height={48}
        width={36}
        draggable={false}
      />
    </Link>
  );
};

export default memo(AppLogo);
