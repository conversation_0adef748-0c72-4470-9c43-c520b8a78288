import { FC, memo } from 'react';

import RenderFields from '@/views/emr/lifestyle/shared/render-fields';

import { FieldGroup } from '@/types/emr/lifestyle/questionnaire';

type Props = {
  formFields: FieldGroup[];
  readonly?: boolean;
  showHeading?: boolean;
  variant?: 'modal' | 'timeline';
};

const AttitudeForm: FC<Props> = ({
  formFields,
  readonly,
  variant = 'timeline',
}) => {
  return (
    <div
      className={`space-y-6 ${
        variant === 'modal' ? 'p-6' : 'p-4'
      } ${readonly ? 'pointer-events-none' : ''}`}
    >
      {formFields?.map((section, index) => (
        <div key={section.id || index} className="space-y-6">
          <div>
            <RenderFields
              fields={section?.fields}
              namePrefix={`questions.${index}.fields`}
              readonly={readonly}
              variant={variant}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default memo(AttitudeForm);
