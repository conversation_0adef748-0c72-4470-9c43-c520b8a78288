import { StylesConfig } from 'react-select';

import { Theme } from '@mui/material';

import { AppAsyncSearchOption } from '.';

export const getStyles = <T extends AppAsyncSearchOption>(
  theme: Theme
): StylesConfig<T, false> => ({
  placeholder: (base) => ({
    ...base,
    color: theme.palette.text.secondary,
    opacity: 0.8,
  }),
  control: (base) => ({
    ...base,
    minHeight: 40,
    height: 40,
    borderColor: theme.palette.divider,
    boxShadow: 'none',
    borderRadius: theme.shape.borderRadius,
    '&:hover': {
      borderColor: theme.palette.primary.main,
    },
  }),
  valueContainer: (base) => ({
    ...base,
    padding: '0 10px',
    height: 40,
  }),
  indicatorsContainer: (base) => ({
    ...base,
    height: 40,
  }),
  dropdownIndicator: (base) => ({
    ...base,
    padding: '4px',
    paddingRight: '8px',
  }),
  input: (base) => ({
    ...base,
    margin: '0',
    padding: '0',
  }),
  menuPortal: (base) => ({
    ...base,
    zIndex: 10000,
  }),
});
