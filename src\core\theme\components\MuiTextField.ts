import { ComponentSettings, OverrideComponent } from '@core/theme/types';
import { getCSSVar } from '@core/theme/utils';

const MuiTextField = ({
  palette,
}: ComponentSettings): OverrideComponent['MuiTextField'] => ({
  styleOverrides: {
    root: {
      '& .MuiInputBase-root': {
        borderRadius: getCSSVar('--radius', '0.5rem'),
        '&.Mui-error .MuiOutlinedInput-notchedOutline': {
          borderColor: `${palette.error.main} !important`,
          borderWidth: '2px',
        },
        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
          borderColor: `${palette.primary.main} !important`,
          borderWidth: '2px',
        },
        '&:hover .MuiOutlinedInput-notchedOutline': {
          borderColor: palette.primary.main,
        },
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: palette.divider,
          borderWidth: '2px',
        },
      },
    },
  },
});

export default MuiTextField;
