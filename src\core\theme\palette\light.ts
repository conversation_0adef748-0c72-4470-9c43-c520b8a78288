import { darken, lighten } from '@mui/material';
import createPalette, {
  PaletteOptions,
} from '@mui/material/styles/createPalette';

import { accentColors, chartColors, lightColors } from '@core/theme/colors';

const lightPaletteColor: PaletteOptions = {
  mode: 'light',
  primary: {
    main: lightColors.primary,
    contrastText: lightColors.primaryForeground,
    light: lighten(lightColors.primary, 0.5),
    dark: darken(lightColors.primary, 0.5),
  },
  secondary: {
    main: lightColors.secondary,
    contrastText: lightColors.secondaryForeground,
    light: lightColors.secondaryLight,
    dark: darken(lightColors.secondary, 0.5),
  },
  error: {
    main: lightColors.destructive,
    contrastText: lightColors.destructiveForeground,
    light: lighten(lightColors.destructive, 0.5),
    dark: darken(lightColors.destructive, 0.5),
  },
  background: {
    default: lightColors.background,
    paper: lightColors.card,
  },
  text: {
    primary: lightColors.foreground,
    secondary: lightColors.mutedForeground,
    disabled: lightColors.disabledText,
  },
  divider: lightColors.border,
  accent: {
    pink: accentColors.pink,
    green: accentColors.green,
    main: lightColors.accent,
    contrastText: lightColors.accentForeground,
  },
  chart: chartColors.light,
};

export const lightPalette = createPalette(lightPaletteColor);
