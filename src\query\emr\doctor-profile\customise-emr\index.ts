import { EmrTypes } from '@/store/emr/doctor-profile/customise-emr';

import { arcaAxios } from '@/core/lib/interceptor';

export const getCustomiseEmrDropdownOptions = async (source: string) => {
  return await arcaAxios.get(`/customise-emr?source_name=${source}`);
};

export const createCustomiseEmr = async (data: EmrTypes, doctorId: string) => {
  return await arcaAxios.post(
    `/doctor/customise-emr?doctorId=${doctorId}`,
    data
  );
};

export const getCustomiseEmr = async (doctorId: string) => {
  return await arcaAxios.get(`/doctor/customise-emr?doctorId=${doctorId}`);
};

export const updateCustomiseEmr = async (
  id: string,
  data: EmrTypes,
  _p0: { signal: AbortSignal }
) => {
  return await arcaAxios.patch(`/doctor/customise-emr?id=${id}`, data);
};
