import { useState, useCallback, useRef } from 'react';

import { toast } from 'sonner';

import { uploadTestResult } from '@/query/emr/lab';

export const MAX_FILE_SIZE = 5 * 1024 * 1024;
export const ACCEPTED_FILE_TYPES = [
  'application/pdf',
  'image/jpeg',
  'image/png',
  'image/jpg',
];
export const ACCEPTED_FILE_EXTENSIONS = '.pdf,.jpg,.jpeg,.png';

interface UseFileUploadProps {
  onUploadSuccess?: () => void;
  onClose?: () => void;
  resetFormValues?: () => void;
}

interface FileUploadState {
  selectedFiles: File[];
  isDragging: boolean;
  isUploading: boolean;
  error: string | null;
  imagePreviews: (string | null)[];
}

export function useFileUpload({
  onUploadSuccess,
  onClose,
  resetFormValues,
}: UseFileUploadProps = {}) {
  const [state, setState] = useState<FileUploadState>({
    selectedFiles: [],
    isDragging: false,
    isUploading: false,
    error: null,
    imagePreviews: [],
  });

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const resetState = useCallback(() => {
    setState({
      selectedFiles: [],
      isDragging: false,
      isUploading: false,
      error: null,
      imagePreviews: [],
    });

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    if (resetFormValues) {
      resetFormValues();
    }
  }, [resetFormValues]);

  const handleClose = useCallback(() => {
    resetState();
    if (onClose) {
      onClose();
    }
  }, [onClose, resetState]);

  const handleFileSelect = useCallback((files: FileList | File[]) => {
    let validFiles: File[] = [];
    let previews: (string | null)[] = [];
    let error: string | null = null;
    Array.from(files).forEach((file) => {
      if (!ACCEPTED_FILE_TYPES.includes(file.type)) {
        error =
          'Invalid file type. Please upload PDF, JPG, JPEG or PNG files only.';
        return;
      }
      if (file.size > MAX_FILE_SIZE) {
        error = 'File size exceeds the limit of 5MB.';
        return;
      }
      validFiles.push(file);
      if (file.type.startsWith('image/')) {
        previews.push(URL.createObjectURL(file));
      } else {
        previews.push(null);
      }
    });
    setState((prev) => ({
      ...prev,
      selectedFiles: [...prev.selectedFiles, ...validFiles],
      imagePreviews: [...prev.imagePreviews, ...previews],
      error,
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const handleRemoveFileByIndex = useCallback((index: number) => {
    setState((prev) => {
      const newFiles = [...prev.selectedFiles];
      const newPreviews = [...prev.imagePreviews];
      newFiles.splice(index, 1);
      newPreviews.splice(index, 1);
      return {
        ...prev,
        selectedFiles: newFiles,
        imagePreviews: newPreviews,
        error: null,
      };
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const handleRemoveFile = useCallback((e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    setState((prev) => ({
      ...prev,
      selectedFiles: [],
      imagePreviews: [],
      error: null,
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setState((prev) => ({ ...prev, isDragging: true }));
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setState((prev) => ({ ...prev, isDragging: false }));
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setState((prev) => ({ ...prev, isDragging: false }));
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFileSelect(files);
      }
    },
    [handleFileSelect]
  );

  const handleUpload = useCallback(
    async (additionalData?: Record<string, any>) => {
      if (
        !state.selectedFiles.length &&
        !additionalData?.fileIdsToRemove?.length
      )
        return;
      try {
        setState((prev) => ({ ...prev, isUploading: true }));
        const formData = new FormData();

        // Only append files if there are new files to upload
        state.selectedFiles.forEach((file) => {
          formData.append('files', file);
        });

        // Append all additional data including fileIdsToRemove if present
        if (additionalData) {
          Object.entries(additionalData).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
              if (key === 'fileIdsToRemove' && Array.isArray(value)) {
                // Handle array of file IDs to remove
                formData.append('fileIdsToRemove', JSON.stringify(value));
              } else {
                formData.append(key, value);
              }
            }
          });
        }

        await uploadTestResult(formData);
        toast.success('Test result uploaded successfully');
        resetState();
        if (onUploadSuccess) {
          onUploadSuccess();
        }
        if (onClose) {
          onClose();
        }
      } catch (error) {
        console.error('Error uploading file:', error);
        toast.error('Failed to upload file. Please try again.');
      } finally {
        setState((prev) => ({ ...prev, isUploading: false }));
      }
    },
    [state.selectedFiles, onUploadSuccess, onClose, resetState]
  );

  const setIsDragging = useCallback((isDragging: boolean) => {
    setState((prev) => ({ ...prev, isDragging }));
  }, []);

  return {
    selectedFiles: state.selectedFiles,
    isDragging: state.isDragging,
    isUploading: state.isUploading,
    error: state.error,
    imagePreviews: state.imagePreviews,
    fileInputRef,
    handleFileSelect,
    handleRemoveFile,
    handleRemoveFileByIndex,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleUpload,
    handleClose,
    resetState,
    setIsDragging,
  };
}

export default useFileUpload;
