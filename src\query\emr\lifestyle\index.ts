import { useCurrentPatientStore } from '@/store/currentPatientStore';

import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import { arcaAxios } from '@/core/lib/interceptor';
import {
  Questionnaire,
  QuestionnaireResponse,
} from '@/types/emr/lifestyle/questionnaire';

const controllers = new Map<string, AbortController>();

export const getLifestyleQuestions = async (
  lifestyleSource: LifestyleSources
): Promise<Questionnaire> => {
  const key = `getLifestyleQuestions-${lifestyleSource}`;
  if (controllers.has(key)) {
    controllers.get(key)!.abort();
  }
  const controller = new AbortController();
  controllers.set(key, controller);

  try {
    const response = await arcaAxios.get<Questionnaire[]>(
      `/lifestyle/question?source=${lifestyleSource}`,
      {
        signal: controller.signal,
      }
    );
    controllers.delete(key);

    if (response.data && response.data.length > 0) {
      const { questions, source } = response.data[response.data.length - 1];
      return { questions, source };
    } else {
      throw new Error(`No questionnaire data available for ${lifestyleSource}`);
    }
  } catch (error) {
    controllers.delete(key);
    throw error;
  }
};

export const getPatientLifestyle = async (
  source: LifestyleSources,
  fromDate?: string,
  toDate?: string
) => {
  const patientId = useCurrentPatientStore.getState().patient?.id;
  if (!patientId) {
    console.error('No patient ID found for lifestyle data fetch');
    return;
  }

  const params = new URLSearchParams({
    patientId,
    source,
  });

  if (fromDate) params.append('fromDate', fromDate);
  if (toDate) params.append('toDate', toDate);

  try {
    const { data } = await arcaAxios.get(
      `/patient/lifestyle?${params.toString()}`
    );

    const formattedData = data?.reverse();

    return formattedData;
  } catch (error) {
    console.error('Error in getPatientLifestyle API call:', error);
    throw error;
  }
};

export const createLifestyleData = async (data: Record<string, unknown>) => {
  const patientId = useCurrentPatientStore.getState().patient?.id;
  if (!patientId) {
    console.error('No patient ID found for lifestyle data creation');
    return;
  }

  try {
    const response = await arcaAxios.post(
      `/patient/lifestyle?patientId=${patientId}`,
      data
    );

    return response;
  } catch (error) {
    console.error('Error in createLifestyleData API call:', error);
    throw error;
  }
};

export const updateLifestyleData = async (
  {
    doctorDesignation,
    doctorName,
    questions,
    status,
    source,
    doctor,
  }: Partial<QuestionnaireResponse>,
  id: string
) => {
  const payload = {
    doctorDesignation,
    doctorName,
    questions,
    status,
    source,
    doctor,
  };

  try {
    const response = await arcaAxios.patch(
      `/patient/lifestyle?id=${id}`,
      payload
    );

    return response;
  } catch (error: any) {
    console.error(' PATCH request failed:', error);
    console.error(' Error response:', error.response);
    console.error(' Error status:', error.response?.status);
    console.error(' Error data:', error.response?.data);
    throw error;
  }
};
