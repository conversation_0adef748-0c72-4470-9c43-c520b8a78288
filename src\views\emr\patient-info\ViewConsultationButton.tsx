import React, { memo } from 'react';

import { ButtonBase } from '@mui/material';

import { useRouter } from 'next/navigation';

import { cn } from '@/lib/utils';

import { routes } from '@/constants/routes';

interface ViewConsultationButtonProps {
  patientId: string;
  className?: string;
}

const ViewConsultationButton = ({ className }: ViewConsultationButtonProps) => {
  const router = useRouter();

  const handleViewConsultation = () => {
    // Navigate to the consultation page with the source parameter
    router.push(`${routes.EMR_CONSULTATION}?source=patient_search`);
  };

  return (
    <div
      className={cn(
        'text-[#012436] rounded-lg border border-[#012436] bg-white',
        'text-base font-normal whitespace-nowrap', // Updated to font-normal (400) and text-base (16px)
        'flex items-center justify-center min-w-[180px] h-9 rounded-[8px]', // Added explicit height and border radius
        className
      )}
    >
      <ButtonBase
        onClick={handleViewConsultation}
        className="!py-2 !px-2 w-full"
      >
        View Patient Records
      </ButtonBase>
    </div>
  );
};

export default memo(ViewConsultationButton);
