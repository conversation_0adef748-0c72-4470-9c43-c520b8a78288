import { memo } from 'react';

import { IconButton } from '@mui/material';

import { FluentDelete32Filled } from '@/assets/svg/FluentDelete32Filled';

type Props = {
  onRemove?: () => void;
};

const RemoveButton: React.FC<Props> = ({ onRemove }) => {
  return (
    <IconButton onClick={onRemove} size="small" color="error">
      <FluentDelete32Filled width={22} height={22} />
    </IconButton>
  );
};

export default memo(RemoveButton);
