import React, { FC, memo, useState } from 'react';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';
import { useUserStore } from '@/store/userStore';

import { getDoctorName } from '@/utils/constants/lifeStyle';

import {
  lifestyleFormMode,
  renderFormModes,
  RenderFormModesTypes,
} from '@/constants/lifestyle';

import Accordion from '../shared/Accordion';
import FinalizeModal from '../shared/FinalizeModal';

import DietaryAssessmentModal from './DietaryAssessmentModal';
import DietaryAssessmentTranscriptionView from './DietaryAssessmentTranscriptionView';

type Props = {};

const DietaryAssessmentExpandMobileView: FC<Props> = () => {
  const { name } = useUserStore();
  const doctor = useDoctorStore((state) => state.doctorProfile);
  const { closeModal, dataToEdit } = useLifestyleUtilStore();

  const [expand, setExpand] = useState(true);
  const [open, setOpen] = useState(false);
  const [renderForm, setRenderForm] = useState<RenderFormModesTypes>(
    renderFormModes.TRANSCRIPT_MODE
  );

  const toggleExpand = () => {
    setExpand((prev) => !prev);
    closeModal();
  };

  return (
    <div className="mt-5">
      <Accordion
        open={expand}
        onFinalise={() => setOpen(true)}
        designation={doctor?.general?.department}
        doctorName={getDoctorName(doctor?.general?.fullName, name)}
        onExpand={toggleExpand}
        expand={expand}
        showFinaliseButton={dataToEdit?.status === lifestyleFormMode.EDITABLE}
      >
        <div
          style={{ maxHeight: 'calc(100dvh - 25rem)' }}
          className=" relative overflow-y-auto overflow-x-hidden p-2 pb-3 "
        >
          {renderForm === renderFormModes.RECORD_MODE && (
            <DietaryAssessmentTranscriptionView
              setRenderForm={setRenderForm}
              renderForm={renderForm}
            />
          )}
          {renderForm === renderFormModes.TRANSCRIPT_MODE && (
            <>
              <DietaryAssessmentModal
                setRenderForm={setRenderForm}
                renderForm={renderForm}
              />
            </>
          )}
        </div>
      </Accordion>
      <FinalizeModal
        open={open}
        onClose={() => setOpen(false)}
        onFinalize={() => setOpen(false)}
      />
    </div>
  );
};

export default memo(DietaryAssessmentExpandMobileView);
