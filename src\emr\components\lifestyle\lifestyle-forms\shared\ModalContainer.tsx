import { Dispatch, memo, SetStateAction } from 'react';

import { Box } from '@mui/material';
import { MdOutlineSave } from 'react-icons/md';

import Loading from '@/lib/common/loading';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import IconamoonEdit from '@/assets/svg/IconamoonEdit';
import RecordViewIcon from '@/assets/svg/RecordViewIcon';
import TranscriptViewIcon from '@/assets/svg/TranscriptViewIcon';

import {
  lifestyleModes,
  renderFormModes,
  RenderFormModesTypes,
} from '@/constants/lifestyle';

type Props = {
  children: React.ReactNode;
  onSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;
  title: string;
  showTitle?: boolean;
  renderForm?: RenderFormModesTypes;
  showSwitchRecordButton?: boolean;
  showSaveButton?: boolean;
  showEditButton?: boolean;
  setRenderForm?: Dispatch<SetStateAction<RenderFormModesTypes>>;
  loading?: boolean;
};

const ModalContainer: React.FC<Props> = ({
  children,
  onSubmit,
  title,
  showTitle = true,
  renderForm,
  setRenderForm,
  showSwitchRecordButton,
  showSaveButton = true,
  showEditButton = true,
  loading = false,
  ...props
}) => {
  const isMobile = useIsMobile();
  const { formMode: mode, setFormMode } = useLifestyleUtilStore();

  return (
    <form
      {...props}
      className="w-full h-full min-h-full p-2 flex flex-col gap-2 justify-between flex-1 overflow-hidden"
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit && onSubmit(e);
      }}
    >
      {showTitle && (
        <div className="text-xl font-semibold pb-2 border-b-2 h-8 flex items-center">
          {title}
        </div>
      )}

      <div className="flex-1 overflow-y-auto ">
        <div className="flex flex-col gap-4 w-full pr-3 h-full">{children}</div>
      </div>

      <Box
        className={`flex justify-between pt-2 flex-shrink-0 h-12 px-1 sn:px-5 
        ${!isMobile && 'border-t-2'}
    ${isMobile ? 'fixed bottom-19.5 gap-2 left-4.5 py-3 px-2 flex-shrink-0 z-[9999] gap=1 rounded-md  h-14 right-4.5  bg-white border border-[#637D92]' : ''}`}
      >
        {mode === lifestyleModes.VIEW && showEditButton ? (
          <button
            type="button"
            onClick={() => setFormMode?.(lifestyleModes.EDIT)}
            className={`py-2.5 flex-shrink-0 px-3 sn:px-5 border border-black hover:bg-black/5 rounded-full flex items-center gap-[5px] ${
              isMobile ? 'bg-black text-white hover:bg-black/80 text-sm  ' : ''
            }`}
          >
            Edit Record
            <IconamoonEdit
              fontSize={isMobile ? 11 : 18}
              color={isMobile ? 'white' : ''}
            />
          </button>
        ) : (
          ''
        )}

        {mode !== lifestyleModes.VIEW && showSaveButton && (
          <button
            className="py-2.5 flex-shrink-0 px-3 sn:px-5 bg-black text-sm md:text-base  text-white hover:bg-black/80 rounded-full flex items-center gap-[5px] border border-transparent"
            type="submit"
            disabled={loading}
          >
            Save Record {loading ? <Loading /> : <MdOutlineSave size={22} />}
          </button>
        )}
        {showSwitchRecordButton && (
          <>
            {renderForm === renderFormModes.TRANSCRIPT_MODE && (
              <button
                onClick={() => {
                  if (setRenderForm) {
                    setRenderForm(renderFormModes.RECORD_MODE);
                  }
                  setFormMode?.(lifestyleModes.VIEW);
                }}
                className="py-2.5  px-2  sn:px-5 text-sm border border-black hover:bg-black/5 rounded-full flex items-center gap-[5px] leading-none"
              >
                Switch to Transcript View <TranscriptViewIcon fontSize={16} />
              </button>
            )}
            {renderForm === renderFormModes.RECORD_MODE && (
              <button
                onClick={() => {
                  if (setRenderForm) {
                    setRenderForm(renderFormModes.TRANSCRIPT_MODE);
                  }
                  setFormMode?.(lifestyleModes.VIEW);
                }}
                className="py-2.5 px-2 border text-sm border-black hover:bg-black/5 rounded-full flex items-center gap-[5px] leading-none"
              >
                Switch to Record View <RecordViewIcon fontSize={16} />
              </button>
            )}
          </>
        )}
      </Box>
    </form>
  );
};

export default memo(ModalContainer);
