export const formatValue = (value: string) => {
  return value.replace(/\D/g, '');
};

export const formatToNumber = (value: string): string => {
  const num = Number(value.replace(/\D/g, ''));
  if (isNaN(num)) return '0';
  return num.toString();
};

export const formatToNumberWithDot =
  (pos: number = 1) =>
  (value: string) => {
    const trimmed = value.trim();

    if (trimmed === '' || trimmed === '.') return value;

    const match = trimmed.match(/^(\d*)(\.?)(\d*)$/);
    if (!match) return '0';

    const [, intPart, dot, decimalPart] = match;

    if (dot) {
      return `${intPart}${dot}${decimalPart.slice(0, pos)}`;
    }

    return intPart;
  };

export const formatToAadhar = (value: string) => {
  const digits = value.replace(/\D/g, '');
  return digits.match(/.{1,4}/g)?.join('-') ?? digits;
};

export const formatToAbha = (value: string) => {
  const digits = value.replace(/\D/g, '');
  return digits.match(/.{1,4}/g)?.join('-') ?? digits;
};

export const formatToAlphaNumeric = (value: string) => {
  return value.replace(/[^a-zA-Z0-9 /]/g, '');
};

export const formatToAlphabet = (value: string) => {
  return value.replace(/[^a-zA-Z]/g, '');
};
export const formatToAlphaNumericToUpperCase = (value: string) => {
  return value.replace(/[^a-zA-Z0-9\s]/g, '').toUpperCase();
};
