import React, { useCallback, useEffect, useMemo } from 'react';

import { Box, Typography } from '@mui/material';

// Store imports
import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useCustomiseEmrStore } from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useOrganizationStore } from '@/store/organizationStore';
import { useUserStore } from '@/store/userStore';

// Types

// Utils and templates
import { printHtmlContent } from '@/utils/print/printUtils';
import {
  getConsultationHtml,
  consultationStyles,
} from '@/utils/print/templates/consultation-template';

// Components
import {
  formatOrganization,
  formatPatientData,
} from '@/views/emr/prescription/shared/Print/utils';
import PrintModal from '@/views/shared/print/PrintModal';

import type { PatientI } from '@/types';

import {
  FieldContainer,
  ExaminationItem,
  SpecialField,
  HistoryField,
  SystemicExaminationItem,
} from './Components';
import {
  getVitalsData,
  getAnthropometryData,
  getGeneralPhysicalExamData,
  getSystemicExamData,
  getHistoryFields,
  VitalSign,
  ExaminationField,
  SystemicExamination,
} from './Constants';
import { styles } from './PrintStyles';

interface ConsultationTemplateData {
  id: string;
  date: string;
  doctor: string;
  notes?: string;
  diagnosis?: string;
  treatment?: string;
  summary?: any;
}

interface PrintModalProps {
  open: boolean;
  onClose: () => void;
  consultations: any[];
  patient?: PatientI;
  doctorName?: string;
}

// Custom hook for organization data
const useOrganizationData = (open: boolean, organizationId?: string) => {
  const { organization, fetchOrganization } = useOrganizationStore();

  useEffect(() => {
    if (open && organizationId) {
      fetchOrganization(organizationId);
    }
  }, [open, organizationId, fetchOrganization]);

  return { organization };
};

export const renderHtmlContent = (htmlString?: string) => {
  if (!htmlString) {
    return <Typography sx={styles.fieldValue}>Not Recorded</Typography>;
  }
  return (
    <Box
      dangerouslySetInnerHTML={{ __html: htmlString }}
      sx={{
        ...styles.fieldValue,
        '& p': { margin: 0, color: '#000000' },
        '& *': { color: '#000000 !important' },
      }}
    />
  );
};

const PrintPreview: React.FC<PrintModalProps> = ({
  open,
  onClose,
  consultations = [],
  patient,
  doctorName,
}) => {
  // Store data
  const { patient: currentPatient } = useCurrentPatientStore();
  const { data: userData } = useUserStore();
  const { organization } = useOrganizationData(open, userData?.organizationId);
  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();
  const { doctorProfile } = useDoctorStore();

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile.id);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  // Get the most recent customise EMR data
  const mostRecentData = customiseEmrData?.length
    ? [...customiseEmrData].sort(
        (a, b) =>
          new Date(b.updated_on || '').getTime() -
          new Date(a.updated_on || '').getTime()
      )[0]
    : null;

  const consultation = useMemo(() => consultations[0], [consultations]);
  const summary = useMemo(() => consultation?.summary || {}, [consultation]);
  const patientData = formatPatientData(
    patient || currentPatient || undefined,
    consultation || {}
  );
  const formattedOrganization = formatOrganization(organization);

  // Format consultation data for the template
  const consultationData: ConsultationTemplateData[] = consultations.map(
    (consult) => ({
      id: consult.id,
      date: consult.summary?.updated_on || new Date().toISOString(),
      doctor:
        doctorName ||
        consult.summary?.owner?.name ||
        userData?.name ||
        'Doctor',
      notes: [
        consult.summary?.presentingComplaints,
        consult.summary?.pastMedicalHistory,
        consult.summary?.generalPhysicalExamination?.notes,
      ]
        .filter(Boolean)
        .join('\n\n'),
      diagnosis: consult.summary?.historyOfPresenting,
      treatment: consult.summary?.currentMedicationHistory,
      summary: {
        ...consult.summary,
        systemicExamination: consult.summary?.systemicExamination,
      },
    })
  );

  // Get data using the utility functions from Constants
  const vitalsData = useMemo<VitalSign[]>(
    () => getVitalsData(summary),
    [summary]
  );
  const anthropometryData = useMemo<VitalSign[]>(
    () => getAnthropometryData(summary),
    [summary]
  );
  const generalPhysicalExamData = useMemo<ExaminationField[]>(
    () => getGeneralPhysicalExamData(summary),
    [summary]
  );
  const systemicExamData = useMemo<SystemicExamination[]>(
    () => getSystemicExamData(summary),
    [summary]
  );
  const historyFields = useMemo(() => getHistoryFields(summary), [summary]);

  const handlePrint = useCallback(() => {
    if (!consultation || !consultationData.length) return;

    const html = getConsultationHtml({
      consultations: consultationData,
      patient: patientData,
      organization: formattedOrganization,
      doctor: doctorName || userData?.name || 'Doctor',
      date: consultation.updated_on,
      // Add signature and letter head data
      digitalSignature: mostRecentData?.digitalSignature,
      letterHeadDetails: mostRecentData?.letterHeadDetails,
      organizationLogo: mostRecentData?.organizationLogo,
    });

    printHtmlContent(html, {
      title: `Consultation_Report_${patient?.name || ''}_${new Date().toISOString().split('T')[0]}`,
      styles: consultationStyles,
      onAfterPrint: () => {
        console.log('Print completed or was cancelled');
      },
      onError: (error) => {
        console.error('Printing failed:', error);
      },
    });
  }, [
    consultation,
    patientData,
    formattedOrganization,
    userData,
    patient,
    mostRecentData,
  ]);

  // Early return for no consultations
  if (!consultation) {
    return (
      <PrintModal
        open={open}
        onClose={onClose}
        onPrint={handlePrint}
        patient={patientData}
        title="Consultation Print Preview"
        prescriptionTitle="Consultation Summary"
        organization={formattedOrganization}
        date={consultation?.updated_on}
        width="55vw"
      >
        <Box p={2}>
          <Typography sx={{ color: '#000000' }}>
            No consultation data available
          </Typography>
        </Box>
      </PrintModal>
    );
  }

  return (
    <PrintModal
      open={open}
      onClose={onClose}
      onPrint={handlePrint}
      patient={patientData}
      prescriptionTitle="Consultation Summary"
      organization={formattedOrganization}
      date={consultation.updated_on}
      doctor={summary?.owner?.name || userData?.name || 'Doctor'}
      width="55vw"
    >
      <Box sx={styles.container}>
        {/* History Section */}
        <Box sx={styles.section}>
          <Typography sx={styles.sectionTitle}>History</Typography>
          {historyFields.map((field, index) => (
            <HistoryField
              key={index}
              label={field.label}
              content={field.content}
            />
          ))}
        </Box>

        {/* Examination Section */}
        <Box sx={styles.section}>
          <Typography sx={styles.sectionTitle}>Examination</Typography>

          {/* Vital Signs */}
          <Box sx={{ mb: 3 }}>
            <Typography sx={{ ...styles.fieldLabel, mb: 1.5, fontWeight: 700 }}>
              Vital signs
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {vitalsData.map((vital, index) => (
                <FieldContainer key={index} {...vital} />
              ))}
            </Box>
          </Box>

          {/* Anthropometry */}
          <Box sx={{ mb: 3 }}>
            <Typography sx={{ ...styles.fieldLabel, mb: 1.5, fontWeight: 700 }}>
              Anthropometry
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {anthropometryData.map((measurement, index) => (
                <FieldContainer key={index} {...measurement} />
              ))}
            </Box>
          </Box>

          {/* General Physical Examination */}
          <Box sx={{ mb: 3 }}>
            <Typography sx={{ ...styles.fieldLabel, mb: 1.5, fontWeight: 700 }}>
              General physical examination
            </Typography>
            <Box
              sx={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 6,
                alignItems: 'center',
              }}
            >
              {generalPhysicalExamData.map((item, index) => (
                <ExaminationItem key={index} {...item} />
              ))}
            </Box>
          </Box>

          {/* Special Fields */}
          <Box sx={{ mb: 3, display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <SpecialField
              label="Pedal Edema"
              value={summary?.generalPhysicalExamination?.pedalEdema}
              notes={summary?.generalPhysicalExamination?.pedalEdemaNotes}
            />
            <SpecialField
              label="Lymphadenopathy"
              value={summary?.generalPhysicalExamination?.lymphadenopathy}
              notes={summary?.generalPhysicalExamination?.lymphadenopathyNotes}
            />
          </Box>

          {/* HEENT */}
          <Box sx={{ mb: 3 }}>
            <Typography sx={{ ...styles.fieldLabel, mb: 1.5, fontWeight: 700 }}>
              HEENT
            </Typography>
            {renderHtmlContent(summary?.heent)}
          </Box>

          {/* Systemic Examination */}
          <Box sx={{ mb: 3 }}>
            <Typography sx={{ ...styles.fieldLabel, mb: 1.5, fontWeight: 700 }}>
              Systemic Examination
            </Typography>
            {systemicExamData.map(({ key, ...systemProps }) => (
              <SystemicExaminationItem key={key} {...systemProps} />
            ))}
          </Box>
          <Box
            width={100}
            height={41}
            display="flex"
            alignItems="center"
            justifyContent="center"
            mb={1}
          >
            {mostRecentData?.digitalSignature ? (
              <img
                src={mostRecentData.digitalSignature}
                alt="Digital Signature"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                }}
              />
            ) : (
              <Box
                bgcolor="#C2CDD6"
                width="100%"
                height="100%"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Typography variant="caption" color="text.secondary">
                  Signature
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </PrintModal>
  );
};

export default PrintPreview;
