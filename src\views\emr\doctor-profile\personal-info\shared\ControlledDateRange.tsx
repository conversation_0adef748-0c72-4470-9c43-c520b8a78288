import React, { memo } from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import DateRangePicker from '@/core/components/date-range-picker';
import { DateRangePickerProps } from '@/core/components/date-range-picker/types';

type Props<T extends FieldValues> = DateRangePickerProps & {
  control: Control<T>;
  name: Path<T>;
};

const ControlledDateRange = <T extends FieldValues>({
  name,
  control,
  ...rest
}: Props<T>) => {
  return (
    <Controller<T>
      name={name}
      control={control}
      render={({ field }) => <DateRangePicker {...field} {...rest} />}
    />
  );
};

export default memo(ControlledDateRange) as typeof ControlledDateRange;
