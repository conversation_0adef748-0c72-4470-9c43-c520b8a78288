import { TableCellProps } from '@mui/material';

import { DateRange } from '@/core/components/date-range-picker/types';

export interface DoctorInfo {
  general?: GeneralInfo;
  personal?: PersonalInfo;
  emergencyContacts?: EmergencyContact[];
  professionalDetails?: ProfessionalDetails;
  bankDetails?: BankDetails;
  languagesKnown?: LanguageKnown[];
  insurance?: Insurance[];
  researchAndPublications?: ResearchPublication[];
  affiliations?: Affiliation[];
  documents?: Documents;
  id?: string;
  family?: Family[];
  created_by?: string;
  updated_by?: string;
  created_on?: string;
  updated_on?: string;
  _rid?: string;
  _self?: string;
  _etag?: string;
  _attachments?: string;
  _ts?: number;
  username?: string;
}

export interface GeneralInfo {
  fullName?: string;
  designation?: string;
  department?: string;
  doctorID?: string;
  contactNumber?: string;
  workEmail?: string;
  hospitalName?: string;
  workLocation?: string;
}

export interface PersonalInfo {
  age?: number | string;
  bloodGroup?: string;
  height?: number | string;
  weight?: number | string;
  isPersonWithDisability?: string | boolean;
  percentOfDisability?: string | null;
  identificationMark?: string;
  maritalStatus?: string;
  dateOfWedding?: string;
  nationality?: string;
  religion?: string;
  caste?: string;
  otherCaste?: string;
  otherReligion?: string;
  category?: string;
  reservationDetails?: string;
  idProof?: IdProof;
  address?: Address;
  hometownDetails?: {
    hometown: string;
    state: string;
    district: string;
    country: string;
  };
  birthDetails?: {
    placeOfBirth: string;
    state: string;
    district: string;
    country: string;
  };
}

export interface IdProof {
  type?: string;
  number?: string;
  url?: string;
  description?: string;
}

export interface Address {
  permanent?: AddressDetails;
  current?: AddressDetails;
}

export interface AddressDetails {
  home?: string;
  street?: string;
  city?: string;
  pinCode?: string;
  district?: string;
  state?: string;
  country?: string;
  phone?: string;
  email?: string;
  proof?: Proof;
}

export interface Proof {
  description?: string;
  url?: string;
}

export interface Family {
  uuId?: string;
  name?: string;
  relation?: string;
  age?: string;
  occupation?: string;
  dependent?: string;
  dob?: string;
  status?: string;
  aadharNumber?: string;
  documents?: File | string;
}

export interface EmergencyContact {
  name?: string;
  relation?: string;
  city?: string;
  contactNumber?: string;
  email?: string;
}

export interface BankDetails {
  bank: string;
  ifsc: string;
  document: string;
  branch: string;
  accountNumber: string;
  description: string;
}

export interface ProfessionalDetails {
  medicalRegistration?: MedicalRegistration;
  specialties?: string[];
  qualifications?: Qualification[];
  certifications?: Certification[];
  experience?: Experience[];
}

export interface MedicalRegistration {
  councilName?: string;
  registrationNumber?: string;
  validFrom?: string;
  validTo?: string;
  proof?: Proof;
}

export interface Qualification {
  uuId?: string;
  degree?: string;
  specialization?: string;
  university?: string;
  institute?: string;
  yearOfCompletion?: string;
  marks?: string;
  documents?: string[];
  status?: string;
  doc1?: File | string;
  duration?: string;
  doc2?: File | string;
}

export interface Certification {
  uuId?: string;
  name?: string;
  regNumber?: string;
  validFrom?: string;
  validTo?: string;
  dateOfUpdation?: string;
  status?: string;
}

export interface Experience {
  uuId?: string;
  hospitalName?: string;
  department?: string;
  designation?: string;
  from?: string;
  to?: string;
  duration?: DateRange;
  salary?: string;
  documents?: string[];
  status?: string;
  doc1?: File | string;
  doc2?: File | string;
}

export interface LanguageKnown {
  language: string;
  fluency: string[];
}

export interface Insurance {
  uuId?: string;
  policyName?: string;
  policyNumber?: string;
  validFrom?: string;
  validTo?: string;
  coverageAmount?: string;
  status?: string;
  documents?: string[];
}

export interface ResearchPublication {
  title?: string;
  journal?: string;
  publicationDate?: string;
  link?: string;
  status?: string;
}

export interface Affiliation {
  organizationName?: string;
  role?: string;
  from?: string;
  to?: string;
  status?: string;
}

export interface DocumentDetails {
  number?: string;
  name?: string;
  issuedAt?: string | null;
  description?: string;
  url?: string;
  renewedAt?: string | null;
  issuedPlace?: string;
}

export interface Documents {
  aadhar?: DocumentDetails;
  passport?: DocumentDetails;
  panCard?: DocumentDetails;
  medicalLicense?: DocumentDetails;
}

export type ItemToDelete = {
  uuId?: string;
  index: number;
} | null;

export const personalDefaultValues = {
  age: '',
  bloodGroup: '',
  height: '',
  weight: '',
  isPersonWithDisability: 'no',
  percentOfDisability: '',
  identificationMark: '',
  maritalStatus: '',
  dateOfWedding: '',
  nationality: '',
  religion: '',
  caste: '',
  otherCaste: '',
  otherReligion: '',
  category: 'General',
  reservationDetails: '',
  idProof: {
    type: '',
    number: '',
    url: '',
    description: '',
  },
  address: {
    permanent: {
      home: '',
      street: '',
      city: '',
      pinCode: '',
      district: '',
      state: '',
      country: '',
      phone: '',
      email: '',
      proof: {
        description: '',
        url: '',
      },
    },
    current: {
      home: '',
      street: '',
      city: '',
      pinCode: '',
      district: '',
      state: '',
      country: '',
      phone: '',
      email: '',
      proof: {
        description: '',
        url: '',
      },
    },
  },
  hometownDetails: {
    hometown: '',
    state: '',
    district: '',
    country: '',
  },
  birthDetails: {
    placeOfBirth: '',
    state: '',
    district: '',
    country: '',
  },
};

export const defaultInsurance: Insurance = {
  policyName: '',
  policyNumber: '',
  validFrom: '',
  validTo: '',
  status: '',
};

export const defaultQualification: Qualification = {
  degree: '',
  specialization: '',
  university: '',
  institute: '',
  yearOfCompletion: '',
  duration: '',
  marks: '',
  doc1: undefined,
  doc2: undefined,
  status: '',
};

export const defaultCertification: Certification = {
  name: '',
  regNumber: '',
  dateOfUpdation: '',
  validFrom: '',
  validTo: '',
  status: '',
};

export const defaultFamily: Family = {
  name: '',
  relation: '',
  age: '',
  occupation: '',
  dependent: '',
  dob: '',
  status: '',
  aadharNumber: '',
  documents: '',
};

export const defaultExperience: Experience = {
  hospitalName: '',
  department: '',
  designation: '',
  from: '',
  to: '',
  salary: '',
  documents: [],
  doc1: '',
  doc2: '',
  status: '',
  duration: undefined,
};

export const inputCellProps: TableCellProps = {
  sx: { padding: '0px !important', height: '100%' },
};

export const fileCellProps: TableCellProps = {
  sx: {
    padding: '0px !important',
    width: 80,
    minWidth: 80,
    height: 50,
    minHeight: 50,
    maxHeight: 50,
  },
};

export const actionButtonCellProps: TableCellProps = {
  align: 'center',
  sx: {
    minWidth: 25,
    width: 25,
    padding: '0px 5px !important',
    verticalAlign: 'top',
  },
};

export const getInputCellProps = (
  disabled: boolean,
  isNotValid?: boolean
): TableCellProps => ({
  sx: {
    padding: '0px !important',
    height: '100%',
    minHeight: 30,
    verticalAlign: 'top',
    ...(disabled && {
      backgroundColor: 'rgb(229 231 235 / var(--tw-bg-opacity))',
      pointerEvents: 'none',
    }),
    ...(isNotValid && {
      border: '2px solid red !important',
    }),
  },
});

export const getFileCellProps = (disabled: boolean): TableCellProps => ({
  sx: {
    padding: '0px !important',
    width: 80,
    minWidth: 80,
    minHeight: 30,
    maxHeight: 50,
    verticalAlign: 'top',
    ...(disabled && {
      backgroundColor: 'rgb(229 231 235 / var(--tw-bg-opacity))',
    }),
  },
});

export type ProfilePictureResponse = {
  profilePictureUrl: string;
};
