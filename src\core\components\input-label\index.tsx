import React, { FC, memo } from 'react';

export type InputLabelProps = {
  label?: string;
  required?: boolean;
  className?: string;
  indicationLabel?: string;
  error?: boolean;
};

const InputLabel: FC<InputLabelProps> = ({
  className = '',
  label,
  required = false,
  error,
  indicationLabel,
}) => {
  if (!label) return null;

  return (
    <div
      className={`app-input-label flex flex-col whitespace-nowrap mb-0.5 md:mb-1 text-sm md:text-base ${className}`}
    >
      <span
        className={`app-input-label-span flex items-center gap-1 whitespace-nowrap -tracking-[2.2%]`}
      >
        {label}
        {required && (
          <span className="text-red-500 text-xl leading-none">*</span>
        )}
      </span>
      {indicationLabel && (
        <span className={`text-xs ${error ? 'text-red-500' : 'text-gray-500'}`}>
          {indicationLabel}
        </span>
      )}
    </div>
  );
};

export default memo(InputLabel);
