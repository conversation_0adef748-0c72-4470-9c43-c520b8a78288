const colors = {
  common: {
    ashGray: '#DAE1E7',
    skyBlue: '#1AA6F1',
    paleBlue: '#dae1e7',
    aquaGreen: '#06C6A7',
    darkerNavyBlue: '#001926',
    navyBlue: '#012436',
    blue: '#2E5A74',
    charcoalGray: '#5B5B5B',
    red: '#FF0000',
    lightGray: '#EDEDED',
    lightBlue: '#E7EBEF',
    azureBlue: '#367AFF',
    lightSkyBlue: '#E5F3FB',
    mediumGray: '#9A9A9A',
    dark: '#000000',
    white: '#FFFFFF',
    skyBlueLight: '#A2DAF8',
    redRose: '#E4626F',
    paleBlush: '#FFF7F8',
    deepChampagne: '#F4C790',
    mistGray: '#C2CDD6',
  },

  text: {
    slateGray: '#637D92',
    darkerSlateGray: '#323F49',
    steelGray: '#7E8A91',
  },

  status: {
    ready: '#06C6A766',
    awaited: '#FBE9D3',
    notPaid: '#F6C9CD',
    upload: '#FFFFFF',
    uploaded: '#06C6A7',
  },

  sidebar: {
    pink: '#FF7DAC',
    blue: '#012436',
  },
  dashboard: {
    appointments: {
      bgColor: '#E9FBF9',
      borderColor: '#1B998B',
    },
    patientQueue: {
      bgColor: '#F3EFF6',
      borderColor: '#AB92BF',
    },
    avgWaitTime: {
      bgColor: '#FFF2E5',
      borderColor: '#FFAA5A',
    },
    totalPatients: {
      bgColor: '#FAEBEE',
      borderColor: '#BA324F',
    },
  },
};

export default colors;
