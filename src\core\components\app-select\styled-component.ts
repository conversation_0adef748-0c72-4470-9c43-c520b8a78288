import Select from '@mui/material/Select';
import { styled } from '@mui/material/styles';

export const StyledSelect = styled(Select)(({ theme }) => ({
  '&.MuiInputBase-root': {
    height: 40,
    '&.Mui-error .MuiOutlinedInput-notchedOutline': {
      borderColor: `${theme.palette.error.main} !important`,
      borderWidth: '2px',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: `${theme.palette.primary.main} !important`,
      borderWidth: '2px',
    },
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.primary.main,
    },
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.divider,
      borderWidth: '2px',
    },
  },
  '& .MuiSelect-select': {
    padding: '8.5px 14px',
  },
}));
