import React, { FC, memo } from 'react';

import { useLifestyleFilterStore } from '@/store/emr/lifestyle/filter-store';

import { lifestyleFilterOptions } from '@/constants/emr/lifestyle/filter';

import AppButton from '@/core/components/app-button';
import AppSelect from '@/core/components/app-select';

type Props = {
  title: string;
  className?: string;
  hideFilterBy?: boolean;
};

const PageTitle: FC<Props> = ({ title, className = '', hideFilterBy }) => {
  const { filterBy, setFilterBy, clearFilter } = useLifestyleFilterStore();

  return (
    <div className="flex items-center justify-between min-h-13 p-2 pb-0.5 pl-3">
      <span className={`font-semibold text-[17px] ${className}`}>{title}</span>
      {!hideFilterBy && (
        <div className="flex items-center gap-2">
          <AppButton
            onClick={clearFilter}
            variant="text"
            sx={{ minWidth: 120, color: '#2563eb', fontSize: '14px' }}
          >
            Clear Filter
          </AppButton>
          <span className="text-sm text-gray-500 whitespace-nowrap">
            Filter By
          </span>
          <AppSelect
            options={lifestyleFilterOptions}
            value={filterBy}
            onChange={setFilterBy}
            className="w-[160px]"
          />
        </div>
      )}
    </div>
  );
};

export default memo(PageTitle);
