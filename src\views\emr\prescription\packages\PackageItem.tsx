import { memo } from 'react';

import { IconButton } from '@mui/material';
import { FaAngleRight } from 'react-icons/fa6';

import { cn } from '@/lib/utils';

import colors from '@/utils/colors';

type PackageItemProps = {
  label: string;
  isActive: boolean;
  onClick: () => void;
};

const PackageItem: React.FC<PackageItemProps> = ({
  label = '',
  isActive = false,
  onClick,
}) => {
  return (
    <div
      style={{
        backgroundColor: isActive ? colors.common.navyBlue : 'transparent',
      }}
      className={cn(
        'flex items-center justify-between p-0.5 pl-3 cursor-pointer select-none transition-all duration-200',
        {
          'text-white rounded-base': isActive,
          'text-gray-700 hover:bg-gray-50 border-b': !isActive,
        }
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-2">
        <span className="text-sm">{label}</span>
      </div>
      <IconButton size="small">
        <FaAngleRight
          className={cn('text-white opacity-0 transition-all duration-300', {
            'translate-x-[-2rem]': !isActive,
            'opacity-100 translate-x-0': isActive,
          })}
        />
      </IconButton>
    </div>
  );
};

export default memo(PackageItem);
