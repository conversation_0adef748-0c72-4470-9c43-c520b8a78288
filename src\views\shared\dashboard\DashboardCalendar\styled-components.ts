import { Box, IconButton, Typography, styled } from '@mui/material';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';

// Styled Components
export const CalendarContainer = styled('div')({
  backgroundColor: 'white',
  borderRadius: '12px',
  overflow: 'hidden',
  border: '1px solid #e0e0e0',
});

export const CalendarTitle = styled(Typography)({
  marginBottom: '16px',
  paddingBottom: '5px',
  fontSize: '16px',
  fontWeight: 500,
  borderBottom: '1px solid #e0e0e0',
});

export const NavigationButton = styled(IconButton)({
  width: '32px',
  height: '32px',
  '&:hover': {
    backgroundColor: 'rgba(0, 0, 0, 0.04)',
  },
});

export const MonthYearDisplay = styled(Typography)({
  fontWeight: 500,
  cursor: 'pointer',
  '&:hover': {
    color: 'primary.main',
  },
});

export const CalendarWrapper = styled(Box)({
  paddingTop: '16px',
});

export const DateCalendarStyled = styled(DateCalendar)({
  width: '100%',
  height: 'auto',
  '& .MuiPickersCalendarHeader-root': {
    display: 'none', // We're using custom header
  },
  '& .MuiDayCalendar-header': {
    paddingTop: 0,
    '& .MuiDayCalendar-weekDayLabel': {
      fontSize: '0.75rem',
      fontWeight: 500,
      color: 'rgba(0, 0, 0, 0.6)',
      width: 40,
      height: 40,
      margin: '0',
    },
  },
  '& .MuiPickersDay-root': {
    fontSize: '0.875rem',
    width: 40,
    height: 40,
    borderRadius: 0,
    margin: '0',
    fontWeight: 400,
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)',
    },
    '&.Mui-selected': {
      backgroundColor: 'transparent',
      color: 'inherit',
    },
    '&.MuiPickersDay-today': {
      fontWeight: 'bold',
      '&:hover': {
        backgroundColor: 'rgba(0, 0, 0, 0.04)',
      },
    },
    // Make all Sundays red
    '&[role="gridcell"]:nth-of-type(7n)': {
      color: '#d32f2f',
    },
  },
  '& .MuiPickersDay-dayOutsideMonth': {
    color: 'rgba(0, 0, 0, 0.38)',
    '&[role="gridcell"]:nth-of-type(7n)': {
      color: 'rgba(211, 47, 47, 0.38)', // Faded red for outside month Sundays
    },
  },
  '& .MuiDayCalendar-weekContainer': {
    margin: 0,
  },
  // Year/Month view styles
  '& .MuiYearCalendar-root': {
    maxHeight: '280px',
    width: '100%',
    margin: 0,
    overflow: 'auto',
    scrollbarWidth: 'none', // Firefox
    '&::-webkit-scrollbar': {
      // Chrome, Safari
      display: 'none',
    },
    '& .MuiPickersYear-yearButton': {
      width: '100%',
      borderRadius: 0,
      margin: 0,
      fontSize: '1rem',
      padding: '8px 0',
      fontWeight: 400,
      '&.Mui-selected': {
        fontWeight: 'bold',
        backgroundColor: 'transparent',
        color: 'inherit',
        '&:hover': {
          backgroundColor: 'rgba(0, 0, 0, 0.04)',
        },
      },
      '&:hover': {
        backgroundColor: 'rgba(0, 0, 0, 0.04)',
      },
    },
  },
});
