'use client';

import { useRouter } from 'next/navigation';

import { LOCAL_STORAGE_KEYS } from '@/constants/local-storage';
import { routes } from '@/constants/routes';

export default function SelectApp() {
  const router = useRouter();

  const handleAppSelect = (route: string) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(LOCAL_STORAGE_KEYS.SELECTED_APP, route);
    }
    router.push(route);
  };

  return (
    <div
      className="relative flex items-center justify-center min-h-screen w-full"
      style={{
        backgroundImage: "url('/images/auth-background.png')",
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      <div className="absolute inset-0 bg-blue-50/30 z-0" />

      <div className="relative z-10 flex flex-col items-center justify-center p-8">
        <div className="flex flex-col sm:flex-row gap-24">
          <div
            className="flex flex-col items-center group cursor-pointer transition-all duration-300 hover:scale-105"
            onClick={() => handleAppSelect(routes.MRD_DASHBOARD)}
          >
            <div
              className="w-40 h-40 rounded-xl border border-gray-300 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-300 flex items-center justify-center mb-3"
              style={{
                background:
                  'linear-gradient(to bottom, #FFFFFF 0%, #8FD4F8 100%)',
              }}
            >
              <img
                src="/images/MRD.png"
                alt="MRD"
                className="w-16 h-16 object-contain"
              />
            </div>
            <span className="text-lg font-medium text-gray-700">MRD</span>
          </div>

          <div
            className="flex flex-col items-center group cursor-pointer transition-all duration-300 hover:scale-105"
            onClick={() => handleAppSelect(routes.EMR_DASHBOARD)}
          >
            <div
              className="w-40 h-40 rounded-xl border border-gray-300 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-300 flex items-center justify-center mb-3"
              style={{
                background:
                  'linear-gradient(to bottom, #FFFFFF 0%, #8FD4F8 100%)',
              }}
            >
              <img
                src="/images/EMR.png"
                alt="EMR"
                className="w-16 h-16 object-contain"
              />
            </div>
            <span className="text-lg font-medium text-gray-700">EMR</span>
          </div>
        </div>
      </div>
    </div>
  );
}
