import { Props as ReactSelectProps } from 'react-select';

export type Position = 'first' | 'middle' | 'last';

export type TimeOption = {
  value: string;
  label: string;
};

export type AppTimePickerProps = {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  className?: string;
  label?: string;
  required?: boolean;
  helperText?: string;
  error?: boolean;
  slotProps?: {
    hourSelect?: ReactSelectProps<TimeOption, false>;
    minuteSelect?: ReactSelectProps<TimeOption, false>;
    periodSelect?: ReactSelectProps<TimeOption, false>;
    select?: ReactSelectProps<TimeOption, false>;
  };
};
