import axios, { AxiosInstance, InternalAxiosRequestConfig } from 'axios';

import { getToken } from '@core/lib/auth/services';

import { throttledUpdateLastActivity } from '@/utils/session';

import API_CONFIG from '@/core/configs/api';

const { API_URL, SUBSCRIPTION_KEY, API_BASE_URL } = API_CONFIG;

const pendingRequests = new Map();

const generateRequestKey = (config: InternalAxiosRequestConfig<unknown>) => {
  return `${config.method}:${config.url}:${JSON.stringify(config.params)}:${JSON.stringify(config.data)}`;
};

const createRequestInterceptor = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.request.use(
    async (config) => {
      throttledUpdateLastActivity();

      try {
        const token = await getToken();
        config.headers.set('Authorization', `Bearer ${token}`);

        if (config.method?.toLowerCase() === 'get') {
          const requestKey = generateRequestKey(config);
          if (pendingRequests.has(requestKey)) {
            const cancel = pendingRequests.get(requestKey);
            cancel();
          }
          const source = axios.CancelToken.source();
          config.cancelToken = source.token;
          pendingRequests.set(requestKey, source.cancel);
        }

        return config;
      } catch {
        return config;
      }
    },
    (error) => Promise.reject(error)
  );
};

const createResponseInterceptor = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.response.use(
    (response) => {
      const requestKey = generateRequestKey(response.config);
      pendingRequests.delete(requestKey);
      return response;
    },
    async (error) => {
      if (axios.isCancel(error)) {
        console.warn('Duplicate request canceled:', error.message);
        return new Promise(() => {});
      }

      return Promise.reject(error);
    }
  );
};

const headers = {
  'Ocp-Apim-Subscription-Key': SUBSCRIPTION_KEY,
};

export const arcaAxios = axios.create({
  baseURL: API_URL,
  headers,
});

createRequestInterceptor(arcaAxios);
createResponseInterceptor(arcaAxios);

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers,
});

createRequestInterceptor(api);
createResponseInterceptor(api);
