import React, { memo } from 'react';

import { cn } from '@/lib/utils';

import { useStatusModalStore } from '@/store/status-modal-store';

import { Button } from '@/components/ui/button';

import { ModalIcon } from './modal-icons';

const StatusModal = () => {
  const { isOpen, config, hideModal } = useStatusModalStore();

  if (!isOpen || !config) return null;

  const handleBackdropClick = () => {
    if (!config.actions) {
      hideModal();
    }
  };

  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <div
      className="fixed inset-0 bg-black/40 flex items-center justify-center transition-all z-[10001]"
      onClick={handleBackdropClick}
    >
      <div
        className={cn(
          'rounded-2xl bg-white flex flex-col items-center justify-center gap-6 p-6 min-h-60 w-90 max-w-md mx-4',
          config.className
        )}
        onClick={handleContentClick}
      >
        {/* Icon */}
        {config.showIcon && <ModalIcon type={config.type} />}

        {/* Title */}
        {config.title && (
          <div className="text-center font-bold text-xl -tracking-[2.2%]">
            {config.title}
          </div>
        )}

        {/* Message */}
        <div className="text-center font-semibold text-xl -tracking-[2.2%]">
          {config.message}
        </div>

        {/* Actions */}
        {config.actions && (
          <div className="flex gap-3 w-full max-w-xs">
            {config.actions.secondary && (
              <Button
                variant={config.actions.secondary.variant || 'outline'}
                onClick={config.actions.secondary.onClick}
                className="flex-1"
              >
                {config.actions.secondary.label}
              </Button>
            )}
            {config.actions.primary && (
              <Button
                variant={config.actions.primary.variant || 'default'}
                onClick={config.actions.primary.onClick}
                className="flex-1"
              >
                {config.actions.primary.label}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(StatusModal);
