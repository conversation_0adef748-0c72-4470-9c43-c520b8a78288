/**
 * @deprecated This type is deprecated and will be removed in future versions.
 * Please use the ```SVGProps<SVGSVGElement>``` instead.
 * ```
 * import React from 'react';
 * import type { SVGProps } from 'react';
 *
 * export default function EosIconsThreeDotsLoading(
 *   props: Readonly<SVGProps<SVGSVGElement>>
 * ) {
 *   return (
 *     <svg
 *       {...props}
 *     >
 * ```
 */
export interface IconProps {
  className?: string;
}
