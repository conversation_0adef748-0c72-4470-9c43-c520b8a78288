import React, { FC, useMemo } from 'react';

import {
  Control,
  FieldArrayWithId,
  FieldErrors,
  useWatch,
} from 'react-hook-form';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { getMinMaxDate } from '@/utils/emr/doctor-profile/personal-info';
import {
  allowAlphanumericInput,
  preventNonAlphabeticInput,
} from '@/utils/validation';

import { certificationHeaders } from '@/constants/emr/doctor-profile/personal-info';

import Table from '@/core/components/table';
import {
  actionButtonCellProps,
  getInputCellProps,
  ItemToDelete,
} from '@/types/emr/doctor-profile/personal-info';

import ActionButton from '../shared/ActionButton';
import TableDatePicker from '../shared/TableDatePicker';
import TableTextarea from '../shared/TableTextarea';

import { FormData } from '.';

type Props = {
  fields: FieldArrayWithId<FormData, 'certifications', 'id'>[];
  control: Control<FormData>;
  handleItemEdit: (index: number) => () => void;
  handleOnDelete: (itemToDelete: ItemToDelete) => void;
  itemToEdit: number | null;
  error?: FieldErrors<FormData>;
};

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const CertificationTable: FC<Props> = ({
  fields,
  control,
  itemToEdit,
  handleItemEdit,
  handleOnDelete,
  error,
}) => {
  const watchedCertifications = useWatch({
    control,
    name: 'certifications',
  });

  const rows = useMemo(
    () =>
      fields.map((field, index) => ({
        key: field.id,
        name: {
          value: (
            <TableTextarea
              name={`certifications.${index}.name`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={preventNonAlphabeticInput}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        regNo: {
          value: (
            <TableTextarea
              name={`certifications.${index}.regNumber`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={allowAlphanumericInput}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        validFrom: {
          value: (
            <TableDatePicker
              name={`certifications.${index}.validFrom`}
              control={control}
              disabled={itemToEdit !== index}
              format={DATE_DD_MM_YYYY_SLASH}
              maxDate={getMinMaxDate(watchedCertifications[index]?.validTo)}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        validTo: {
          value: (
            <TableDatePicker
              name={`certifications.${index}.validTo`}
              control={control}
              disabled={itemToEdit !== index}
              format={DATE_DD_MM_YYYY_SLASH}
              minDate={getMinMaxDate(watchedCertifications[index]?.validFrom)}
              isNotValid={!!error?.certifications?.[index]?.validTo?.message}
            />
          ),
          cellProps: getInputCellProps(
            itemToEdit !== index,
            !!error?.certifications?.[index]?.validTo?.message
          ),
        },
        updatedOn: {
          value: (
            <TableDatePicker
              name={`certifications.${index}.dateOfUpdation`}
              control={control}
              disabled={itemToEdit !== index}
              format={DATE_DD_MM_YYYY_SLASH}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        status: {
          value: (
            <TableTextarea
              name={`certifications.${index}.status`}
              control={control}
              disabled={itemToEdit !== index}
              onKeyDown={preventNonAlphabeticInput}
            />
          ),
          cellProps: getInputCellProps(itemToEdit !== index),
        },
        edit: {
          value:
            itemToEdit !== index ? (
              <ActionButton actionFor="edit" onClick={handleItemEdit(index)} />
            ) : null,
          cellProps: actionButtonCellProps,
        },
        delete: {
          value: (
            <ActionButton
              actionFor="delete"
              onClick={() => handleOnDelete({ index, uuId: field?.uuId })}
            />
          ),
          cellProps: actionButtonCellProps,
        },
      })),
    [
      fields,
      control,
      itemToEdit,
      watchedCertifications,
      error?.certifications,
      handleItemEdit,
      handleOnDelete,
    ]
  );

  return (
    <Table
      headers={certificationHeaders}
      rows={rows}
      tableContainerProps={{
        sx: {
          '& tbody td': {
            minHeight: 30,
          },
        },
      }}
    />
  );
};

export default CertificationTable;
