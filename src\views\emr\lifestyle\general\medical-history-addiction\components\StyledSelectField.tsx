import React from 'react';

import { Control, Path } from 'react-hook-form';

import { default as ControlledSelectField } from '@/components/controlled-inputs/ControlledSelectField';

import { MedicalHistoryAddictionForm } from '@/types/emr/lifestyle/medical-history-addiction';

type DiagnosisFieldPath<T extends string> = `diagnosis.${number}.${T}`;

export type StyledSelectFieldProps = Omit<
  React.ComponentProps<typeof ControlledSelectField>,
  'control' | 'name'
> & {
  styles?: Record<string, unknown>;
  control: Control<MedicalHistoryAddictionForm>;
  name:
    | Path<MedicalHistoryAddictionForm>
    | DiagnosisFieldPath<'yearOfDiagnosis'>
    | DiagnosisFieldPath<'status'>;
};

const StyledSelectField: React.FC<StyledSelectFieldProps> = (props) => (
  <ControlledSelectField
    {...props}
    inputLabelProps={{
      style: {
        fontSize: '14px',
      },
    }}
    styles={{
      control: (base) => ({
        ...base,
        fontSize: '14px',
        minHeight: '36px',
        height: '36px',
      }),
      valueContainer: (base) => ({
        ...base,
        height: '36px',
        padding: '0 8px',
      }),
      indicatorsContainer: (base) => ({
        ...base,
        height: '36px',
      }),
      menu: (base) => ({
        ...base,
        fontSize: '14px',
      }),
      singleValue: (base) => ({
        ...base,
        fontSize: '14px',
      }),
      placeholder: (base) => ({
        ...base,
        fontSize: '14px',
      }),
      ...props.styles,
    }}
    className="text-xs"
  />
);

export default StyledSelectField;
