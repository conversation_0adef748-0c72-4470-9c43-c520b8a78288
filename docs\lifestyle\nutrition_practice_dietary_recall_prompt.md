# Ambient Listening Prompt – 24 hour Dietary Recall

## Use case:

Ambiently listen to a doctor–patient conversation and extract structured lifestyle data related to: **24 hour Dietary Recall**.

## Output Instruction:

Analyze and return structured information in JSON format for the following fields:  
`breakfast`, `mid_morning`, `lunch`, `evening_snacks`, `dinner`, `post_dinner_snacks`  
Each field's value must be in **string format**.

- If a value is not available, still include the key with an empty array.
- Each meal group should contain an array of food items with their details.
- No markdown fences (` ```json `) should be included.

## Expected JSON Structure:

Each meal group should be an array containing objects with these fields:

- `time_range`: Start and end time in 24-hour format (e.g., "07:00-08:00")
- `food_item`: The food consumed as plain text
- `serving_type`: One of ["Number", "Bowl", "Spoons", "Cups"]
- `quantity`: Numeric value (minimum 1)

## Additional Rules:

- Only output in **English** regardless of input language.
- Do not include any reasoning or explanations.
- Normalize time values to 24-hour format (e.g., "8 AM" → "08:00").
- If time is mentioned as a single value, create a 1-hour range (e.g., "8 AM" → "08:00-09:00").
- Map meal times to appropriate meal groups:
  - breakfast: Morning meals (typically 06:00-10:00)
  - mid_morning: Snacks between breakfast and lunch (typically 10:00-12:00)
  - lunch: Midday meal (typically 12:00-14:00)
  - evening_snacks: Between lunch and dinner (typically 16:00-18:00)
  - dinner: Evening meal (typically 19:00-21:00)
  - post_dinner_snacks: After dinner (typically 21:00 onwards)
- Default to "Number" for serving_type if not specified.
- Default to 1 for quantity if not specified.

## Example Output:

```json
{
  "questions": [
    {
      "id": "dietary_recall",
      "title": "24 hour Dietary Recall",
      "icon": "mdi:clock-outline",
      "fields": [
        {
          "id": "dietary_table",
          "label": "Food Intake",
          "type": "grouped_table",
          "headers": [
            {
              "id": "time_range",
              "label": "Timings",
              "type": "time_range"
            },
            {
              "id": "food_item",
              "label": "Food Consumed",
              "type": "text"
            },
            {
              "id": "serving_type",
              "label": "Servings type",
              "type": "select",
              "options": ["Number", "Bowl", "Spoons", "Cups"]
            },
            {
              "id": "quantity",
              "label": "Quantity",
              "type": "number",
              "min": 1
            },
            {
              "id": "icon",
              "label": "",
              "type": "icon"
            }
          ],
          "groupBy": "meal",
          "mealGroups": [
            {
              "id": "breakfast",
              "label": "Breakfast",
              "defaultRows": []
            },
            {
              "id": "mid_morning",
              "label": "Mid-morning",
              "defaultRows": []
            },
            {
              "id": "lunch",
              "label": "Lunch",
              "defaultRows": []
            },
            {
              "id": "evening_snacks",
              "label": "Evening snacks",
              "defaultRows": []
            },
            {
              "id": "dinner",
              "label": "Dinner",
              "defaultRows": []
            },
            {
              "id": "post_dinner_snacks",
              "label": "Post-dinner snacks",
              "defaultRows": []
            }
          ],
          "value": [
            {
              "id": "fe4b3b59-ec0e-4bb1-bb30-6663d81ca2a4",
              "label": "Breakfast",
              "rows": [
                {
                  "time_range": {
                    "from": "",
                    "to": ""
                  },
                  "food_item": "Dosha",
                  "serving_type": {
                    "label": "Number",
                    "value": "Number"
                  },
                  "quantity": "2",
                  "icon": ""
                }
              ]
            },
            {
              "id": "0ecc3a52-09e6-4683-a875-83e29bab5481",
              "label": "Mid-morning",
              "rows": [
                {
                  "time_range": {
                    "from": "12:07 PM",
                    "to": "12:07 PM"
                  },
                  "food_item": "Lime",
                  "serving_type": {
                    "label": "Cups",
                    "value": "Cups"
                  },
                  "quantity": "1",
                  "icon": ""
                }
              ]
            },
            {
              "id": "3176bff8-ea95-4d76-9e77-a6cb0507fb4b",
              "label": "Lunch",
              "rows": [
                {
                  "time_range": {
                    "from": "1:07 PM",
                    "to": ""
                  },
                  "food_item": "Lunch",
                  "serving_type": {
                    "label": "Bowl",
                    "value": "Bowl"
                  },
                  "quantity": "2",
                  "icon": ""
                }
              ]
            },
            {
              "id": "5e0fa43a-c09f-4e3b-929d-3298291fe67a",
              "label": "Evening snacks",
              "rows": [
                {
                  "time_range": {
                    "to": "5:08 PM",
                    "from": "5:08 PM"
                  },
                  "food_item": "Coffee",
                  "serving_type": {
                    "label": "Cups",
                    "value": "Cups"
                  },
                  "quantity": "1",
                  "icon": ""
                }
              ]
            },
            {
              "id": "2783017e-832f-4740-947d-d83bb85f7e7d",
              "label": "Dinner",
              "rows": [
                {
                  "time_range": {
                    "to": "7:15 PM",
                    "from": "7:09 PM"
                  },
                  "food_item": "Rice",
                  "serving_type": {
                    "label": "Bowl",
                    "value": "Bowl"
                  },
                  "quantity": "1",
                  "icon": ""
                }
              ]
            },
            {
              "id": "2ac1400f-837a-49b8-ad38-d70876ad94b6",
              "label": "Post-dinner snacks",
              "rows": [
                {
                  "time_range": {
                    "to": "10:20 PM",
                    "from": "10:09 PM"
                  },
                  "food_item": "Bread",
                  "serving_type": {
                    "label": "Number",
                    "value": "Number"
                  },
                  "quantity": "2",
                  "icon": ""
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```
