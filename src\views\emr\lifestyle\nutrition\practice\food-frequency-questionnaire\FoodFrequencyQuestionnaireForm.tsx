import { FC, memo, useState, useMemo } from 'react';

import { useFormContext } from 'react-hook-form';

import RenderFields from '@/views/emr/lifestyle/shared/render-fields';
import FrequencyTableField from '@/views/emr/lifestyle/shared/render-fields/FrequencyTableField';
import SectionTitle from '@/views/emr/lifestyle/shared/SectionTitle';

import AppButton from '@/core/components/app-button';
import { FieldGroup, FormField } from '@/types/emr/lifestyle/questionnaire';

interface FrequencySection {
  id: string;
  title: string;
  icon?: string;
  fields: FormField[];
}

interface FrequencyFieldGroup extends FieldGroup {
  sections?: FrequencySection[];
}

type Props = {
  formFields: FrequencyFieldGroup[];
  readonly?: boolean;
  isExpandView?: boolean;
  isExpanded?: boolean;
};

const FoodFrequencyQuestionnaireForm: FC<Props> = ({
  formFields,
  readonly,
  isExpandView = false,
  isExpanded = false,
}) => {
  const { control } = useFormContext();

  const hasFrequencySections =
    formFields?.[0]?.sections && formFields[0].sections.length > 0;

  const [expandedSections, setExpandedSections] = useState<string[]>([
    'cereals_grains',
  ]);

  const allSectionIds = useMemo(() => {
    return formFields?.[0]?.sections?.map((section) => section.id) || [];
  }, [formFields]);

  const allExpanded = useMemo(() => {
    return (
      allSectionIds.length > 0 &&
      allSectionIds.every((id) => expandedSections.includes(id))
    );
  }, [allSectionIds, expandedSections]);

  const handleExpandCollapseAll = () => {
    if (allExpanded) {
      setExpandedSections([]);
    } else {
      setExpandedSections(allSectionIds);
    }
  };
  const handleSectionToggle = (sectionId: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionId)
        ? prev.filter((id) => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  if (hasFrequencySections) {
    return (
      <div className="w-full p-base flex flex-col gap-base">
        <SectionTitle
          title={formFields[0]?.title}
          icon={formFields[0]?.icon}
          renderTitleEnd={() =>
            isExpandView ? null : (
              <AppButton variant="outlined" onClick={handleExpandCollapseAll}>
                {allExpanded ? 'Collapse All' : 'Expand All'}
              </AppButton>
            )
          }
        />
        <FrequencyTableField
          sections={formFields[0].sections as FieldGroup[]}
          control={control}
          readonly={readonly}
          expandedSections={expandedSections}
          onSectionToggle={handleSectionToggle}
          isExpanded={isExpanded}
        />
      </div>
    );
  }

  return (
    <div className={`space-y-6 p-base w-full`}>
      {formFields?.map((section, index) => (
        <div key={section.id || index} className="space-y-6 w-full">
          <SectionTitle title={section?.title} icon={section?.icon} />
          <div className="w-full">
            <RenderFields
              fields={section?.fields}
              namePrefix={`questions.${index}.fields`}
              readonly={readonly}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default memo(FoodFrequencyQuestionnaireForm);
