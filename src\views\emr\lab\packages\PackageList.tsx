import React from 'react';

import Loading from '@/lib/common/loading';

import {
  PackageData,
  usePackageSelectorStore,
} from '@/store/emr/lab/package-store';

import { modalModes } from '@/types/emr/lab';

type PackageListProps = {
  packages: PackageData[];
  selectedPackage: PackageData | null;
  onSelectPackage: (pkg: PackageData) => void;
  isLoading?: boolean;
};

export const PackageList: React.FC<PackageListProps> = ({
  packages,
  selectedPackage,
  onSelectPackage,
}) => {
  const { isLoading, modalMode } = usePackageSelectorStore();

  const showLoading = modalMode === modalModes.VIEW && isLoading;
  return (
    <div className="w-1/4 border-r border-gray-600 overflow-y-auto px-3">
      {showLoading ? (
        <div className="flex justify-center py-4">
          <Loading />
        </div>
      ) : packages.length > 0 ? (
        packages.map((pkg) => (
          <div
            key={pkg.id}
            onClick={() => onSelectPackage(pkg)}
            className={`px-4 py-2 cursor-pointer hover:bg-gray-100 text-sm ${
              selectedPackage?.id === pkg.id ? 'bg-[#DAE1E7]' : ''
            }`}
          >
            {pkg.name}
            {selectedPackage?.id === pkg.id && (
              <span className="float-right">›</span>
            )}
          </div>
        ))
      ) : (
        <div className="p-4 text-center text-gray-500">No packages found</div>
      )}
    </div>
  );
};
