import React, { memo, useCallback, useEffect, useState } from 'react';

import { debounce } from 'lodash';
import { BiSearch } from 'react-icons/bi';

import TextInput from '@core/components/text-input';

import { usePrescriptionStore } from '@/store/emr/prescription';

import EosIconsThreeDotsLoading from '@/assets/svg/EosIconsThreeDotsLoading';

import {
  prescriptionHistoryView,
  PrescriptionHistoryViewType,
  prescriptionMode,
} from '@/types/emr/prescription';

const { DETAILS } = prescriptionHistoryView;
const { VIEW } = prescriptionMode;

const getPlaceholder = (historyView: PrescriptionHistoryViewType) => {
  if (historyView !== DETAILS) {
    return 'Search by Medicine/Doctor';
  }
  return 'Search by Medicine';
};

const HistorySearch = () => {
  const {
    isLoading,
    historyView,
    prescriptionMode,
    searchHistoryExpandedSearch,
    resetHistoryExpandedSearch,
    setHistorySearchQuery,
  } = usePrescriptionStore();

  const [searchQuery, setSearchQuery] = useState('');

  const debouncedSearch = debounce((q: string) => {
    setHistorySearchQuery(q);
    if (prescriptionMode === VIEW && historyView === DETAILS) {
      searchHistoryExpandedSearch(q);
    }
  }, 500);

  const handleSearch = useCallback(
    (q: string) => {
      setSearchQuery(q);
      debouncedSearch(q);
    },
    [debouncedSearch]
  );

  useEffect(() => {
    return () => {
      resetHistoryExpandedSearch();
      setHistorySearchQuery('');
      setSearchQuery('');
    };
  }, [resetHistoryExpandedSearch, setHistorySearchQuery]);

  useEffect(() => {
    setSearchQuery('');
  }, [historyView]);

  return (
    <TextInput
      placeholder={getPlaceholder(historyView)}
      onChange={(e) => handleSearch(e.target.value)}
      value={searchQuery}
      fieldClassName="!py-1 !pl-3 text-sm placeholder:italic"
      endDecoration={
        isLoading ? (
          <EosIconsThreeDotsLoading fontSize={32} />
        ) : (
          <BiSearch className="text-gray-400 text-lg" />
        )
      }
    />
  );
};

export default memo(HistorySearch);
