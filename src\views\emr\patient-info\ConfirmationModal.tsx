import { memo } from 'react';

import Loader from '@/core/components/app-loaders/Loader';
import CustomModal from '@/core/components/modal';
import PrimaryButton from '@/core/components/primary-button';

type ConfirmationType = 'end-consultation' | 'move-to-queue';

interface ConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  patientName?: string;
  type?: ConfirmationType;
  isLoading?: boolean;
}

const ConfirmationModal = memo(function ConfirmationModal({
  open,
  onClose,
  onConfirm,
  patientName,
  type = 'end-consultation',
  isLoading = false,
}: ConfirmationModalProps) {
  const getContent = () => {
    switch (type) {
      case 'move-to-queue':
        return {
          message: 'Are you sure you want to move the patient to queue?',
          confirmText: 'Move to Queue',
        };
      case 'end-consultation':
      default:
        return {
          message: 'Do you want to end the consultation?',
          confirmText: 'Mark as Consulted',
        };
    }
  };

  const { message, confirmText } = getContent();

  return (
    <CustomModal
      open={open}
      onClose={onClose}
      showDivider={false}
      width="343px"
      minHeight="auto"
      content={
        <div className="space-y-3 text-left">
          <p
            className="font-normal text-sm text-gray-700"
            style={{ fontSize: '14px', fontWeight: 400 }}
          >
            {message}
          </p>

          {patientName && (
            <p
              className="text-blue-500"
              style={{ fontSize: '20px', fontWeight: 500 }}
            >
              {patientName}
            </p>
          )}
        </div>
      }
      actions={
        <div className="flex gap-2 w-full">
          <PrimaryButton
            onClick={onClose}
            variant="outlined"
            className="flex-1 whitespace-nowrap"
            style={{
              borderColor: '#012436',
              color: '#012436',
              fontSize: '16px',
              fontWeight: 400,
              minWidth: '0',
              width: '50%',
            }}
          >
            Cancel
          </PrimaryButton>

          <PrimaryButton
            onClick={onConfirm}
            variant="outlined"
            className="flex-1 whitespace-nowrap"
            style={{
              borderColor: '#012436',
              color: '#012436',
              fontSize: '16px',
              fontWeight: 400,
              minWidth: '0',
              width: '50%',
            }}
          >
            {isLoading ? <Loader /> : confirmText}
          </PrimaryButton>
        </div>
      }
      actionsSx={{
        justifyContent: 'center',
        gap: 1,
        mr: 0,
        mt: 2,
        mb: 2,
      }}
      contentSx={{
        textAlign: 'left',
        py: 2,
      }}
    />
  );
});

export default ConfirmationModal;
