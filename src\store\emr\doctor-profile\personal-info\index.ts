import { AxiosError } from 'axios';
import { toast } from 'sonner';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import {
  createDoctorProfileInfo,
  getDoctorProfileByEmail,
  getDoctorProfileInfo,
  updateDoctorProfileInfo,
  uploadDocument,
} from '@/query/emr/doctor-profile/personal-info';

import { DoctorInfo } from '@/types/emr/doctor-profile/personal-info';

type DoctorStoreState = {
  doctorProfile: DoctorInfo | null;
  isLoading: boolean;
  errorMessage: string | null;
  tabName: string;
  isDeleting: boolean;
};

type DoctorStoreActions = {
  setTabName: (_name: string) => void;
  handleApiError: (_error: any) => void;
  fetchDoctorProfile: (_id: string) => Promise<DoctorInfo | null>;
  createDoctorProfile: (_data: Partial<DoctorInfo>) => Promise<void>;
  updateDoctorProfile: (
    _id: string,
    _data: Partial<DoctorInfo>
  ) => Promise<void>;
  deleteDoctorProfileTableItem: (
    _id: string,
    _data: Partial<DoctorInfo>
  ) => Promise<void>;
  fetchDoctorProfileByEmail: (_email: string) => Promise<DoctorInfo | null>;
  uploadDocument: (_data: FormData) => Promise<string>;
  clearStorage: () => void;
};

type DoctorStore = DoctorStoreState & DoctorStoreActions;

export const useDoctorStore = create<DoctorStore>()(
  persist(
    (set, get) => ({
      doctorProfile: null,
      isLoading: false,
      errorMessage: null,
      tabName: 'Doctor Profile',
      isDeleting: false,

      setTabName: (name: string) => set({ tabName: name }),

      handleApiError: (error: AxiosError) => {
        const errorMessage = error.message || 'An unexpected error occurred';
        set({ errorMessage, isLoading: false });
        toast.error(errorMessage);
      },

      fetchDoctorProfile: async (id) => {
        try {
          const response = await getDoctorProfileInfo(id);
          const doctor = response.data;
          set({ doctorProfile: doctor, isLoading: false });
          return doctor;
        } catch (error) {
          get().handleApiError(error);
          return null;
        }
      },

      fetchDoctorProfileByEmail: async (email) => {
        try {
          const response = await getDoctorProfileByEmail(email);
          const doctor = response.data;
          set({
            doctorProfile: doctor,
            isLoading: false,
          });
          return doctor;
        } catch (error) {
          get().handleApiError(error);
          return null;
        }
      },

      createDoctorProfile: async (data) => {
        set({ isLoading: true, errorMessage: null });
        try {
          const response = await createDoctorProfileInfo(data);
          const newDoctor = response.data;
          set({ doctorProfile: newDoctor, isLoading: false });
          const { tabName } = get();
          toast.success(`${tabName} saved successfully`);
        } catch (error) {
          get().handleApiError(error);
        } finally {
          set({ isLoading: false });
        }
      },

      updateDoctorProfile: async (id, data) => {
        set({ isLoading: true, errorMessage: null });
        try {
          const response = await updateDoctorProfileInfo(id, data);
          const updatedDoctor = response.data;
          set({ doctorProfile: updatedDoctor, isLoading: false });
          const { tabName } = get();
          toast.success(`${tabName} saved successfully`);
        } catch (error) {
          get().handleApiError(error);
        } finally {
          set({ isLoading: false });
        }
      },

      deleteDoctorProfileTableItem: async (id, data) => {
        set({ isDeleting: true, errorMessage: null });
        try {
          const response = await updateDoctorProfileInfo(id, data);
          const updatedDoctor = response.data;
          set({ doctorProfile: updatedDoctor, isDeleting: false });
          const { tabName } = get();
          toast.success(`${tabName} deleted successfully`);
        } catch (error) {
          get().handleApiError(error);
          set({ isDeleting: false });
        }
      },

      uploadDocument: async (data) => {
        set({ isLoading: true, errorMessage: null });
        try {
          const response = await uploadDocument(data);
          return response.data.blobUrl;
        } catch (error) {
          get().handleApiError(error);
        }
      },

      clearStorage: () => {
        set({ doctorProfile: null });
      },
    }),
    {
      name: 'doctor-store',
    }
  )
);
