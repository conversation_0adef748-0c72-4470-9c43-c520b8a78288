'use client';

import { useEffect } from 'react';

import { useRouter } from 'next/navigation';

import Loading from '@/lib/common/loading';

import { routes } from '@/constants/routes';

export default function LifestylePage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to general as the default lifestyle page
    router.replace(routes.EMR_LIFESTYLE_GENERAL);
  }, [router]);

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-gray-500">
        <Loading />
      </div>
    </div>
  );
}
