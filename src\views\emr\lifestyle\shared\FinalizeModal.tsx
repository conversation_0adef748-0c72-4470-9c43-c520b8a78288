import { FC, memo } from 'react';

import DeleteModal, { DeleteModalProps } from '@/core/components/delete-modal';

type FinalizeModalProps = Omit<
  DeleteModalProps,
  | 'onDelete'
  | 'confirmationMessage'
  | 'deleteButtonText'
  | 'cancelButtonText'
  | 'isLoading'
> & {
  onFinalize: () => void;
  loading?: boolean;
};

const FinalizeModal: FC<FinalizeModalProps> = ({
  onFinalize,
  loading = false,
  ...props
}) => {
  return (
    <DeleteModal
      {...props}
      isLoading={loading}
      onDelete={onFinalize}
      confirmationMessage="Are you sure you want to finalize the record? This step is irreversible."
      deleteButtonText="Yes"
      cancelButtonText="No"
      cancelButtonProps={{
        color: 'error',
      }}
      deleteButtonProps={{
        color: 'primary',
      }}
    />
  );
};

export default memo(FinalizeModal);
