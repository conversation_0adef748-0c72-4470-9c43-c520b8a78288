import React from 'react';
import type { SVGProps } from 'react';

const AvgWaitTimeIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <mask
      id="mask0_4535_30794"
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="24"
      height="24"
    >
      <rect width="24" height="24" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_4535_30794)">
      <path
        d="M15.2998 16.6998L10.9998 12.3998V6.9998H12.9998V11.5998L16.6998 15.2998L15.2998 16.6998ZM2.8498 16.0748C2.63314 15.5915 2.45814 15.0915 2.3248 14.5748C2.19147 14.0581 2.0998 13.5331 2.0498 12.9998H4.0748C4.1248 13.3498 4.19564 13.6998 4.2873 14.0498C4.37897 14.3998 4.49147 14.7415 4.6248 15.0748L2.8498 16.0748ZM2.0498 10.9998C2.0998 10.4665 2.19147 9.9373 2.3248 9.4123C2.45814 8.8873 2.64147 8.38314 2.8748 7.8998L4.6248 8.8998C4.49147 9.23314 4.37897 9.5748 4.2873 9.9248C4.19564 10.2748 4.1248 10.6331 4.0748 10.9998H2.0498ZM6.1748 20.1498C5.7248 19.8165 5.30814 19.454 4.9248 19.0623C4.54147 18.6706 4.18314 18.2498 3.8498 17.7998L5.5998 16.7998C5.83314 17.0998 6.07897 17.379 6.3373 17.6373C6.59564 17.8956 6.8748 18.1415 7.1748 18.3748L6.1748 20.1498ZM5.6248 7.1748L3.8498 6.1748C4.18314 5.7248 4.54147 5.30814 4.9248 4.9248C5.30814 4.54147 5.7248 4.18314 6.1748 3.8498L7.1748 5.6248C6.89147 5.85814 6.62064 6.10397 6.3623 6.3623C6.10397 6.62064 5.85814 6.89147 5.6248 7.1748ZM10.9998 21.9498C10.4665 21.8998 9.9373 21.8081 9.4123 21.6748C8.8873 21.5415 8.38314 21.3581 7.8998 21.1248L8.8998 19.3748C9.23314 19.5081 9.5748 19.6206 9.9248 19.7123C10.2748 19.804 10.6331 19.8748 10.9998 19.9248V21.9498ZM8.8998 4.6248L7.8998 2.8748C8.38314 2.64147 8.8873 2.45814 9.4123 2.3248C9.9373 2.19147 10.4665 2.0998 10.9998 2.0498V4.0748C10.6331 4.1248 10.2748 4.19564 9.9248 4.2873C9.5748 4.37897 9.23314 4.49147 8.8998 4.6248ZM12.9998 21.9498V19.9248C13.3665 19.8748 13.7248 19.804 14.0748 19.7123C14.4248 19.6206 14.7665 19.5081 15.0998 19.3748L16.0998 21.1248C15.6165 21.3581 15.1123 21.5415 14.5873 21.6748C14.0623 21.8081 13.5331 21.8998 12.9998 21.9498ZM15.0998 4.6248C14.7665 4.49147 14.4248 4.37897 14.0748 4.2873C13.7248 4.19564 13.3665 4.1248 12.9998 4.0748V2.0498C13.5331 2.0998 14.0623 2.19147 14.5873 2.3248C15.1123 2.45814 15.6165 2.64147 16.0998 2.8748L15.0998 4.6248ZM17.8248 20.1498L16.8248 18.3748C17.1081 18.1415 17.379 17.8956 17.6373 17.6373C17.8956 17.379 18.1415 17.1081 18.3748 16.8248L20.1498 17.8248C19.8165 18.2748 19.4581 18.6956 19.0748 19.0873C18.6915 19.479 18.2748 19.8331 17.8248 20.1498ZM18.3748 7.1748C18.1415 6.89147 17.8956 6.62064 17.6373 6.3623C17.379 6.10397 17.1081 5.85814 16.8248 5.6248L17.8248 3.8498C18.2748 4.16647 18.6915 4.51647 19.0748 4.8998C19.4581 5.28314 19.8081 5.6998 20.1248 6.1498L18.3748 7.1748ZM19.9248 10.9998C19.8748 10.6331 19.804 10.2748 19.7123 9.9248C19.6206 9.5748 19.5081 9.23314 19.3748 8.8998L21.1248 7.8748C21.3415 8.3748 21.5206 8.8873 21.6623 9.4123C21.804 9.9373 21.8998 10.4665 21.9498 10.9998H19.9248ZM21.1248 16.0998L19.3748 15.0998C19.5081 14.7665 19.6206 14.4248 19.7123 14.0748C19.804 13.7248 19.8748 13.3665 19.9248 12.9998H21.9498C21.8998 13.5331 21.8081 14.0623 21.6748 14.5873C21.5415 15.1123 21.3581 15.6165 21.1248 16.0998Z"
        fill="#1C1B1F"
        fill-opacity="0.5"
      />
    </g>
  </svg>
);

export default AvgWaitTimeIcon;
