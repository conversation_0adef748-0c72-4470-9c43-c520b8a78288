import { useEffect } from 'react';

import { BROAD_CAST_CHANNEL, BaseMessage } from '@/constants/broadcast-channel';

export function useBroadcastChannel<T extends BaseMessage>(
  onMessage: (data: T) => void
) {
  useEffect(() => {
    const channel = new BroadcastChannel(BROAD_CAST_CHANNEL);

    channel.onmessage = (event) => {
      onMessage(event.data as T);
    };

    return () => {
      channel.close();
    };
  }, [onMessage]);
}
