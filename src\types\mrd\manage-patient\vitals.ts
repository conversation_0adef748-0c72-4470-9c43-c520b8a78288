import { VitalStatus } from '@/components/vitals-card/types';

export type Vitals = {
  bloodPressure?: string;
  bmi?: string;
  diastolic?: string;
  height?: string;
  pulse?: string;
  rr?: string;
  systolic?: string;
  weight?: string;
  heartRate?: number;
  respiratoryRate?: number;
  spO2?: number;
  sbp?: string;
  dbp?: string;
  temperature?: number;
  waistCircumference?: string;
  pp?: string;
  createdAt?: string;
  vitalStatuses: {
    height: VitalStatus;
    weight: VitalStatus;
    bmi: VitalStatus;
    pulse: VitalStatus;
    rr: VitalStatus;
    bp: VitalStatus;
    temperature: VitalStatus;
    spO2: VitalStatus;
    sbp?: VitalStatus;
    dbp?: VitalStatus;
    pp?: VitalStatus;
  };
  ageGroup: string;
};
