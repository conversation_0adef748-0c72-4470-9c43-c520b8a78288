OpenTelemetry Integration in a Next.js Application
To integrate OpenTelemetry with a Next.js application and send telemetry to Azure Application Insights, you need to configure both the application code and the environment variables for your deployment. This guide covers the essential steps for a Next.js application using the App Router.

1. Install Dependencies
   First, you need to install the necessary packages for OpenTelemetry and the Azure exporter. Open your project in VS Code and use the integrated terminal to run the following command:

Bash

npm install @opentelemetry/api @vercel/otel @azure/monitor-opentelemetry-exporter
@opentelemetry/api: The core API for OpenTelemetry.

@vercel/otel: A utility library from Vercel that simplifies OpenTelemetry setup specifically for Next.js environments (Node.js and Edge).

@azure/monitor-opentelemetry-exporter: The official exporter that sends telemetry data directly to Azure Application Insights.

2. Configure the Next.js App
   You need to make two key changes to your Next.js application to enable OpenTelemetry.

Create the Instrumentation File
Create a file named instrumentation.ts in the root of your project. This file is a special Next.js hook that runs once on the server when the application starts.

TypeScript

// instrumentation.ts
import { registerOTel } from '@vercel/otel';
import { AzureMonitorTraceExporter } from '@azure/monitor-opentelemetry-exporter';

export async function register() {
registerOTel({
// A logical name for your application, used for filtering telemetry.
serviceName: 'your-nextjs-app',
traceExporter: new AzureMonitorTraceExporter({
connectionString: process.env.APPLICATIONINSIGHTS_CONNECTION_STRING,
}),
});
}
This code sets up a tracer and directs it to send all traces to the Azure Monitor exporter, using a connection string from an environment variable.

Enable the Instrumentation Hook
Next, you need to tell Next.js to use the instrumentation.ts file. Open your next.config.js file and add the instrumentationHook property to the experimental object.

JavaScript

/\*_ @type {import('next').NextConfig} _/
const nextConfig = {
// Add this block to enable the instrumentation file
experimental: {
instrumentationHook: true,
},
// ... your existing configurations
};

export default nextConfig;
This is a required step for the instrumentation.ts file to be executed during the server startup.

3. Configure the Connection String
   The telemetry exporter needs a connection string to know where to send the data. It's crucial to handle this securely using environment variables.

For Local Development
Create a .env.local file in the root of your project and add the connection string. This file should be in your .gitignore file to prevent it from being committed to your repository.

Ini, TOML

# .env.local

APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=YOUR_INSTRUMENTATION_KEY;IngestionEndpoint=https://YOUR_INGESTION_ENDPOINT/
For Azure Static Web App Production Environment
You mentioned that you already have this set up, which is great. You must ensure the variable name is exactly APPLICATIONINSIGHTS_CONNECTION_STRING and that the value is the full connection string from your Azure Application Insights resource. You can verify this in the Azure portal under Static Web App > Configuration.

4. Deploy and Verify
   After making these changes, commit your code and push it to your GitHub repository.

Deployment: Your GitHub Actions workflow will automatically build and deploy the updated application to Azure.

Verification: After the deployment is complete, navigate to your live website to generate some traffic. Then, go to the Application Insights resource in the Azure portal.

Check the Live Metrics Stream to see real-time data.

In the Logs section, you can run Kusto queries to see your data, such as requests | take 10.

If you were seeing errors previously, following these steps should resolve them by ensuring the correct packages are installed, the code is properly configured, and the environment variable is being used correctly by the OpenTelemetry exporter.
