'use client';

import dynamic from 'next/dynamic';
import { usePathname } from 'next/navigation';

import { ProtectedRoute } from '@/components/permission/protected-route';

// Import the layout wrapper with dynamic import to avoid SSR issues
const MrdLayoutWrapper = dynamic(() => import('@/core/layout/mrd'), {
  ssr: false,
});

// This is the client component that handles the permission checks
export default function MrdLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Check if the current route is the MRD module root
  const isMrdRoot = pathname === '/mrd';

  // If it's the MRD root, we don't need to check permissions here
  // as the home page will handle the redirection based on permissions
  if (isMrdRoot) {
    return <MrdLayoutWrapper>{children}</MrdLayoutWrapper>;
  }

  // For all other MRD routes, require MRD access permission
  return (
    <ProtectedRoute requiredPermissions={['mrd.access']} redirectPath="/">
      <MrdLayoutWrapper>{children}</MrdLayoutWrapper>
    </ProtectedRoute>
  );
}
