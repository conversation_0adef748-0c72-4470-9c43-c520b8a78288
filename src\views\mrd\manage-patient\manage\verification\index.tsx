import { memo, useEffect, useRef } from 'react';

import { useFormContext, useWatch } from 'react-hook-form';

import { formatAadhar } from '@/utils/mrd/manage-patient/aadhar';
import {
  formatPAN,
  formatPassport,
} from '@/utils/mrd/manage-patient/validation';

import { proofOptions } from '@/constants/mrd/manage-patient/select-options';

import ControlledImageUploader from '@/components/controlled-inputs/ControlledImageUploader';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';
import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import { PatientDetails } from '@/types/mrd/manage-patient/patient-details';

const Verification = () => {
  const { control, setValue } = useFormContext<PatientDetails>();
  const previousProofTypeRef = useRef<string | undefined>();

  const proofType = useWatch({
    control,
    name: 'proof.type',
  });

  const existingProofData = useWatch({
    control,
    name: 'proof',
  });

  useEffect(() => {
    const currentProofType = proofType?.value;
    const previousProofType = previousProofTypeRef.current;
    if (previousProofType && previousProofType !== currentProofType) {
      setValue('proof.aadharNumber', '');
      setValue('proof.passportNumber', '');
      setValue('proof.panNumber', '');
    }
    previousProofTypeRef.current = currentProofType;
  }, [proofType?.value, setValue]);

  const renderProofNumberField = () => {
    const proofTypeValue = proofType?.value;

    switch (proofTypeValue) {
      case 'Aadhar card':
        return (
          <ControlledTextField
            name="proof.aadharNumber"
            control={control}
            label="Aadhar Number"
            placeholder="0000 0000 000"
            initiallyReadonly
            fullWidth
            formatValue={formatAadhar}
            slotProps={{
              input: {
                inputProps: { maxLength: 14 },
              },
            }}
          />
        );
      case 'Passport':
        return (
          <ControlledTextField
            name="proof.passportNumber"
            control={control}
            label="Passport Number"
            placeholder="A1234567"
            initiallyReadonly
            fullWidth
            formatValue={formatPassport}
            slotProps={{
              input: {
                inputProps: { maxLength: 9 },
              },
            }}
          />
        );
      case 'Pan card':
        return (
          <ControlledTextField
            name="proof.panNumber"
            control={control}
            label="PAN Number"
            placeholder="**********"
            initiallyReadonly
            fullWidth
            formatValue={formatPAN}
            slotProps={{
              input: {
                inputProps: { maxLength: 10 },
              },
            }}
          />
        );
      default:
        if (existingProofData?.passportNumber) {
          return (
            <ControlledTextField
              name="proof.passportNumber"
              control={control}
              label="Passport Number"
              placeholder="A1234567"
              initiallyReadonly
              fullWidth
              formatValue={formatPassport}
              slotProps={{
                input: {
                  inputProps: { maxLength: 9 },
                },
              }}
            />
          );
        }
        if (existingProofData?.panNumber) {
          return (
            <ControlledTextField
              name="proof.panNumber"
              control={control}
              label="PAN Number"
              placeholder="**********"
              initiallyReadonly
              fullWidth
              formatValue={formatPAN}
              slotProps={{
                input: {
                  inputProps: { maxLength: 10 },
                },
              }}
            />
          );
        }

        return (
          <ControlledTextField
            name="proof.aadharNumber"
            control={control}
            label="Aadhar Number"
            placeholder="0000 0000 000"
            initiallyReadonly
            fullWidth
            formatValue={formatAadhar}
            slotProps={{
              input: {
                inputProps: { maxLength: 14 },
              },
            }}
          />
        );
    }
  };

  return (
    <div className="w-full h-full flex flex-col gap-base py-base">
      <div className="w-[85%] flex gap-base">
        <ControlledSelectField
          name="proof.type"
          control={control}
          label="Proof Type"
          placeholder="Select"
          options={proofOptions}
          initiallyReadonly
        />
        {renderProofNumberField()}
        <ControlledTextField
          name="proof.abhaNumber"
          control={control}
          label="Abha Number (optional)"
          placeholder="0000 0000 000"
          initiallyReadonly
          fullWidth
          slotProps={{
            input: {
              inputProps: { maxLength: 14 },
            },
          }}
        />
      </div>
      <div className="w-[85%] flex gap-base">
        <ControlledImageUploader
          name="proof.url"
          control={control}
          label="ID Proof"
          accept=".png, .jpg, .jpeg, .pdf"
          maxSizeInMB={5}
          indicationLabel="(The file size must be less than 5MB.)"
          showError={false}
        />
      </div>
    </div>
  );
};

export default memo(Verification);
