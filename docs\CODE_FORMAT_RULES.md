# Code Format Rules - ARCA EMR

Essential coding standards for the ARCA EMR project.

## Naming Conventions

| Type                | Format     | Example           |
| ------------------- | ---------- | ----------------- |
| Folders             | kebab-case | `user-profile/`   |
| TS Files            | kebab-case | `date-utils.ts`   |
| Components          | PascalCase | `UserProfile.tsx` |
| Variables/Functions | camelCase  | `getUserData`     |
| Types/Interfaces    | PascalCase | `UserData`        |
| Constants           | UPPER_CASE | `MAX_FILE_SIZE`   |

## Import Order

1. React imports
2. Next.js imports
3. External dependencies (alphabetical)
4. @core/\*\* imports
5. @/\*\* imports (grouped by type)
6. Relative imports
7. CSS/SCSS imports

```typescript
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@mui/material';
import { DateRange } from '@core/components/date-range-picker';
import { useUserStore } from '@/store/userStore';
import TextInput from '../text-input';
import './styles.scss';
```

## React Components

- Use arrow functions and memo at export
- Define props interface above component
- Group hooks at the top

```typescript
interface UserProfileProps {
  name: string;
  email: string;
}

const UserProfile = ({ name, email }: UserProfileProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { currentUser } = useUserStore();

  return (
    <div>
      <h1>{name}</h1>
      <p>{email}</p>
    </div>
  );
};

export default memo(UserProfile);
```

## TypeScript Guidelines

- Use `type` for unions and object shapes
- Prefix unused parameters with underscore `_`

```typescript
type UserData = {
  id: string;
  name: string;
};

type Status = 'loading' | 'success' | 'error';

const handleSubmit = (_event: FormEvent, data: FormData) => {
  processData(data);
};
```

## Development Commands

```bash
# Code quality
npm run lint        # Check formatting
npm run lint:fix    # Auto-fix issues
npm run format:all  # Format all files

# Manual pre-commit check
npx lint-staged
```

## Feature Organization

Follow the 4-layer pattern from lifestyle/doctor-profile features:

1. **Routes**: `app/(private)/emr/[feature-name]/`
2. **Components**: `views/emr/[feature-name]/` or `core/components/`
3. **State**: `store/emr/[feature-name]/`
4. **Queries**: `query/emr/[feature-name]/`

**Important**: `emr/` folder is legacy - use `views/` or `core/components/` instead.

See [FOLDER_STRUCTURE_GUIDE.md](./FOLDER_STRUCTURE_GUIDE.md) for details.
