'use client';

import { FC, useCallback, useEffect, useRef, useState } from 'react';

import * as speechsdk from 'microsoft-cognitiveservices-speech-sdk';
import { BiX } from 'react-icons/bi';
import { TbPlayerPause, TbPlayerPlay } from 'react-icons/tb';

import { getDoctorProfile } from '@/query/emr/doctor-profile/personal-info';

import { throttledUpdateLastActivity } from '@/utils/session';

import SineWaves from '@/helpers/sine-waves/sine-waves';

import {
  recordingStates,
  RecordingState,
  waves,
  languageOptions,
  CurrentModal,
  currentModal,
} from '@/constants/ambient-listening';

import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import PrimaryButton from '@/core/components/primary-button';

interface RecordConsultationProps {
  engine: speechsdk.ConversationTranscriber | null;
  onRecordEnd: (transcript: string, duration: number) => void;
  handleChooseLanguage: (newLanguage: string) => void;
  selectedLanguage: string;
  currentMode: CurrentModal;
  setCurrentMode: (mode: CurrentModal) => void;
  onCancel?: () => void;
  isLanguageSelection?: boolean;
}

const { IDLE, PAUSED, RECORDING } = recordingStates;
const { LANGUAGE_SELECTION, RECORD_CONSULTATION } = currentModal;

const PERIODIC_ACTION_INTERVAL = 5 * 60 * 1000;

const RecordConsultation: FC<RecordConsultationProps> = ({
  engine,
  onRecordEnd,
  handleChooseLanguage,
  selectedLanguage,
  setCurrentMode,
  currentMode,
  onCancel,
  isLanguageSelection,
}) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const [recordingState, setRecordingState] = useState<RecordingState>(IDLE);
  const [wavesConfig, setWavesConfig] = useState({
    speed: 0,
    wavesWidth: '0',
  });

  //speech states
  const [currentOffset, setCurrentOffset] = useState(0);
  const [isListening, setIsListening] = useState(false);
  const [recordingStartTime, setRecordingStartTime] = useState<number | null>(
    null
  );

  const [transcriptHistory, setTranscriptHistory] = useState(['']);
  const [transcript, setTranscript] = useState('');
  const [tEvent, setTvent] = useState(null);

  const transcriptEl = useRef(null);

  const wavesEl = useRef(null);

  const handlePeriodicAction = useCallback(() => {
    getDoctorProfile();
  }, []);

  const handleTranscribing = useCallback(
    (s: any, e: any) => {
      throttledUpdateLastActivity();
      if (e && e.privResult && e.privResult.privJson) {
        const event = JSON.parse(e.privResult.privJson);
        if (transcriptEl.current) {
          (transcriptEl.current as HTMLDivElement).scrollTop = (
            transcriptEl.current as HTMLDivElement
          ).scrollHeight;
        }
        setTvent(() => event);
      }
    },
    [transcriptEl]
  );

  const handleCancelRecord = () => {
    if (engine && isListening) {
      engine.stopTranscribingAsync();
    }
    setRecordingState(IDLE);
    setIsListening(false);
    setRecordingStartTime(null);
    setTranscript('');
    setTranscriptHistory(['']);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    onCancel?.();
  };

  useEffect(() => {
    if (engine) {
      engine.transcribing = handleTranscribing;
    }

    return () => {
      setTranscript('');
      setTranscriptHistory(['']);
      setRecordingStartTime(null);
      if (engine) {
        engine.stopTranscribingAsync();
      }
      // Clear interval when component unmounts
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [engine, handleTranscribing]);

  useEffect(() => {
    if (tEvent) {
      const { Type, Offset, Text } = tEvent;
      if (Type === 'ConversationTranscription') {
        setTranscript(() => {
          if (Offset !== currentOffset) {
            const newHist = [...transcriptHistory, transcript];

            setTranscriptHistory(newHist);
            setCurrentOffset(Offset);
          }
          return Text;
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tEvent]);

  const toggleRecord = () => {
    if (isListening) {
      setIsListening(false);
      engine?.stopTranscribingAsync(() => {
        setRecordingState(PAUSED);
      });
    } else {
      setIsListening(true);
      setRecordingStartTime(Date.now());
      engine?.startTranscribingAsync(() => {
        setRecordingState(RECORDING);
      });
    }
  };

  useEffect(() => {
    if (wavesEl.current != null && typeof window !== 'undefined') {
      // Updated multi-wave configuration for more dynamic wave pattern
      const singleGreenWave = [
        // {
        //   timeModifier: 1.5, // Faster movement
        //   lineWidth: 2,
        //   amplitude: 45, // Height of the waves
        //   wavelength: 50, // Shorter wavelength creates more peaks
        //   strokeStyle: '#1FC6A6',
        // },
        {
          timeModifier: 1.2, // Slightly different timing for variation
          lineWidth: 1.5,
          amplitude: 35,
          wavelength: 70,
          strokeStyle: '#1FC6A6',
        },
        // {
        //   timeModifier: 0.8, // Different timing for more complexity
        //   lineWidth: 1,
        //   amplitude: 25,
        //   wavelength: 90,
        //   strokeStyle: '#1FC6A6',
        // },
      ];

      new SineWaves({
        el: wavesEl.current,
        speed: wavesConfig.speed,
        ease: 'SineInOut',
        wavesWidth: wavesConfig.wavesWidth,
        waves: currentMode !== LANGUAGE_SELECTION ? singleGreenWave : waves,
        resizeEvent: function () {
          if (currentMode !== LANGUAGE_SELECTION) {
            var gradient = this.ctx.createLinearGradient(0, 0, this.width, 0);
            gradient.addColorStop(0, '#1FC6A6');
            gradient.addColorStop(1, '#1FC6A6');
            // Apply gradient to all waves
            var index = -1;
            var length = this.waves.length;
            while (++index < length) {
              this.waves[index].strokeStyle = gradient;
            }
          } else {
            var gradient = this.ctx.createLinearGradient(0, 0, this.width, 0);
            gradient.addColorStop(0, 'rgba(25, 255, 255, 0)');
            gradient.addColorStop(0.5, 'rgba(100, 100, 255, 1)');
            gradient.addColorStop(1, 'rgba(255, 255, 255, 1)');
            var index = -1;
            var length = this.waves.length;
            while (++index < length) {
              this.waves[index].strokeStyle = gradient;
            }
          }
        },
      });
    }
  }, [wavesConfig.speed, wavesConfig.wavesWidth, currentMode]);

  const handleStartRecord = () => {
    setCurrentMode(RECORD_CONSULTATION);
    setRecordingState(RECORDING);
    toggleRecord();
    setWavesConfig({
      speed: 8, // Increased speed for more dynamic animation
      wavesWidth: '75%',
    });
    // Start periodic action every 5 minutes
    intervalRef.current = setInterval(
      handlePeriodicAction,
      PERIODIC_ACTION_INTERVAL
    );
  };

  const handlePauseRecord = () => {
    setRecordingState(PAUSED);
    toggleRecord();
    setWavesConfig({
      speed: 0,
      wavesWidth: '0%',
    });
    // Clear interval when recording is paused
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  return (
    <div className="w-full min-h-full flex flex-col justify-between flex-1">
      <div>
        {/* <div className="pb-2.5 px-2.5 border-b">
          <span className="text-xl">
            {currentMode === LANGUAGE_SELECTION
              ? 'Recording Preference'
              : 'Recording Consultation'}
          </span>
        </div> */}

        {recordingState === IDLE && currentMode === LANGUAGE_SELECTION && (
          <div className="h-full  ">
            <div className="w-full max-w-xl py-2 px-3  flex flex-col gap-2">
              <span className="mb-2 font-normal text-[12px]">
                To get started, please select your preferred language from the
                options below. This will help us provide you with the best
                possible experience.
              </span>
              <RadioGroup
                value={selectedLanguage}
                onValueChange={handleChooseLanguage}
                className="grid grid-cols-3 gap-4"
              >
                {languageOptions.map((language) => (
                  <label
                    key={language.value}
                    className="flex items-center gap-2 cursor-pointer text-[16px] font-medium"
                  >
                    <RadioGroupItem value={language.value} />
                    {language.label}
                  </label>
                ))}
              </RadioGroup>
            </div>
          </div>
        )}
        {currentMode !== LANGUAGE_SELECTION && (
          <div className="flex flex-col items-center justify-center bg-[#E6EBF0] py-2 px-2 rounded-md mb-4">
            <>
              <span className="text-xs mb-2 text-center text-[#222] font-medium">
                Ambient Recording Active...
              </span>
              <canvas ref={wavesEl} className="w-full h-20" />
            </>
          </div>
        )}
        {currentMode === RECORD_CONSULTATION && (
          <div
            className="h-[40vh] flex flex-col gap-2 overflow-y-auto  px-3"
            ref={transcriptEl}
          >
            <span className="text-[18px] font-medium font-['Inter'] pb-2 block">
              Live transcript:
            </span>
            <div className="gap-3">
              {[...transcriptHistory, transcript].map(
                (x, i) => x && <p key={i}>{x}</p>
              )}
            </div>
          </div>
        )}
      </div>
      <div
        className={
          isLanguageSelection
            ? ' border-t border-[#DAE1E7] py-4 px-5 flex items-center justify-center'
            : ' border-t border-[#DAE1E7] py-3 px-5 flex items-center justify-between'
        }
      >
        <div
          className={isLanguageSelection ? 'hidden' : 'flex items-center gap-5'}
        >
          {(recordingState === RECORDING || recordingState === PAUSED) && (
            <>
              <PrimaryButton
                variant="contained"
                className="bg-[#012436] text-[16px] font-normal w-[180px] h-9 rounded-lg"
                onClick={() => {
                  const endTime = Date.now();
                  const duration = recordingStartTime
                    ? (endTime - recordingStartTime) / 1000
                    : 0; // duration in seconds
                  console.log(`Recording duration: ${duration} seconds`);
                  onRecordEnd(
                    [...transcriptHistory, transcript].join(' '),
                    duration
                  );
                }}
              >
                End recording
                <BiX className="text-xl" />
              </PrimaryButton>
            </>
          )}
        </div>
        {recordingState !== RECORDING && isLanguageSelection && (
          <PrimaryButton
            variant="contained"
            className="bg-[#012436] text-[16px] font-normal px-7 h-9  rounded-lg"
            onClick={handleStartRecord}
          >
            Start recording
          </PrimaryButton>
        )}
        {recordingState === RECORDING && !isLanguageSelection && (
          <PrimaryButton
            variant="outlined"
            className="text-[16px] font-normal w-[180px] border-[#012436] text-[#012436] h-9  rounded-lg"
            onClick={handlePauseRecord}
          >
            Pause recording
            <TbPlayerPause className="text-lg" />
          </PrimaryButton>
        )}
        {recordingState !== RECORDING && !isLanguageSelection && (
          <PrimaryButton
            variant="contained"
            className="bg-[#012436] text-[16px] font-normal  h-9  rounded-lg"
            onClick={handleStartRecord}
          >
            Resume recording
            <TbPlayerPlay className="text-lg" />
          </PrimaryButton>
        )}
      </div>
    </div>
  );
};

export default RecordConsultation;
