import React, { FC, memo } from 'react';

import Loading from '@/lib/common/loading';

type TimelineItem = {
  content: React.ReactNode;
};

type Props = {
  items: TimelineItem[];
  loading?: boolean;
};

const TimeLine: FC<Props> = ({ items, loading = false }) => {
  if (loading) {
    return (
      <div className="w-full h-[96%] flex items-center justify-center">
        <Loading />
      </div>
    );
  }

  return (
    <div className="w-full h-[calc(100%-2rem)]">
      {items?.map((item, index) => (
        <div key={index} className="flex w-full">
          <div className="w-full flex flex-col mt-2">{item.content}</div>
        </div>
      ))}
      {items?.length === 0 && (
        <div className="w-full h-full flex items-center justify-center text-secondary-foreground">
          No records found.
        </div>
      )}
    </div>
  );
};

export default memo(TimeLine);
