import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createLifestyleData,
  getLifestyleQuestions,
  getPatientLifestyle,
  updateLifestyleData,
} from '@/query/emr/lifestyle';

import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import {
  Questionnaire,
  QuestionnaireResponse,
} from '@/types/emr/lifestyle/questionnaire';

import { useLifestyleFilterStore } from '../../filter-store';

type FoodFrequencyQuestionnaireState = {
  questions: Questionnaire;
  questionLoading: boolean;
  updating: boolean;
  patientData: QuestionnaireResponse[];
  loading: boolean;
  finalizing: boolean;
};

type FoodFrequencyQuestionnaireActions = {
  getLifestyleQuestions: () => Promise<void>;
  createLifestyleData: (data: QuestionnaireResponse) => Promise<void>;
  updateLifestyleData: (data: QuestionnaireResponse) => Promise<void>;
  finalizeRecord: (id: string) => Promise<void>;
  getPatientData: (
    fromDate?: string,
    toDate?: string,
    silent?: boolean
  ) => Promise<void>;
  refreshData: () => void;
};

export type FoodFrequencyQuestionnaireStore = FoodFrequencyQuestionnaireState &
  FoodFrequencyQuestionnaireActions;

const defaultQuestions = {
  source: LifestyleSources.NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE,
  questions: [],
};

const initialState: FoodFrequencyQuestionnaireState = {
  questions: defaultQuestions,
  questionLoading: false,
  updating: false,
  patientData: [],
  loading: false,
  finalizing: false,
};

export const foodFrequencyQuestionnaireStore =
  create<FoodFrequencyQuestionnaireStore>((set, get) => ({
    ...initialState,
    getLifestyleQuestions: async () => {
      try {
        set({ questionLoading: true });
        const data = await getLifestyleQuestions(
          LifestyleSources.NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE
        );
        set({ questions: data });
      } catch (error) {
        console.error(error);
        set({ questions: defaultQuestions });
      } finally {
        set({ questionLoading: false });
      }
    },

    createLifestyleData: async (data) => {
      try {
        set({ updating: true });
        await createLifestyleData({ ...data, source: defaultQuestions.source });
        toast.success('Food frequency questionnaire created successfully');
      } catch (error) {
        console.error(error);
        toast.error('Failed to create food frequency questionnaire');
      } finally {
        set({ updating: false });
        get().refreshData();
      }
    },

    updateLifestyleData: async (data) => {
      try {
        set({ updating: true });
        await updateLifestyleData(
          { ...(data ?? {}), source: defaultQuestions.source },
          data?.id as string
        );
        toast.success('Food frequency questionnaire updated successfully');
      } catch (error) {
        console.error(error);
        toast.error('Failed to update food frequency questionnaire');
      } finally {
        set({ updating: false });
        get().refreshData();
      }
    },

    getPatientData: async (fromDate, toDate, silent = false) => {
      try {
        set({ loading: !silent });
        const data = await getPatientLifestyle(
          LifestyleSources.NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE,
          fromDate,
          toDate
        );
        set({ patientData: data ?? [] });
      } catch (error) {
        console.error(error);
        set({ patientData: [] });
      } finally {
        set({ loading: false });
      }
    },

    finalizeRecord: async (id: string) => {
      try {
        set({ finalizing: true });
        await updateLifestyleData(
          { status: LifestyleRecordStatus.FINALIZED },
          id
        );

        const { patientData } = get();
        const updatedData = patientData.map((item) =>
          item.id === id
            ? { ...item, status: LifestyleRecordStatus.FINALIZED }
            : item
        );

        set({ patientData: updatedData });
        toast.success('Food frequency questionnaire finalized successfully');
      } catch (error) {
        console.error('Error finalizing record:', error);
        toast.error('Failed to finalize food frequency questionnaire');
      } finally {
        set({ finalizing: false });
        get().refreshData();
      }
    },

    refreshData: () => {
      const { getPatientData } = get();
      const { fromDate, toDate } = useLifestyleFilterStore.getState();
      getPatientData(fromDate, toDate, true);
    },
  }));
