import { FC, ReactNode } from 'react';

import { PERMISSION_KEYS } from '@/constants/permission-keys';

import MrdPatientQueue from '@/views/mrd/queue';

import PermissionGuard from '@/core/guard/PermissionGuard';

const MRD_MANAGE_PATIENT_PERMISSIONS = [
  PERMISSION_KEYS.MRD_MANAGE_PATIENT_VIEW,
  PERMISSION_KEYS.MRD_MANAGE_PATIENT_EDIT,
];

type Props = {
  children: Readonly<ReactNode>;
};

const ManagePatientLayout: FC<Props> = ({ children }) => {
  return (
    <PermissionGuard requiredPermissions={MRD_MANAGE_PATIENT_PERMISSIONS}>
      <div className="flex justify-between w-full gap-base h-full">
        <div className="rounded-base shadow-base bg-white w-[70%] h-full p-base">
          {children}
        </div>
        <MrdPatientQueue />
      </div>
    </PermissionGuard>
  );
};

export default ManagePatientLayout;
