import { Path, useFormContext, useWatch } from 'react-hook-form';

import colors from '@/utils/colors';

import { LifestyleQuestion, Questions } from '@/emr/types/lifestyle';

import ControlledInput from '../ControlledInput';

type FieldProps = {
  appendName: Path<LifestyleQuestion>;
  isReadOnly?: boolean;
  isMaximized?: boolean;
  values?: Questions[];
  name: Path<LifestyleQuestion>;
};

const TableNumberFields = ({
  appendName,
  isMaximized,
  isReadOnly,
}: FieldProps) => {
  const { control } = useFormContext<LifestyleQuestion>();

  const fields = useWatch({ control, name: appendName });

  return (
    <div className="flex flex-col gap-2">
      {fields?.map((q: any, index: number) => (
        <ControlledInput
          key={index}
          control={control}
          name={`${appendName}.${index}.value` as any}
          className="w-full"
          variant={isMaximized ? 'transparent' : undefined}
          inputClassName={
            isMaximized
              ? `!w-full border border-[${colors.text.slateGray}] rounded-md`
              : '!w-full'
          }
          placeholder="0000"
          disabled={isReadOnly}
        />
      ))}
    </div>
  );
};

export default TableNumberFields;
