import { toast } from 'sonner';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { getAllDoctors } from '@/query/mrd/manage-patient/book-consultation';
import { updateAppointment } from '@/query/mrd/queue/patient';
import { getPatientQueue, GetPatientQueueRes } from '@/query/patient';

import { AppointmentStatus } from '@/constants/mrd/manage-patient/consultation';

type Doctor = {
  id: string;
  name: string;
};

type DoctorStoreState = {
  doctors: Doctor[];
  isQueueLoading: boolean;
  selectedDoctor: Doctor | null;
  selectedAppointments: GetPatientQueueRes[];
  appointmentsByDoctor: Record<string, GetPatientQueueRes[]>;
  consultingQueueItem: GetPatientQueueRes['queues'][number] | null;
  date: Date;
  setDate: (date?: Date) => void;
  loadSelectedDoctorAppointments: () => void;
  setSelectedDoctor: (doctor: Doctor | null) => void;
  setAppointments: (
    doctorId: string,
    appointments: GetPatientQueueRes[]
  ) => void;
  replaceAppointments: (params: {
    doctorId: string;
    appointment: GetPatientQueueRes;
  }) => void;
  setQueueLoading: (isLoading: boolean) => void;
  setConsultingQueueItem: (
    queueItem: GetPatientQueueRes['queues'][number] | null,
    doctorId: string
  ) => void;
  updateQueueItem: (params: {
    doctorId: string;
    appointmentId: string;
    queueItem: GetPatientQueueRes['queues'][number];
  }) => void;
};

export const useDoctorStore = create<DoctorStoreState>()(
  persist(
    (set) => ({
      doctors: [],
      selectedDoctor: null,
      appointmentsByDoctor: {},
      selectedAppointments: [],
      isQueueLoading: false,
      consultingQueueItem: null,
      date: new Date(),
      setDate(date?: Date) {
        return set((state) => {
          return {
            ...state,
            date: date || new Date(),
          };
        });
      },
      setQueueLoading: (isLoading: boolean) =>
        set((state) => {
          return {
            ...state,
            isQueueLoading: isLoading,
          };
        }),
      setSelectedDoctor: (doctor: Doctor | null) =>
        set((state) => {
          return {
            ...state,
            selectedDoctor: doctor,
            consultingQueueItem: null,
          };
        }),
      setConsultingQueueItem: (queueItem, doctorId) =>
        set((state) => {
          const appointments = state.appointmentsByDoctor[doctorId];

          if (!appointments) {
            return state;
          }

          const changed = appointments.map((appointment) => {
            const mapped = appointment.queues.map((item) => {
              if (item.queueId !== (queueItem?.queueId || '')) {
                return item;
              }

              return {
                ...item,
                status: AppointmentStatus.Consultation,
              };
            });

            return {
              ...appointment,
              queues: mapped,
            };
          });

          const newState = {
            ...state,
            consultingQueueItem: queueItem,
            appointmentsByDoctor: {
              ...state.appointmentsByDoctor,
              [doctorId]: changed,
            },
          };

          if (state.selectedDoctor?.id === doctorId) {
            newState.selectedAppointments = changed;
          }

          // Sync to localStorage
          localStorage.setItem(
            'doctorStore',
            JSON.stringify({
              consultingQueueItem: queueItem,
              appointmentsByDoctor: newState.appointmentsByDoctor,
            })
          );

          return newState;
        }),
      replaceAppointments: (params) =>
        set((state) => {
          let appointments = state.appointmentsByDoctor[params.doctorId];

          if (!appointments) {
            appointments = [];
          }

          const isAlreadyPresent = appointments.find(
            (item) => item.appointmentId === params.appointment.appointmentId
          );

          if (isAlreadyPresent) {
            appointments = appointments.map((item) => {
              if (item.appointmentId === params.appointment.appointmentId) {
                return params.appointment;
              }

              return item;
            });
          } else {
            appointments.push(params.appointment);
          }

          return {
            ...state,
            appointmentsByDoctor: {
              ...state.appointmentsByDoctor,
              [params.doctorId]: appointments,
            },
          };
        }),
      loadSelectedDoctorAppointments: () =>
        set((state) => {
          if (!state.selectedDoctor) {
            return state;
          }

          const appointments =
            state.appointmentsByDoctor[state.selectedDoctor.id];

          if (!appointments) {
            return {
              ...state,
              selectedAppointments: [],
              consultingQueueItem: null,
            };
          }

          const currentConsulting = getCurrentlyConsulting(appointments);

          const filteredAppointments = appointments.map((appointment) => {
            const filteredQueues = appointment.queues.filter(
              (item: Record<string, any>) =>
                item.status === AppointmentStatus.Booked
            );

            return {
              ...appointment,
              queues: filteredQueues,
            };
          });

          return {
            ...state,
            selectedAppointments: filteredAppointments,
            consultingQueueItem: currentConsulting,
          };
        }),
      setAppointments: (doctorId: string, appointments: GetPatientQueueRes[]) =>
        set((state) => {
          const newState = {
            ...state,
            appointmentsByDoctor: {
              ...state.appointmentsByDoctor,
              [doctorId]: appointments,
            },
          };

          if (state.selectedDoctor?.id === doctorId) {
            const currentConsulting =
              appointments.length > 0
                ? getCurrentlyConsulting(appointments)
                : null;

            newState.consultingQueueItem = currentConsulting;
          }

          return newState;
        }),
      updateQueueItem: (params) =>
        set((state) => {
          const appointments = state.appointmentsByDoctor[params.doctorId];

          if (!appointments) {
            return state;
          }

          const updated = appointments.map((appointment) => {
            appointment.queues = appointment.queues.map((item) => {
              if (item.queueId === params.queueItem.queueId) {
                return params.queueItem;
              }

              return item;
            });

            return appointment;
          });

          return {
            ...state,
            appointmentsByDoctor: {
              ...state.appointmentsByDoctor,
              [params.doctorId]: updated,
            },
          };
        }),
    }),
    {
      name: 'doctor-storage',
      partialize: (state) => ({
        selectedDoctor: state.selectedDoctor,
        date: state.date,
      }),
      serialize: (state) => {
        return JSON.stringify({
          ...state,
          state: {
            ...state.state,
            date:
              state.state.date instanceof Date
                ? state.state.date.toISOString()
                : state.state.date,
          },
        });
      },
      deserialize: (str) => {
        const parsed = JSON.parse(str);
        return {
          ...parsed,
          state: {
            ...parsed.state,
            date: parsed.state.date ? new Date(parsed.state.date) : new Date(),
          },
        };
      },
    }
  )
);

function getCurrentlyConsulting(appointments: GetPatientQueueRes[]) {
  if (!appointments || appointments.length === 0) {
    return null;
  }

  for (const appointment of appointments) {
    for (const queueItem of appointment.queues) {
      if (queueItem.status === AppointmentStatus.Consultation) {
        return queueItem;
      }
    }
  }

  return null;
}

function findQueueItem(queueId: string, appointments: Record<string, any>[]) {
  for (const appointment of appointments) {
    for (const queueItem of appointment.queues) {
      if (queueItem.queueId === queueId) {
        return queueItem;
      }
    }
  }

  return null;
}

export const fetchAllDoctors = async () => {
  const state = useDoctorStore.getState();

  try {
    const doctors = await getAllDoctors();

    useDoctorStore.setState({
      ...state,
      doctors,
    });

    return doctors;
  } catch (err) {
    console.error(err);
    toast.error('Error fetching doctors');

    return null;
  }
};

export async function fetchAppointments(doctorId: string) {
  const state = useDoctorStore.getState();

  try {
    state.setQueueLoading(true);

    const res = await getPatientQueue(doctorId, state.date);

    if (!res) {
      useDoctorStore.getState().setAppointments(doctorId, []);
    } else {
      useDoctorStore.getState().setAppointments(doctorId, [res.data]);
    }

    useDoctorStore.getState().loadSelectedDoctorAppointments();

    state.setQueueLoading(false);

    return res;
  } catch (err) {
    console.error(err);
    toast.error('Error fetching appointments');
    state.setQueueLoading(false);

    return [];
  }
}

export async function markQueueIdAsConsulting(
  queueId: string,
  doctorId: string
) {
  try {
    const state = useDoctorStore.getState();
    const allAppointments = state.selectedAppointments;

    if (!allAppointments?.length) {
      return;
    }

    const currentItem = getCurrentlyConsulting(allAppointments);

    if (currentItem) {
      toast.error('Doctor is currently consulting');
      state.setConsultingQueueItem(currentItem, doctorId);

      return;
    }

    const queueItem = findQueueItem(queueId, allAppointments);
    if (!queueItem) {
      toast.error('Queue item not found');
      return;
    }

    state.setConsultingQueueItem(queueItem, doctorId);
    toast.success(`Moved to consulting`);
    await updateAppointment({
      queueId,
      payload: {
        time: queueItem.time,
        status: AppointmentStatus.Consultation,
        queuePosition: queueItem.queuePosition,
      },
    });
  } catch (err) {
    console.error(err);
    toast.error('Error moving to consulting');
  }
}

if (typeof window !== 'undefined') {
  window.addEventListener('storage', (e) => {
    if (e.key === 'doctorStore' && e.newValue) {
      const newState = JSON.parse(e.newValue);
      const state = useDoctorStore.getState();

      useDoctorStore.setState({
        ...state,
        consultingQueueItem: newState.consultingQueueItem,
        appointmentsByDoctor: newState.appointmentsByDoctor,
        selectedAppointments: state.selectedDoctor?.id
          ? newState.appointmentsByDoctor[state.selectedDoctor.id] || []
          : state.selectedAppointments,
      });

      if (state.selectedDoctor?.id) {
        useDoctorStore.getState().loadSelectedDoctorAppointments();
      }
    }

    if (e.key === 'emrAction' && e.newValue) {
      const { action, doctorId } = JSON.parse(e.newValue);
      const state = useDoctorStore.getState();

      if (action === 'moveDown') {
        state.setConsultingQueueItem(null, doctorId);
      } else if (action === 'markConsulted') {
        state.setConsultingQueueItem(null, doctorId);
      }

      if (state.selectedDoctor?.id === doctorId) {
        fetchAppointments(doctorId);
      }
    }
  });
}
