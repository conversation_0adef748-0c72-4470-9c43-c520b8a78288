export const getTimeBasedGreeting = (): string => {
  const hour = new Date().getHours();

  if (hour >= 5 && hour < 12) {
    return 'Good Morning';
  } else if (hour >= 12 && hour < 17) {
    return 'Good Afternoon';
  } else if (hour >= 17 && hour < 21) {
    return 'Good Evening';
  } else {
    return 'Good Night';
  }
};

export const getLastUpdatedText = (lastRefreshTime?: Date): string => {
  if (!lastRefreshTime) {
    return 'Never';
  }

  const now = new Date();
  const diffInMs = now.getTime() - lastRefreshTime.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  // Check if it's today
  const isToday = now.toDateString() === lastRefreshTime.toDateString();

  if (isToday) {
    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `Today, ${diffInMinutes} min${diffInMinutes === 1 ? '' : 's'} ago`;
    } else {
      return `Today, ${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
    }
  } else if (diffInDays === 1) {
    return `Yesterday, ${lastRefreshTime.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })}`;
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  } else {
    return lastRefreshTime.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year:
        now.getFullYear() !== lastRefreshTime.getFullYear()
          ? 'numeric'
          : undefined,
    });
  }
};
