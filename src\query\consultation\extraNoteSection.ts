import { DiagnosisRecords } from '@/store/extraNoteDiagnosisStore';

import { arcaAxios } from '@/core/lib/interceptor';

export const createExtraNoteDiagnosis = async (
  data: DiagnosisRecords,
  patientId: string
) => {
  return await arcaAxios.post(
    `/patient/diagnosis-notes?patientId=${patientId}`,
    data
  );
};

export const getExtraNoteDiagnosis = async (
  patientId: string,
  diagnosisStatus?: 'provisional' | 'confirmed',
  activityStatus?: 'active' | 'inactive'
) => {
  const params = new URLSearchParams({ patientId });
  if (diagnosisStatus) params.append('diagnosisStatus', diagnosisStatus);
  if (activityStatus) params.append('activityStatus', activityStatus);

  return await arcaAxios.get(`/patient/diagnosis-notes?${params.toString()}`);
};

export const updateExtraNoteDiagnosis = async (
  id: string,
  data: DiagnosisRecords
) => {
  return await arcaAxios.patch(`/patient/diagnosis-notes?id=${id}`, data);
};
