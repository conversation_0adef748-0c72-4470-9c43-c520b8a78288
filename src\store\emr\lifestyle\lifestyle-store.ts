import { create } from 'zustand';

import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

type LifestyleStoreState = {
  source: LifestyleSources | null;
  modalOpen: boolean;
};

type LifestyleStoreActions = {
  setSource: (source: LifestyleSources | null) => void;
  setModalOpen: (modalOpen: boolean) => void;
};

export type LifestyleStore = LifestyleStoreState & LifestyleStoreActions;

const initialState: LifestyleStoreState = {
  source: null,
  modalOpen: false,
};

export const lifestyleStore = create<LifestyleStore>((set) => ({
  ...initialState,
  setSource: (source) => set({ source }),
  setModalOpen: (modalOpen) => set({ modalOpen }),
}));
