export const vitalStatus = {
  NORMAL: 'normal',
  HIGH: 'high',
  LOW: 'low',
  UNKNOWN: 'unknown',
} as const;

export const vitalImprovement = {
  IMPROVED: 'improved',
  WORSENED: 'worsened',
  STABLE: 'stable',
} as const;

export type VitalStatus = (typeof vitalStatus)[keyof typeof vitalStatus];
export type VitalImprovement =
  (typeof vitalImprovement)[keyof typeof vitalImprovement];

export type VitalCardAttributes = {
  label?: string;
  value?: string;
  unit?: string;
  status?: VitalStatus;
  improvement?: VitalImprovement;
  percentage?: string;
  time?: string;
};

export const defaultVitalCardAttributes: VitalCardAttributes[] = [
  {},
  {},
  {},
  {},
  {},
  {},
];

export type VitalCardProps = VitalCardAttributes & {
  loading?: boolean;
};
