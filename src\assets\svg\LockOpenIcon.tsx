import * as React from 'react';
import { SVGProps } from 'react';

const LockOpenIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="14"
    viewBox="0 0 12 14"
    fill="none"
  >
    <path
      d="M8.91667 6.41666V4.08333C8.91739 3.36003 8.64934 2.66225 8.16453 2.12547C7.67973 1.58869 7.01277 1.25119 6.29312 1.1785C5.57348 1.10581 4.8525 1.30311 4.27014 1.7321C3.68778 2.16109 3.2856 2.79116 3.14167 3.5M10.0833 6.41666H1.91667C1.27233 6.41666 0.75 6.939 0.75 7.58333V11.6667C0.75 12.311 1.27233 12.8333 1.91667 12.8333H10.0833C10.7277 12.8333 11.25 12.311 11.25 11.6667V7.58333C11.25 6.939 10.7277 6.41666 10.0833 6.41666Z"
      stroke="#012436"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export default LockOpenIcon;
