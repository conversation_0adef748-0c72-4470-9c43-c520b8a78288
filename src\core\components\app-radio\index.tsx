import React from 'react';

import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormHelperText from '@mui/material/FormHelperText';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';

import InputLabel from '../input-label';

import { AppRadioProps } from './type';

const AppRadio: React.FC<AppRadioProps> = ({
  name,
  options,
  value,
  onChange,
  className = '',
  errors,
  required = false,
  disabled = false,
  label,
  formControlProps,
  ...rest
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  return (
    <FormControl
      component="fieldset"
      required={required}
      error={!!errors}
      className={className}
      disabled={disabled}
      fullWidth
      sx={{ width: '100%' }}
      {...formControlProps}
    >
      <InputLabel
        label={label}
        required={required}
        className="mb-0 md:mb-0 text-sm md:text-sm"
      />
      <RadioGroup
        row
        name={name}
        value={value ?? ''}
        onChange={handleChange}
        {...rest}
      >
        {options.map((option) => (
          <FormControlLabel
            key={option.value}
            value={option.value}
            control={
              <Radio
                size="small"
                sx={{
                  color: '#000000 !important',
                  '&.Mui-checked': {
                    color: '#000000 !important',
                  },
                  '&.Mui-disabled': {
                    color: '#000000 !important',
                    opacity: 1,
                    margin: '9px !important',
                  },
                  '&.Mui-disabled.Mui-checked': {
                    color: '#000000 !important',
                    opacity: 1,
                  },
                  '& .MuiSvgIcon-root': {
                    color: '#000000 !important',
                  },
                  '&.Mui-disabled .MuiSvgIcon-root': {
                    color: '#000000 !important',
                    opacity: 1,
                    fontSize: '1px !important',
                  },
                }}
              />
            }
            label={option.label}
            disabled={disabled}
            sx={{
              minWidth: 0,
              '& .MuiFormControlLabel-label': {
                fontSize: 14,
                fontWeight: 400,
                letterSpacing: -0.2,
              },
            }}
          />
        ))}
      </RadioGroup>
      {errors?.message && (
        <FormHelperText error>{errors.message}</FormHelperText>
      )}
    </FormControl>
  );
};

export default AppRadio;
