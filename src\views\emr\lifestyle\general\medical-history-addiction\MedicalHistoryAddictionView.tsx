import React, { memo } from 'react';

import { useFormContext } from 'react-hook-form';

import { SubstanceHistory } from '@/constants/emr/lifestyle/medical-history-addiction';

import { BaseOption } from '@/types';
import {
  Addiction,
  MedicalHistoryAddictionForm,
} from '@/types/emr/lifestyle/medical-history-addiction';

import StyledKeyValuePair from './components/StyledKeyValuePair';

const formatSelectValue = (value: BaseOption | string | undefined): string => {
  if (typeof value === 'object' && value?.label) {
    return value.label;
  }
  if (typeof value === 'string') {
    return value;
  }
  return '-';
};

const { NO, CURRENT, FORMER } = SubstanceHistory;

const formatSubstanceValue = (substance: Addiction) => {
  if (substance?.history === NO) return 'No';
  if (substance?.history === CURRENT) return 'Yes';
  if (substance?.history === FORMER) return 'Former';
  return 'No';
};

const MedicalHistoryAddictionView: React.FC = () => {
  const { getValues } = useFormContext<MedicalHistoryAddictionForm>();

  const data = getValues();

  return (
    <div className="flex flex-col h-full p-6 space-y-6 overflow-y-auto">
      {/* Diagnosis Section - Table Layout */}
      {data?.diagnosis && data.diagnosis.length > 0 && (
        <div className="space-y-4">
          {data.diagnosis.map((diagnosis, index) => (
            <div key={index} className="space-y-4">
              {/* Diagnosis Header Row */}
              <div className="grid grid-cols-4 gap-6">
                <StyledKeyValuePair
                  label="Diagnosis"
                  value={diagnosis?.diseaseName || '-'}
                />
                <StyledKeyValuePair
                  label="Year of Diagnosis"
                  value={formatSelectValue(diagnosis?.yearOfDiagnosis)}
                />
                <StyledKeyValuePair
                  label="Diagnosis Duration"
                  value={diagnosis?.diagnosisDuration || '-'}
                />
                <StyledKeyValuePair
                  label="Status"
                  value={diagnosis?.status || '-'}
                />
              </div>

              {/* Treatment History Section */}
              {diagnosis?.treatmentHistory && (
                <div className="space-y-2">
                  <StyledKeyValuePair
                    label="Treatment History"
                    value={diagnosis.treatmentHistory}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Substance Use Section - Grid Layout */}
      <div className="space-y-4">
        <div className="grid grid-cols-5 gap-1">
          <StyledKeyValuePair
            label="Smoking History"
            value={formatSubstanceValue(data?.smoking)}
          />
          <StyledKeyValuePair
            label="Alcohol"
            value={formatSubstanceValue(data?.alcohol)}
          />
          <StyledKeyValuePair
            label="Oral Tobacco"
            value={formatSubstanceValue(data?.tobacco)}
          />
          <StyledKeyValuePair
            label="Drugs"
            value={formatSubstanceValue(data?.drugs)}
          />
        </div>
      </div>
    </div>
  );
};

export default memo(MedicalHistoryAddictionView);
