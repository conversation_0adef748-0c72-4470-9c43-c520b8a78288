import { ReactNode } from 'react';

import { TableCellProps } from '@mui/material';
import { TableContainerProps } from '@mui/material/TableContainer';

export const sortOrder = {
  ASC: 'asc',
  DESC: 'desc',
} as const;

export type SortOrder = (typeof sortOrder)[keyof typeof sortOrder];

export type SortOptions = {
  fieldName: string | null;
  order: SortOrder | null;
  onSort: (fieldName: string, order: SortOrder | null) => void;
};

export type SortButtonProps = {
  sortOrder: SortOrder | null;
};

export type HeaderProperty = {
  key: string;
  header: ReactNode;
  cellProps?: TableCellProps;
  sortFieldName?: string | null;
  sortOptions?: SortOptions;
};

export type Header = HeaderProperty & {
  subHeaders?: Header[];
};

export type Cell = {
  value: React.ReactNode;
  cellProps?: TableCellProps;
  returnNullForEmpty?: boolean;
};

export type SubCell = {
  [subKey: string]: Cell;
};

export type Row = {
  [key: string]: Cell | SubCell | string;
};

export type TableProps = {
  headers: Header[];
  rows: Row[];
  loading?: boolean;
  stickyHeader?: boolean;
  tableContainerProps?: TableContainerProps;
  noDataMessage?: ReactNode;
  sortOptions?: SortOptions;
};

export type TableHeaderProps = {
  headers: Header[];
  sortOptions?: SortOptions;
};

export type TableBodyContentProps = {
  loading?: boolean;
  headers: Header[];
  rows: Row[];
  noDataMessage?: ReactNode;
};
