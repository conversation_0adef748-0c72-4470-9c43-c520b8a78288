'use client';

import { useEffect } from 'react';

import { throttledUpdateLastActivity } from '@/utils/session';

export default function SessionIdleMonitor() {
  useEffect(() => {
    const activityEvents = [
      'mousemove',
      'keydown',
      'click',
      'scroll',
      'touchstart',
    ];
    activityEvents.forEach((event) => {
      window.addEventListener(event, throttledUpdateLastActivity, {
        passive: true,
      });
    });

    throttledUpdateLastActivity();

    return () => {
      activityEvents.forEach((event) =>
        window.removeEventListener(event, throttledUpdateLastActivity)
      );
    };
  }, []);

  return null;
}
