import React from 'react';

import { BiSearch } from 'react-icons/bi';

interface SearchInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const SearchInput: React.FC<SearchInputProps> = ({
  value,
  onChange,
  placeholder = 'Search...',
  className = '',
  disabled = false,
}) => {
  return (
    <div className={`relative ${className}`}>
      <input
        type="text"
        placeholder={placeholder}
        className="w-full pl-3 pr-10 py-2 border border-gray-200 rounded-lg focus:border-blue-500 focus:ring-0 text-sm font-normal transition-colors duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
        style={{ fontSize: '14px', height: '32px' }}
        value={value}
        onChange={onChange}
        disabled={disabled}
      />
      <BiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
    </div>
  );
};

export default SearchInput;
