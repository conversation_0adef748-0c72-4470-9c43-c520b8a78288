import { LifestyleMode } from '@/constants/emr/lifestyle';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

export interface BaseLifestyleModalProps {
  patientData?: QuestionnaireResponse | null;
  mode?: LifestyleMode;
  onAfterSubmit?: () => void;
  hideSaveButton?: boolean;
  isExpanded?: boolean;
  // Add any other common props used across modals
  [key: string]: any; // For any additional props that might be passed
}
