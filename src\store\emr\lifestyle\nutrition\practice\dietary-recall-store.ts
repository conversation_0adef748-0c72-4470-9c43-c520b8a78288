import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createLifestyleData,
  getLifestyleQuestions,
  getPatientLifestyle,
  updateLifestyleData,
} from '@/query/emr/lifestyle';

import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import {
  Questionnaire,
  QuestionnaireResponse,
} from '@/types/emr/lifestyle/questionnaire';

import { useLifestyleFilterStore } from '../../filter-store';

type DietaryRecallState = {
  questions: Questionnaire;
  questionLoading: boolean;
  updating: boolean;
  patientData: QuestionnaireResponse[];
  loading: boolean;
  finalizing: boolean;
};

type DietaryRecallActions = {
  getLifestyleQuestions: () => Promise<void>;
  createLifestyleData: (data: QuestionnaireResponse) => Promise<void>;
  updateLifestyleData: (data: QuestionnaireResponse) => Promise<void>;
  finalizeRecord: (id: string) => Promise<void>;
  getPatientData: (
    fromDate?: string,
    toDate?: string,
    silent?: boolean
  ) => Promise<void>;
  refreshData: () => void;
};

export type DietaryRecallStore = DietaryRecallState & DietaryRecallActions;

const defaultQuestions = {
  source: LifestyleSources.NUTRITION_PRACTICE_DIETARY_RECALL,
  questions: [],
};

const initialState: DietaryRecallState = {
  questions: defaultQuestions,
  questionLoading: false,
  updating: false,
  patientData: [],
  loading: false,
  finalizing: false,
};

export const dietaryRecallStore = create<DietaryRecallStore>((set, get) => ({
  ...initialState,
  getLifestyleQuestions: async () => {
    try {
      set({ questionLoading: true });
      const data = await getLifestyleQuestions(
        LifestyleSources.NUTRITION_PRACTICE_DIETARY_RECALL
      );
      set({ questions: data });
    } catch (error) {
      console.error(error);
      set({ questions: defaultQuestions });
    } finally {
      set({ questionLoading: false });
    }
  },

  createLifestyleData: async (data) => {
    try {
      set({ updating: true });
      await createLifestyleData({ ...data, source: defaultQuestions.source });
      toast.success('24-hour dietary recall created successfully');
    } catch (error) {
      console.error(error);
      toast.error('Failed to create 24-hour dietary recall');
    } finally {
      set({ updating: false });
      get().refreshData();
    }
  },

  updateLifestyleData: async (data) => {
    try {
      set({ updating: true });
      await updateLifestyleData(
        { ...(data ?? {}), source: defaultQuestions.source },
        data?.id as string
      );
      toast.success('24-hour dietary recall updated successfully');
    } catch (error) {
      console.error(error);
      toast.error('Failed to update 24-hour dietary recall');
    } finally {
      set({ updating: false });
      get().refreshData();
    }
  },

  getPatientData: async (fromDate, toDate, silent = false) => {
    try {
      set({ loading: !silent });
      const data = await getPatientLifestyle(
        LifestyleSources.NUTRITION_PRACTICE_DIETARY_RECALL,
        fromDate,
        toDate
      );

      const sortedData =
        data?.sort((a: QuestionnaireResponse, b: QuestionnaireResponse) => {
          const dateA = a.updated_on ? new Date(a.updated_on) : new Date();
          const dateB = b.updated_on ? new Date(b.updated_on) : new Date();
          return dateB.getTime() - dateA.getTime();
        }) ?? [];
      set({ patientData: sortedData });
    } catch (error) {
      console.error(error);
      set({ patientData: [] });
    } finally {
      set({ loading: false });
    }
  },

  finalizeRecord: async (id: string) => {
    try {
      set({ finalizing: true });
      await updateLifestyleData(
        { status: LifestyleRecordStatus.FINALIZED },
        id
      );

      const { patientData } = get();
      const updatedData = patientData.map((item) =>
        item.id === id
          ? { ...item, status: LifestyleRecordStatus.FINALIZED }
          : item
      );

      set({ patientData: updatedData });
      toast.success('24-hour dietary recall finalized successfully');
    } catch (error) {
      console.error('Error finalizing record:', error);
      toast.error('Failed to finalize 24-hour dietary recall');
    } finally {
      set({ finalizing: false });
      get().refreshData();
    }
  },

  refreshData: () => {
    const { getPatientData } = get();
    const { fromDate, toDate } = useLifestyleFilterStore.getState();
    getPatientData(fromDate, toDate, true);
  },
}));
