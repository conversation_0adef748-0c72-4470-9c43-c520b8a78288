'use client';

import React, { memo, useEffect, useState } from 'react';

import { DatePicker } from '@/lib/common/date_picker';
import { Pagination } from '@/lib/Pagination';

import { useMrdDashboardStore } from '@/store/mrd/dashboard';
import { useDoctorStore } from '@/store/mrd/queue/doctor';

import SearchInput from '@/components/common/SearchInput';

import TableV2 from '@/core/components/table-v2';

import DoctorSelect from './DoctorSelect';

const UpcomingAppointments = () => {
  const {
    upcomingAppointments,
    isLoadingAppointments,
    filters,
    totalItems,
    totalPages,
    setSearchQuery,
    setPage,
    setPageSize,
    fetchUpcomingAppointments,
    fetchDoctors,
  } = useMrdDashboardStore();

  const { date, setDate } = useDoctorStore();
  const [searchInput, setSearchInput] = useState(filters.searchQuery);

  // Helper functions
  const formatStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Table configuration
  const tableHeaders = [
    {
      key: 'patient',
      header: 'Patient',
    },
    {
      key: 'dateTime',
      header: 'Date & Time',
    },
    {
      key: 'status',
      header: 'Status',
    },
  ];

  const tableRows = upcomingAppointments.map((appointment) => ({
    patient: {
      value: (
        <div>
          <div
            style={{
              fontSize: '14px',
              fontWeight: 400,
              color: '#000000',
            }}
          >
            {appointment.patientName}
          </div>
        </div>
      ),
    },
    dateTime: {
      value: (
        <div>
          <div
            style={{
              fontSize: '14px',
              fontWeight: 400,
            }}
          >
            {appointment.appointmentTime}
          </div>
        </div>
      ),
    },
    status: {
      value: (
        <span
          style={{
            fontSize: '14px',
            fontWeight: 400,
            color: '#000000',
          }}
        >
          {formatStatus(appointment.status)}
        </span>
      ),
    },
  }));

  // Load initial data
  useEffect(() => {
    fetchDoctors();
    fetchUpcomingAppointments();
  }, [fetchDoctors, fetchUpcomingAppointments]);

  // Handle search input with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSearchQuery(searchInput);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchInput, setSearchQuery]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  return (
    <div
      className="bg-white px-4 rounded-lg border border-[#DAE1E7] h-full flex flex-col upcoming-appointments-container"
      style={{ borderRadius: '8px', minHeight: '250px' }}
    >
      {/* Header */}
      <div className=" py-2">
        {/* Header Row - Title on left, Doctor Select and Date Picker on right */}
        <div className="flex items-center justify-between mb-3 border-b py-2">
          <h3
            className="font-medium"
            style={{
              fontSize: '16px',
              fontWeight: 500,
              color: '#000000',
            }}
          >
            Upcoming Appointments
          </h3>

          {/* Doctor Select and Date Picker on right side */}
          <div className="flex items-center gap-3">
            <div>
              <DoctorSelect />
            </div>
            <div>
              <DatePicker
                value={date}
                onChange={(newDate) => {
                  setDate(newDate);
                }}
                className="!py-2 !border-[#64707D]"
              />
            </div>
          </div>
        </div>

        {/* Search Bar - Full width on next line */}
        <SearchInput
          value={searchInput}
          onChange={handleSearchChange}
          placeholder="Search by name, patient id"
        />
      </div>

      {/* Table Content */}
      <div className="flex-1  flex flex-col overflow-hidden">
        <div className="flex-1 overflow-auto">
          <TableV2
            headers={tableHeaders}
            rows={tableRows}
            loading={isLoadingAppointments}
            noDataMessage="No upcoming appointments found"
            tableContainerProps={{
              sx: {
                '& .MuiTableContainer-root': {
                  boxShadow: 'none',
                  border: 'none',
                },
                '& .MuiTable-root': {
                  borderCollapse: 'separate',
                  borderSpacing: 0,
                  border: 'none',
                  '& .MuiTableHead-root': {
                    '& .MuiTableRow-root': {
                      '& .MuiTableCell-root': {
                        borderBottom: '1px solid #E5E7EB',
                        backgroundColor: 'transparent',
                        borderLeft: 'none',
                        borderRight: 'none',
                        borderTop: 'none',
                        fontSize: '14px',
                        fontWeight: 500,
                        color: '#64707D',
                        padding: '12px 16px',
                        textAlign: 'center',
                      },
                    },
                  },
                  '& .MuiTableBody-root': {
                    '& .MuiTableRow-root': {
                      backgroundColor: 'white !important',
                      '&:hover': {
                        backgroundColor: 'white !important',
                      },
                      '&:nth-of-type(even)': {
                        backgroundColor: 'white !important',
                      },
                      '& .MuiTableCell-root': {
                        borderBottom: '1px solid #E5E7EB !important',
                        borderLeft: 'none',
                        borderRight: 'none',
                        borderTop: 'none',
                        backgroundColor: 'white !important',
                      },
                      '&:last-child': {
                        '& .MuiTableCell-root': {
                          borderBottom: 'none',
                        },
                      },
                    },
                  },
                },
              },
            }}
          />
        </div>

        {/* Pagination */}
        {totalItems > 0 && (
          <div className="border-t border-gray-100 px-4 py-2">
            <Pagination
              currentPage={filters.page}
              totalPages={totalPages}
              totalItems={totalItems}
              pageSize={filters.pageSize}
              onPageChange={setPage}
              onPageSizeChange={setPageSize}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(UpcomingAppointments);
