export const styles = {
  section: {
    mb: 3,
    '&:last-child': { mb: 0 },
  },
  sectionTitle: {
    fontSize: '14px',
    fontWeight: 600,
    color: '#000000',
    mb: 1.5,
    pb: 0.5,
    padding: '8px 5px',
  },
  fieldLabel: {
    fontSize: '12px',
    fontWeight: 600,
    color: '#000000',
    mb: 0.5,
  },
  fieldValue: {
    fontSize: '12px',
    color: '#000000',
    mb: 1,
    lineHeight: 1.4,
  },
  fieldContainer: {
    display: 'flex',
    flexDirection: 'column',
    minWidth: '110px',
    width: '110px',
    mr: 2,
    mb: 1,
  },
  fieldTitle: {
    fontSize: '12px',
    fontWeight: 600,
    color: '#000000',
    mb: 0.5,
    lineHeight: 1.2,
    whiteSpace: 'nowrap',
    overflow: 'visible',
  },
  fieldUnit: {
    fontSize: '10px',
    color: '#000000',
    mb: 0.5,
    lineHeight: 1.2,
    minHeight: '12px',
  },
  fieldInput: {
    fontSize: '12px',
    color: '#000000',
    backgroundColor: '#fff',
    border: 'none',
    padding: '4px 8px',
    minHeight: '24px',
    display: 'flex',
    alignItems: 'center',
  },
  inlineText: {
    fontSize: '12px',
    color: '#000000',
  },
  container: {
    p: 3,
    fontFamily: 'Arial, sans-serif',
    backgroundColor: '#fff',
    minHeight: '800px',
    color: '#000000',
  },
} as const;
