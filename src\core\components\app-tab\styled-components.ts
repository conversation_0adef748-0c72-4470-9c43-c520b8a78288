'use client';

import Box from '@mui/material/Box';
import { alpha, styled } from '@mui/material/styles';

export const HorizontalWrapper = styled(Box)(({ theme }) => ({
  '& .MuiTab-root': {
    textTransform: 'none',
    letterSpacing: -0.25,
    color: theme.palette.primary.main,
    textAlign: 'start',
    boxShadow: 'none',
  },

  '& .MuiTabs-indicator': {
    left: 0,
    width: '2px',
    backgroundColor: theme.palette.primary.main,
  },

  '&.vertical': {
    flexGrow: 1,
    display: 'flex',
    borderRadius: theme.shape.borderRadius,
    backgroundColor: 'transparent',

    '& .MuiTabs-root': {
      minWidth: 200,
      borderTopLeftRadius: theme.shape.borderRadius,
      borderBottomLeftRadius: theme.shape.borderRadius,
    },

    '& .MuiTab-root': {
      alignItems: 'flex-start',
      transition: 'background-color 200ms',

      '&.Mui-selected': {
        color: theme.palette.primary.main,
        backgroundColor: alpha(theme.palette.primary.main, 0.5),
      },
    },
  },

  '& .vertical-tabs': {
    borderRight: `1px solid ${theme.palette.grey[300]}`,
  },

  '& .vertical-panels': {
    width: '100%',
    padding: theme.spacing(6),
  },

  '&.horizontal': {
    width: '100%',
    height: '100%',
    overflowY: 'hidden',
    '& .MuiTabs-root': {
      backgroundColor: 'transparent',
      height: '34px',
      minHeight: '34px',
      borderBottom: theme.shape.border,

      '& .MuiTab-root': {
        minHeight: '34px',
        padding: theme.spacing(0, 2),
      },
    },
  },

  '&.horizontal.outlined': {
    '& .MuiTabs-root': {
      border: `1px solid ${theme.palette.divider}`,
      boxShadow: 'none',
    },
  },

  '& .horizontal-panels': {
    marginTop: theme.spacing(2),
    borderRadius: theme.shape.borderRadius,
    backgroundColor: theme.palette.common.white,
    width: '100%',
    height: 'calc(100% - 40px)',
  },
}));
