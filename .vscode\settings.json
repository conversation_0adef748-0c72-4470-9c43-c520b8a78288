{"cSpell.words": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "abha", "addictionhistory", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Apim", "appt", "arca", "archivo", "<PERSON><PERSON>", "<PERSON><PERSON>", "bgcolor", "<PERSON><PERSON><PERSON>", "blocknote", "<PERSON><PERSON><PERSON>", "chana", "channa", "chori", "CMCH", "cognitiveservices", "continuetoken", "currentmedicationhistory", "customise", "dalia", "diethistory", "eastus", "extralight", "familyhistory", "Finalise", "Finalised", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalphysicalexamination", "HDFC", "heent", "historyofpresenting", "humbleicons", "ICICI", "Iconamoon", "Intolerances", "Macronutrients", "maida", "<PERSON><PERSON><PERSON>", "masoor", "Micronutrients", "<PERSON><PERSON>", "moong", "msal", "<PERSON><PERSON>", "Pancard", "paneer", "partialize", "pastmedicalhistory", "pastsurgicalhistory", "<PERSON><PERSON>", "physicalactivityhistory", "Pincode", "postcoordination", "<PERSON><PERSON><PERSON>t", "presentingcomplaints", "qlementine", "rava", "Religare", "rheumatological", "Selectfield", "sleephistory", "S<PERSON>med", "sonner", "speechsdk", "Stagewise", "stresshistory", "subparameters", "systemicexamination", "Updation", "urad", "Vada", "<PERSON><PERSON><PERSON><PERSON>", "webchat"], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}