import * as yup from 'yup';

declare module 'yup' {
  interface StringSchema {
    validDate(message?: string): this;
  }
}

yup.addMethod(
  yup.string,
  'validDate',
  function (message = 'Invalid date format') {
    return this.test('valid-date', message, function (value) {
      if (!value) return true;
      return !isNaN(Date.parse(value));
    });
  }
);

export const consultationSchema = yup.object().shape({
  consultation: yup.array().of(
    yup.object().shape({
      doctorId: yup
        .object({ id: yup.string().required('Required') })
        .required('Required'),
      department: yup.string().required('Required'),
      date: yup.string().required('Required').validDate('Invalid date format'),
      time: yup.string().required('Required'),
    })
  ),
});
