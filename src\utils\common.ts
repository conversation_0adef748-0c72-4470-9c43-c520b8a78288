import { BaseOption } from '@/types';

/**
 * Returns the larger of the input value and minimum value
 * @param value - The input number to compare
 * @param min - The minimum allowed value, defaults to 1
 * @returns The larger of value and min. Returns min if value is undefined/null
 */
export const getMin = (value?: number, min: number = 1) => {
  if (!value) return min;
  return Math.max(value, min);
};

/**
 * Returns the smaller of the input value and maximum value
 * @param value - The input number to compare
 * @param max - The maximum allowed value, defaults to 1
 * @returns The smaller of value and max. Returns max if value is undefined/null
 */
export const getMax = (value?: number, max: number = 1) => {
  if (!value) return max;
  return Math.min(value, max);
};

/**
 * Returns the value clamped between the minimum and maximum values
 * @param value - The input number to clamp
 * @param min - The minimum allowed value, defaults to 1
 * @param max - The maximum allowed value, defaults to 1
 * @returns The value clamped between min and max
 */
export const getMinMax = (value?: number, min: number = 1, max: number = 1) => {
  return getMin(getMax(value, max), min);
};

/**
 * Returns the input value if it is not undefined/null, otherwise returns the default value
 * @param value - The input value to check
 * @param defaultValue - The default value to return if value is undefined/null, defaults to empty string
 * @returns The input value if it is not undefined/null, otherwise the default value
 */
export const getOrDefault = <T>(
  value: T | undefined | null,
  defaultValue: T = '' as T
): T => {
  if (value === undefined || value === null || value === '')
    return defaultValue;
  return value;
};

/**
 * Converts a constant object to an array of BaseOption
 * @param constantObj - Record<string, string> (e.g., SubstanceFrequency)
 * @param labelCase - Whether to convert labels to title case (default: true)
 */
export function convertToBaseOptions<T extends Record<string, string>>(
  constantObj: T,
  labelCase = true
): BaseOption[] {
  return Object.values(constantObj).map((value) => {
    let label = value;

    if (labelCase) {
      label = value
        .replace(/_/g, ' ')
        .replace(/-/g, '–')
        .replace(/\b\w/g, (c) => c.toUpperCase());
    }

    return { value, label };
  });
}
