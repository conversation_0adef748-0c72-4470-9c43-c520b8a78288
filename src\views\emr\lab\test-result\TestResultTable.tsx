import React, { useCallback, useEffect, useMemo } from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useTestStore } from '@/store/emr/lab/reports-store';
import { useTestReportFilterStore } from '@/store/emr/lab/test-report-filter';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { getLabTestsTableRows } from '@/utils/emr/lab/result';
import { filterBy } from '@/utils/filter-by-util';

import { testResultHeaders } from '@/constants/emr/lab';

import Table from '@/core/components/table';
import { Row } from '@/core/components/table/types';

const { DATE_YYYY_MM_DD } = DateFormats;

const TestResultTable: React.FC = () => {
  const {
    sortField,
    sortOrder,
    customEndDate,
    customStartDate,
    dateFilter,
    department,
    searchText,
    onSort,
  } = useTestReportFilterStore();

  const patient = useCurrentPatientStore((state) => state.patient);

  const { testResult, isLoading, fetchPatientTestResult } = useTestStore();

  const fetchTestResult = useCallback(() => {
    if (!patient) return;

    fetchPatientTestResult({
      patientId: patient.id,
      sortField,
      sortOrder,
      dateFilter: dateFilter || filterBy.ALL,
      customStartDate: formatDate(customStartDate, DATE_YYYY_MM_DD),
      customEndDate: formatDate(customEndDate, DATE_YYYY_MM_DD),
      department: department || 'ALL',
      searchText,
    });
  }, [
    fetchPatientTestResult,
    patient,
    sortOrder,
    dateFilter,
    customStartDate,
    customEndDate,
    department,
    searchText,
  ]);

  const rows = useMemo<Row[]>(
    () => getLabTestsTableRows({ testResult, fetchTestResult }),
    [testResult, fetchTestResult]
  );

  useEffect(() => {
    fetchTestResult();
  }, [fetchTestResult]);

  return (
    <Table
      headers={testResultHeaders}
      rows={rows}
      sortOptions={{ fieldName: sortField, order: sortOrder, onSort }}
      loading={isLoading}
      stickyHeader
      noDataMessage="No test results found"
      tableContainerProps={{
        sx: {
          maxHeight: '100%',
          '& th': {
            whiteSpace: 'nowrap',
            fontSize: '0.875rem',
            padding: '8px 16px',
            borderLeft: '1px solid #64707D',
          },
          '& td': {
            fontSize: '0.725rem',
            padding: '2px 4px',
          },
        },
      }}
    />
  );
};

export default TestResultTable;
