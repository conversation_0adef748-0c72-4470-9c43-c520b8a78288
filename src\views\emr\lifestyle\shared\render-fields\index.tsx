import React, { useCallback } from 'react';

import { useFormContext } from 'react-hook-form';

import { LifestyleMode } from '@/constants/emr/lifestyle';

import { FormField } from '@/types/emr/lifestyle/questionnaire';

import { fieldComponents } from './field-renderers';

interface RenderFieldsProps {
  fields: FormField[];
  namePrefix?: string;
  groupClassName?: string;
  fieldClassName?: string;
  readonly?: boolean;
  mode?: LifestyleMode;
  patientData?: any[];
  sectionId?: string;
  variant?: 'modal' | 'timeline';
}

const RenderFields: React.FC<RenderFieldsProps> = ({
  fields,
  namePrefix = '',
  groupClassName = 'space-y-6',
  fieldClassName = '',
  readonly,
  mode,
  patientData = [],
  variant,
  sectionId,
}) => {
  const { control, watch, setValue } = useFormContext();

  const renderField = useCallback(
    (field: FormField, name: string, index: number) => {
      if (!field.type) {
        console.warn(`Field has no type defined:`, field);
        return null;
      }

      const FieldComponent = fieldComponents[field.type];
      if (!FieldComponent) {
        console.warn(`No renderer found for field type: ${field.type}`);
        return null;
      }

      const fieldName = field.type === 'table' ? name : `${name}.value`;

      return (
        <div key={field.id} className={fieldClassName}>
          <FieldComponent
            name={fieldName}
            field={field}
            control={control}
            watch={watch}
            setValue={setValue}
            readonly={readonly}
            fieldIndex={index}
            mode={mode}
            patientData={patientData}
            variant={variant}
            sectionId={sectionId}
          />
        </div>
      );
    },
    [
      control,
      watch,
      setValue,
      readonly,
      fieldClassName,
      mode,
      patientData,
      variant,
    ]
  );

  if (!fields || fields.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">No fields to display</div>
    );
  }

  // Check if this is a food intake section in read-only mode
  const isFoodIntakeReadonly = readonly && sectionId === 'food_intake';

  if (isFoodIntakeReadonly) {
    // Process fields to group them in pairs when possible
    const processedFields = [];
    let i = 0;

    while (i < fields.length) {
      const currentField = fields[i];
      const nextField = fields[i + 1];

      // If we have at least two fields of the same type, add them as a pair
      if (nextField && currentField.type === nextField.type) {
        processedFields.push([currentField, nextField]);
        i += 2;
      }
      // Handle single field at the end
      else {
        processedFields.push([currentField]);
        i++;
      }
    }

    return (
      <div className="space-y-4">
        {processedFields.map((row, rowIndex) => (
          <div
            key={rowIndex}
            className={`flex flex-wrap -mx-2 ${
              row.length > 1 ||
              (row[0]?.type === 'radio' &&
                rowIndex < processedFields.length - 1)
                ? 'border-b border-gray-200 pb-4'
                : ''
            }`}
          >
            {row.map((field, fieldInRowIndex) => {
              const fieldIndex = fields.indexOf(field);
              const widthClass = row.length === 2 ? 'w-1/2' : 'w-full';

              return (
                <div key={fieldIndex} className={`${widthClass} px-2`}>
                  {renderField(
                    field,
                    `${namePrefix}.${fieldIndex}`,
                    fieldIndex
                  )}
                </div>
              );
            })}
          </div>
        ))}
      </div>
    );
  }

  // Default rendering for non-food-intake or non-readonly mode
  return (
    <div className={`${groupClassName} ${readonly ? 'readonly-form' : ''}`}>
      {fields?.map((field, index) =>
        renderField(field, `${namePrefix}.${index}`, index)
      )}
    </div>
  );
};

export default RenderFields;
