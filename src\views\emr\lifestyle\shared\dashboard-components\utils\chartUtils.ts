interface CalculateBarWidthParams {
  dataLength: number;
  maxBarWidth: number;
  chartWidth?: number;
  margin?: number;
  barGap?: number;
  minBarWidth?: number;
}

export const calculateBarWidth = ({
  dataLength,
  maxBarWidth,
  chartWidth = 800, // Default chart width
  margin = 50, // Total horizontal margins
  barGap = 10, // Gap between bars
  minBarWidth = 10, // Minimum bar width
}: CalculateBarWidthParams): number => {
  if (!dataLength) return maxBarWidth;

  const availableWidth = (chartWidth - margin) / dataLength;
  const calculatedWidth = availableWidth - barGap;

  return Math.min(maxBarWidth, Math.max(minBarWidth, calculatedWidth));
};
