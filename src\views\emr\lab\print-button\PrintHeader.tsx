import React, { memo, useEffect } from 'react';

import { Box, Typography } from '@mui/material';

import { useCustomiseEmrStore } from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useOrganizationStore } from '@/store/organizationStore';
import { useUserStore } from '@/store/userStore';

import { formatOrganization } from '../../prescription/shared/Print/utils';

const typographyStyle = {
  fontSize: '12px',
  color: '#001926',
  lineHeight: '1.4',
  textAlign: 'right' as const,
  maxWidth: '200px',
  whiteSpace: 'nowrap' as const,
  overflow: 'hidden' as const,
  textOverflow: 'ellipsis' as const,
};

interface OrganizationData {
  name?: string;
  address?: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
}

const PrintHeader = () => {
  const { doctorProfile } = useDoctorStore();
  const { data: userData } = useUserStore();
  const { organization, fetchOrganization } = useOrganizationStore();
  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();

  const formattedOrganization = formatOrganization(organization);

  useEffect(() => {
    userData?.organizationId && fetchOrganization(userData?.organizationId);
  }, [fetchOrganization, userData?.organizationId]);

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile.id);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  const mostRecentData = customiseEmrData?.length
    ? [...customiseEmrData].sort(
        (a, b) =>
          new Date(b.updated_on || '').getTime() -
          new Date(a.updated_on || '').getTime()
      )[0]
    : null;

  return (
    <div
      className="w-full flex justify-between p-base border-b"
      style={{ position: 'relative' }}
    >
      <div className="text-sm font-semibold">
        <Typography
          variant="inherit"
          sx={{ fontWeight: 400, fontSize: '1rem', marginTop: -3 }}
        >
          {doctorProfile?.general?.fullName ?? '-'}
        </Typography>

        {mostRecentData?.letterHeadDetails &&
          mostRecentData.letterHeadDetails
            .split('\n')
            .map((line: string, idx: number) => (
              <Typography
                key={idx}
                variant="inherit"
                sx={{ fontWeight: 400, fontSize: '1rem', margin: 0 }}
              >
                {line.trim()}
              </Typography>
            ))}
      </div>
      <div className="flex gap-base">
        {mostRecentData?.organizationLogo ? (
          <img
            src={mostRecentData.organizationLogo}
            alt="Organization Logo"
            style={{ maxWidth: 150, maxHeight: 80, borderRadius: 4 }}
          />
        ) : null}
        <div>
          <OrganizationInfo organization={formattedOrganization} />
        </div>
      </div>
    </div>
  );
};

export default memo(PrintHeader);

const OrganizationInfo: React.FC<{
  organization?: OrganizationData | null;
}> = ({ organization }) => (
  <Box>
    <Typography sx={typographyStyle}>
      {organization?.name || 'Hospital Name'}
    </Typography>
    {organization?.address &&
      (Array.isArray(organization.address) ? (
        organization.address.map((line, idx) => (
          <Typography key={idx} sx={typographyStyle}>
            {line}
          </Typography>
        ))
      ) : organization.address.includes(',') ||
        organization.address.includes('\n') ? (
        organization.address
          .replace(/\n/g, ',')
          .split(',')
          .slice(0, 2)
          .map((line, idx) => (
            <Typography key={idx} sx={typographyStyle}>
              {line.trim()}
            </Typography>
          ))
      ) : (
        <Typography sx={typographyStyle}>{organization.address}</Typography>
      ))}
    {organization?.phone && (
      <Typography sx={typographyStyle}>{organization.phone}</Typography>
    )}
  </Box>
);
