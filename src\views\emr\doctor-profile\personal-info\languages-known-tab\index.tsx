import React, { useCallback, useEffect, useRef, useState } from 'react';

import { useFieldArray, useForm } from 'react-hook-form';

import Stack from '@mui/material/Stack';

import useIsMobile from '@/hooks/use-mobile-layout';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { scrollToElementWithOffset } from '@/utils/emr/doctor-profile/personal-info';

import {
  languages,
  profileTabs,
  READ,
  SPEAK,
  WRITE,
} from '@/constants/emr/doctor-profile/personal-info';

import {
  ItemToDelete,
  LanguageKnown,
} from '@/types/emr/doctor-profile/personal-info';

import SectionTitle from '../SectionTitle';
import DeleteModal from '../shared/DeleteModal';
import SaveButton from '../shared/SaveButton';

import LanguagesKnownMob from './LanguagesKnownMob';
import LanguagesKnownTable from './LanguagesKnownTable';

type Fluency = {
  read: boolean;
  write: boolean;
  speak: boolean;
};

export type LanguageKnownType = { fluency: Fluency; language: string };

export type FormData = {
  languages: Record<string, Fluency>;
  languagesKnown: Array<LanguageKnownType>;
};

const defaultFluency: Fluency = { read: false, write: false, speak: false };
const defaultLanguages: LanguageKnownType = {
  language: '',
  fluency: defaultFluency,
};

const getDefaultValues = (): FormData => {
  return languages?.reduce(
    (acc, lang) => ({
      ...acc,
      languages: {
        ...acc?.languages,
        [lang]: defaultFluency,
      },
    }),
    {
      languages: {},
      languagesKnown: [defaultLanguages],
    }
  );
};

const getFormattedData = ({
  data,
  isMobile,
}: {
  data: FormData;
  isMobile: boolean;
}): LanguageKnown[] => {
  if (!isMobile) {
    const formattedData: LanguageKnown[] = Object.entries(data.languages)
      .map(([language, fluency]) => {
        if (!language) return null;
        const fluencyLevels = Object.keys(fluency).filter(
          (level) => fluency[level as keyof typeof fluency]
        );
        if (fluencyLevels.length > 0 && language) {
          return {
            language,
            fluency: fluencyLevels,
          };
        }
        return null;
      })
      .filter((item): item is LanguageKnown => item !== null);

    return formattedData;
  } else {
    const formattedData: LanguageKnown[] = data.languagesKnown
      .map((item) => {
        if (!item.language) return null;
        return {
          language: item.language,
          fluency: Object.keys(item.fluency).filter(
            (level) => item.fluency[level as keyof typeof item.fluency]
          ),
        };
      })
      ?.filter((item): item is LanguageKnown => item !== null);
    return formattedData;
  }
};

export default function LanguagesKnownTab() {
  const isMobile = useIsMobile();
  const lastFieldRef = useRef<HTMLDivElement | null>(null);

  const { data: userData } = useUserStore();
  const {
    doctorProfile,
    createDoctorProfile,
    updateDoctorProfile,
    fetchDoctorProfileByEmail,
    setTabName,
    deleteDoctorProfileTableItem,
    isDeleting,
  } = useDoctorStore();

  const [itemToDelete, setItemToDelete] = useState<ItemToDelete>(null);
  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);

  const { handleSubmit, control, reset, getValues } = useForm<FormData>({
    defaultValues: getDefaultValues(),
  });

  const { append, fields, remove } = useFieldArray<FormData>({
    control,
    name: 'languagesKnown',
    shouldUnregister: true,
  });

  const handleOnAdd = useCallback(() => {
    append(defaultLanguages);
    setTimeout(() => {
      scrollToElementWithOffset(lastFieldRef.current);
    }, 100);
  }, [append]);

  const handleOnDelete = useCallback((item: ItemToDelete) => {
    setItemToDelete(item);
  }, []);

  const handleDelete = useCallback(async () => {
    if (itemToDelete) {
      if (itemToDelete?.uuId && doctorProfile?.id) {
        const languagesKnown = doctorProfile?.languagesKnown;

        const filteredExperience = languagesKnown?.filter(
          (item) => item?.language !== itemToDelete?.uuId
        );

        await deleteDoctorProfileTableItem(doctorProfile.id, {
          languagesKnown: filteredExperience,
        });
      } else {
        remove(itemToDelete?.index);
      }
      setItemToDelete(null);
    }
  }, [
    itemToDelete,
    doctorProfile?.id,
    doctorProfile?.languagesKnown,
    deleteDoctorProfileTableItem,
    remove,
  ]);

  const handleCancel = useCallback(() => {
    setItemToDelete(null);
  }, []);

  useEffect(() => {
    if (doctorProfile && doctorProfile.languagesKnown) {
      const languagesKnown: LanguageKnownType[] =
        doctorProfile?.languagesKnown?.map((lang) => ({
          language: lang?.language as string,
          fluency: {
            read: lang?.fluency?.includes(READ) || false,
            write: lang?.fluency?.includes(WRITE) || false,
            speak: lang?.fluency?.includes(SPEAK) || false,
          },
        }));

      const languagesData: FormData = {
        languages: {},
        languagesKnown: languagesKnown?.length
          ? languagesKnown
          : [defaultLanguages],
      };

      doctorProfile?.languagesKnown?.forEach((lang) => {
        const fluency = lang?.fluency || [];

        const language = lang?.language;
        if (language) {
          languagesData.languages[language] = {
            read: fluency?.includes(READ),
            write: fluency?.includes(WRITE),
            speak: fluency?.includes(SPEAK),
          };
        }
      });
      reset(languagesData);
    }
  }, [doctorProfile, reset]);

  const onSubmit = async (data: FormData) => {
    try {
      const payload = {
        languagesKnown: getFormattedData({ data, isMobile }),
      };

      if (doctorProfile && doctorProfile?.id) {
        await updateDoctorProfile(doctorProfile?.id, payload);
      } else {
        await createDoctorProfile({ ...payload, username: userData?.email });
      }
      setEditableField(new Set());
      setIsSubmitted(true);
    } catch (error) {
      console.error('Error:', error);
    }
  };

  useEffect(() => {
    setTabName(profileTabs.LANGUAGES_KNOWN);
    if (userData?.email) {
      fetchDoctorProfileByEmail(userData?.email);
    }
  }, [userData?.email, fetchDoctorProfileByEmail, setTabName]);

  useEffect(() => {
    if (fields?.length === 0) {
      handleOnAdd();
    }
  }, [fields?.length, handleOnAdd]);

  return (
    <Stack
      component="form"
      spacing={{ xs: 3, md: 2 }}
      pb={{ xs: 8, md: 0 }}
      onSubmit={handleSubmit(onSubmit)}
    >
      <SectionTitle className="mb-5 mt-1" title="Languages Known" />
      {isMobile ? (
        <LanguagesKnownMob
          control={control}
          fields={fields}
          handleOnDelete={handleOnDelete}
          editableField={editableField}
          getValues={getValues}
          isSubmitted={isSubmitted}
          setEditableField={setEditableField}
          onAdd={handleOnAdd}
          lastFieldRef={lastFieldRef}
        />
      ) : (
        <LanguagesKnownTable control={control} />
      )}
      <SaveButton />
      <DeleteModal
        open={!!itemToDelete}
        onClose={handleCancel}
        onDelete={handleDelete}
        isLoading={isDeleting}
      />
    </Stack>
  );
}
