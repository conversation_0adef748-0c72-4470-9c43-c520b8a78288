// Constants

export const MODAL_CONFIG = {
  width: '70vw',
  maxHeight: '80vh',
  minHeight: '500px',
  contentHeight: '80vh',
  headerHeight: '110px',
  footerHeight: '60px',
};

export const TYPOGRAPHY_STYLES = {
  doctor: {
    fontSize: '14px',
    color: '#001926',
    lineHeight: '1.4',
  },
  organization: {
    fontSize: '12px',
    color: '#001926',
    lineHeight: '1.4',
    textAlign: 'right' as const,
    maxWidth: '200px',
    whiteSpace: 'nowrap' as const,
    overflow: 'hidden' as const,
    textOverflow: 'ellipsis' as const,
  },
  prescriptionTitle: {
    color: '#012436',
    fontSize: '16px',
  },
  patientInfo: {
    fontSize: '14px',
    color: '#012436',
  },
  footer: {
    fontSize: '12px',
    color: '#666',
    fontStyle: 'italic' as const,
  },
};

export const CONTAINER_STYLES = {
  modal: {
    display: 'flex',
    flexDirection: 'column' as const,
    flex: 1,
    overflow: 'hidden' as const,
    padding: 0,
  },
  main: {
    fontFamily: 'Arial, sans-serif',
    color: '#012436',
    fontSize: '0.875rem',
    display: 'flex',
    flexDirection: 'column' as const,
    height: MODAL_CONFIG.contentHeight,
    overflow: 'hidden' as const,
    position: 'relative' as const,
  },
  header: {
    borderBottom: '1px solid #e0e0e0',
    pb: 2,
    minHeight: MODAL_CONFIG.headerHeight,
    overflow: 'visible',
  },
  patientInfo: {
    borderBottom: '1px solid #e0e0e0',
    pb: 1,
    flexWrap: 'wrap' as const,
    gap: '8px 0',
    '& .MuiTypography-root': TYPOGRAPHY_STYLES.patientInfo,
  },
  content: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column' as const,
    position: 'relative' as const,
    overflowY: 'auto' as const,
    overflowX: 'hidden' as const,
    paddingRight: '8px',
    '& *': { color: '#012436' },
    '&::-webkit-scrollbar': {
      width: '6px',
    },
    '&::-webkit-scrollbar-track': {
      background: '#f1f1f1',
      borderRadius: '3px',
    },
    '&::-webkit-scrollbar-thumb': {
      background: '#888',
      borderRadius: '3px',
    },
    '&::-webkit-scrollbar-thumb:hover': {
      background: '#555',
    },
  },
  footer: {
    position: 'relative' as const,
    mt: 2,
    pt: 2,
    borderTop: '1px solid #e0e0e0',
    backgroundColor: '#f9f9f9',
    px: 2,
    py: 2,
    minHeight: MODAL_CONFIG.footerHeight,
    '@media print': {
      display: 'none',
    },
  },
};
