import React, {
  memo,
  FC,
  useCallback,
  useEffect,
  SetStateAction,
  Dispatch,
} from 'react';

import { Controller, useForm } from 'react-hook-form';

import Box from '@mui/material/Box';
import { LuPencilLine } from 'react-icons/lu';

import EditableText from '@/lib/common/editable_text';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import {
  LifestyleNoteRecords,
  useLifestyleNoteStore,
} from '@/store/lifestyle-note-store';

import { noteModes } from '@/utils/constants/consultation';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { isHtmlContentEmpty } from '@/utils/textUtil';

import CustomModal from '@/core/components/modal';
import PrimaryButton from '@/core/components/primary-button';

import { StyledTypography } from '../consultation/Common';

import OutLinedIconButton from './lifestyle-forms/shared/OutlinedIconButton';

type Props = {
  open: boolean;
  onClose: () => void;
  selectedTitle: string | null;
  setOpen: Dispatch<SetStateAction<boolean>>;
  showCancelButton?: boolean;
  setShowCancelButton: Dispatch<SetStateAction<boolean>>;
};

const { VIEW, EDIT } = noteModes;

const LifestyleNoteModal: FC<Props> = ({
  open,
  onClose,
  setShowCancelButton,
  showCancelButton,
}) => {
  const {
    records,
    updateLifestyleNotes,
    createLifestyleNote,
    mode,
    setMode,
    selectedRecord,
    setSelectedRecord,
    loading,
  } = useLifestyleNoteStore();
  const { doctorProfile } = useDoctorStore();
  const { patient } = useCurrentPatientStore();

  const { control, handleSubmit, setValue } = useForm({
    defaultValues: { content: '' },
  });

  const handleEdit = useCallback(
    (record: LifestyleNoteRecords) => {
      setSelectedRecord(record);
      setMode(EDIT);
    },
    [setSelectedRecord, setMode]
  );

  const onSubmit = useCallback(
    async (data: { content: string }) => {
      if (isHtmlContentEmpty(data?.content)) return;
      if (!patient?.id) return;

      try {
        const newRecord: LifestyleNoteRecords = {
          author: doctorProfile?.general?.fullName,
          note: data.content,
          patientId: patient?.id,
          tags: [],
          type: 'lifestyleNote',
        };

        if (selectedRecord?.id) {
          await updateLifestyleNotes(selectedRecord?.id, newRecord);
        } else {
          await createLifestyleNote(newRecord);
        }
        onClose();
        setSelectedRecord(null);
      } catch (error) {
        console.error('Error submitting data:', error);
      }
    },
    [
      patient?.id,
      selectedRecord,
      doctorProfile?.general?.fullName,
      onClose,
      setSelectedRecord,
      updateLifestyleNotes,
      createLifestyleNote,
    ]
  );

  useEffect(() => {
    if (selectedRecord) {
      setValue('content', selectedRecord?.note || '');
    }
  }, [selectedRecord, setValue]);

  return (
    <CustomModal
      open={open}
      onClose={onClose}
      title="Notes"
      minHeight={{ xs: '55vh', md: '25vh' }}
      maxHeight={{ xs: '55vh', md: '25vh' }}
      width={{ xs: '90vw', md: '40vw' }}
      formProps={
        mode !== VIEW ? { onSubmit: handleSubmit(onSubmit) } : undefined
      }
      content={
        <>
          {records.length > 0 &&
            mode === VIEW &&
            records.map((record, index) => {
              const isEmpty = isHtmlContentEmpty(record.note);

              return !isEmpty ? (
                <Box
                  key={index}
                  mb={2}
                  sx={{
                    borderBottom: '1px solid #C2CDD6',
                    padding: '8px',
                  }}
                  className="rounded-thin-scrollbar"
                >
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="space-between"
                  >
                    <StyledTypography variant="caption">
                      {formatDate(
                        record.updated_on,
                        DateFormats.READABLE_DATE_TIME
                      )}
                    </StyledTypography>

                    <Box display="flex" alignItems="center" sx={{ gap: 1 }}>
                      <OutLinedIconButton
                        onClick={() => {
                          handleEdit(record);
                          setShowCancelButton(true);
                        }}
                        sx={{ width: 25, height: 25 }}
                      >
                        <LuPencilLine color="black" size={14} />
                      </OutLinedIconButton>
                    </Box>
                  </Box>

                  <EditableText
                    key={record.id}
                    defaultValue={record?.note || ''}
                    editable={false}
                    bg="white"
                    className="break-words whitespace-pre-wrap w-full"
                    emptyPlaceholder={''}
                  />
                </Box>
              ) : null;
            })}

          {mode !== VIEW && (
            <>
              {selectedRecord && mode === EDIT && (
                <StyledTypography variant="caption">
                  {formatDate(
                    selectedRecord.updated_on,
                    DateFormats.READABLE_DATE_TIME
                  )}
                </StyledTypography>
              )}

              <Controller
                name="content"
                control={control}
                render={({ field }) => (
                  <EditableText
                    bg="white"
                    placeholder="Enter text"
                    defaultValue={mode === EDIT ? selectedRecord?.note : ''}
                    {...field}
                    emptyPlaceholder={''}
                  />
                )}
              />
            </>
          )}
        </>
      }
      actions={
        mode === VIEW && records.length > 0 ? (
          <PrimaryButton
            className="capitalize h-8 text-md"
            onClick={() => {
              setMode('create');
              setSelectedRecord(null);
              setShowCancelButton(true);
            }}
          >
            Add New Note
          </PrimaryButton>
        ) : (
          <>
            {showCancelButton && (
              <PrimaryButton
                onClick={() => setMode(VIEW)}
                className="capitalize text-md h-8 !bg-[#637D92]"
              >
                Cancel
              </PrimaryButton>
            )}

            <PrimaryButton
              type="submit"
              className="capitalize text-md h-8"
              isLoading={loading}
            >
              Save Changes
            </PrimaryButton>
          </>
        )
      }
    />
  );
};

export default memo(LifestyleNoteModal);
