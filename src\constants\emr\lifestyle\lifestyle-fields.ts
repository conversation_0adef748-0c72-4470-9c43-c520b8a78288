export const LifestyleFields = {
  RADIO: 'radio',
  SLIDER: 'slider',
  CONDITIONAL: 'conditional',
  NUMBER: 'number',
  TEXT: 'text',
  TABLE: 'table',
  TEXTAREA: 'textarea',
  SECTION: 'section',
  GROUPED_TABLE: 'grouped_table',
  TIME_RANGE: 'time_range',
  CONDITIONAL_SELECT: 'conditional_select',
  FREQUENCY: 'frequency',
  SEARCHABLE_SELECT: 'searchable_select',
  DEPENDENT_AUTOFILL: 'dependent_autofill',
  FIELD_GROUP: 'field_group',
} as const;

export type LifestyleFields =
  (typeof LifestyleFields)[keyof typeof LifestyleFields];
