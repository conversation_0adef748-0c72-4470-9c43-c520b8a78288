import { ReactNode } from 'react';

export type SidebarItem = {
  label: string;
  path: string;
  icon: ReactNode;
  disabled?: boolean;
  permissions?: string[];
  department?: string[];
};

export type SidebarProps = {
  items?: SidebarItem[];
  highlightColor?: string;
  renderBottom?: () => ReactNode;
};

export interface NavButtonProps {
  icon: ReactNode;
  text: string;
  href: string;
  disabled?: boolean;
  highlightColor?: string;
}
