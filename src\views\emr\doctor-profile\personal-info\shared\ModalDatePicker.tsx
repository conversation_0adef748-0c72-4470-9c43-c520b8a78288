import React, { memo, useState } from 'react';

import {
  Dialog,
  DialogContent,
  IconButton,
  SxProps,
  TextField,
} from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';

import DateIcon from '@/assets/svg/DateIcon';

interface Props {
  value?: Dayjs | null;
  onChange?: (_date: Dayjs | null) => void;
  disabled?: boolean;
  isNotValid?: boolean;
  sx?: SxProps;
  minDate?: Dayjs;
}

const TableDatePicker = ({
  value,
  onChange,
  disabled,
  isNotValid,
  minDate,
  sx,
}: Props) => {
  const [open, setOpen] = useState(false);

  const handleDateChange = (date: Dayjs | null) => {
    onChange?.(date);
    setOpen(false);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        <TextField
          value={value ? dayjs(value).format('DD/MM/YYYY') : ''}
          placeholder="DD/MM/YYYY"
          size="small"
          disabled={disabled}
          fullWidth
          error={isNotValid}
          sx={{
            '& .MuiInputBase-root': {
              fontSize: '0.8rem',
              border: 'none',
              paddingRight: '30px',
              ...(isNotValid && { color: 'red' }),
              '&.Mui-disabled': {
                backgroundColor: 'rgb(229 231 235 / var(--tw-bg-opacity))',
                color: 'black !important',
                opacity: '1 !important',
              },
            },
            '& input::placeholder': {
              fontSize: '0.7rem',
            },
            '& fieldset': { border: 'none' },
            ...sx,
          }}
          InputProps={{
            endAdornment: (
              <IconButton
                size="small"
                onClick={() => !disabled && setOpen(true)}
                disabled={disabled}
                sx={{ marginRight: '-10px' }}
              >
                <DateIcon />
              </IconButton>
            ),
            readOnly: true,
          }}
        />

        <Dialog
          open={open}
          onClose={() => setOpen(false)}
          maxWidth={false}
          PaperProps={{
            sx: {
              width: 320,
              height: 'auto',
              p: 0,
              overflow: 'hidden',
              borderRadius: 2,
              boxShadow: 24,
            },
          }}
        >
          <DialogContent
            sx={{
              p: 0,
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <DateCalendar
              value={value}
              onChange={handleDateChange}
              minDate={minDate}
              sx={{
                width: 290,
                height: 'auto',
                '& .MuiPickersDay-root': {
                  fontSize: '0.75rem',
                  width: 30,
                  height: 30,
                  margin: '5px',
                },
                '& .MuiCalendarPicker-root': {
                  padding: 0,
                  margin: 0,
                },
              }}
            />
          </DialogContent>
        </Dialog>
      </div>
    </LocalizationProvider>
  );
};

export default memo(TableDatePicker);
