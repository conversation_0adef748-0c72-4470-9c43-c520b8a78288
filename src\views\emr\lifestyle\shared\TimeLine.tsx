import React, { FC, memo } from 'react';

import Loading from '@/lib/common/loading';

type TimeLineItem = {
  content: React.ReactNode;
  id: string;
};

type Props = {
  items: TimeLineItem[];
  loading?: boolean;
};

const TimeLine: FC<Props> = ({ items, loading = false }) => {
  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Loading />
      </div>
    );
  } else if (items?.length === 0) {
    return (
      <div className="w-full h-full flex items-center justify-center text-secondary-foreground">
        No records found.
      </div>
    );
  } else {
    return (
      <div className="w-full h-full flex flex-col flex-1 min-h-0 gap-2">
        {items?.map((item) => (
          <div key={item?.id} className="flex w-full">
            <div className="w-full flex flex-col">{item.content}</div>
          </div>
        ))}
      </div>
    );
  }
};

export default memo(TimeLine);
