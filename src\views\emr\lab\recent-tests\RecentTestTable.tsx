import React, { memo } from 'react';

import { getOrDefault } from '@/utils/common';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { TestResultItem } from '@/types/emr/lab';

import PrintButton from '../print-button';

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

type Props = {
  test: TestResultItem;
};

const RecentTestTable = ({ test }: Props) => {
  const testList = Object.entries(test.labTests).flatMap(
    ([_, labTestArray]) => labTestArray
  );

  return (
    <div className="p-1 rounded-md border-2 mb-2">
      <div className="py-2 flex justify-between items-center">
        <span className="text-sm font-semibold ">
          {formatDate(test.created_on, DATE_DD_MM_YYYY_SLASH)}
        </span>
        <PrintButton testItem={test} iconButtonProps={{ size: 'small' }} />
      </div>
      <div className="flex flex-col w-full">
        {testList.map((labTest) => (
          <div
            key={labTest.id}
            className="flex flex-nowrap border-x border-t border-black w-full text-xs -tracking-[0.04em] last-of-type:border-b"
          >
            <div className="w-1/3 p-1 min-w-0 border-r border-black break-words">
              {labTest.testName}
            </div>
            <div className="w-1/3 p-1 min-w-0 border-r border-black break-words">
              {getOrDefault(labTest.results, 'Result')}
            </div>
            <div className="w-1/3 p-1 min-w-0 border-black break-words">
              {getOrDefault(labTest.reference, 'Reference')}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default memo(RecentTestTable);
