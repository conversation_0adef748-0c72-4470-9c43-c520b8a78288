'use client';

import React, { useCallback } from 'react';

import { SingleValue, MultiValue, ActionMeta, Props } from 'react-select';

import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import { useTheme } from '@mui/material/styles';
import { FiSearch } from 'react-icons/fi';

import dynamic from 'next/dynamic';

import AppTextField from '../app-text-field';
import InputLabel from '../input-label';

import { getBaseStyles, getBaseTheme } from './theme';
import { AppSelectFieldProps } from './type';

const ReactSelect = dynamic(() => import('react-select'), {
  ssr: false,
  loading: () => <AppTextField />,
});

function BaseSelect<T>(props: Props<T, boolean>) {
  return <ReactSelect {...(props as any)} />;
}

// Custom DropdownIndicator component with search icon
const SearchDropdownIndicator = (props: any) => {
  const { selectProps } = props;

  if (!selectProps.showSearchIcon) {
    return null;
  }

  return (
    <div
      {...props.innerProps}
      style={{
        alignItems: 'center',
        display: 'flex',
        padding: '8px',
        cursor: 'pointer',
      }}
    >
      <FiSearch
        size={16}
        color={selectProps.isDisabled ? '#9ca3af' : '#6b7280'}
      />
    </div>
  );
};

const AppSelect = <T extends Record<string, any>>(
  props: AppSelectFieldProps<T> & { showSearchIcon?: boolean }
) => {
  const {
    label,
    placeholder = 'Choose',
    options = [],
    value,
    isMulti = false,
    components: passedComponents = {},
    formControlProps = {},
    getOptionValue = (op) => String(op?.value),
    getOptionLabel = (op) => String(op?.label),
    required,
    helperText,
    error = false,
    inputLabelProps,
    onChange,
    color = 'primary',
    isDisabled = false,
    initiallyReadonly: _initiallyReadonly = false,
    showSearchIcon = false,
    ...rest
  } = props;

  const theme = useTheme();

  const customComponents: any = {
    IndicatorSeparator: null,
    DropdownIndicator: showSearchIcon ? SearchDropdownIndicator : null,
    ...passedComponents,
  };

  const handleChange = useCallback(
    (newValue: MultiValue<T> | SingleValue<T>, actionMeta: ActionMeta<T>) => {
      if (onChange) {
        if (isMulti) {
          (
            onChange as unknown as (
              newValue: T[],
              actionMeta?: ActionMeta<T>
            ) => void
          )(newValue as T[], actionMeta);
        } else {
          (onChange as (newValue: T, actionMeta?: ActionMeta<T>) => void)(
            newValue as T,
            actionMeta
          );
        }
      }
    },
    [onChange, isMulti]
  );

  return (
    <FormControl
      fullWidth
      size="small"
      color={color}
      disabled={isDisabled}
      sx={{ width: '100%' }}
      {...formControlProps}
    >
      <InputLabel
        label={label}
        required={required}
        className="mb-0 md:mb-0 text-sm md:text-sm"
      />
      <BaseSelect<T>
        id="select"
        menuPlacement="auto"
        options={options}
        isMulti={isMulti}
        value={value}
        components={customComponents}
        placeholder={placeholder}
        styles={getBaseStyles<T>(theme, error, color) as any}
        theme={(current) => getBaseTheme(current, theme)}
        getOptionValue={getOptionValue}
        getOptionLabel={getOptionLabel}
        onChange={handleChange}
        isDisabled={isDisabled}
        required={required}
        showSearchIcon={showSearchIcon}
        {...(rest as any)}
      />
      {helperText && (
        <FormHelperText sx={{ color: theme.palette.error.main }}>
          {helperText}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export default AppSelect;
