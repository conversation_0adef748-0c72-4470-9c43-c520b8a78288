import React, { useState, useEffect } from 'react';

import { Control, Controller, FieldPath } from 'react-hook-form';

import { Box, TextareaAutosize } from '@mui/material';

import colors from '@/utils/colors';

import PencilIcon from '@/assets/svg/PencilIcon';

import CustomModal from '@/core/components/modal';
import PrimaryButton from '@/core/components/primary-button';

import { Prescription } from '../NewPrescription';

import CustomTextField from './TableCustomTextField';

interface InstructionsSelectorProps {
  name: `prescription.${number}.${string}`;
  control: Control<Prescription>;
  rules?: { [key: string]: any };
  clearErrors: (
    name?: FieldPath<Prescription> | FieldPath<Prescription>[]
  ) => void;
  isNotValid?: boolean;
  setValue: (name: `prescription.${number}.${string}`, value: string) => void;
}

const InstructionsSelector: React.FC<InstructionsSelectorProps> = ({
  name,
  control,
  rules,
  clearErrors,
  isNotValid,
  setValue,
}) => {
  const [open, setOpen] = useState(false);
  const [instructionValue, setInstructionValue] = useState('');
  const [displayValue, setDisplayValue] = useState('');

  useEffect(() => {
    const fieldValue =
      control._formValues?.prescription?.[name.split('.')[1]]?.[
        name.split('.')[2]
      ];

    if (fieldValue) {
      setInstructionValue(fieldValue);
      setDisplayValue(fieldValue);
    } else {
      setInstructionValue('');
      setDisplayValue('');
    }
  }, [control._formValues, name]);
  const handleOpen = () => {
    setOpen(true);
    const fieldValue =
      control._formValues?.prescription?.[name.split('.')[1]]?.[
        name.split('.')[2]
      ];
    if (fieldValue) {
      setInstructionValue(fieldValue);
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSave = () => {
    setDisplayValue(instructionValue);

    const parts = name.split('.');
    const index = parseInt(parts[1]);
    const field = parts[2];

    const currentValues = { ...control._formValues };
    if (currentValues.prescription && currentValues.prescription[index]) {
      currentValues.prescription[index][field] = instructionValue;

      control._subjects.state.next({
        ...currentValues,
      });
      setValue(name, instructionValue);
      if (clearErrors) {
        clearErrors(name);
      }
    }

    setOpen(false);
  };

  return (
    <div>
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({}) => (
          <>
            <CustomTextField
              value={displayValue}
              onClick={handleOpen}
              endAdornmentIcon={
                <PencilIcon
                  className={`w-auto h-3 ${isNotValid ? 'text-[#E4626F]' : 'text-black'}`}
                />
              }
              placeholder="Instructions"
              color={
                displayValue
                  ? 'inherit'
                  : isNotValid
                    ? ` ${colors.common.redRose} `
                    : 'gray'
              }
            />

            <CustomModal
              open={open}
              onClose={handleClose}
              title="Instructions"
              width="450px"
              minHeight="85px"
              titleBoxSx={{ mb: 0 }}
              titleTypographySx={{ fontSize: '18px', fontWeight: 600 }}
              showDivider={false}
              contentSx={{ mb: 0 }}
              actionsSx={{
                justifyContent: 'center',
                mr: undefined,
              }}
              content={
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '100%',
                  }}
                >
                  <Box
                    sx={{
                      flex: 1,
                      backgroundColor: '#f5f5f5',
                      mb: 2,
                    }}
                  >
                    <TextareaAutosize
                      value={instructionValue}
                      onChange={(e) => setInstructionValue(e.target.value)}
                      style={{
                        width: '100%',
                        height: '100px',
                        padding: '8px',
                        border: 'none',
                        backgroundColor: '#f5f5f5',
                        resize: 'none',
                        outline: 'none',
                      }}
                    />
                  </Box>
                </Box>
              }
              actions={
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    gap: 1,
                    width: '100%',
                    px: 1,
                  }}
                >
                  <PrimaryButton
                    type="button"
                    onClick={handleClose}
                    className="capitalize text-black bg-[#C2CDD6] text-md h-7 w-full"
                  >
                    Cancel
                  </PrimaryButton>
                  <PrimaryButton
                    type="button"
                    onClick={handleSave}
                    className="capitalize text-md h-7 w-full"
                  >
                    Save
                  </PrimaryButton>
                </Box>
              }
            />
          </>
        )}
      />
    </div>
  );
};

export default InstructionsSelector;
