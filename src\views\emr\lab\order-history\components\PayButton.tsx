import React from 'react';

interface PayButtonProps {
  disabled: boolean;
  onClick: () => void;
}

const PayButton: React.FC<PayButtonProps> = ({ disabled, onClick }) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`px-10 py-1 font-bold text-white bg-gray-800 border border-gray-800 rounded-md shadow-sm hover:bg-gray-900 disabled:opacity-50 disabled:cursor-not-allowed font-archivo`}
    >
      Pay
    </button>
  );
};

export default PayButton;
