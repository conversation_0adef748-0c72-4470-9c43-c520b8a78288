export interface ProfileData {
  general: {
    name: string;
    designation: string;
    department: string;
    employeeID: string;
    mobile: string;
    workEmail: string;
  };
  personal: {
    age: number;
    bloodGroup: string;
    height: number;
    weight: number;
    isPersonWithDisability: boolean;
    percentOfDisability: number;
    identificationMark: string;
    maritalStatus: string;
    dateOfWedding: string;
    nationality: string;
    religion: string;
    caste: string;
    category: string;
    reservationDetails: string;
    idProof: {
      description: string;
      url: string;
    };
    address: {
      permanent: {
        home: string;
        street: string;
        city: string;
        pin: string;
        district: string;
        state: string;
        country: string;
        residentialPhone: string;
        personalPhone: string;
        personalEmail: string;
        proof: {
          description: string;
          url: string;
        };
      };
      current: {
        home: string;
        street: string;
        city: string;
        pin: string;
        district: string;
        state: string;
        country: string;
        residentialPhone: string;
        personalPhone: string;
        personalEmail: string;
        proof: {
          description: string;
          url: string;
        };
      };
    };
  };
  emergencyContacts: {
    name: string;
    relation: string;
    city: string;
    residencePhone: string;
    mobile: string;
    email: string;
  }[];
  bankDetails: {
    name: string;
    branch: string;
    ifsc: string;
    accountNumber: string;
    document: {
      description: string;
      url: string;
    };
  };
  familyMembers: {
    name: string;
    relation: string;
    dependent: string;
    dob: string;
    status: string;
    aadharNumber: string;
    occupation: string;
    doc: string;
  }[];
  medicalInsurance: {
    policy: string;
    policyNumber: string;
    from: string;
    to: string;
    status: string;
  }[];
  documents: {
    aadhar: {
      number: string;
      name: string;
      issuedAt: string;
      description: string;
      url: string;
    };
    passport: {
      number: string;
      name: string;
      issuedAt: string;
      renewedAt: string;
      issuedPlace: string;
      description: string;
      url: string;
    };
    panCard: {
      number: string;
      name: string;
      issuedAt: string;
      description: string;
      url: string;
    };
  };
  qualifications: {
    title: string;
    specialization: string;
    university: string;
    institute: string;
    duration: string;
    yearOfCompletion: string;
    marks: string;
    docs: string[];
    status: string;
  }[];
  certifications: {
    name: string;
    regNumber: string;
    validFrom: string;
    validTo: string;
    dateOfUpdation: string;
    status: string;
  }[];
  employment: {
    employerName: string;
    designation: string;
    from: string;
    to: string;
    salary: string;
    docs: string[];
    status: string;
  }[];
  languagesKnown: {
    name: string;
    actions: ('read' | 'write' | 'speak')[];
  }[];
}
