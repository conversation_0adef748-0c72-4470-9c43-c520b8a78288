import React from 'react';

interface DashboardWidgetCardProps {
  title: string;
  count: number | string | null;
  status: string;
  icon: React.ReactNode;
  bgColor: string;
  borderColor: string;
  className?: string;
}

const DashboardWidgetCard: React.FC<DashboardWidgetCardProps> = ({
  title,
  count,
  status,
  icon,
  bgColor,
  borderColor,
  className = '',
}) => {
  const formatCount = (value: number | string | null): string => {
    if (value === null || value === undefined) return '---';
    if (typeof value === 'string') return value;
    if (typeof value === 'number' && value >= 10000) {
      return Math.round(value / 1000) + 'k';
    }
    return value.toString();
  };

  return (
    <div
      className={`rounded-lg p-4 ${className}`}
      style={{
        backgroundColor: bgColor,
        borderColor: borderColor,
        borderWidth: '1px',
        borderStyle: 'solid',
        borderRadius: '8px',
        height: '120px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
      }}
    >
      {/* Header Row - Title and Icon */}
      <div className="flex items-start justify-between">
        <h3
          className="font-medium"
          style={{
            fontSize: '14px',
            fontWeight: 500,
            color: '#001926',
            lineHeight: '1.2',
          }}
        >
          {title}
        </h3>
        <div className="flex-shrink-0">{icon}</div>
      </div>

      {/* Bottom Section - Count and Status */}
      <div className="flex flex-col">
        {/* Count */}
        <div
          className="font-medium mb-1"
          style={{
            fontSize: '32px',
            fontWeight: 500,
            color: '#000000',
            lineHeight: '1',
          }}
        >
          {formatCount(count)}
        </div>

        {/* Status */}
        <div
          className="text-xs"
          style={{
            fontSize: '10px',
            fontWeight: 300,
            color: '#404040',
            lineHeight: '1.2',
          }}
        >
          {status}
        </div>
      </div>
    </div>
  );
};

export default DashboardWidgetCard;
