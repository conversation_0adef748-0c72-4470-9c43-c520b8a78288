import { useEffect, useState } from 'react';

import { cn } from '@/lib/utils';

import colors from '@/utils/colors';

import { PatientStatus } from '@/constants/mrd/manage-patient/consultation';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';

type PatientStatusSelectorProps = {
  value?: PatientStatus | (string & {});
  onChange?: (value: PatientStatus) => void;
  disabled?: boolean;
};

export default function PatientStatusSelector(
  props: PatientStatusSelectorProps
) {
  const [localValue, setLocalValue] = useState<string>(PatientStatus.Booked);

  const options = [
    {
      label: 'Patient Arrived',
      value: PatientStatus.Arrived,
    },
    {
      label: 'Patient Booked',
      value: PatientStatus.Booked,
    },
    {
      label: 'Patient Proxy Visit',
      value: PatientStatus.ProxyVisit,
    },
    {
      label: 'Patient No-Show',
      value: PatientStatus.NoShow,
    },
  ];

  function handleChange(value: string) {
    setLocalValue(value);

    if (props.onChange) {
      props.onChange(value as PatientStatus);
    }
  }

  useEffect(() => {
    setLocalValue(props.value || '');
  }, [props.value]);

  return (
    <div className="min-w-[30%] xl:min-w-[10%]">
      <Select value={localValue} onValueChange={handleChange}>
        <SelectTrigger
          disabled={props.disabled}
          className={cn(
            [
              'text-black rounded-full border-none',
              'transition-all duration-300',
              `text-sm md:text-xs h-9 md:h-7.5 px-3 md:px-3 bg-[${colors.common.paleBlue}]`,
            ],
            {
              'bg-[#dae1e7]': localValue === PatientStatus.Booked,
              'bg-[#06c6a7]': localValue === PatientStatus.Arrived,
              'bg-[#ff9f2a]': localValue === PatientStatus.ProxyVisit,
              'bg-[#e4626f]': localValue === PatientStatus.NoShow,
            }
          )}
        >
          <div className="flex-grow text-center">
            {localValue ? localValue : 'Select a status'}
          </div>
        </SelectTrigger>

        <SelectContent className="rounded-lg">
          {options.map((option) => (
            <SelectItem
              key={option.value}
              className="last:border-b-[0] border-b border-[#C2CDD6] rounded-b-none h-9 md:h-7 text-sm md:text-xs"
              value={option.value}
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
