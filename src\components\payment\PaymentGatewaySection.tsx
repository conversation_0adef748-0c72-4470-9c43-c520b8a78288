'use client';

import React from 'react';

import { useUserStore } from '@/store/userStore';

import { PERMISSION_KEYS } from '@/constants/permission-keys';

import { hasPermission } from '@/core/lib/auth/permissions';

interface PaymentFeature {
  id: string;
  label: string;
  permission: string;
  checked: boolean;
}

interface PaymentGatewaySectionProps {
  paymentFeatures: PaymentFeature[];
  onFeatureToggle: (featureId: string, checked: boolean) => void;
}

const PaymentGatewaySection: React.FC<PaymentGatewaySectionProps> = ({
  paymentFeatures,
  onFeatureToggle,
}) => {
  const { permissions = [] } = useUserStore();

  const hasPaymentCreatePermission = hasPermission(
    permissions,
    PERMISSION_KEYS.PAYMENT_CREATE
  );

  if (!hasPaymentCreatePermission) {
    return null;
  }

  const availableFeatures = paymentFeatures.filter((feature) =>
    hasPermission(permissions, feature.permission)
  );

  if (availableFeatures.length === 0) {
    return null;
  }

  return (
    <div className="mt-10 max-w-5xl">
      <label
        className="text-base font-medium text-black mb-4 block"
        style={{ fontSize: '14px', fontWeight: 500 }}
      >
        Payment Gateway
      </label>

      <div className="mt-4">
        <h3 className="text-sm font-medium text-gray-700 mb-3">
          Billing Feature Access
        </h3>

        <div className="space-y-3">
          {availableFeatures.map((feature) => (
            <div key={feature.id} className="flex items-center">
              <input
                type="checkbox"
                id={feature.id}
                checked={feature.checked}
                onChange={(e) => onFeatureToggle(feature.id, e.target.checked)}
                className="h-4 w-4 text-black focus:ring-gray-500 border-gray-300 rounded accent-black"
                style={{ accentColor: 'black' }}
              />
              <label
                htmlFor={feature.id}
                className="ml-3 text-sm font-medium text-gray-700 cursor-pointer"
                style={{ fontSize: '14px', fontWeight: 500 }}
              >
                {feature.label}
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PaymentGatewaySection;
