import { FC, memo, useEffect, useMemo } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { LifestyleMode } from '@/constants/emr/lifestyle';

import { Questionnaire } from '@/types/emr/lifestyle/questionnaire';

import ExercisePatternForm from './ExercisePatternForm';

type Props = {
  data: Questionnaire;
};

const ExercisePatternTimelineForm: FC<Props> = ({ data }) => {
  const methods = useForm<Questionnaire>({
    defaultValues: data,
    mode: 'onChange',
  });

  const formFields = useMemo(() => {
    if (!data?.questions?.length) return [];
    return data.questions;
  }, [data]);

  useEffect(() => {
    if (data) {
      const transformedData = {
        ...data,
        questions: data.questions?.map((questionGroup, _groupIndex) => ({
          ...questionGroup,
          fields: questionGroup.fields?.map((field, _fieldIndex) => {
            if (field.value !== undefined) {
              return {
                ...field,
              };
            }
            return field;
          }),
        })),
      };

      const formData: any = {};
      data.questions?.forEach((questionGroup, groupIndex) => {
        if (!formData.questions) formData.questions = [];
        if (!formData.questions[groupIndex])
          formData.questions[groupIndex] = { fields: [] };

        questionGroup.fields?.forEach((field, fieldIndex) => {
          if (field.value !== undefined) {
            if (!formData.questions[groupIndex].fields[fieldIndex]) {
              formData.questions[groupIndex].fields[fieldIndex] = {};
            }
            formData.questions[groupIndex].fields[fieldIndex].value =
              field.value;
          }
        });
      });

      methods.reset({ ...transformedData, ...formData });
    }
  }, [data, methods]);

  return (
    <FormProvider {...methods}>
      <ExercisePatternForm
        formFields={formFields}
        readonly={true}
        showHeading={true}
        mode={LifestyleMode.VIEW}
        variant="timeline"
      />
    </FormProvider>
  );
};

export default memo(ExercisePatternTimelineForm);
