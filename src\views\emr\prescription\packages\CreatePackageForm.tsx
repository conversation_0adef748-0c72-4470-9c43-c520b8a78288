import React from 'react';

import { usePrescriptionPackageStore } from '@/store/emr/prescription/package';

import AppTextField from '@/core/components/app-text-field';
import TableV2 from '@/core/components/table-v2';
import { HeaderV2, RowV2 } from '@/core/components/table-v2/types';

import PackageMedicineSearch from './PackageMedicineSearch';

interface CreatePackageFormProps {
  tableHeaders: HeaderV2[];
  medicineRows: RowV2[];
}

export const CreatePackageForm: React.FC<CreatePackageFormProps> = ({
  tableHeaders,
  medicineRows,
}) => {
  const {
    newPackageName,
    editingPackageName,
    selectedPackage,
    medicineItems,
    setPackageName,
  } = usePrescriptionPackageStore();

  const isEditingExisting = !!selectedPackage;

  const packageName = isEditingExisting ? editingPackageName : newPackageName;

  return (
    <div className="p-4 flex flex-col h-full">
      <div className="mb-4 space-y-3">
        <div>
          <PackageMedicineSearch />
        </div>
        <div className="w-1/2">
          <AppTextField
            value={packageName}
            onChange={setPackageName}
            fullWidth
            size="small"
            placeholder="Name your package"
            initiallyReadonly={!!packageName}
          />
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        {medicineItems.length > 0 ? (
          <TableV2
            headers={tableHeaders}
            rows={medicineRows}
            stickyHeader
            tableContainerProps={{
              sx: {
                maxHeight: '400px',
              },
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <p>Search and add medicines to create a package.</p>
          </div>
        )}
      </div>
    </div>
  );
};
